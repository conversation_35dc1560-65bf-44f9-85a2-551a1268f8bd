"""
AI Model Configuration

This file centralizes all AI model configurations used throughout the application.
Update model names and configurations here to easily switch between different providers.
"""

# AI Analysis Models
ANALYSIS_MODELS = {
    "gemini": {
        "name": "gemini-2.5-pro",
        "provider": "google",
        "enabled": True
    },
    "qwen": {
        "name": "qwen2.5vl:7b",
        "provider": "ollama",
        "enabled": True
    },
    "llava": {
        "name": "llava:7b",
        "provider": "ollama",
        "enabled": True
    },
    "llama": {
        "name": "llama3.2-vision:11b",
        "provider": "ollama",
        "enabled": True
    },
    "gemma": {
        "name": "gemma3:4b",
        "provider": "ollama",
        "enabled": True
    }
}

# Dialogue Generation Models
DIALOGUE_MODELS = {
    "gemini": {
        "name": "gemini-2.5-flash-lite",
        "provider": "google",
        "enabled": True
    },    
    "gpt": {
        "name": "gpt-oss:20b",
        "provider": "ollama",
        "enabled": True
    },
    "deepseek": {
        "name": "deepseek-r1:8b",
        "provider": "ollama",
        "enabled": True
    },   
    "llama": {
        "name": "llama3",
        "provider": "ollama",
        "enabled": True
    }
}

# Text-to-Speech Models
TTS_MODELS = {
    "typecast": {
        "provider": "typecast",
        "enabled": True,
        "voice_id": "default"  # Can be overridden per request
    },
    "kokoro": {
        "provider": "kokoro",
        "enabled": True,
        "voice_id": "default"  # Can be overridden per request
    },
    "elevenlabs": {
        "provider": "elevenlabs",
        "enabled": False,
        "voice_id": "default"
    }
}

def get_default_model(model_type: str) -> str:
    """Get the default model name for a given type"""
    if model_type == "analysis":
        return ANALYSIS_MODELS["gemini"]["name"]
    elif model_type == "dialogue":
        return DIALOGUE_MODELS["gemini"]["name"]
    elif model_type == "tts":
        return "kokoro"
    raise ValueError(f"Unknown model type: {model_type}")

def is_model_enabled(model_type: str, model_name: str) -> bool:
    """Check if a specific model is enabled"""
    model_map = {
        "analysis": ANALYSIS_MODELS,
        "dialogue": DIALOGUE_MODELS,
        "tts": TTS_MODELS
    }
    
    if model_type not in model_map:
        return False
        
    model = model_map[model_type].get(model_name, {})
    return model.get("enabled", False)
