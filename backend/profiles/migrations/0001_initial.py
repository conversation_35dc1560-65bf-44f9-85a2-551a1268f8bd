# Generated by Django 5.1.4 on 2025-05-06 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='StudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('age', models.IntegerField()),
                ('grade', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pain_points', models.JSONField(blank=True, default=list, help_text="Student's learning challenges")),
                ('data_sources', models.JSONField(blank=True, default=list, help_text='Sources of profile data')),
                ('video_settings', models.J<PERSON><PERSON>ield(blank=True, default=dict, help_text='Optional video creation settings for temp profiles', null=True)),
            ],
        ),
        migrations.CreateModel(
            name='LearningStyle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('style', models.CharField(max_length=50)),
                ('confidence', models.FloatField()),
                ('source', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='learning_style', to='profiles.studentprofile')),
            ],
        ),
        migrations.CreateModel(
            name='KnowledgeLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=50)),
                ('level', models.IntegerField()),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='knowledge_levels', to='profiles.studentprofile')),
            ],
        ),
        migrations.CreateModel(
            name='Interest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('topic', models.CharField(max_length=100)),
                ('confidence', models.FloatField()),
                ('source', models.CharField(max_length=50)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interests', to='profiles.studentprofile')),
            ],
        ),
        migrations.CreateModel(
            name='YouTubeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consent', models.BooleanField(default=False)),
                ('last_synced', models.DateTimeField(auto_now=True)),
                ('watched_videos', models.JSONField(blank=True, default=list, help_text='List of watched video IDs with watch time and dates')),
                ('subscribed_channels', models.JSONField(blank=True, default=list, help_text='List of channels the user is subscribed to')),
                ('search_history', models.JSONField(blank=True, default=list, help_text='Recent YouTube searches')),
                ('created_playlists', models.JSONField(blank=True, default=list, help_text='Playlists created by the user')),
                ('saved_playlists', models.JSONField(blank=True, default=list, help_text='Playlists saved by the user')),
                ('likes', models.JSONField(blank=True, default=list, help_text='Videos the user has liked')),
                ('comments', models.JSONField(blank=True, default=list, help_text='Comments made by the user')),
                ('profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='youtube_data', to='profiles.studentprofile')),
            ],
        ),
    ]
