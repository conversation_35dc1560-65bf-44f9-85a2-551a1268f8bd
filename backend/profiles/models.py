from django.db import models

class StudentProfile(models.Model):
    name = models.CharField(max_length=100, null= True, blank=True)
    age = models.IntegerField(default=0)
    grade = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    def __str__(self):
        if not self.name or not self.name.strip() or self.name == '-':
            return f"Grade {self.grade} Student (ID: {self.id})"
        return f"{self.name} (ID: {self.id})"
    pain_points = models.JSONField(
        default=list,
        blank=True,
        help_text="Student's learning challenges"
    )
    data_sources = models.JSONField(
        default=list,
        blank=True,
        help_text="Sources of profile data"
    )
    # Add optional video creation settings
    video_settings = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        help_text="Optional video creation settings for temp profiles"
    )

    def is_temp_profile(self):
        return self.name.startswith('temp_')

class Interest(models.Model):
    profile = models.ForeignKey(StudentProfile, related_name='interests', on_delete=models.CASCADE)
    topic = models.CharField(max_length=100)
    confidence = models.FloatField()
    source = models.CharField(max_length=50)

class LearningStyle(models.Model):
    profile = models.OneToOneField(StudentProfile, related_name='learning_style', on_delete=models.CASCADE)
    style = models.CharField(max_length=50)
    confidence = models.FloatField()
    source = models.CharField(max_length=50)
    description = models.TextField(null=True, blank=True)

class KnowledgeLevel(models.Model):
    profile = models.ForeignKey(StudentProfile, related_name='knowledge_levels', on_delete=models.CASCADE)
    subject = models.CharField(max_length=50)
    level = models.IntegerField()

class YouTubeData(models.Model):
    profile = models.OneToOneField(StudentProfile, related_name='youtube_data', on_delete=models.CASCADE)
    consent = models.BooleanField(default=False)
    last_synced = models.DateTimeField(auto_now=True)
    
    # Watched Videos - optional
    watched_videos = models.JSONField(
        default=list, 
        blank=True,
        help_text="List of watched video IDs with watch time and dates"
    )
    
    # Subscribed Channels - optional
    subscribed_channels = models.JSONField(
        default=list,
        blank=True,
        help_text="List of channels the user is subscribed to"
    )
    
    # Search History - optional
    search_history = models.JSONField(
        default=list,
        blank=True,
        help_text="Recent YouTube searches"
    )
    
    # Playlists - optional
    created_playlists = models.JSONField(
        default=list,
        blank=True,
        help_text="Playlists created by the user"
    )
    saved_playlists = models.JSONField(
        default=list,
        blank=True,
        help_text="Playlists saved by the user"
    )
    
    # Engagement Metrics - optional
    likes = models.JSONField(
        default=list,
        blank=True,
        help_text="Videos the user has liked"
    )
    comments = models.JSONField(
        default=list,
        blank=True,
        help_text="Comments made by the user"
    )
