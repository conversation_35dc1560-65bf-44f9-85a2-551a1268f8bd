from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import StudentProfile, LearningStyle, KnowledgeLevel, YouTubeData
from .serializers import StudentProfileSerializer
from .utils.youtube_parser import parse_takeout_data

class StudentProfileViewSet(viewsets.ModelViewSet):
    queryset = StudentProfile.objects.all()
    serializer_class = StudentProfileSerializer

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except StudentProfile.DoesNotExist:
            return Response(
                {"error": "Profile not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['POST'])
    def upload_youtube_data(self, request, pk=None):
        profile = self.get_object()
        
        if 'takeout_file' not in request.FILES:
            return Response({'error': 'No file uploaded'}, status=400)
            
        try:
            parsed_data = parse_takeout_data(request.FILES['takeout_file'])
            
            youtube_data, _ = YouTubeData.objects.get_or_create(
                profile=profile,
                defaults={'consent': True}
            )
            
            youtube_data.watched_videos = parsed_data['watched_videos']
            youtube_data.search_history = parsed_data['search_history']
            youtube_data.likes = parsed_data['likes']
            youtube_data.save()
            
            return Response({'message': 'YouTube data processed successfully'})
            
        except Exception as e:
            return Response({'error': str(e)}, status=400)
