from django.contrib import admin
from .models import StudentProfile, Interest, LearningStyle, KnowledgeLevel, YouTubeData
from django.db import transaction
from profiles.models import Interest, LearningStyle, KnowledgeLevel, YouTubeData
class InterestInline(admin.TabularInline):
    model = Interest
    extra = 0
@admin.register(StudentProfile)
class StudentProfileAdmin(admin.ModelAdmin):
    list_display = ('name', 'age', 'grade')
    search_fields = ('name',)
    inlines = [InterestInline]
    
    def delete_queryset(self, request, queryset):
        # Use a transaction to ensure all deletions succeed or none do
        with transaction.atomic():
            # Delete all related objects in the correct order
            # First, delete all many-to-one relationships
            Interest.objects.filter(profile__in=queryset).delete()
            KnowledgeLevel.objects.filter(profile__in=queryset).delete()
        
            # Then delete one-to-one relationships
            LearningStyle.objects.filter(profile__in=queryset).delete()
            YouTubeData.objects.filter(profile__in=queryset).delete()
            
            # Finally delete the profiles themselves
            queryset.delete()

@admin.register(Interest)
class InterestAdmin(admin.ModelAdmin):
    list_display = ('profile', 'topic', 'confidence')

@admin.register(LearningStyle)
class LearningStyleAdmin(admin.ModelAdmin):
    list_display = ('profile', 'style', 'confidence')

@admin.register(KnowledgeLevel)
class KnowledgeLevelAdmin(admin.ModelAdmin):
    list_display = ('profile', 'subject', 'level')

@admin.register(YouTubeData)
class YouTubeDataAdmin(admin.ModelAdmin):
    list_display = ('profile', 'consent', 'last_synced')
    readonly_fields = ('last_synced',)

    def get_video_count(self, obj):
        return len(obj.watched_videos)
    get_video_count.short_description = 'Videos Watched'
