from rest_framework import serializers
from .models import StudentProfile, Interest, LearningStyle, KnowledgeLevel, YouTubeData

class InterestSerializer(serializers.ModelSerializer):
    class Meta:
        model = Interest
        fields = ['topic', 'confidence', 'source']

class LearningStyleSerializer(serializers.ModelSerializer):
    class Meta:
        model = LearningStyle
        fields = ['style', 'confidence', 'source', 'description']

class KnowledgeLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = KnowledgeLevel
        fields = ['subject', 'level']

class YouTubeDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = YouTubeData
        fields = [
            'consent',
            'last_synced',
            'watched_videos',
            'subscribed_channels',
            'search_history',
            'created_playlists',
            'saved_playlists',
            'likes',
            'comments'
        ]

class StudentProfileSerializer(serializers.ModelSerializer):
    interests = InterestSerializer(many=True, read_only=True)
    learning_style = LearningStyleSerializer(read_only=True)
    knowledge_levels = KnowledgeLevelSerializer(many=True, read_only=True)
    youtube_data = YouTubeDataSerializer(read_only=True)
    name = serializers.CharField(required=False, allow_blank=True, allow_null=True)


    class Meta:
        model = StudentProfile
        fields = [
            'id', 
            'name', 
            'age', 
            'grade', 
            'interests', 
            'learning_style', 
            'knowledge_levels', 
            'youtube_data', 
            'pain_points', 
            'data_sources'
        ] 