<!DOCTYPE html>
<html>
<head>
    <title>LTI Launch</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <div class="container">
        <h1>LTI Launch Successful</h1>
        <p>You have successfully launched the Quiz Tool via LTI.</p>
        
        {% if user.is_authenticated %}
            <p>Welcome, {{ user.username }}!</p>
            <p>Your roles in this context: {{ lti_roles|join:", " }}</p>
            
            <div id="quiz-container">
                <!-- Quiz content will be loaded here -->
                <h2>Available Quizzes</h2>
                <div id="quizzes-list">
                    <!-- Quizzes will be dynamically loaded here -->
                </div>
            </div>
        {% else %}
            <p>Authentication failed. Please try again.</p>
        {% endif %}
    </div>
    
    <script>
        // Add any client-side LTI handling here
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LTI launch page loaded');
            // Additional JavaScript for quiz loading can go here
        });
    </script>
</body>
</html>
