from typing import List, Dict
from pydantic import BaseModel, Field
from enum import Enum

class QuestionType(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    SCALE = "scale"
    FREE_TEXT = "free_text"
    CHECKBOX = "checkbox"

class Question(BaseModel):
    id: str
    type: QuestionType
    text: str
    options: List[str] = []
    category: str  # interests, learning_style, knowledge, challenges

    class Config:
        use_enum_values = True

class Questionnaire(BaseModel):
    questions: List[Question] = [
        # Interests
        Question(
            id="interests_1",
            type=QuestionType.CHECKBOX,
            text="What subjects does your child enjoy most?",
            options=["Science", "Math", "History", "Art", "Music", "Reading", "Writing", "Sports"],
            category="interests"
        ),
        Question(
            id="interests_2",
            type=QuestionType.FREE_TEXT,
            text="What topics or activities get your child most excited?",
            category="interests"
        ),
        
        # Learning Style
        Question(
            id="learning_1",
            type=QuestionType.SCALE,
            text="How does your child prefer to learn new things?",
            options=["Strongly Visual", "Somewhat Visual", "Balanced", "Somewhat Auditory", "Strongly Auditory"],
            category="learning_style"
        ),
        Question(
            id="learning_2",
            type=QuestionType.MULTIPLE_CHOICE,
            text="When explaining something new, what works best?",
            options=[
                "Showing pictures or diagrams",
                "Verbal explanations",
                "Hands-on demonstrations",
                "Reading about it",
                "A combination of methods"
            ],
            category="learning_style"
        ),
        
        # Knowledge Levels
        Question(
            id="knowledge_1",
            type=QuestionType.SCALE,
            text="How would you rate your child's reading comprehension?",
            options=["Below Grade Level", "Approaching Grade Level", "At Grade Level", "Above Grade Level", "Well Above Grade Level"],
            category="knowledge"
        ),
        Question(
            id="knowledge_2",
            type=QuestionType.SCALE,
            text="How would you rate your child's math skills?",
            options=["Below Grade Level", "Approaching Grade Level", "At Grade Level", "Above Grade Level", "Well Above Grade Level"],
            category="knowledge"
        ),
        
        # Challenges
        Question(
            id="challenges_1",
            type=QuestionType.CHECKBOX,
            text="What aspects of learning does your child find challenging?",
            options=[
                "Reading long texts",
                "Writing assignments",
                "Math problems",
                "Memorization",
                "Staying focused",
                "Following instructions",
                "Working independently"
            ],
            category="challenges"
        ),
        Question(
            id="challenges_2",
            type=QuestionType.FREE_TEXT,
            text="What strategies have worked well for overcoming learning challenges?",
            category="challenges"
        )
    ]

    def get_by_category(self, category: str) -> List[Question]:
        return [q for q in self.questions if q.category == category] 