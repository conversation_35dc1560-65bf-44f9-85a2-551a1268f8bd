import json
from datetime import datetime
from typing import List, Dict

class YouTubeAnalyzer:
    def analyze_takeout_data(self, data: Dict) -> Dict:
        """Analyze YouTube Takeout JSON data"""
        try:
            watched_videos = []
            search_history = []
            
            # Parse watch history
            if 'watch-history' in data:
                for entry in data['watch-history']:
                    if 'titleUrl' in entry:  # Only count actual video watches
                        watched_videos.append({
                            'video_id': entry['titleUrl'].split('=')[-1],
                            'title': entry['title'],
                            'channel': entry.get('subtitles', [{}])[0].get('name'),
                            'watched_at': entry['time']
                        })
            
            # Parse search history
            if 'search-history' in data:
                for entry in data['search-history']:
                    search_history.append({
                        'query': entry['query'],
                        'timestamp': entry['time']
                    })
            
            return {
                'watched_videos': watched_videos,
                'search_history': search_history
            }
            
        except Exception as e:
            print(f"Error analyzing YouTube data: {e}")
            return {
                'watched_videos': [],
                'search_history': []
            } 