from typing import List, Optional
import requests
from .schema import StudentProfile, StudentInterest, LearningStyle, ProfileSource
from django.conf import settings

class StudentProfiler:
    def __init__(self):
        self.api_key = settings.PERPLEXITY_API_KEY
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def analyze_questionnaire(self, responses: dict) -> StudentProfile:
        """Analyze structured questionnaire responses"""
        prompt = f"""
        Analyze these questionnaire responses and create a student profile:
        {responses}
        
        Extract:
        1. Student interests and confidence levels
        2. Learning style and confidence level
        3. Knowledge levels in different subjects
        4. Potential pain points or challenges
        """
        
        profile_data = self._query_perplexity(prompt)
        return self._parse_profile(profile_data, ProfileSource.QUESTIONNAIRE)

    def analyze_freeform(self, text: str) -> StudentProfile:
        """Analyze parent's freeform description"""
        prompt = f"""
        Analyze this parent's description of their child and create a student profile:
        {text}
        
        Extract:
        1. Student interests and confidence levels
        2. Learning style and confidence level
        3. Knowledge levels in different subjects
        4. Potential pain points or challenges
        """
        
        profile_data = self._query_perplexity(prompt)
        return self._parse_profile(profile_data, ProfileSource.FREEFORM)

    def analyze_youtube(self, transcripts: List[str]) -> StudentProfile:
        """Analyze YouTube watch history and transcripts"""
        prompt = f"""
        Analyze these YouTube video transcripts and create a student profile:
        {transcripts}
        
        Extract:
        1. Topics and themes the student is interested in
        2. Preferred video styles suggesting learning style
        3. Video complexity indicating knowledge levels
        4. Attention patterns and potential challenges
        """
        
        profile_data = self._query_perplexity(prompt)
        return self._parse_profile(profile_data, ProfileSource.YOUTUBE)

    def combine_profiles(self, profiles: List[StudentProfile]) -> StudentProfile:
        """Combine profiles from different sources, weighing by confidence"""
        # Implementation to merge profiles, considering confidence levels
        pass

