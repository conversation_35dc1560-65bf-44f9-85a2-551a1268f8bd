from typing import List, Optional
from pydantic import BaseModel, Field
from enum import Enum

class ProfileSource(Enum):
    QUESTIONNAIRE = "questionnaire"
    FREEFORM = "freeform"
    YOUTUBE = "youtube"

class StudentInterest(BaseModel):
    topic: str
    confidence: float = Field(default=0.8, ge=0, le=1.0)
    source: ProfileSource
    
class LearningStyle(BaseModel):
    style: str  # visual, auditory, kinesthetic, etc.
    confidence: float = Field(default=0.8, ge=0, le=1.0)
    source: ProfileSource

class StudentProfile(BaseModel):
    id: str
    name: str
    age: int
    grade: int
    interests: List[StudentInterest]
    learning_style: LearningStyle
    knowledge_levels: dict
    pain_points: List[str]
    data_sources: List[ProfileSource] 