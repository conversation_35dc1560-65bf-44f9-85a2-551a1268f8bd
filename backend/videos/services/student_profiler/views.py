from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from profiles.models import StudentProfile
from .utils import StudentProfiler
from .questionnaire import Questionnaire
from .youtube_analyzer import <PERSON>Analy<PERSON>

profiler = StudentProfiler()
questionnaire = Questionnaire()
youtube_analyzer = YouTubeAnalyzer()

@csrf_exempt
@require_http_methods(["GET"])
def get_questionnaire(request):
    """Get the questionnaire structure"""
    return JsonResponse({
        'questions': questionnaire.dict()['questions']
    })

@csrf_exempt
@require_http_methods(["POST"])
def submit_questionnaire(request):
    """Submit questionnaire responses"""
    try:
        data = json.loads(request.body)
        profile = profiler.analyze_questionnaire(data['responses'])
        return JsonResponse(profile.dict())
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def submit_freeform(request):
    """Submit freeform text description"""
    try:
        data = json.loads(request.body)
        profile = profiler.analyze_freeform(data['text'])
        return JsonResponse(profile.dict())
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def analyze_youtube(request):
    """Analyze YouTube watch history"""
    try:
        data = json.loads(request.body)
        history = youtube_analyzer.get_watch_history(data['playlist_id'])
        video_ids = [v['contentDetails']['videoId'] for v in history]
        transcripts = youtube_analyzer.get_transcripts(video_ids)
        analysis = youtube_analyzer.analyze_history(history, transcripts)
        profile = profiler.analyze_youtube(analysis)
        return JsonResponse(profile.dict())
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def combine_profiles(request):
    """Combine multiple profile sources"""
    try:
        data = json.loads(request.body)
        profiles = [StudentProfile(**p) for p in data['profiles']]
        combined = profiler.combine_profiles(profiles)
        return JsonResponse(combined.dict())
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500) 