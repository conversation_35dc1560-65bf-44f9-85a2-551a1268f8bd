import requests
import cv2
import base64
import os
from pathlib import Path
import tempfile
from typing import List, Dict
import subprocess
import logging
import numpy as np
logger = logging.getLogger(__name__)
# from videos.services.video_generator.script_processor import ScriptProcessor
from pexelsapi.pexels import Pexels
from django.conf import settings
import time
# Suppress FFmpeg output
os.environ['FFMPEG_LOG_LEVEL'] = 'error'
os.environ['MOVIEPY_VERBOSE'] = '0'

# Suppress other logging
import logging
logging.basicConfig(level=logging.WARNING)
logging.getLogger('moviepy').setLevel(logging.WARNING)
logging.getLogger('ffmpeg').setLevel(logging.WARNING)
pexels_api_key = settings.PEXELS_API_KEY
pixabay_api_key = settings.PIXABAY_API_KEY
coverr_api_key = settings.COVERR_API_KEY
freepik_api_key = settings.FREEPIK_API_KEY
def get_tiny_videos_from_pexels(keyword, download_dir,results_to_download: int = 5, existing_video_ids: List[str] = None) -> List[Dict]:
    """
    Search for videos on Pexels using simple keywords.
    Downloads multiple videos based on results_to_download parameter.
    Checks against existing_video_ids to avoid duplicates.
    Returns a list of video data dictionaries.
    """
    logger.info("Search for videos on Pexels using simple keywords")
    try:
        # print("\n=== Video Search Debug Info ===")
        # print(f"Query: {keyword}")
        # print(f"Videos to download: {results_to_download}")
        if existing_video_ids is None:          #should this be set to empty here
            existing_video_ids = []
        downloaded_videos_list = []
        temp_dir = Path(download_dir) / "temp"
        temp_dir.mkdir(exist_ok=True)
        page = 1
        max_pages_to_check = 10  # Increased to get more videos
        min_duration_sec = 20
        max_duration_sec = 300
        videos_processed = 0
        pexel = Pexels(pexels_api_key)
        while len(downloaded_videos_list) < results_to_download and page <= max_pages_to_check:
            try:
                per_page_count = 80  # Pexels max is 80 per page
                print(f"Searching Pexels for '{keyword}' on page {page} with {per_page_count} results per page...")
                search_results = pexel.search_videos(
                    query=keyword,  #try to put quotes around it
                    orientation='landscape',
                    size='small',
                    page=page,
                    per_page=per_page_count
                )
                videos = search_results.get('videos', [])
                if not videos:
                    print(f"No videos found for '{keyword}' on page {page}")
                    break
                for video in videos:
                    if len(downloaded_videos_list) >= results_to_download:
                        break
                    video_id = str(video.get('id'))
                    duration = video.get('duration', 0)
                    if (duration < min_duration_sec or duration > max_duration_sec):
                        print(f"skipping {video_id}. duration outside acceptable range: {duration}")
                        continue
                    if video_id in existing_video_ids:
                        print(f"Skipping duplicate video {video_id}")
                        continue                    
                    video_files = video.get('video_files', [])
                    video_url = None
                    MAX_VIDEO_SIZE = 200 * 1024 * 1024
                    MIN_WIDTH = 960
                    TARGET_WIDTH = 1280
                    TARGET_HEIGHT = 720

                    # Step 1: Try to get HD videos that meet min width
                    preferred = [
                        vf for vf in video_files
                        if vf.get('quality') == 'hd'
                        and vf.get('width', 0) >= MIN_WIDTH
                        and vf.get('size', 0) <= MAX_VIDEO_SIZE
                        and is_aspect_ratio_close(vf.get('width', 0), vf.get('height', 0))
                    ]

                    # Step 2: If none, try any HD video under max size
                    preferred = [
                        vf for vf in video_files
                        if vf.get('quality') == 'hd'
                        and vf.get('width', 0) >= MIN_WIDTH
                        and vf.get('size', 0) <= MAX_VIDEO_SIZE
                        and is_aspect_ratio_close(vf.get('width', 0), vf.get('height', 0))
                    ]

                    # Step 3: If still none, try any video under max size
                    if not preferred:
                        preferred = [
                        vf for vf in video_files
                        if vf.get('quality') == 'hd'
                        and vf.get('width', 0) >= MIN_WIDTH
                        and vf.get('size', 0) <= MAX_VIDEO_SIZE
                        and is_aspect_ratio_close(vf.get('width', 0), vf.get('height', 0))
                    ]

                    # Step 4: If still none, try any video at all
                    if not preferred:
                        preferred = video_files

                    # Step 5: Pick the closest by dimensions, if any
                    if preferred:
                        def score(vf):
                            return abs(vf.get('width', 0) - TARGET_WIDTH) + abs(vf.get('height', 0) - TARGET_HEIGHT)
                        best = min(preferred, key=score)
                        video_url = best.get('link')
                        print(f"Selected fallback video url: {video_url}")
                    else:
                        print(f"No suitable video URL found for video {video_id}")
                        video_url = None
                                        
                    
                    temp_path = temp_dir / f"temp_pexels_{video_id}.mp4"
                    try:
                        print(f"Downloading tiny Pexels video {video_id} to temp folder...")
                        best_30s_start = download_video_sample(video_url, str(temp_path),duration=5)
                        # print(f"Best 30s start: {best_30s_start}")
                        video['best_30s_start'] = best_30s_start
                        downloaded_videos_list.append(video)
                    except Exception as e:
                        print(f"Error processing Pexels video {video_id}: {str(e)}")
                    videos_processed += 1
            except Exception as e:
                print(f"Error searching for '{keyword}' on page {page}: {str(e)}")
                break
            page += 1
            time.sleep(1)  # Rate limiting
        # print(f"\nSuccessfully processed {len(downloaded_videos_list)} Pexels videos with sufficient motion for '{keyword}'")
        # print(f"Processed {videos_processed} total videos to find {len(downloaded_videos_list)} suitable ones")
        return downloaded_videos_list
    except Exception as e:
        print(f"Error in pexels video search and download: {str(e)}")
        return []
def get_tiny_videos_from_pixabay(keyword, download_dir,results_to_download: int = 5, existing_video_ids: List[str] = None) -> List[Dict]:
    """
    Search for videos on Pixabay using keywords.
    Downloads multiple videos based on results_to_download parameter.
    Checks against existing_video_ids to avoid duplicates.
    Returns a list of video data dictionaries.
    """
    logger.info("Search for videos on Pixabay using keywords")
    pixabay_base_url = "https://pixabay.com/api/videos/"
    try:
        print("\n=== Pixabay Video Search Debug Info ===")
        print(f"Input keywords: {keyword}")
        print(f"Videos to download: {results_to_download}")
        if existing_video_ids is None:
            existing_video_ids = []
        downloaded_videos_list = []
        temp_dir = Path(download_dir) / "temp"
        temp_dir.mkdir(exist_ok=True)
        page = 1
        max_pages_to_check = 10  # Increased to get more videos
        min_duration_sec = 20
        max_duration_sec = 300
        videos_processed = 0
        while len(downloaded_videos_list) < results_to_download and page <= max_pages_to_check:
            try:
                per_page_count = 80
                print(f"Searching Pixabay for '{keyword}' on page {page} with {per_page_count} results per page...")
                params = {
                    'key': pixabay_api_key,
                    'q': keyword,
                    'per_page': per_page_count,
                    'page': page,
                    # 'video_type': 'film',
                    'order': 'popular',
                    #'editors_choice': 'true',
                    # 'safesearch': 'true'
                }
                response = requests.get(pixabay_base_url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    # print(f"Pixabay response: {data}")
                    videos = data.get('hits', [])
                    if not videos:
                        print(f"No videos found for '{keyword}' on page {page}")
                        break
                    for video in videos:
                        if len(downloaded_videos_list) >= results_to_download:
                            break
                        video_id = str(video.get('id'))
                        duration = video.get('duration', 0)
                        if (duration < min_duration_sec or duration > max_duration_sec):
                            print(f"skipping {video_id}. duration outside acceptable range: {duration}")
                            continue
                        if video_id in existing_video_ids:
                            print(f"Skipping duplicate video {video_id}")
                            continue
                        
                        video_candidates = [
                            v for v in video.get('videos', {}).values()
                            if is_aspect_ratio_close(v.get('width', 0), v.get('height', 0))
                        ]
                        if not video_candidates:
                            print(f"Skipping video {video_id} due to aspect ratio")
                            continue
                        best = max(video_candidates, key=lambda v: v.get('width', 0))
                        video_url = best.get('url')
                        temp_path = temp_dir / f"temp_pixabay_{video_id}.mp4"
                        try:
                            print(f"Downloading tiny sample of Pixabay video {video_id}...")
                            best_30s_start = download_video_sample(video_url, str(temp_path), duration=5)
                            # print(f"Best 30s start: {best_30s_start}")
                            video['best_30s_start'] = best_30s_start
                            downloaded_videos_list.append(video)
                        except Exception as e:
                            print(f"Error processing Pixabay video {video_id}: {str(e)}")
                            if os.path.exists(temp_path):
                                os.remove(temp_path)
                            continue
                        videos_processed += 1
                else:
                    print(f"Error searching for '{keyword}': {response.status_code} - {response.text}")
                    break
            except Exception as e:
                print(f"Error searching for '{keyword}' on page {page}: {str(e)}")
                break
            page += 1
            time.sleep(1)  # Rate limiting
        print(f"Successfully processed {len(downloaded_videos_list)} Pixabay videos with sufficient motion for '{keyword}'\n")
        print(f"Processed {videos_processed} total videos to find {len(downloaded_videos_list)} suitable ones")
        return downloaded_videos_list
        
    except Exception as e:
        print(f"Error in Pixabay video search and download: {str(e)}")
        return []
def get_tiny_videos_from_coverr(keyword, download_dir, results_to_download=5, existing_video_ids=None):
    """
    Search Coverr for videos matching the keyword.
    Downloads and returns a list of video dicts.
    """
    if existing_video_ids is None:
        existing_video_ids = []
    
    logger.info("Search for videos on Coverr using keywords")
    try:
        print(f"\n=== Coverr Video Search Debug Info ===")
        print(f"Query: {keyword}")
        print(f"Videos to download: {results_to_download}")
        
        downloaded_videos_list = []
        temp_dir = Path(download_dir) / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        # According to Coverr API docs, we need to include urls=true to get video URLs
        url = "https://api.coverr.co/videos"
        headers = {
            "Authorization": f"Bearer {coverr_api_key}"
        }
        params = {
            "query": keyword,
            "page_size": results_to_download * 2,  # Get more to filter
            "urls": "true",  # Required to get video URLs
            "page": 0
        }
        
        resp = requests.get(url, headers=headers, params=params)
        resp.raise_for_status()
        data = resp.json()
        
        for video in data.get("hits", []):
            if len(downloaded_videos_list) >= results_to_download:
                break
                
            vid = str(video.get("id"))
            if vid in existing_video_ids:
                continue
                
            # Check duration (Coverr provides duration in seconds)
            duration = video.get("duration", 0)
            if duration < 30 or duration > 300:
                print(f"Skipping {vid}. duration outside acceptable range: {duration}")
                continue
                
            # Get video URL from Coverr's urls structure
            urls = video.get("urls", {})
            video_url = urls.get("mp4")  # Use mp4 for full quality
            if not video_url:
                print(f"No video URL found for {vid}")
                continue
                
            temp_path = temp_dir / f"temp_coverr_{vid}.mp4"
            try:
                print(f"Downloading tiny sample of Coverr video {vid}...")
                best_30s_start = download_video_sample(video_url, str(temp_path), duration=5)
                print(f"Best 30s start: {best_30s_start}")
                
                # Map to expected structure matching other video sources
                video_data = {
                    "id": vid,
                    "video_files": [{
                        "link": video_url,
                        "width": video.get("max_width", 0),
                        "height": video.get("max_height", 0),
                        "quality": "hd" if video.get("max_width", 0) >= 1280 else "sd"
                    }],
                    "best_30s_start": best_30s_start or 0,
                    "title": video.get("title"),
                    "duration": duration,
                    "tags": video.get("tags", [])
                }
                downloaded_videos_list.append(video_data)
                
            except Exception as e:
                print(f"Error processing Coverr video {vid}: {str(e)}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                continue
                
        print(f"\nSuccessfully processed {len(downloaded_videos_list)} Coverr videos for '{keyword}'")
        return downloaded_videos_list
        
    except Exception as e:
        print(f"Error in Coverr video search and download: {str(e)}")
        return []
def get_tiny_videos_from_freepik(keyword, download_dir, results_to_download: int = 5, existing_video_ids: List[str] = None) -> List[Dict]:
    """
    Search for videos on Freepik using simple keywords.
    Downloads multiple videos based on results_to_download parameter.
    Checks against existing_video_ids to avoid duplicates.
    Returns a list of video data dictionaries.
    """
    logger.info("Search for videos on Freepik using simple keywords")
    try:
        print("\n=== Freepik Video Search ===")
        print(f"Query: {keyword}")
        print(f"Videos to download: {results_to_download}")
        
        if existing_video_ids is None:
            existing_video_ids = []
            
        downloaded_videos_list = []
        temp_dir = Path(download_dir) / "temp"
        temp_dir.mkdir(exist_ok=True)
        
        # Use Freepik API to search for videos
        url = "https://api.freepik.com/v1/resources"
        headers = {
            "x-freepik-api-key": freepik_api_key.strip()
        }
        params = {
            "query": keyword,
            "limit": min(results_to_download * 2, 20),  # Max 20 per page
            "type": "video",  # Filter for video content
            "page": 1,
            "order": "relevance"
        }
        
        resp = requests.get(url, headers=headers, params=params, timeout=30)
        resp.raise_for_status()
        data = resp.json()
        
        # The API returns videos in the 'data' array
        for video in data.get("data", []):
            if len(downloaded_videos_list) >= results_to_download:
                break
                
            vid = str(video.get("id"))
            if vid in existing_video_ids:
                continue
                
            # Get video metadata
            meta = video.get("meta", {})
            available_formats = meta.get("available_formats", {})
            
            # Look for video format in available formats
            video_format = None
            if "mp4" in available_formats:
                video_format = "mp4"
            elif "webm" in available_formats:
                video_format = "webm"
            
            if not video_format:
                print(f"Skipping {vid}. No supported video format found")
                continue
                
            # Get the best quality video
            video_items = available_formats[video_format].get("items", [])
            if not video_items:
                print(f"Skipping {vid}. No video items found")
                continue
                
            # Get the highest quality version
            video_item = max(video_items, key=lambda x: x.get("size", 0))
            video_url = video_item.get("url")
            
            if not video_url:
                print(f"No video URL found for {vid}")
                continue
                
            # Get duration if available
            duration = meta.get("duration", 0)  # in seconds
            if duration > 0 and (duration < 30 or duration > 300):
                print(f"Skipping {vid}. Duration outside acceptable range: {duration}s")
                continue
                
            temp_path = temp_dir / f"temp_freepik_{vid}.{video_format}"
            try:
                print(f"Downloading sample of Freepik video {vid}...")
                best_30s_start = download_video_sample(video_url, str(temp_path), duration=5)
                print(f"Best 30s start: {best_30s_start}")
                
                # Map to expected structure matching other video sources
                video_data = {
                    "id": vid,
                    "video_files": [{
                        "link": video_url,
                        "width": video_item.get("width", 0),
                        "height": video_item.get("height", 0),
                        "quality": "hd" if video_item.get("width", 0) >= 1280 else "sd",
                        "file_type": video_format
                    }],
                    "best_30s_start": best_30s_start or 0,
                    "title": video.get("title", f"Freepik Video {vid}"),
                    "duration": duration,
                    "tags": video.get("tags", []),
                    "source": "freepik"
                }
                downloaded_videos_list.append(video_data)
                
            except Exception as e:
                print(f"Error processing Freepik video {vid}: {str(e)}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                continue
                
        print(f"\nSuccessfully processed {len(downloaded_videos_list)} Freepik videos for '{keyword}'")
        return downloaded_videos_list
        
    except Exception as e:
        print(f"Error in Freepik video search and download: {str(e)}")
        return []
            
def retry(func, max_attempts=3, delay=2, *args, **kwargs):
    for attempt in range(max_attempts):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"Attempt {attempt+1} failed: {e}")
            if attempt < max_attempts - 1:
                time.sleep(delay)
            else:
                raise
def run_ffmpeg_download(command):
    # Add loglevel parameter to suppress FFmpeg output
    if "ffmpeg" in command[0]:
        command = command[:1] + ["-loglevel", "error"] + command[1:]
    subprocess.run(command, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
def speed_up_and_process_video(
    video_url, output_path, speed_factor=1.0, 
    start_time=0, duration=None, scale="1280:720", keep_audio=False
):
    import subprocess
    setpts = 1 / speed_factor
    vf_filters = [f"scale={scale}", f"setpts={setpts}*PTS", "fps=30"]
    vf = ",".join(vf_filters)
    command = [
        "ffmpeg",
        "-ss", str(start_time),
        "-i", video_url,
        "-loglevel", "error",  # Suppress FFmpeg output
    ]
    if duration:
        command += ["-t", str(duration)]
    command += [
        "-vf", vf,
        "-c:v", "libx264",
        "-y",
    ]
    if not keep_audio:
        command += ["-an"]
    command.append(output_path)
    subprocess.run(command, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

def download_video_sample(video_url: str, output_path: str,  duration: int = 5, width: int = 256, height: int = 144) -> int:
    """
    Download full video, analyze for best segments, then crop 5s sample.
    Returns the best 30s start time.
    """
    # processor = ScriptProcessor()
    try:
        # Create temp directory
        temp_dir = Path(output_path).parent
        temp_dir.mkdir(exist_ok=True)
        
        # Step 1: Download the entire video to a temp file
        full_video_path = temp_dir / f"full_{Path(output_path).name}"
        download_command = [
            'ffmpeg',
            '-i', video_url,
            '-c:v', 'libx264',
            '-vf', f'scale={width}:{height}',
            '-y',
            '-loglevel', 'error',  # Suppress FFmpeg output
            str(full_video_path)
        ]
        retry(run_ffmpeg_download, max_attempts=3, delay=2, command=download_command)

        # Step 2: Analyze the full video to find best segments
        best_5s_start = find_best_5s_start(str(full_video_path))
        best_30s_start = find_best_30s_start(str(full_video_path))
        
        # Step 3: Crop the 5-second sample for analysis
        crop_command = [
            'ffmpeg',
            '-ss', str(best_5s_start),
            '-i', str(full_video_path),
            '-t', str(duration),
            '-vf', f'scale={width}:{height}',
            '-c:v', 'libx264',
            '-an',
            '-y',
            '-loglevel', 'error',  # Suppress FFmpeg output
            str(output_path)
        ]
        retry(run_ffmpeg_download, max_attempts=3, delay=2, command=crop_command)
        # Clean up the full video
        os.remove(full_video_path)
        
        # Return the best 30s start time for later use
        return best_30s_start
    except Exception as e:
        logger.error(f"Error downloading video sample: {str(e)}")
        return None
def get_best_coverr_video_url(video_files, target_width, target_height):
    """
    Get the best video URL from Coverr's video_files structure.
    According to Coverr API docs, video_files is a list of objects with link, width, height, quality.
    """
    if not video_files:
        return None
        
    # Filter for HD videos first
    hd_videos = [vf for vf in video_files if vf.get('quality') == 'hd']
    if not hd_videos:
        hd_videos = video_files  # fallback to any quality
        
    # Find the closest match by width/height
    def score(vf):
        return abs(vf.get('width', 0) - target_width) + abs(vf.get('height', 0) - target_height)
    
    if hd_videos:
        best = min(hd_videos, key=score)
        return best.get('link')
        
    return None
def get_best_pixabay_video_url(videos_dict, target_width, target_height):
    video = videos_dict.get('videos', {})
    # print(f"get best pixabay url video dict: {video}")
    candidates = []
    for size in ['tiny', 'small', 'medium', 'large']:
        v = video.get(size)
        if v:
            candidates.append(v)
    if not candidates:
        return None
    # Find the closest match by width/height
    def score(v):
        width = v.get('width', 0)
        height = v.get('height', 0)
        return abs(width - target_width) + abs(height - target_height)
    
    best = min(candidates, key=score)
    return best.get('url')
def get_best_freepik_video_url(video_files, target_width, target_height):
    video_files = video_files.get('video_files', [])
    hd_videos = [vf for vf in video_files if vf.get('quality') == 'hd']
    if not hd_videos:
        hd_videos = video_files  # fallback to any quality
    
    # Find the closest match by width/height
    def score(vf):
        return abs(vf.get('width', 0) - target_width) + abs(vf.get('height', 0) - target_height)
    best = min(hd_videos, key=score)
    return best.get('link')
def get_best_pexels_video_url(video_files, target_width, target_height):
        # print(f"get best pexels url video files: {video_files}")
        video_files = video_files.get('video_files', [])
        hd_videos = [vf for vf in video_files if vf.get('quality') == 'hd']
        if not hd_videos:
            hd_videos = video_files  # fallback to any quality

        # Find the closest match by width/height
        def score(vf):
            return abs(vf.get('width', 0) - target_width) + abs(vf.get('height', 0) - target_height)
        best = min(hd_videos, key=score)
        return best.get('link')
def is_aspect_ratio_close(width, height, target_ratio=16/9, tolerance=0.15):
    """
    Returns True if the video's aspect ratio is within the tolerance of the target ratio.
    """
    if not width or not height:
        return False
    actual_ratio = width / height
    return abs(actual_ratio - target_ratio) <= tolerance
def search_sites(keywords, download_dir, results_per_keyword=1, sites=None, tracker=None, already_downloaded=None):
    # Convert to set for faster lookups
    already_downloaded = set(already_downloaded or [])
    processed_videos = []
    
    for site in sites:
        get_videos_func = SITE_VIDEO_FUNCTIONS.get(site)
        if not get_videos_func:
            print(f"Site {site} not supported.")
            continue
            
        for keyword in keywords:
            print(f"\n.................Searching {site} for: {keyword}")
            
            # Get more results than needed to account for filtering
            video_data = get_videos_func(
                keyword, 
                download_dir,
                results_to_download=results_per_keyword,  # Get more results
                existing_video_ids=list(already_downloaded)  # Pass already seen videos
            )
            
            if not video_data:
                print(f"No videos found for {keyword} on {site}")
                continue
                
            new_videos = []
            
            # Filter out already downloaded videos
            for v in video_data:
                vid = str(v.get('id'))
                if vid not in already_downloaded:
                    print(f"Found new video: {vid}")
                    new_videos.append(v)
                    already_downloaded.add(vid)  # Mark as seen
                    
                    if len(new_videos) >= results_per_keyword:
                        break
            
            # Process the new videos
            for v in new_videos:
                vid = str(v.get('id'))
                print(f"Processing video ID: {vid}")
                
                # Add to tracker if provided
                if tracker is not None:
                    target_width = 1280
                    target_height = 720
                    get_best_url_func = globals().get(f"get_best_{site}_video_url")
                    video_url = get_best_url_func(v, target_width, target_height) if get_best_url_func else None
                    
                    tracker.append_row({
                        'Filename': f"{vid}.mp4",
                        'Search Term': keyword,
                        'Subject': '',
                        'Video URL': video_url,
                        'Start Time': int(round(float(v.get('best_30s_start', 0))))
                    })
                
                processed_videos.append(v)
    
    return processed_videos
    
def get_a_clip(keyword, download_dir, results_per_keyword=1, sites=None, tracker=None, already_downloaded=None):
    # Convert to set for faster lookups
    already_downloaded = set(already_downloaded or [])
    processed_videos = []
    
    for site in sites:
        get_videos_func = SITE_VIDEO_FUNCTIONS.get(site)
        if not get_videos_func:
            print(f"Site {site} not supported.")
            continue
            
        print(f"\n.................Searching {site} for: {keyword}")
        
        # Get more results than needed to account for filtering
        video_data = get_videos_func(
            keyword, 
            download_dir,
            results_to_download=results_per_keyword,  # Get more results
            existing_video_ids=list(already_downloaded)  # Pass already seen videos
        )
        
        if not video_data:
            print(f"No videos found for {keyword} on {site}")
            continue
            
        new_videos = []
        
        # Filter out already downloaded videos
        for v in video_data:
            vid = str(v.get('id'))
            if vid not in already_downloaded:
                print(f"Found new video: {vid}")
                new_videos.append(v)
                already_downloaded.add(vid)  # Mark as seen
                
                if len(new_videos) >= results_per_keyword:
                    break
        
        # Process the new videos
        for v in new_videos:
            vid = str(v.get('id'))
            print(f"Processing video ID: {vid}")
            
            # Add to tracker if provided
            if tracker is not None:
                target_width = 1280
                target_height = 720
                get_best_url_func = globals().get(f"get_best_{site}_video_url")
                video_url = get_best_url_func(v, target_width, target_height) if get_best_url_func else None
                
                tracker.append_row({
                    'Filename': f"{vid}.mp4",
                    'Search Term': keyword,
                    'Subject': '',
                    'Video URL': video_url,
                    'Start Time': int(round(float(v.get('best_30s_start', 0))))
                })
            
            processed_videos.append(v)
    
    return processed_videos

def find_best_5s_start( video_path: str) -> int:
    """Find the start time of the best 5s segment."""
    return find_best_segment_start(video_path, segment_length=5)

def find_best_30s_start(video_path: str) -> int:
    """Find the start time of the best 30s segment."""
    return find_best_segment_start(video_path, segment_length=30)

def find_best_segment_start(video_path: str, segment_length: int) -> int:
    """Find the start time of the segment with the highest average motion"""
    # if len(motion_scores) < segment_frames:
    #     return 0.0
    # logger.info("Get best start time")
    # print(f"find_best_segment_start called with: {video_path}, segment_length: {segment_length}")

    cap = cv2.VideoCapture(video_path)
    # print(f"cap.isOpened(): {cap.isOpened()}")
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    segment_frames = int(segment_length * fps)
    duration = total_frames / fps
        # print(f"Total frames: {total_frames}, FPS: {fps}, Duration: {duration}")
        # Analyze motion throughout the video
    motion_scores = []
    ret, prev_frame = cap.read()
    # print(f"First frame read: {ret}")
    if not ret:
        cap.release()
        raise ValueError("Could not read video file")
    
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    
    for frame_idx in range(1, total_frames):
        ret, frame = cap.read()
        if not ret:
            break
            
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        diff = cv2.absdiff(prev_gray, gray)
        motion_score = np.sum(diff)
        motion_scores.append(motion_score)
        prev_gray = gray
    
    cap.release()


    max_avg_motion = 0
    best_start_frame = 0
    
    for start_frame in range(len(motion_scores) - segment_frames + 1):
        segment_motion = motion_scores[start_frame:start_frame + segment_frames]
        avg_motion = sum(segment_motion) / len(segment_motion)
        
        if avg_motion > max_avg_motion:
            max_avg_motion = avg_motion
            best_start_frame = start_frame
    # print(f"Best start time for {segment_length} sec clip: best startframe / fps: {best_start_frame} / {fps} = {best_start_frame / fps}")
    return best_start_frame / fps




SITE_VIDEO_FUNCTIONS = {
    "pexels": get_tiny_videos_from_pexels,
    "pixabay": get_tiny_videos_from_pixabay,
    "coverr": get_tiny_videos_from_coverr,  # You will need to implement this!
    "freepik": get_tiny_videos_from_freepik,
}