import logging
import subprocess
import time
from typing import Dict
from moviepy import <PERSON>FileClip, VideoFileClip, concatenate_videoclips
import requests
import cv2
import base64
import os
from pathlib import Path
import tempfile
import google.generativeai as genai
from django.conf import settings
import language_tool_python
from typing import List, Dict

from videos.services.audio_generator.utils import combine_audio_segments, text_to_speech

# from backend.videos.services.audio_generator.utils import combine_audio_segments
logger = logging.getLogger(__name__)
tool = language_tool_python.LanguageTool('en-US')

# Suppress FFmpeg and MoviePy output
os.environ['FFMPEG_LOG_LEVEL'] = 'error'
os.environ['MOVIEPY_VERBOSE'] = '0'
logging.getLogger('moviepy').setLevel(logging.WARNING)

def stitch(tts_engine: str, dialogue_path: str, base_dir: str, processed_videos: List[Dict], teacher_voice_id: str, student_voice_id: str) -> List[Dict]:
    try:
        with open(dialogue_path, 'r') as f:
            dialogue = f.read()
        audio_dir = base_dir / 'audio_segments'
        selected_dir = base_dir / 'selected'
        video_segments = []
        audio_segments = []
        lines = dialogue.split('\n')
        # Find video markers
        print("Finding video markers...")
        video_markers = []
        clip_counter = 0
        for i, line in enumerate(lines):
            if '[NEW CLIP:' in line.strip():
                # Extract filename from the marker - handle filenames with spaces
                marker_content = line.split('[')[1].split(']')[0]
                # Split on 'LENGTH:' to separate filename from duration
                filename_part = marker_content.split('LENGTH:')[0]
                # Get everything after the colon and trim
                filename = filename_part.split(':', 1)[1].strip()
                # Extract filename from the marker
                filename = line.split('[')[1].split(']')[0].split(':')[1].strip().split(' ')[0]
                # Find the matching video in selected directory
                video_path = selected_dir / filename
                if not video_path.exists():
                    print(f"Warning: Video file not found: {filename}")
                    continue
                video_markers.append({
                    'index': i,
                    'filename': filename,
                    'full_path': str(video_path)
                })
                clip_counter += 1
        print(f"Video markers: {video_markers}")
        # Process each video sequentially
        print("Processing video segments...")
        for vid_idx, marker in enumerate(video_markers):
            start_idx = marker['index'] + 1
            end_idx = video_markers[vid_idx + 1]['index'] if vid_idx + 1 < len(video_markers) else len(lines)
            # Get dialogue for this clip
            print(f"Getting dialogue for clip {vid_idx + 1}...")
            dialogue_lines = parse_dialogue(lines, start_idx, end_idx)
            # Generate combined audio for this segment (instead of separate files for each line)
            print("Generating combined audio for this dialogue segment...")
            # Collect all dialogue text for this segment
            dialogue_texts = []
            for line_idx, d in enumerate(dialogue_lines):
                if not d['text']:
                    continue
                # Format the text with speaker names for better TTS
                # speaker_name = "Teacher" if d['is_teacher'] else "Student"
                # dialogue_texts.append(f"{speaker_name}: {d['text']}")
            
            # Generate a single combined audio file for the entire segment
            segment_audio_path = str(audio_dir / f"video_{vid_idx + 1}_combined.wav")
            # Create a single text with all dialogue, separated by pauses
            combined_text = " ".join([text for text in dialogue_texts if text.strip()])
            if combined_text:
                # Use the teacher's voice for the combined audio (or student's if that's more appropriate)
                main_voice_id = teacher_voice_id
                audio_file = text_to_speech(tts_engine, combined_text, main_voice_id, str(segment_audio_path))
                if audio_file:
                    audio_segments.append(audio_file)
            
            # Get audio duration
            total_duration = 0
            if audio_segments and os.path.exists(audio_segments[-1]):
                with AudioFileClip(audio_segments[-1]) as audio:
                    total_duration = audio.duration
            # Process video
            print("Processing video clip...")
            video_path = selected_dir / marker['filename']
            if video_path.exists():
                clip = tweak_video(video_path, total_duration)
                if clip:
                    video_segments.append(clip)
                    print(f"Successfully processed video {vid_idx + 1} (duration: {clip.duration:.3f}s)")
                else:
                    print(f"Failed to process video: {marker['filename']}")
            else:
                print(f"Video not found: {marker['filename']}")
        print(f"Stitch: starting with {len(video_segments)} video segments and {len(audio_segments)} audio segments")
        for i, seg in enumerate(video_segments):
            print(f"Video segment {i}: {seg}")
        for i, seg in enumerate(audio_segments):
            print(f"Audio segment {i}: {seg}, exists: {os.path.exists(seg)}")
        print(f"Dialogue path: {dialogue_path}")
        print(f"Base dir: {base_dir}")
        print(f"Processed videos: {processed_videos}")
        print(f"Teacher voice: {teacher_voice_id}, Student voice: {student_voice_id}")
        # Combine all segments
        if not video_segments:
            raise Exception("No video segments were created")
        print(f"Stitch: {len(video_segments)} video segments, {len(audio_segments)} audio segments")
        for seg in video_segments:
            print(f"Video segment: {seg}")
        for seg in audio_segments:
            print(f"Audio segment: {seg}, exists: {os.path.exists(seg)}")
        try:
            final_video = concatenate_videoclips(video_segments, method="compose")
            print("concatenate_videoclips succeeded")
            final_audio = combine_audio_segments(audio_segments, str(audio_dir / 'complete_audio.wav'))
            print(f"combine_audio_segments returned: {final_audio}")
            # final_video.write_videofile(...)
            # print("write_videofile succeeded")
        except Exception as e:
            print(f"Exception in stitch: {e}")
            import traceback
            print(traceback.format_exc())
            raise
        
        # Final sync check and audio attachment
        print("Final sync check...")
        if final_audio and os.path.exists(final_audio):
            audio_clip = AudioFileClip(final_audio)
            if abs(final_video.duration - audio_clip.duration) > 0.001:
                print(f"Final duration mismatch - video: {final_video.duration:.3f}s, audio: {audio_clip.duration:.3f}s")
            final_video = final_video.with_audio(audio_clip)
        
        # Write final video
        output_path = base_dir / 'final_lesson.mp4'
        print(f"\nWriting final video to {output_path}...")
        print(f"Video segments: {[str(v) for v in video_segments]}")
        print(f"Audio segments: {audio_segments}")
        print(f"Does output path exist before write? {output_path.exists()}")
        final_video.write_videofile(
str(output_path),
codec='libx264',
audio_codec='aac',
fps=30,
threads=1,
preset='ultrafast',  # Same as your working crop method
bitrate='1000k',
logger=None,

        )
        print("Final video written successfully")
        
        # Cleanup
        for segment in video_segments:
            segment.close()
        final_video.close()
        if 'audio_clip' in locals():
            audio_clip.close()
        
        # Clean up audio files
        for audio_file in audio_segments:
            if os.path.exists(audio_file):
                os.remove(audio_file)
        
        print(f"Final video created successfully at {output_path}")
        return video_segments
    except Exception as e:
        print(f"Error in stitch: {e}")
        import traceback
        print(traceback.format_exc())
        raise

def tweak_video(video_path, dialogue_duration):
    logger.info("Tweak a video to match dialogue duration exactly by adjusting speed.")
    try:
        print(f"\nProcessing video: {video_path}")
        
        # Check if file exists
        video_file = Path(video_path)
        if not video_file.exists():
            print(f"Video file does not exist: {video_path}")
            return None
        print(f"Video path: {video_path}")
        
        # Calculate speed factor needed
        # Load the video to get original duration
        temp_clip = VideoFileClip(str(video_path))
        original_duration = temp_clip.duration
        temp_clip.close()
        
        print(f"Original duration: {original_duration:.2f}s, Target duration: {dialogue_duration:.2f}s")
        
        # Calculate speed factor needed
        if dialogue_duration <= 0:
            print("Warning: Dialogue duration is zero or negative, using original video")
            return VideoFileClip(str(video_path))
            
        speed_factor = original_duration / dialogue_duration
        print(f"Adjusting speed by factor of {speed_factor:.2f}")
        
        # Speed up the video using ffmpeg
        speed_up_video(video_path, speed_factor)
        
        # Load the sped up video
        sped_up_clip = VideoFileClip(str(video_path))
        print(f"Final clip duration: {sped_up_clip.duration:.2f}s")
        return sped_up_clip
        
    except Exception as e:
        print(f"Error processing video {video_path}: {str(e)}")
        return None


def parse_dialogue(lines, start_idx, end_idx):
    logger.info("Parse dialogue between two video markers, alternating speakers.")
    dialogue_lines = []
    is_teacher = True  # Start with teacher
    
    for i in range(start_idx, end_idx):
        line = lines[i].strip()
        if '[NEW CLIP:' not in line:  # Skip video markers
            if ':' in line:
                print(f"speaker line, before strip: {line[:550]}")
                line = line.split(':', 1)[1].strip()
                print(f"speaker line, sending: {line[:550]}")
            else:
                print("no colon")
            # Every line (including blank) switches speaker
            dialogue_lines.append({
                'text': line,
                'is_teacher': is_teacher
            })
            is_teacher = not is_teacher
    
    return dialogue_lines

def speed_up_video(video_path: str, speed_factor: float = 1.5) -> str:
    video_path = Path(video_path)
    temp_path = video_path.with_name(video_path.stem + "_temp" + video_path.suffix)
    video_filter = f"setpts={1/speed_factor}*PTS,fps=30"
    cmd = [
        "ffmpeg", "-y", "-i", str(video_path),
        "-filter:v", video_filter,
        "-loglevel", "error",  # Suppress FFmpeg output
        str(temp_path)
    ]
    subprocess.run(cmd, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    os.replace(str(temp_path), str(video_path))
    return str(video_path) 
