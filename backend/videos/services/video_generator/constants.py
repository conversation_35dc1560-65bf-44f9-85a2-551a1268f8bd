import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

"""
Constants for video generation using Runway ML
"""

RUNWAY_API_KEY = os.getenv('RUNWAYML_API_SECRET', 'your_runway_api_key_here')  # Changed to RUNWAYML_API_SECRET
print("Loading API key:", RUNWAY_API_KEY[:15] + "...")  # Debug print

# Video generation settings
VIDEO_SETTINGS = {
    'duration': 5,  # seconds per scene
    'fps': 30,
    'width': 1024,
    'height': 576,  # 16:9 aspect ratio
}

# Style presets for different content types
STYLE_PRESETS = {
    'educational': {
        'style': 'cinematic',
        'cfg_scale': 7.5,
    },
    'scientific': {
        'style': 'documentary',
        'cfg_scale': 8.0,
    },
    'narrative': {
        'style': 'storytelling',
        'cfg_scale': 7.0,
    }
} 