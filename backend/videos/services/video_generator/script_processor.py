from datetime import datetime
import logging
from pathlib import Path

from django.conf import settings
import spacy
from .external_video_sites import search_sites
from .ai_analysis import analyze_video_content # You need to create this wrapper function
from .dialog_generation import generate_dialogue
from .video_stitcher import stitch
import language_tool_python

from . import external_video_sites
# from keybert import KeyBERT
# from sentence_transformers import SentenceTransformer


logger = logging.getLogger(__name__)

class ScriptProcessor:
    def __init__(self, student_profile, tts_engine="kokoro", llm_model="gemini-2.5-flash-lite"):
        self.student_profile = student_profile
        self.tts_engine = tts_engine
        self.llm_model = llm_model
        self.logger = logging.getLogger(__name__)
        self.language_tool = language_tool_python.LanguageTool('en-US')
        
        # Set up Pexels API configuration
        self.pexels_api_key = settings.PEXELS_API_KEY
        self.pexels_base_url = "https://api.pexels.com/videos"
        self.pixabay_api_key = settings.PIXABAY_API_KEY
        self.pixabay_base_url = "https://pixabay.com/api/videos/"
        self.freepik_api_key = settings.FREEPIK_API_KEY
        self.freepik_base_url = "https://api.freepik.com/v1/resources"

        # Video dimension constraints
        self.min_width = 960
        self.max_width = 1920
        self.min_height = 540
        self.max_height = 1080
        
        self.download_dir = Path(settings.MEDIA_ROOT) / 'temp_videos'
        self.download_dir.mkdir(parents=True, exist_ok=True)
        # Initialize Whisper model for transcription
        # self.whisper_model = whisper.load_model("base")
        
        # Rate limiting settings
        self.last_api_call = datetime.now()
        self.min_delay = 10.0  # Minimum delay between API calls in seconds
        self.max_retries = 3  # Maximum number of retries for failed API calls
        self.batch_size = 5   # Number of videos to process before a longer pause

        # Add video configuration
        self.config = {
            'video': {
                'fps': 30,  # Standard frame rate
                'codec': 'libx264',
                'audio_codec': 'aac',
                'preset': 'ultrafast',
                'threads': 1,
                'bitrate': '1000k'
            }
        }   

        # Initialize spaCy
        try:
            self.nlp = spacy.load('en_core_web_lg')  
            if not self.nlp.vocab.vectors.shape[0]:  # Check if vectors are loaded
                raise ValueError("No word vectors found in the model")
            logger.info("Successfully loaded spaCy model with word vectors")
        except OSError:
            logger.warning("Downloading spaCy model with word vectors...")
            spacy.cli.download('en_core_web_lg')
            self.nlp = spacy.load('en_core_web_lg')
        except Exception as e:
            logger.error(f"Error loading spaCy model: {e}")
            raise

    def process_video_pipeline(self, videos, keywords, output_dir):
        """Main pipeline orchestrator"""
        try:
            download_dir = self.download_dir
            sites = ["pexels", "pixabay"]  # or whatever sites you want to use
            videos_dict_list = external_video_sites.search_sites(keywords, download_dir, sites)
            # 1. Analyze videos using static function
            analyzed_videos = analyze_video_content(download_dir, self.student_profile, keywords, self.llm_model)
                    
            # 2. Generate dialogue using static function
            dialogue = generate_dialogue(analyzed_videos, self.student_profile, 
                                       output_dir, 20, keywords, 200, self.llm_model)
            
            # 3. Generate audio using static function
            # audio_segments = generate_audio(dialogue, self.student_profile, self.tts_engine)
            
            # 4. Stitch video and audio using static function
            final_video = stitch(analyzed_videos,  output_dir)
            
            return final_video
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise

    def dl(self, videos, keywords, output_dir):
        """Main pipeline orchestrator"""
        try:
            download_dir = self.download_dir
            sites = ["pexels", "pixabay"]  # or whatever sites you want to use
            videos_dict_list = external_video_sites.search_sites(keywords, download_dir, sites)
            # 1. Analyze videos using static function
            # analyzed_videos = analyze_videos(download_dir, self.student_profile, keywords, self.llm_model)
                    
            # # 2. Generate dialogue using static function
            # dialogue = generate_dialogue(analyzed_videos, self.student_profile, 
            #                            output_dir, 20, keywords, 200, self.llm_model)
            
            # # 3. Generate audio using static function
            # # audio_segments = generate_audio(dialogue, self.student_profile, self.tts_engine)
            
            # # 4. Stitch video and audio using static function
            # final_video = stitch_video_audio(analyzed_videos, audio_segments, output_dir)
            
            # return final_video
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise

    def anal(self, videos, keywords, output_dir):
        """Main pipeline orchestrator"""
        try:
            download_dir = self.download_dir
            sites = ["pexels", "pixabay"]  # or whatever sites you want to use
            videos_dict_list = external_video_sites.search_sites(keywords, download_dir, sites)
            # 1. Analyze videos using static function
            analyzed_videos = analyze_videos(download_dir, self.student_profile, keywords, self.llm_model)
                    
            # 2. Generate dialogue using static function
            dialogue = generate_dialogue(analyzed_videos, self.student_profile, 
                                       output_dir, 20, keywords, 200, self.llm_model)
            
            # 3. Generate audio using static function
            # audio_segments = generate_audio(dialogue, self.student_profile, self.tts_engine)
            
            # 4. Stitch video and audio using static function
            final_video = stitch_video_audio(analyzed_videos, audio_segments, output_dir)
            
            return final_video
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise

    def pb(self, videos, keywords, output_dir):
        """Main pipeline orchestrator"""
        try:
            download_dir = self.download_dir
            sites = ["pexels", "pixabay"]  # or whatever sites you want to use
            videos_dict_list = external_video_sites.search_sites(keywords, download_dir, sites)
            # 1. Analyze videos using static function
            analyzed_videos = analyze_videos(download_dir, self.student_profile, keywords, self.llm_model)
                    
            # 2. Generate dialogue using static function
            dialogue = generate_dialogue(analyzed_videos, self.student_profile, 
                                       output_dir, 20, keywords, 200, self.llm_model)
            
            # 3. Generate audio using static function
            # audio_segments = generate_audio(dialogue, self.student_profile, self.tts_engine)
            
            # 4. Stitch video and audio using static function
            final_video = stitch_video_audio(analyzed_videos, audio_segments, output_dir)
            
            return final_video
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise
    def st(self, videos, keywords, output_dir):
        """Main pipeline orchestrator"""
        try:
            download_dir = self.download_dir
            sites = ["pexels", "pixabay"]  # or whatever sites you want to use
            videos_dict_list = external_video_sites.search_sites(keywords, download_dir, sites)
            # 1. Analyze videos using static function
            analyzed_videos = analyze_videos(download_dir, self.student_profile, keywords, self.llm_model)
                    
            # 2. Generate dialogue using static function
            dialogue = generate_dialogue(analyzed_videos, self.student_profile, 
                                       output_dir, 20, keywords, 200, self.llm_model)
            
            # 3. Generate audio using static function
            # audio_segments = generate_audio(dialogue, self.student_profile, self.tts_engine)
            
            # 4. Stitch video and audio using static function
            final_video = stitch_video_audio(analyzed_videos, audio_segments, output_dir)
            
            return final_video
            
        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            raise







    
 


    
    

