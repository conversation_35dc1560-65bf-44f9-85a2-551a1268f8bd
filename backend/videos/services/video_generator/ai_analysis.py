from datetime import datetime
import json
import logging
import subprocess
import time
from typing import Dict, List
from moviepy import VideoFileClip
import requests
import cv2
import re
import base64
import os
from pathlib import Path
import tempfile
import google.generativeai as genai
from django.conf import settings
import language_tool_python
from lms_project.ai_config import get_default_model, is_model_enabled, ANALYSIS_MODELS

logger = logging.getLogger(__name__)
tool = language_tool_python.LanguageTool('en-US')

def analyze(video_path, analysis_prompt):
    model_name = get_default_model("analysis")
    print(f"model: {model_name}")
    if "gemini" in model_name:
        return analyze_with_gemini(video_path, analysis_prompt)
    else:   
        return analyze_with_ollama(video_path, analysis_prompt, model_name)
    
def analyze_with_gemini(video_path, analysis_prompt):
    """Analyze video content using Google Gemini API"""
    
    # Configure Gemini API
    genai.configure(api_key=settings.GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-2.5-flash-lite')
    
    # Retry configuration
    max_retries = 3
    base_delay = 5
    
    for attempt in range(max_retries):
        try:
            # print(f"Attempt {attempt + 1}/{max_retries} - Analyzing video: {video_path}")

            # Upload video with retry
            video_file = None
            for upload_attempt in range(3):
                try:
                    video_file = genai.upload_file(path=video_path)
                    print(f"Completed upload: {video_file.uri}")
                    break
                except Exception as e:
                    if upload_attempt == 2:  # Last attempt
                        raise
                    print(f"Upload attempt {upload_attempt + 1} failed: {str(e)}")
                    time.sleep(base_delay * (upload_attempt + 1))

            if not video_file:
                raise Exception("Failed to upload video after all attempts")

            # Wait for processing with timeout
            wait_start = time.time()
            max_wait = 60  # Maximum 60 seconds wait
            while video_file.state.name == "PROCESSING":
                if time.time() - wait_start > max_wait:
                    raise Exception("Video processing timeout")
                print('Waiting for video to be processed.')
                time.sleep(5)
                video_file = genai.get_file(video_file.name)
                
            if video_file.state.name == "FAILED":
                raise ValueError(f"Video processing failed: {video_file.state.name}")

            # Get the full analysis
            response = model.generate_content([analysis_prompt, video_file], request_options={"timeout": 1000})
            
            # Parse the analysis
            content = response.text.strip()
            
            # Return the response in the same format as Ollama
            return {
                "response": content,
                "model": "gemini-2.5-flash-lite",
                "success": True
            }
            
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:  # Last attempt
                raise Exception(f"All {max_retries} attempts failed: {str(e)}")
            time.sleep(base_delay * (attempt + 1))
    
    # This should never be reached, but just in case
    raise Exception("Unexpected error in analyze_with_gemini")

def analyze_with_ollama(video_path, analysis_prompt, model_name):
    # Extract base64 frames
    img_list = extract_base64_frames(video_path)
    
    # For Ollama API, images should be sent as base64 strings
    payload = {
        "model": model_name,
        "prompt": analysis_prompt,
        "images": img_list,
        "stream": False
    }
    
    ollama_url = "http://localhost:11434/api/generate"
    print(f"Making request to Ollama endpoint: {ollama_url} with model: {model_name}")
    
    try:
        response = requests.post(ollama_url, json=payload, timeout=600)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error making request to Ollama: {e}")
        print(f"Response status: {response.status_code if 'response' in locals() else 'N/A'}")
        print(f"Response text: {response.text if 'response' in locals() else 'N/A'}")
        raise

def generate_dialogue_with_gemini(prompt):
    genai.configure(api_key=settings.GEMINI_API_KEY)
    m = get_default_model("dialogue")
    model = genai.GenerativeModel(m)
    max_retries = 3
    base_delay = 5
    for attempt in range(max_retries):
        try:
            response = model.generate_content(prompt, request_options={"timeout": 1000})
            content = response.text.strip()
           
            return {
                "response": content,
                "model": model,
                "success": True
            }

        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:  # Last attempt
                raise Exception(f"All {max_retries} attempts failed: {str(e)}")
            time.sleep(base_delay * (attempt + 1))
    raise Exception("Unexpected error in generate_with_gemini_text_only")

def extract_base64_frames(video_path, num_frames=5):
    """Extract frames from video and convert to base64 strings for Ollama models"""
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Calculate frame indices evenly spaced throughout the video
    if num_frames == 1:
        frame_indices = [total_frames // 2]  # Middle frame
    else:
        step = total_frames // (num_frames + 1)
        frame_indices = [step * (i + 1) for i in range(num_frames)]
    
    img_base64_list = []
    for idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            _, buffer = cv2.imencode('.jpg', frame)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            img_base64_list.append(img_base64)
    cap.release()
    return img_base64_list

def extract_jpg_frames(video_path, num_frames=3):
    """Extract frames from video and save as JPG files for Llama models"""
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    middle = total_frames // 2
    offset = total_frames // 10
    frame_indices = [
        max(0, middle - offset),
        middle,
        min(total_frames - 1, middle + offset)
    ]
    
    # Create temporary directory for JPG files
    temp_dir = tempfile.mkdtemp()
    jpg_paths = []
    
    for i, idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            jpg_path = os.path.join(temp_dir, f"frame_{i}.jpg")
            cv2.imwrite(jpg_path, frame)
            jpg_paths.append(jpg_path)
    
    cap.release()
    return jpg_paths

def analyze_video_content(video_path: str, student_profile: Dict, keywords: List[str], video_id=None, original_url=None,) -> Dict:

    max_retries = 6
    base_delay = 10  # Start with 5 seconds delay
    with VideoFileClip(video_path) as clip:
        video_duration = clip.duration
    interests_line = ""
    interests_json = ""
    interests = student_profile.get('interests', [])
    if interests:
        interests_topics = ', '.join(i['topic'] for i in interests)
        interests_line = f"        - Relevance to at least one of the student's interests: {', '.join(i['topic'] for i in interests)}"
        interests_json = '"interest_scores": {\n' + ',\n'.join(f'                "{i["topic"]}": ' for i in interests) + '\n            }'
    
    analysis_prompt = f"""
    Analyze this video for educational content. 
    The subject array should be a list of 5 single word concepts from this video. (i.e. ['Sky', 'Clouds', 'Nature', 'Scenery', 'Weather'])
    Rate each aspect from 1-20, with decimals for precision:
        - Educational value for a student in grade {student_profile.get('grade')}
        - Age appropriateness for a student in grade {student_profile.get('grade')} (i.e. no shirtless people)
        - Relevance to at least one of the keywords: {', '.join(keywords)}
        - Motion Amount
        {interests_line}
        - The concepts chosen should not be any of these words: {', '.join(keywords)}
    Return a JSON object with EXACTLY these fields and EXACTLY this format (no extra comments):
    {{
        "subject": ["List", "of", "one", "word", "concepts"],
        "visual_description": "What the video shows",
        "scores": {{
            "educational_value": ,
            "age_appropriate": ,
            "relevance_to_at_least_one_keyword": ,
            "motion": ,
            {interests_json}
        }},
    }}

    - The concepts chosen should not be any of these words: {', '.join(keywords)}
    """



    print("Analysis prompt (start):", analysis_prompt[:60], "...")        
    
    analysis_json = analyze(video_path, analysis_prompt)
    content = analysis_json.get("response", "").strip()
    if '```json' in content:
        json_str = content.split('```json')[1].split('```')[0].strip()
    elif '```' in content:
        # Handles ``` ... ```
        json_str = content.split('```')[1].strip()
    else:
        json_str = content.strip()
    try:
        json_str = json_str.replace('"', '"').replace('"', '"')
        json_str = clean_llm_json(json_str)
        analysis = json.loads(json_str)
        analysis['duration'] = video_duration
        print(f"Clip: {video_path}   Extracted subject: {analysis.get('subject', '')}   Duration: {video_duration:.2f}s")
        # Validate required fields 
        print(f"Analysis Dict: {analysis}")
        if not isinstance(analysis, dict):
            raise ValueError("Analysis must be a dictionary")
        if 'scores' not in analysis:
            raise ValueError("Analysis missing 'scores' field")

    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {str(e)}")
        print(f"Raw response: {content}")
        # Return a default analysis structure
        return {
            "filename": os.path.basename(video_path),
            "error": f"JSON parsing error: {str(e)}",
            "scores": {
                "educational_value": 10.0,
                "age_appropriate": 10.0,
                "motion": 10,
                "relevance_to_at_least_one_keyword": 10.0,
                "interest_scores": {
                    interest['topic']: 10.0 
                    for interest in student_profile.get('interests', [])
                },
                "overall_score": 10.0
            },
            "analysis": {
                "concepts": [],
                "teaching_applications": "Analysis failed",
                "visual_description": "Analysis failed",
                "age_considerations": "Analysis failed"
            }
        }
    # move video to processed directory
    # clip = VideoFileClip(video_path)      
    # clip.write_videofile(str(dest), codec='libx264',bitrate="1000k",preset='medium', logger=None)
    # clip.close()
    # Calculate weighted overall score
    scores = analysis["scores"]
    weights = {
        "educational_value": 2.0,
        "age_appropriate": 0.5,
        "motion": 1.0,
        "relevance_to_at_least_one_keyword": 1.0
    }
    # Add interest score weights
    total_interest_weight = 0.5  # Total weight for all interests combined
    interest_weights = {
        interest['topic']: (total_interest_weight * interest['confidence'])
        for interest in student_profile['interests']
    }
    # Calculate weighted sum
    weighted_sum = (
        scores["educational_value"] * weights["educational_value"] +
        scores["age_appropriate"] * weights["age_appropriate"] +
        scores["motion"] * weights["motion"] +
        scores["relevance_to_at_least_one_keyword"] * weights["relevance_to_at_least_one_keyword"] +
        sum(scores["interest_scores"][topic] * weight 
            for topic, weight in interest_weights.items())
    )
    # Calculate total weight
    total_weight = (
        sum(weights.values()) +
        sum(interest_weights.values())
    )
        # Calculate final weighted average
    analysis["scores"]["overall_score"] = weighted_sum / total_weight
    # Return combined results
    return {
        "filename": os.path.basename(video_path),
        "subject":analysis.get("subject", []) ,
        "video_id": video_id,  # Store the original video ID
        "video_url": original_url,
        "visual_description": analysis.get("visual_description", "Analysis failed"),
        "scores": {
            **scores,  # Include all original scores
            "overall_score": analysis["scores"]["overall_score"]  # Add overall score
        },
        "timestamp": datetime.now().isoformat(),
        
    }

def clean_llm_json(raw: str) -> str:
    # Remove markdown code block markers
    raw = re.sub(r"^```json|^```|```$", "", raw, flags=re.MULTILINE).strip()
    # Remove comments (// ...)
    raw = re.sub(r"//.*", "", raw)
    # Remove trailing commas before } or ]
    raw = re.sub(r",\s*([}\]])", r"\1", raw)
    return raw

def find_interesting_segments(video_path: str) -> Dict:
    """Find the most interesting 5-second and 30-second segments in a video"""
    logger.info("Finding most interesting video segments")
    
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    
    # Calculate frame ranges for different segment lengths
    segment_5s_frames = int(5 * fps)
    segment_30s_frames = int(30 * fps)
    
    # Analyze motion throughout the video
    motion_scores = []
    ret, prev_frame = cap.read()
    if not ret:
        cap.release()
        raise ValueError("Could not read video file")
    
    prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
    
    for frame_idx in range(1, total_frames):
        ret, frame = cap.read()
        if not ret:
            break
            
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        diff = cv2.absdiff(prev_gray, gray)
        motion_score = np.sum(diff)
        motion_scores.append(motion_score)
        prev_gray = gray
    
    cap.release()
    
    # Find best 5-second segment
    best_5s_start = self.find_best_segment_start(motion_scores, segment_5s_frames, fps)
    best_5s_end = best_5s_start + 5
    
    # Find best 30-second segment
    best_30s_start = self.find_best_segment_start(motion_scores, segment_30s_frames, fps)
    best_30s_end = best_30s_start + 30
    
    # Create the 5-second analysis clip
    analysis_clip_path = str(Path(video_path).parent / f"analysis_{Path(video_path).stem}_5s.mp4")
    self.extract_video_segment(video_path, analysis_clip_path, best_5s_start, best_5s_end)
    
    return {
        '5_second_start': best_5s_start,
        '5_second_end': best_5s_end,
        '5_second_path': analysis_clip_path,
        '30_second_start': best_30s_start,
        '30_second_end': best_30s_end
    }

def extract_video_segment(video_path: str, output_path: str, start_time: float, end_time: float) -> str:
    """Extract a segment from a video using ffmpeg"""
    try:
        command = [
            'ffmpeg',
            '-ss', str(start_time),
            '-i', video_path,
            '-t', str(end_time - start_time),
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-y',
            '-loglevel', 'error',  # Suppress FFmpeg output
            output_path
        ]
        
        subprocess.run(command, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return output_path
    except Exception as e:
        logger.error(f"Error extracting video segment: {str(e)}")
        return video_path
    
