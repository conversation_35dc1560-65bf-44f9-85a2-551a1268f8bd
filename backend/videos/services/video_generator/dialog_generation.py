import logging
import time
import requests
import cv2
import base64
import os
from pathlib import Path
import tempfile
import google.generativeai as genai
from typing import List, Dict
from django.conf import settings
import language_tool_python
from lms_project.ai_config import get_default_model, is_model_enabled, DIALOGUE_MODELS

logger = logging.getLogger(__name__)
tool = language_tool_python.LanguageTool('en-US')

def generate_dialogue(videos: List[Dict], student_profile: Dict, base_dir: str, seconds_per_clip: int, keywords: List[str], total_duration_seconds: int) -> str:
    print("Generate connected dialogue")
    # Get number of available videos
    selected_dir = base_dir / 'selected'
    # available_videos = len([f for f in os.listdir(selected_dir) if f.endswith('.mp4')])
    # print(f"videos for dialog:  {videos}")
    # # Create a list of video info with durations
    # video_info = []     #what's the point in this
    # for video in videos:
    #     video_info.append({
    #         'filename': Path(video.get('local_path', '')).name,
    #         'duration': video.get('duration', 0),
    #         'visual_description': video.get('visual_description', '')
    #     })

    # Generate dialogue sequentially for each clip
    full_dialogue = []
    conversation_context = ""  # This will accumulate the full conversation
    #shouldn't videos length be 20
    for clip_idx, video in enumerate(videos):
        print(f"Generating dialogue for clip {clip_idx + 1}/{len(videos)}: {Path(video['local_path']).name}")
        is_last_clip = (clip_idx == len(videos) - 1)
        # Create context-aware prompt for this specific clip
        clip_prompt = _create_clip_dialogue_prompt(
            video,
            clip_idx, 
            student_profile, 
            seconds_per_clip, 
            keywords, 
            conversation_context,
            is_last_clip= is_last_clip
        )
        print(f"clip prompt: {clip_prompt[:50]}")
        # Generate dialogue for this clip
        clip_dialogue = _generate_single_clip_dialogue(clip_prompt)
        print(f"Generate Dialog-clip dialogue: {clip_dialogue[:550]}")
        # Extract the dialogue content (remove any markers that might have been generated)
        clip_lines = clip_dialogue.split('\n')
        dialogue_content = []
        
        for line in clip_lines:
            if line.strip() and not line.strip().startswith('[NEW CLIP'):
                dialogue_content.append(line)
        
        # Add the clip marker and dialogue to full dialogue
        full_dialogue.append(f"[NEW CLIP: {Path(video['local_path']).name} LENGTH: {seconds_per_clip}]")
        full_dialogue.extend(dialogue_content)
        
        # Update conversation context by ADDING to it (not overwriting)
        # Add the new dialogue to the existing context
        new_dialogue_text = "\n".join(dialogue_content)
        if conversation_context:
            conversation_context += f"\n\n{new_dialogue_text}"
        else:
            conversation_context = new_dialogue_text
        
        # Add a small delay between requests to avoid overwhelming the API
        time.sleep(1)
    
    # Combine all dialogue
    final_dialogue = "\n".join(full_dialogue)
    # Save dialogue
    with open(base_dir / 'lesson_dialogue.txt', 'w') as f:
        f.write(final_dialogue)
    return grammar_check(final_dialogue)

def _create_clip_dialogue_prompt(video_visual_description: Dict, clip_idx: int, student_profile: Dict, seconds_per_clip: int, keywords: List[str], conversation_context: str, is_last_clip=False) -> str:
    # print(f"video_visual_description: {video_visual_description}")
    # print(f"clip_idx: {clip_idx}")
    # print(f"student_profile: {student_profile}")
    # print(f"seconds_per_clip: {seconds_per_clip}")
    # print(f"keywords: {keywords}")
    # print(f"conversation_context: {conversation_context}")
    # print(f"is_last_clip: {is_last_clip}")
    student_name = student_profile.get('name', 'Student')
    interests = student_profile.get('interests', [])
    context_instruction = ""
    if conversation_context:
        context_instruction = f"""
                                - Here is the dialogue up to this point for context:
                                {conversation_context}
                                ---- Please continue the conversation naturally, building on what has been discussed so far. ----
                                """
    else:
        context_instruction = "This is the beginning of the lesson. Start with an engaging introduction casually mentioning the keywords, {keywords}."        
    if is_last_clip:
            wrapup_instruction = (
                "\n- This is the final clip. Wrap up the lesson, summarize the key points, and end with a positive, encouraging statement."
            )
    else:
        wrapup_instruction = ""
    # Create conditional interests line
    interests_line = ""
    
    if interests:
        interests_topics = ', '.join(i['topic'] for i in interests)
        interests_line = f"{student_name} is interested in {interests_topics}."

    # Get age-appropriate speech patterns
    speech_patterns = _get_age_appropriate_speech_patterns(student_profile['grade'])

    prompt = f"""
                Create part of a energenic dialogue between a grade {student_profile.get('grade')} elementary school student{'' if not student_name.strip() or student_name == '-' else f' named {student_name}'} and the teacher.
                {interests_line}
                This will be clip {clip_idx + 1} out of 10 of the lesson. 
                {context_instruction}

                {wrapup_instruction}

                VIDEO CLIP INFORMATION:
                - Filename: {Path(video_visual_description['local_path']).name}
                - Visual Description: {video_visual_description['analysis']['visual_description']}

                {speech_patterns}

                DIALOGUE REQUIREMENTS:
                - Focus on the content shown in this video clip
                - Keep the focus on {keywords}
                - CRITICAL: Dialogue must be exactly {seconds_per_clip} seconds long when spoken
                - Use the socratic method
                - Match the student's grade level
                - Be fun, engaging, and educational
                - Stay focused on the core scientific concepts
                - Do not include any markers or formatting - just the dialogue
                - Do not end with a question
                - Do not ask a question (either speaker) if it cannot be answered in the allotted time.
                - Do not say filenames.
                - Include clear explanations that build on previous knowledge
                - IMPORTANT: The student should speak using the age-appropriate speech patterns above

                DIALOGUE FORMAT:
                Teacher: [dialogue]
                {student_name}: [dialogue using age-appropriate speech patterns]
                Teacher: [dialogue]
                {student_name}: [dialogue using age-appropriate speech patterns]
                (continue as needed for {seconds_per_clip} seconds)

                Remember: This should feel like a natural continuation of the lesson (or an introduction, if this is the 1st video), not a disconnected segment.
                """
                    # print(f"Prompt: {prompt}")
    return prompt

from django.conf import settings
import language_tool_python
from lms_project.ai_config import get_default_model, is_model_enabled, DIALOGUE_MODELS

logger = logging.getLogger(__name__)
tool = language_tool_python.LanguageTool('en-US')


def _generate_single_clip_dialogue(prompt: str) -> str:
    model_name = get_default_model("dialogue")
    print(f"Using model: {model_name}")
    model_config = DIALOGUE_MODELS.get(model_name, {})
    if "gemini" in model_name:
        import google.generativeai as genai
        from django.conf import settings
        genai.configure(api_key=settings.GEMINI_API_KEY)
        gemini_model = genai.GenerativeModel(model_name)
        max_retries = 3
        base_delay = 5
        for attempt in range(max_retries):
            try:
                response = gemini_model.generate_content([prompt], request_options={"timeout": 1000})
                content = response.text.strip()
                content = '\n'.join(line for line in content.split('\n') if line.strip())
                return content
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise Exception(f"All {max_retries} attempts failed: {str(e)}")
                time.sleep(base_delay * (attempt + 1))
        raise Exception("Unexpected error in generate_with_gemini")
    else:  # Default to Ollama
        ollama_url = "http://localhost:11434/api/generate"
        payload = {
            "model":  model_name,
            "prompt": prompt,
            "stream": False
        }
        try:
            print(f"Making request to Ollama for single clip dialogue...")
            response = requests.post(ollama_url, json=payload, timeout=300)
            response.raise_for_status()
            ollama_response = response.json()
            dialogue = ollama_response.get("response", "").strip()
            return '\n'.join(line for line in dialogue.split('\n') if line.strip())
        except requests.exceptions.RequestException as e:
            print(f"Error communicating with Ollama: {e}")
            # Return a fallback dialogue
            return f"""Teacher: ERROR ERROR!!
                        Student: OH GOD, NO!!
                        Teacher: Great! Let's explore this next concept together."""

def _get_age_appropriate_speech_patterns(grade: int) -> str:
    """
    Get age-appropriate speech patterns for different grade levels.
    Returns a string describing how the student should speak based on their grade.
    """
    if grade <= 2:  # K-2nd grade
        return """
    EXAMPLES OF AGE-APPROPRIATE SPEECH PATTERNS FOR GRADE {}:
    - Use simple, short sentences
    - Use basic vocabulary appropriate for early readers
    - Express excitement with "Wow!", "Cool!", "Awesome!"
    - Use "yeah" instead of "yes"
    - Use "gonna" instead of "going to"
    - Use "wanna" instead of "want to"
    - Express confusion with "Huh?" or "What?"
    - Use simple questions like "Why?" or "How?"
    - Show enthusiasm with "I love it!" or "That's so cool!"
    - Use contractions like "don't", "can't", "won't"
    - Keep responses short and enthusiastic
    """.format(grade)
    
    elif grade <= 4:  # 3rd-4th grade
        return """
    EXAMPLES OF AGE-APPROPRIATE SPEECH PATTERNS FOR GRADE {}:
    - Use slightly more complex sentences but still keep them clear
    - Use "yeah" or "yes" interchangeably
    - Use "gonna" and "wanna" occasionally but also "going to" and "want to"
    - Express excitement with "That's amazing!", "Incredible!", "Fantastic!"
    - Use "I think..." and "Maybe..." to show reasoning
    - Ask more detailed questions like "How does that work?" or "Why does that happen?"
    - Use "That makes sense!" or "I understand!" to show comprehension
    - Show curiosity with "I wonder..." or "What if..."
    - Use some academic vocabulary but explain it naturally
    - Keep enthusiasm high but with more sophisticated expressions
    """.format(grade)
    
    elif grade <= 6:  # 5th-6th grade
        return """
    EXAMPLES OF AGE-APPROPRIATE SPEECH PATTERNS FOR GRADE {}:
    - Use more complex sentences and vocabulary
    - Use "yes" more than "yeah" but can use both
    - Use "going to" and "want to" more than contractions
    - Express excitement with "That's fascinating!", "Remarkable!", "Extraordinary!"
    - Use "I believe..." and "It seems like..." to show critical thinking
    - Ask analytical questions like "What causes that?" or "How does that relate to..."
    - Use "That's logical!" or "That connects to..." to show understanding
    - Show deeper curiosity with "I'm curious about..." or "What's interesting is..."
    - Use more academic vocabulary naturally
    - Maintain enthusiasm while showing more sophisticated thinking
    """.format(grade)
    
    else:  # 7th grade and up
        return """
    EXAMPLES OF AGE-APPROPRIATE SPEECH PATTERNS FOR GRADE {}:
    - Use "yeah" instead of "yes"
    - Ask complex questions like "What are the implications of..." or "How does this connect to..."
    - Show intellectual curiosity
    - Use academic vocabulary rarely and semi-confidently
    - Maintain enthusiasm while demonstrating mature analytical thinking
    """.format(grade)

def grammar_check(text: str) -> str:
    matches = tool.check(text)
    corrected = language_tool_python.utils.correct(text, matches)
    return corrected



