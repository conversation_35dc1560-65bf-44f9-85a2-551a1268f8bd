import csv
import os

class VideoCSVTracker:
    def __init__(self, csv_path, fieldnames):
        self.csv_path = csv_path
        self.fieldnames = fieldnames

    def append_row(self, row):
        file_exists = os.path.isfile(self.csv_path)
        with open(self.csv_path, 'a', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=self.fieldnames)
            if not file_exists:
                writer.writeheader()
            writer.writerow(row)

    def update_row(self, match_field, match_value, update_dict):
        rows = []
        updated = False
        with open(self.csv_path, 'r', newline='') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        for row in rows:
            if row[match_field] == match_value:
                row.update(update_dict)
                updated = True
        if updated:
            with open(self.csv_path, 'w', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=self.fieldnames)
                writer.writeheader()
                writer.writerows(rows)
        return updated