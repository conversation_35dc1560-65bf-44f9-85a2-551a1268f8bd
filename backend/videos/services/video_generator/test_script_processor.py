from .script_processor import ScriptProcessor

def test_script_segmentation():
    # Sample script about gravity
    script = """
    Teacher: Good morning class! Today, we're going to learn about something really exciting - gravity and forces! Who's ready to explore how things move and fall?

    Student: Oh, I love learning about motion! I always wondered why things fall down and not up.

    Teacher: That's a great observation! Gravity is a force that pulls everything toward Earth's center. It's what keeps us on the ground and makes objects fall when we drop them. Let me demonstrate.
    *Teacher drops a pencil*
    See how the pencil falls straight down? That's gravity in action!

    Student: Wow! So gravity is like Earth's magnet that pulls everything down?

    Teacher: That's a clever way to think about it! While gravity isn't exactly like a magnet, it is a force of attraction between all objects with mass. The Earth is so massive that its gravitational pull is what we feel most strongly. That's why everything falls toward the Earth's center.
    """

    try:
        # Initialize the script processor
        processor = ScriptProcessor()
        
        # Process the script
        print("\nProcessing script...")
        segments = processor.process_script(script)
        
        # Print results
        print("\nSegmentation Results:")
        for i, segment in enumerate(segments, 1):
            print(f"\nSegment {i}:")
            print(f"Dialogue: {segment['dialogue'][:100]}...")
            print(f"Duration: {segment['duration']} seconds")
            print(f"Concept: {segment['concept']}")
            print(f"Visual Requirements: {segment['visual_requirements']}")
            print(f"Keywords: {', '.join(segment['keywords'])}")
            print(f"Found Videos: {len(segment['videos'])}")
            
            # Print video details
            for j, video in enumerate(segment['videos'], 1):
                print(f"  Video {j}:")
                print(f"    Duration: {video.get('duration')} seconds")
                print(f"    URL: {video.get('url')}")
        
    except Exception as e:
        print(f"Error in test: {str(e)}")

if __name__ == "__main__":
    test_script_segmentation()