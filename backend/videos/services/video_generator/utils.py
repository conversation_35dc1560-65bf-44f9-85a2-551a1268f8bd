import requests
import os
import time
from typing import List, Optional
from dotenv import load_dotenv
from .schema import VideoScene, VideoRequest
from .constants import RUNWAY_API_KEY
from django.conf import settings
from pathlib import Path
import json

# Load environment variables
load_dotenv()

def generate_video_from_text(text: str, style: str = "educational") -> Optional[str]:
    """Generate a video from text using Runway ML API"""
    try:
        # Debug: Print environment variable
        print("API key from environment:", os.environ.get('RUNWAY_API_KEY'))
        print("API key from constants:", RUNWAY_API_KEY)
        
        # Clean text
        clean_text = text.replace('*', '').replace('#', '').replace('_', '')
        
        # Create output directory
        os.makedirs("video_output", exist_ok=True)
        output_path = f"video_output/lesson_{os.urandom(4).hex()}.mp4"
        
        print(f"Generating video for text: {clean_text[:50]}...")
        
        # API endpoint
        url = "https://api.dev.runwayml.com/v1/text-to-video"
        
        # Print the first 15 chars of the API key to verify it
        print(f"API key starts with: {RUNWAY_API_KEY[:15]}...")
        
        headers = {
            "Authorization": f"Bearer key_{RUNWAY_API_KEY}",
            "Content-Type": "application/json",
            "X-Runway-Version": "2024-11-06"
        }
        
        payload = {
            "text": clean_text,
            "model": "gen3a_turbo",
            "options": {
                "duration": 4,
                "resolution": "1080p"
            }
        }
        
        print(f"Sending request to {url}")
        print(f"Payload: {payload}")
        
        # Start generation
        response = requests.post(url, headers=headers, json=payload)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        if response.status_code != 200:
            print(f"Error response: {response.text}")
            raise Exception(f"Runway API error: {response.text}")
            
        # Save the video
        with open(output_path, 'wb') as f:
            f.write(response.content)
            
        print(f"Video saved to {output_path}")
        return output_path
            
    except Exception as e:
        print(f"Error generating video: {e}")
        return None 

def generate_video_from_audio(audio_path: str, visual_description: str) -> str:
    """Generate a video using Pictory's audio-to-video API"""
    try:
        headers = {
            'CLIENT-ID': settings.PICTORY_CLIENT_ID,
            'CLIENT-SECRET': settings.PICTORY_CLIENT_SECRET,
            'X-Pictory-User-Id': settings.PICTORY_USER_ID,
            'Content-Type': 'application/json'
        }
        
        # First, upload the audio file
        with open(audio_path, 'rb') as audio_file:
            files = {'file': audio_file}
            upload_response = requests.post(
                'https://api.pictory.ai/upload',
                headers=headers,
                files=files
            )
            
            if upload_response.status_code != 200:
                raise Exception(f"Failed to upload audio: {upload_response.text}")
            
            audio_url = upload_response.json().get('url')
        
        # Then create video from audio
        payload = {
            "audio_url": audio_url,
            "visual_description": visual_description,
            "style": "educational",  # or other styles as needed
            "aspect_ratio": "16:9"
        }
        
        response = requests.post(
            'https://api.pictory.ai/audiotovideo',
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"Failed to generate video: {response.text}")
            
        return response.json().get('video_url')
        
    except Exception as e:
        print(f"Error generating video: {e}")
        return None 