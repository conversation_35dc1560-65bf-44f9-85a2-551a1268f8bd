from typing import List
from pydantic import BaseModel, Field

class VideoScene(BaseModel):
    prompt: str = Field(..., description="Text prompt for scene generation")
    duration: int = Field(default=5, description="Scene duration in seconds")
    style: str = Field(default="educational", description="Style preset to use")

class VideoRequest(BaseModel):
    scenes: List[VideoScene]
    metadata: dict = Field(default_factory=dict) 