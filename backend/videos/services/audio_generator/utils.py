"""
utils.py - Educational dialogue generation and audio synthesis
"""

import os
from typing import Optional
import requests
import numpy as np
import scipy.io.wavfile as wavfile
# from gtts import gTTS
import gc
import torch
from pydub import AudioSegment
from django.conf import settings
# from indextts.infer import IndexTTS
from videos.services.audio_generator.schema import Dialogue
from videos.services.audio_generator.prompts import SYSTEM_PROMPT, HUMAN_PROMPT
from kokoro import KPipeline
import soundfile as sf
from .typecast import TypecastAPI
typecast = TypecastAPI()

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
kokoro_pipeline = KPipeline(lang_code='a')
VOICE_ID_MAP = {
    "kokoro": {
        "adult_female": "kokoro_adult_female_id",
        "adult_male": "am_puck",
        "child_female": "af_jessica",
        "child_male": "kokoro_child_male_id",
    },
    "typecast": {
        "adult_female": "tc_62fb679683a541c351dc7c3a",  # Zoey
        "adult_male": "tc_67b6985d4d5d632d97478263",    # Aaron
        "child_female": "tc_6075b407b8a5c6f36b877c88",  # Elle
        "child_male": "tc_602bd4fa8cfef478f1ceef0d",    # Leo
    }
}
# Create a single, reusable instance of the IndexTTS model.
# This assumes your 'checkpoints' folder is in the 'backend' directory.
# checkpoints_dir = os.path.join(BASE_DIR, "checkpoints")
# config_path = os.path.join(checkpoints_dir, "config.yaml")

# print(f"Initializing IndexTTS with:")
# print(f"  Model directory: {checkpoints_dir}")
# print(f"  Config path: {config_path}")
# tts_model = IndexTTS(model_dir=checkpoints_dir, cfg_path=config_path)

# # Map user-friendly voice names to their reference audio files.
# # This makes it easy to add more voices later.
# VOICE_MAP = {
#     "adult_male": os.path.join(BASE_DIR, "voices", "adult_male.wav"),
#     "adult_female": os.path.join(BASE_DIR, "voices", "adult_female.wav"),
#     "child_male": os.path.join(BASE_DIR, "voices", "child_male.wav"),
#     "child_female": os.path.join(BASE_DIR, "voices", "child_female.wav"),
#     # Add more voices here as needed
# }

def cleanup_memory():
    """Free up GPU/CPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def text_to_speech_typecast(text: str, voice_id: str, output_path: str = "output.mp3"):
    """Convert text to speech using Typecast's TTS API"""
    try:
        # Remove the speaker's name and colon from the text if present
        if ':' in text:
            text = text.split(':', 1)[1].strip()
        
        print(f"Generating speech using Typecast API with voice ID: {voice_id}")
        
        # Generate speech using Typecast
        return typecast.text_to_speech(
            text=text,
            voice_id=voice_id,
            emotion="normal",
            output_path=output_path
        )

    except Exception as e:
        print(f"Error in TTS generation: {e}")
        return None



def generate_dialogue_audio(api: str, dialogue_text: str, teacher_voice_id: str, student_voice_id: str, output_path: str = "dialogue_audio.wav"):
    """
    Generate audio for a dialogue between teacher and student.
    Args:
        api: TTS API to use ('kokoro' or 'typecast')
        dialogue_text: The full dialogue text with speaker prefixes
        teacher_voice_id: Voice ID for the teacher
        student_voice_id: Voice ID for the student
        output_path: Path to save the combined audio
    Returns:
        Path to the generated audio file
    """
    temp_files = []
    try:
        # Split dialogue into lines and process each line
        lines = [line.strip() for line in dialogue_text.split('\n') if line.strip()]
        combined = None
        
        for i, line in enumerate(lines):
            if not line:
                continue
                
            # Determine if it's teacher or student speaking
            if line.lower().startswith('teacher:'):
                voice_id = teacher_voice_id
                text = line[8:].strip()  # Remove 'Teacher:' prefix
            else:  # Assume any other line is the student
                voice_id = student_voice_id
                # Remove student name prefix if it exists
                if ':' in line:
                    text = line.split(':', 1)[1].strip()
                else:
                    text = line
                    
            if not text:
                continue
                
            # Generate TTS for this line
            temp_path = f"temp_{i}.wav"
            temp_files.append(temp_path)
            
            text_to_speech(api, text, voice_id, temp_path)
            
            # Add the audio file if it was created
            if os.path.exists(temp_path):
                segment = AudioSegment.from_wav(temp_path)
                
                # Add a small pause before this segment if it's not the first one
                if combined is not None:
                    combined += AudioSegment.silent(duration=200)  # 200ms pause
                    combined += segment
                else:
                    combined = segment
        
        if combined is None:
            raise ValueError("No valid dialogue lines found or audio generation failed")
            
        # Export the final audio
        combined.export(output_path, format="wav")
        return output_path
        
    except Exception as e:
        print(f"Error generating dialogue audio: {e}")
        raise
    finally:
        # Clean up temporary files
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_file}: {e}")

def text_to_speech(api:str, text: str, voice_id: str = None, output_path: str = "output.wav"):
    """
    Convert text to speech using Kokoro TTS.
    voice_id is optional; you can map your own voice IDs to Kokoro voices if desired.
    """
    try:
        # Set default output path based on API
        if output_path is None:
            ext = "mp3" if api == "typecast" else "wav"
            output_path = f"output.{ext}"
        else:
            # Ensure output path has correct extension
            ext = os.path.splitext(output_path)[1].lower()
            if not ext:
                output_path = f"{output_path}.{'mp3' if api == 'typecast' else 'wav'}"
            elif (api == 'typecast' and ext != '.mp3') or (api == 'kokoro' and ext != '.wav'):
                output_path = f"{os.path.splitext(output_path)[0]}.{'mp3' if api == 'typecast' else 'wav'}"

        # Remove the speaker's name and colon if present
        #at this point, for some reason
        
        # if ':' in text:
        #     print(f"speaker line, before strip: {text[:550]}")
        #     text = text.split(':', 1)[1].strip()
        #     print(f"speaker line, sending: {text[:550]}")
        # else:
        #     print("no colon")

        if api == "kokoro":
            print(f"Generating speech using Kokoro TTS for text: {text[:50]}...")
            generator = kokoro_pipeline(text, voice=voice_id)
            for i, (gs, ps, audio) in enumerate(generator):
                if isinstance(audio, torch.Tensor):
                    audio = audio.cpu().numpy()
                sf.write(output_path, audio, 24000)
                return output_path

        elif api == "typecast":
            print(f"Generating speech using Typecast TTS for text: {text[:50]}...")
            return typecast.text_to_speech(
                text=text,
                voice_id=voice_id,
                emotion="normal",
                output_path=output_path
            )

        return None

    except Exception as e:
        print(f"Error in {api} TTS generation: {e}")
        return None
    
# def text_to_speech_voice_clone(text: str, voice_id: str, output_path: str = "output.wav"):
#     """
#     Convert text to speech using IndexTTS for voice cloning.
#     """
#     try:
#         # Remove the speaker's name and colon from the text if present
#         if ':' in text:
#             text = text.split(':', 1)[1].strip()
#         print(f"map {VOICE_MAP}")
#         print(f"vid {voice_id}")
#         # Find the reference audio file path from the voice_id
#         reference_voice_path = VOICE_MAP.get(voice_id)
#         print(f"ref path {reference_voice_path}")
#         if not reference_voice_path:
#             print(f"Error: Voice ID '{voice_id}' not found in VOICE_MAP.")
#             return None
        
#         if not os.path.exists(reference_voice_path):
#              print(f"Error: Reference audio file not found at '{reference_voice_path}'")
#              return None

#         print(f"Generating speech with voice '{voice_id}' for text: {text[:50]}...")
        
#         # Generate audio using IndexTTS
#         tts_model.infer(reference_voice_path, text, output_path)
        
#         print(f"Created audio file: {output_path}")
#         return output_path

#     except Exception as e:
#         print(f"Error in IndexTTS generation: {e}")
#         return None
        
# def process_dialogue(dialogue: Dialogue, teacher_voice_id: str, student_voice_id: str, output_dir: str = "audio_segments"):
#     """Process entire dialogue into audio segments"""
#     try:
#         print(f"Creating output directory: {output_dir}")
#         os.makedirs(output_dir, exist_ok=True)
        
#         audio_files = []
#         for i, line in enumerate(dialogue.lines):
#             print(f"Processing line {i+1}: {line.speaker}: {line.text[:50]}...")
#             output_path = f"{output_dir}/segment_{i}.mp3"
            
#             # Select voice ID based on speaker
#             voice_id = teacher_voice_id if line.speaker == "Teacher" else student_voice_id
            
#             audio_file = text_to_speech(
#                 api="typecast",
#                 text=line.text,
#                 voice_id=voice_id,
#                 output_path=output_path
#             )
#             if audio_file:
#                 print(f"Created audio file: {audio_file}")
#                 audio_files.append(audio_file)
#             else:
#                 print(f"Failed to create audio for line {i+1}")
    
#         if not audio_files:
#             print("No audio files were generated")
#             return None
            
#         # Combine all segments into one file
#         final_audio = combine_audio_segments(audio_files, f"{output_dir}/complete_lesson.mp3")
#         if final_audio:
#             audio_files.append(final_audio)
            
#         return audio_files
#     except Exception as e:
#         print(f"Error in process_dialogue: {e}")
#         return None

def combine_audio_segments(audio_files: list, output_path: str = "final_lesson.wav") -> str:
    try:
        print(f"Combining {len(audio_files)} audio segments...")
        print(f"Audio files to combine: {audio_files}")
        
        # Create a silent pause
        pause = AudioSegment.silent(duration=1000)  # 1 second pause
        
        # Load and combine segments
        combined = AudioSegment.empty()
        for i, file in enumerate(audio_files):
            print(f"Loading segment {i+1} from {file}...")
            try:
                # Handle both WAV and MP3 files
                if file.lower().endswith('.mp3'):
                    segment = AudioSegment.from_mp3(file)
                else:
                    segment = AudioSegment.from_wav(file)
                    
                combined += segment + pause
                print(f"Added segment {i+1}")
            except Exception as e:
                print(f"Error loading segment {i+1}: {e}")
        
        print(f"Exporting combined audio to: {output_path}")
        # Determine output format from file extension
        format_type = "mp3" if output_path.lower().endswith('.mp3') else "wav"
        combined.export(output_path, format=format_type)
        print(f"Created combined audio file: {output_path}")
        return output_path
    except Exception as e:
        print(f"Error combining audio segments: {e}")
        return None

# Keep the existing text_to_speech and process_dialogue functions...
