"""
schema.py
"""

from typing import List
from pydantic import BaseModel, Field

class DialogueLine(BaseModel):
    speaker: str = Field(..., description="Either 'Teacher' or 'Student'")
    text: str = Field(..., description="The spoken line of dialogue")
    
class Dialogue(BaseModel):
    lines: List[DialogueLine]
    metadata: dict = Field(default_factory=dict, description="Additional info like grade level, topic, etc.")
