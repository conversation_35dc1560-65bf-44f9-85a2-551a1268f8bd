"""
typecast.py - Typecast API integration for text-to-speech
"""

import requests
from django.conf import settings
from typing import Optional, List, Dict
import json
import logging

logger = logging.getLogger(__name__)

class TypecastAPI:
    BASE_URL = "https://api.typecast.ai/v1"
    
    def __init__(self):
        self.api_key = settings.TYPECAST_API_KEY
        self.headers = {
            "X-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }
        self._available_voices = None
    
    def get_available_voices(self) -> Optional[List[Dict]]:
        """Get list of available voices from Typecast API"""
        try:
            url = f"{self.BASE_URL}/voices"
            logger.info(f"Making request to Typecast API: {url}")
            response = requests.get(url, headers=self.headers)
            logger.info(f"Received response from Typecast API. Status code: {response.status_code}")
            logger.debug(f"Raw response text (first 500 chars): {response.text[:500]}...")

            if response.status_code == 200:
                try:
                    voices = response.json()
                    logger.info("Successfully parsed JSON from Typecast API")
                except json.JSONDecodeError as jde:
                    logger.error(f"JSONDecodeError when parsing Typecast API response: {jde}")
                    logger.error(f"Full response text causing JSON error: {response.text}")
                    return None
                
                voice_mappings = {
                    'tc_67b6985d4d5d632d97478263': {'name': 'Aaron', 'type': 'natural', 'gender': 'male'},
                    'tc_62fb679683a541c351dc7c3a': {'name': 'Zoey', 'type': 'natural', 'gender': 'female'},
                    'tc_5e11db23b70e890009fb7ae7': {'name': 'Jackson', 'type': 'teen', 'gender': 'male'},
                    'tc_5f8d7b0de146f10007b8042f': {'name': 'Tina', 'type': 'teen', 'gender': 'female'},
                    'tc_602bd4fa8cfef478f1ceef0d': {'name': 'Leo', 'type': 'child', 'gender': 'male'},
                    'tc_6075b407b8a5c6f36b877c88': {'name': 'Elle', 'type': 'child', 'gender': 'female'},
                }
                
                categorized_voices = []
                for voice in voices:
                    voice_id = voice.get('voice_id')
                    if voice_id in voice_mappings:
                        mapping = voice_mappings[voice_id]
                        categorized_voice = {
                            'id': voice_id,
                            'name': mapping['name'],
                            'type': mapping['type'],
                            'gender': mapping['gender'],
                            'model': voice.get('model'),
                            'emotions': voice.get('emotions', [])
                        }
                    else:
                        categorized_voice = {
                            'id': voice_id,
                            'name': voice.get('voice_name'),
                            'type': 'natural',
                            'gender': 'unknown',
                            'model': voice.get('model'),
                            'emotions': voice.get('emotions', [])
                        }
                    categorized_voices.append(categorized_voice)
                
                self._available_voices = categorized_voices
                logger.info(f"Successfully retrieved {len(categorized_voices)} voices from Typecast API")
                return categorized_voices
            else:
                logger.error(f"Typecast API returned non-200 status: {response.status_code}. Response: {response.text}")
                return None
        except Exception as e:
            logger.error(f"An exception occurred during Typecast API call: {e}", exc_info=True)
            return None
    
    def validate_voice_id(self, voice_id: str) -> bool:
        """Validate if a voice ID exists in the available voices"""
        if not self._available_voices:
            self.get_available_voices()
        
        if not self._available_voices:
            return False
            
        return any(voice.get('id') == voice_id for voice in self._available_voices)


    def text_to_speech(self, text: str, voice_id: str, emotion: str = "normal", output_path: str = "output.mp3") -> Optional[str]:
        """Convert text to speech using Typecast's TTS API"""
        try:
            if not self.validate_voice_id(voice_id):
                print(f"Error: Voice ID {voice_id} not found in available voices")
                return None
            url = f"{self.BASE_URL}/text-to-speech"
            
            payload = {
                "voice_id": voice_id,
                "text": text,
                "model": "ssfm-v21",
                "language": "eng",
                "prompt": {
                    "emotion_preset": emotion,
                    "emotion_intensity": 1
                },
                "output": {
                    "volume": 100,
                    "audio_pitch": 0,
                    "audio_tempo": 1,
                    "audio_format": "mp3"  # Changed from wav to mp3 since we're using mp3 files
                },
                "seed": 42
            }
            
            response = requests.post(url, headers=self.headers, json=payload)
            
            if response.status_code != 200:
                print(f"Error in TTS generation: {response.text}")
                return None
                
            with open(output_path, 'wb') as f:
                f.write(response.content)
                
            return output_path
            
        except Exception as e:
            print(f"Error in text_to_speech: {e}")
            return None