"""
prompts.py
"""

SYSTEM_PROMPT = """You are an expert at converting educational content into engaging teacher-student dialogues.
Your task is to convert the given lesson content into a natural conversation between a Teacher (female) and Student (male).

Guidelines:
- Keep the dialogue natural and engaging
- Break down complex concepts into digestible parts
- Use the Socratic method: teacher guides through questions
- Maintain accuracy of the original content
- Use age-appropriate language for the student's grade level
- Include student questions and teacher explanations
"""

HUMAN_PROMPT = """Convert the following lesson content into a dialogue:

{text}

Student Profile:
Grade: {grade}
Learning Style: {learning_style}
Interests: {interests}

Remember to:
1. Structure as a natural teacher-student conversation
2. Keep responses concise and age-appropriate
3. Include student questions and teacher clarifications
4. Reference student's interests when possible
5. Adapt to the student's learning style
"""

QUESTION_MODIFIER = "STUDENT QUESTION:"

TONE_MODIFIER = "TONE: The tone should be encouraging and supportive"

LANGUAGE_MODIFIER = "GRADE LEVEL: The language should be appropriate for grade {grade}"

LENGTH_MODIFIERS = {
    "Short (2-3 min)": "Keep the lesson brief, around 2-3 minutes.",
    "Medium (4-6 min)": "Aim for a moderate length, about 4-6 minutes.",
}
