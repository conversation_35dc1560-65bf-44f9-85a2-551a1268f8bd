import os
from django.conf import settings # Make sure this import is at the top of the file
import subprocess
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.management import call_command
from io import StringIO
import sys
import json
import uuid
import threading
from django.conf import settings
from pathlib import Path
from datetime import datetime
# from videos.services.audio_generator.utils import VOICE_MAP
from videos.models import Video
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status # Import status for error responses
from django.shortcuts import get_object_or_404 # Helper to get object or return 404
from .serializers import VideoSerializer # Import the VideoSerializer
import logging
from videos.services.audio_generator.typecast import TypecastAPI
typecast_api = TypecastAPI()
# Store video generation progress
# For profiles app
# logger = logging.getLogger('profiles')
# logger.debug('Debug message')
# logger.info('Info message')
# logger.warning('Warning message')
# logger.error('Error message')
# 
logger = logging.getLogger(__name__)
logger.debug('Debug message')

DIALOGUE_FILENAME = 'lesson_dialogue.txt'

video_progress = {}

def generate_video_in_thread(video_id, student_id):
    try:
        # Capture output
        # output = StringIO()
        # sys.stdout = output
        print(f"\nStarting video generation for video_id: {video_id}")
        print(f"Student ID: {student_id}")
         
        # Get the video object and update status
        video = Video.objects.get(id=video_id)
        video.status = 'processing'
        video.save()
        
        # Get keywords from the video record and ensure they're not empty
        keywords = video.keywords
        if not keywords:
            print("Warning: No keywords found in video record")
            keywords = []
            
        keywords_str = ','.join(keywords)
        print(f"Using keywords: {keywords_str}")
        
        print("Calling complete_sequential_video_creation command...")
        # Use a separate log file instead of capturing output
        log_file = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id) / 'generation_log.txt'
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Run as subprocess to properly capture all output
        with open(log_file, 'w') as log:
        # Get keywords and other parameters from the video model
            
            process = subprocess.Popen(
                [
                    'python', 'manage.py', 'complete_sequential_video_creation',
                    f'--student-id={student_id}',
                    f'--video-id={video_id}',
                    f'--total-duration-seconds={int(video.duration_seconds)}',  # Add total duration  
                    f'--teacher-voice={video.teacher_voice}',
                    f'--student-voice={video.student_voice}',                                      
                    f'--keywords={keywords_str}',  # Add keywords from UI
                    '--verbosity=1'
                ],
                stdout=log,
                stderr=log,
                cwd=str(Path(settings.BASE_DIR))
            )
            process.wait()  # Wait for the process to complete

        print(f"Command completed. Log file available at: {log_file}")
        
        # After video is generated, save the file path
        video_path = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id) / 'final_lesson.mp4'
        if video_path.exists():
            video.video_file = str(video_path.relative_to(settings.MEDIA_ROOT))
            video.status = 'completed'
            video.save()
            print("Video generation completed successfully")
        else:
            raise Exception("Video file not found after generation")
        
    except Exception as e:
        print(f"Error generating video: {e}")
        import traceback
        print(traceback.format_exc())
        # Update video status to failed
        video.status = 'failed'
        video.save()
    # finally:
    #     # Print captured output to Django console
    #     print(output.getvalue())
    #     # Restore stdout
    #     sys.stdout = sys.__stdout__
    #     output.close()

@csrf_exempt
@require_http_methods(["POST"])
def generate_video(request):
    print("Received video generation request")
    try:
        print("\nReceived video generation request")
        data = json.loads(request.body)
        print(f"Request data: {json.dumps(data, indent=2)}")
        
        student_id = data.get('profile', {}).get('id')

        # Extract all parameters
        video_settings = data.get('videoSettings', {})
        keywords = video_settings.get('keywords', [])
        videos_per_keyword = video_settings.get('videosPerKeyword', 5)
        duration_seconds = video_settings.get('videoDurationSeconds', 60)  # Default to 60 seconds
        teacher_voice = video_settings.get('teacherVoice',)
        student_voice = video_settings.get('studentVoice')
        
        print(f"Student ID: {student_id}")

        print(f"Keywords: {keywords}")

        if not student_id:
            print("Error: Missing student ID")
            return JsonResponse({'error': 'Missing student ID'}, status=400)
        
        # Create video record
        video = Video.objects.create(
            student_id=student_id,
            keywords=keywords,
            videos_per_keyword=videos_per_keyword,
            duration_seconds=duration_seconds,
            teacher_voice=teacher_voice,
            student_voice=student_voice,
            status='pending'
        )
        
        # Use the video's ID instead of generating a new UUID
        video_id = str(video.id)
        print(f"Generated Video ID: {video_id}")
        
        # Start video generation in a separate thread
        thread = threading.Thread(
            target=generate_video_in_thread,
            args=(video_id, student_id)
        )
        thread.start()
        print("Started video generation thread")
        
        return JsonResponse({'videoId': video_id})
        
    except Exception as e:
        print(f"Error starting video generation: {e}")
        import traceback
        print(traceback.format_exc())
        return JsonResponse({'error': str(e)}, status=500)

# @csrf_exempt
# @require_http_methods(["GET"])
# def get_available_one_shot_voices(request):
#     logger.info("Received request for IndexTTS voices")
#     try:
#         voices = []
#         for voice_id, path in VOICE_MAP.items():
#             parts = voice_id.split('_')
#             voice_type = parts[0]
#             gender = parts[1]
            
#             # Capitalize for display name
#             name = f"{voice_type.capitalize()} {gender.capitalize()}"
            
#             # The frontend expects 'natural' for adults for the teacher role
#             if voice_type == 'adult':
#                 voice_type = 'natural'

#             voices.append({
#                 "id": voice_id,
#                 "name": name,
#                 "type": voice_type,
#                 "gender": gender,
#                 "language": "English"
#             })
        
#         logger.info(f"Successfully retrieved {len(voices)} voices from VOICE_MAP")
#         return JsonResponse({'voices': voices})

#     except Exception as e:
#         logger.error("An unhandled exception occurred in get_available_one_shot_voices: %s", str(e), exc_info=True)
#         return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def get_available_typecast_voices(request):
    logger.info("Received request for Typecast voices")
    logger.debug(f"Request method: {request.method}")
    logger.debug(f"Request headers: {dict(request.headers)}")
    
    try:
        logger.info("Attempting to fetch voices from Typecast API...")
        voices = typecast_api.get_available_voices()
        
        if voices is not None:
            logger.info(f"Successfully retrieved {len(voices)} voices from Typecast API")
            logger.debug("Transformed voices being sent to frontend: %s", json.dumps(voices, indent=2))
            return JsonResponse({'voices': voices})
        else:
            logger.error("Failed to retrieve voices from Typecast API")
            return JsonResponse({'error': 'Failed to retrieve voices from Typecast API'}, status=500)
    except Exception as e:
        logger.error("An unhandled exception occurred in get_available_typecast_voices: %s", str(e), exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)

# In your Django views.py
@api_view(['GET'])
def list_grade_filenames(request, grade):
    try:
        grade_dir = os.path.join(settings.MEDIA_ROOT, 'videos', f'grade_{grade}')
        if not os.path.exists(grade_dir):
            return Response({'filenames': []}, status=200)
        
        filenames = []
        for filename in os.listdir(grade_dir):
            if filename.endswith('.mp4'):
                filenames.append(filename)
        
        return Response({'filenames': filenames}, status=200)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@require_http_methods(["GET"])
def get_student_videos(request, student_id):
    # Log request details
    logger.info("HELLO FROM GET_STUDENT_VIDEOS")
    logger.info(f"""
    Request Details:
    - Endpoint: /api/videos/{student_id}/
    - Method: {request.method}
    - User Agent: {request.headers.get('User-Agent', 'Not provided')}
    - Referer: {request.headers.get('Referer', 'Not provided')}
    - Timestamp: {datetime.now()}
    """)
    
    try:
        videos = Video.objects.filter(student_id=student_id).order_by('-created_at')
        video_list = [{
            'id': video.id,
            'keywords': video.keywords,
            'videosPerKeyword': video.videos_per_keyword,
            'teacherVoice': video.teacher_voice,
            'studentVoice': video.student_voice,
            'videoDurationSeconds': video.duration_seconds,
            'status': video.status,
            'createdAt': video.created_at,
            'videoFile': video.video_file.url if video.video_file else None
        } for video in videos]
        
        logger.info(f"Successfully retrieved {len(video_list)} videos for student {student_id}")
        return JsonResponse({'videos': video_list})
    except Exception as e:
        logger.error(f"Error getting videos for student {student_id}: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)
    
@api_view(['GET'])
def get_video_progress(request, video_id):
    try:
        video = Video.objects.get(id=video_id)
        return Response({
            'progress': video.progress,
            'status': video.status,
            'status_message': video.status_message
        })
    except Video.DoesNotExist:
        return Response({'error': 'Video not found'}, status=404)

@require_http_methods(["POST"])
def manual_download_videos(request):
    try:
        data = json.loads(request.body)
        keywords = data.get('keywords')
        videos_per_keyword = data.get('videosPerKeyword', 5)

        if not keywords:
            return JsonResponse({"error": "Keywords are required."}, status=400)

        # TODO: Implement the video searching, downloading, thumbnail generation, and metadata storage logic here.
        # This will involve calling functions similar to those used in your automatic process,
        # but you need to handle storing the results for manual selection.
        # Example: downloaded_videos_metadata = your_download_service.download_videos(keywords, videos_per_keyword)

        # Placeholder response
        downloaded_videos_metadata = [
            {"id": "dummy_id_1", "thumbnailUrl": "/media/thumbnails/dummy_id_1.jpg", "localPath": "/path/to/dummy_id_1.mp4"},
             {"id": "dummy_id_2", "thumbnailUrl": "/media/thumbnails/dummy_id_2.jpg", "localPath": "/path/to/dummy_id_2.mp4"},
        ]


        return JsonResponse({"downloaded_videos": downloaded_videos_metadata})

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON body."}, status=400)
    except Exception as e:
        # Log the error properly
        return JsonResponse({"error": str(e)}, status=500)

@require_http_methods(["POST"])
def manual_process_analyze(request):
    try:
        data = json.loads(request.body)
        selected_video_ids = data.get('selected_video_ids')

        if not selected_video_ids or len(selected_video_ids) != 10:
             return JsonResponse({"error": "Exactly 10 selected video IDs are required."}, status=400)

        # TODO: Retrieve the full metadata for the selected video IDs.
        # TODO: Implement the processing (compress/crop) for ONLY these selected videos.
        # TODO: Implement the minimal analysis (visual description only) using Gemini for these videos.
        # TODO: Store the results and the list of selected videos for the final creation step.

        # Placeholder response
        combined_description = "Placeholder visual description for the selected videos."

        return JsonResponse({"status": "success", "message": "Selected videos processed and analyzed.", "combined_visual_description": combined_description})

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON body."}, status=400)
    except Exception as e:
        # Log the error properly
        return JsonResponse({"error": str(e)}, status=500)

@require_http_methods(["POST"])
def manual_create_final_video(request):
    try:
        data = json.loads(request.body)
        selected_video_ids = data.get('selected_video_ids')
        student_profile_id = data.get('student_profile_id') # Optional

        if not selected_video_ids or len(selected_video_ids) != 10:
             return JsonResponse({"error": "Exactly 10 selected video IDs are required."}, status=400)

        # TODO: Retrieve the metadata (including processed file paths) for the selected videos.
        # TODO: Retrieve the student profile if applicable.
        # TODO: Call the dialogue generation, audio synthesis, and stitching logic using the SELECTED videos.
        # This might be the same or similar logic as your automatic process, but it uses a predefined list of videos.
        # You might need to pass the combined visual description generated in the previous step.

        # Placeholder response
        task_id = "dummy_task_id" # If using async tasks

        return JsonResponse({"status": "processing", "message": "Final video creation started.", "task_id": task_id})


    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON body."}, status=400)
    except Exception as e:
        # Log the error properly
        return JsonResponse({"error": str(e)}, status=500)

@api_view(['GET'])
def list_video_attempts(request):
    """
    List all video creation attempts.
    (Might need to filter by user/student in a real application)
    """
    videos = Video.objects.all().order_by('-created_at') # Get all videos, ordered by creation date
    serializer = VideoSerializer(videos, many=True) # Serialize the queryset
    return Response(serializer.data)

@api_view(['GET'])
def video_attempt_details(request, video_id):
    """
    Get details for a specific video creation attempt, including associated video files and dialogue.
    """
    # Ensure the user is authenticated
    if not request.user.is_authenticated:
        return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

    # Get the video attempt object, ensuring it belongs to the authenticated user
    video_attempt = get_object_or_404(Video, id=video_id, student__user=request.user) # Assuming StudentProfile has a foreign key to User

    # Construct the base directory path for this video attempt
    # This assumes a structure like MEDIA_ROOT/students/<student_id>/<video_id>/
    student_id = video_attempt.student.id
    attempt_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id)

    downloaded_videos_dir = attempt_dir / 'downloads'
    dialogue_file_path = attempt_dir / DIALOGUE_FILENAME # Assuming dialogue.txt exists here

    # List video files in the downloads directory
    downloaded_video_files = []
    if downloaded_videos_dir.exists() and downloaded_videos_dir.is_dir():
        for f in os.listdir(downloaded_videos_dir):
            if f.endswith('.mp4'): # Or other video extensions you use
                 file_path = downloaded_videos_dir / f
                 # Get the relative path from MEDIA_ROOT to use in the frontend
                 relative_file_path = file_path.relative_to(settings.MEDIA_ROOT)
                 downloaded_video_files.append(str(relative_file_path)) # Store as string


    # Read the dialogue file
    dialogue_content = ""
    if dialogue_file_path.exists() and dialogue_file_path.is_file():
        try:
            with open(dialogue_file_path, 'r', encoding='utf-8') as f:
                dialogue_content = f.read()
        except Exception as e:
            print(f"Error reading dialogue file {dialogue_file_path}: {e}")
            dialogue_content = "Error loading dialogue."


    # Prepare the response data
    video_attempt_data = VideoSerializer(video_attempt).data
    response_data = {
        'attempt_details': video_attempt_data,
        'downloaded_videos': downloaded_video_files,
        'dialogue': dialogue_content,
    }

    return Response(response_data)

# @api_view(['GET'])
# def get_student_profile_by_video_id(request, video_id: int):
#     """Fetch student profile details for a given video ID."""
#     try:
#         logger.debug(f"Fetching student profile for video ID: {video_id}")
        
#         # Get the Video object by ID
#         try:
#             video = Video.objects.select_related('student').get(id=video_id)
#             logger.debug(f"Found video: {video.id}")
#         except Video.DoesNotExist:
#             logger.warning(f"Video with ID {video_id} not found")
#             return Response(
#                 {'error': f'Video with ID {video_id} not found'}, 
#                 status=status.HTTP_404_NOT_FOUND
#             )

#         # Check if video has an associated student
#         if not hasattr(video, 'student') or not video.student:
#             logger.warning(f"No student associated with video ID {video_id}")
#             return Response(
#                 {'error': f'No student associated with video ID {video_id}'},
#                 status=status.HTTP_404_NOT_FOUND
#             )

#         student = video.student
#         logger.debug(f"Found student: {student.id} - {student.name}")

#         # Prepare student profile data
#         try:
#             student_profile_data = {
#                 'id': student.id,
#                 'name': student.name,
#                 'grade': student.grade,
#                 'interests': [
#                     {
#                         'topic': interest.topic,
#                         'confidence': interest.confidence,
#                         'source': interest.source
#                     }
#                     for interest in student.interests.all()
#                 ]
#             }
#             logger.debug(f"Successfully prepared student profile data for video ID {video_id}")
#             return Response(student_profile_data)
            
#         except Exception as e:
#             logger.error(f"Error preparing student data for video ID {video_id}: {str(e)}", exc_info=True)
#             return Response(
#                 {'error': 'Error processing student profile data'},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

#     except Exception as e:
#         logger.error(f"Unexpected error in get_student_profile_by_video_id for video ID {video_id}: {str(e)}", exc_info=True)
#         return Response(
#             {'error': 'An unexpected error occurred'},
#             status=status.HTTP_500_INTERNAL_SERVER_ERROR
#         )
    
@require_http_methods(["GET"])
def list_video_filenames(request):
    """
    API endpoint to list all video filenames in the media/videos directory.
    """
    video_dir = os.path.join(settings.MEDIA_ROOT, 'videos')
    video_filenames = []
    if os.path.exists(video_dir) and os.path.isdir(video_dir):
        for filename in os.listdir(video_dir):
            if filename.endswith('.mp4'):
                video_filenames.append(filename)
    return JsonResponse({'filenames': video_filenames})


@require_http_methods(["GET"])
def get_grade_videos(request, grade):
    """
    Get all video files for a specific grade
    URL: /api/videos/grade/<int:grade>/
    """
    try:
        # Define the path to the grade videos
        grade_dir = os.path.join(settings.MEDIA_ROOT,'videos', f'grade_{grade}')
        
        # Check if directory exists
        if not os.path.exists(grade_dir):
            return JsonResponse({'videos': [], 'message': f'No videos found for grade {grade}'})
        
        # Get all video files
        video_extensions = {'.mp4', '.webm', '.ogg', '.avi', '.mov', '.mkv'}
        video_files = []
        
        for filename in os.listdir(grade_dir):
            file_path = os.path.join(grade_dir, filename)
            if os.path.isfile(file_path):
                # Check if it's a video file
                _, ext = os.path.splitext(filename.lower())
                if ext in video_extensions:
                    # Get file info
                    file_stat = os.stat(file_path)
                    video_info = {
                        'filename': filename,
                        'size': file_stat.st_size,
                        'modified': file_stat.st_mtime,
                        'url': f'/media/videos/grade_{grade}/{filename}'
                    }
                    video_files.append(video_info)
        
        # Sort by filename
        video_files.sort(key=lambda x: x['filename'])
        
        return JsonResponse({
            'videos': [v['filename'] for v in video_files],  # Just filenames for frontend
            'video_details': video_files,  # Detailed info if needed
            'grade': grade,
            'count': len(video_files)
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e), 'videos': []}, status=500)

@require_http_methods(["GET"])
def get_all_videos(request):
    """
    Get videos for all grades
    URL: /api/videos/all/
    """
    try:
        all_videos = {}
        
        for grade in range(1, 9):  # Grades 1-8
            grade_dir = os.path.join(settings.MEDIA_ROOT, 'videos', f'grade_{grade}')
            
            if os.path.exists(grade_dir):
                video_extensions = {'.mp4', '.webm', '.ogg', '.avi', '.mov', '.mkv'}
                video_files = []
                
                for filename in os.listdir(grade_dir):
                    file_path = os.path.join(grade_dir, filename)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(filename.lower())
                        if ext in video_extensions:
                            video_files.append(filename)
                
                video_files.sort()
                all_videos[grade] = video_files
            else:
                all_videos[grade] = []
        
        return JsonResponse({'videos_by_grade': all_videos})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# Optional: Add a status check endpoint if you use async tasks
# @require_http_methods(["GET"])
# def manual_video_status(request, video_id):
#    # TODO: Implement logic to check the status of the final video creation task
#    return JsonResponse({"status": "pending", "progress": 0, "message": "Processing..."}) # Placeholder