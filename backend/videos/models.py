from django.db import models
from profiles.models import StudentProfile

class Video(models.Model):
    # Relationship
    student = models.ForeignKey(StudentProfile, on_delete=models.CASCADE, related_name='videos')
    progress = models.IntegerField(default=0)  # 0-100 percentage
    status_message = models.CharField(max_length=200, default='')
    # Video file
    video_file = models.FileField(upload_to='videos/', null=True, blank=True)
    
    # Generation parameters
    keywords = models.JSONField(default=list)
    videos_per_keyword = models.IntegerField(default=5)
    duration_seconds = models.IntegerField(default=200)
    teacher_voice = models.CharField(max_length=50)
    student_voice = models.Char<PERSON>ield(max_length=50)
    
    # Additional metadata
    total_videos_downloaded = models.IntegerField(null=True, blank=True)
    total_videos_used = models.IntegerField(null=True, blank=True)
    total_duration_seconds = models.IntegerField(null=True, blank=True)
    
    # Status tracking
    status = models.Char<PERSON>ield(max_length=20, default='pending')  # pending, processing, completed, failed
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"Video {self.id} for {self.student} - {self.created_at}"