import os
import csv
import subprocess
import time
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from moviepy import VideoFileClip,AudioFileClip, concatenate_videoclips
import requests
from videos.services.video_generator.ai_analysis import generate_dialogue_with_gemini
from lms_project.ai_config import get_default_model
from videos.models import Video
from profiles.models import StudentProfile
from videos.services.audio_generator.utils import VOICE_ID_MAP, generate_dialogue_audio
import json
import platform

class Command(BaseCommand):
    help = 'generate entire dialogue in one pass from csv'
    
    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process')
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')

    def get_voice_id(self, logical_voice, tts_engine):
        return VOICE_ID_MAP[tts_engine][logical_voice]

    def handle(self, *args, **options):
        s_time = time.time()
        student_id = options['student_id']
        video_id = options['video_id']
        try:
            # Get student profile
            profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
            student_profile = {
                'id': str(profile.id),
                'name': profile.name,
                'grade': profile.grade,
                'interests': [
                    {
                        'topic': interest.topic,
                        'confidence': interest.confidence,
                        'source': interest.source
                    }
                    for interest in profile.interests.all()
                ]
            }
            # Get video object
            video_obj = Video.objects.get(id=video_id)
            self.stdout.write(self.style.SUCCESS(f"Processing video {video_obj.id} for student {student_profile['name']}"))
            # Set up directories
            base_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id)
            selected_dir = base_dir / 'selected'
            output_dir = base_dir / 'final'
            output_dir.mkdir(exist_ok=True)
            # Get list of video files
            video_files = sorted(selected_dir.glob('*.mp4'))
            if not video_files:
                raise CommandError(f"No video files found in {selected_dir}")
            # Stitch videos together
            output_video_path = output_dir / 'stitched_video.mp4'
            processed_videos = []
            csv_path = base_dir / 'video_analysis.csv'
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                for i, row in enumerate(reader):
                    if i >= 10:  # Stop after 10 rows
                        break
                    video_data = {
                                    'filename': row.get('Filename', ''),
                                    'visual_description': row.get('Visual Description', ''),
                                    'overall_score': float(row.get('Overall Score', 0) or 0),
                                    'video_id': row.get('Video ID', ''),
                                    'duration': float(row.get('duration_seconds', 0) or 0),
                                    'start_time': 0.0,  # Will be calculated below
                                    'end_time': 0.0     # Will be calculated
                                }
                    # print(f"video data: {video_data}")
                    processed_videos.append(video_data)
            processed_videos = processed_videos[:10]

            outline_parts = []
            for video in processed_videos:
                outline_parts.append(f"{video['filename']} - {video['duration']} seconds - {video['visual_description']}")
            video_outline = "\n".join(outline_parts)
            dialogue = self.generate_dialogue(
                video_outline=video_outline,
                student_profile=student_profile,
                keywords=options.get('keywords', '').split(',') if options.get('keywords') else video_obj.keywords
            )

            
        # Parse dialogue to get recommended video order
            ordered_video_paths = self.parse_video_order_from_dialogue(dialogue, selected_dir)

            # Use dialogue-recommended order if available, otherwise fall back to CSV order
            if ordered_video_paths:
                self.stdout.write(f"🎬 Stitching {len(ordered_video_paths)} videos in dialogue-recommended order")
                video_paths = ordered_video_paths
            else:
                self.stdout.write("⚠️  No video order found in dialogue, using CSV order")
                # Extract file paths from processed_videos in CSV order (existing code)
                video_paths = []
                for video_data in processed_videos:
                    filename = video_data['filename']
                    video_path = selected_dir / filename
                    if video_path.exists():
                        video_paths.append(video_path)

            # Stitch videos in the determined order
            if video_paths:
                self.stitch_videos(video_paths, output_video_path)
            else:
                raise CommandError("No valid video files found")
            
                        # Generate dialogue audio

            audio_output = output_dir / 'dialogue_audio.wav'
            tts_engine = get_default_model('tts')
            self.stdout.write(f"Generating dialogue audio using {tts_engine}...")            
            teacher_voice_id = VOICE_ID_MAP[tts_engine]['adult_male']
            student_voice_id = VOICE_ID_MAP[tts_engine]['child_female']
            with open(output_dir / 'unclean_lesson_dialogue.txt', 'w') as f:
                f.write(dialogue) 
            clean_dialogue = self.clean_dialogue_text(dialogue)
            with open(output_dir / 'clean_lesson_dialogue.txt', 'w') as f:
                f.write(clean_dialogue) 
            generate_dialogue_audio(
                api=tts_engine,
                dialogue_text=clean_dialogue,
                teacher_voice_id=teacher_voice_id,
                student_voice_id=student_voice_id,
                output_path=str(audio_output)
            )
            
            # Load video and audio (remove duplicate)
            self.stdout.write("Loading video and audio...")
            audio_output = output_dir / 'dialogue_audio.wav'
            video = VideoFileClip(str(output_video_path))
            audio = AudioFileClip(str(audio_output))
            
            final_video = video.with_audio(audio)
            final_output_path = output_dir / 'final_video_with_audio.mp4'
            self.stdout.write(f"Writing final output to {final_output_path}...")
            final_video.write_videofile(
                str(final_output_path),
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(output_dir / 'temp_audio.m4a'),
                remove_temp=True,
                threads=4
            )

            # Clean up to free memory
            video.close()
            audio.close()
            final_video.close()
            video_obj.status = 'processed'
            video_obj.save()
            # Print completion message
            execution_time = str(timedelta(seconds=int(time.time() - s_time)))
            self.stdout.write(self.style.SUCCESS(
                f"✅ Successfully processed video in {execution_time}"
            ))
            self.play_completion_sound()
        except Exception as e:
            if 'video_obj' in locals():
                video_obj.status = 'failed'
                video_obj.save()
            raise CommandError(f"Error processing video: {str(e)}")
        
    def stitch_videos(self, video_paths, output_path):
        """Concatenate multiple videos into one"""
        clips = []
        for video_path in video_paths:
            clip = VideoFileClip(str(video_path))
            clips.append(clip)
        
        final_clip = concatenate_videoclips(clips)
        final_clip.write_videofile(
            str(output_path),
            codec='libx264',
            audio_codec='aac',
            temp_audiofile=str(output_path.parent / 'temp-audio.m4a'),
            remove_temp=True,
            logger=None
        )
        
        for clip in clips:
            clip.close()
            
    def clean_dialogue_text(self, dialogue_text):
        """Remove video markers and word count annotations from dialogue text"""
        import re
        
        # Remove complex video markers like:
        # [VIDEO filename.mp4 should start here. Duration = 22.5 seconds. Target WordCount = 60]
        # [VIDEO filename.mp4 should start here]
        cleaned_dialogue = re.sub(r'\[VIDEO\s+[^]]+\]', '', dialogue_text)
        
        # Remove standalone word count annotations like (59 words) or (123 words)
        cleaned_dialogue = re.sub(r'\(\d+\s+words?\)', '', cleaned_dialogue)
        
        # Clean up extra whitespace and empty lines
        cleaned_dialogue = '\n'.join(line.strip() for line in cleaned_dialogue.split('\n') if line.strip())
        
        return cleaned_dialogue

    def generate_dialogue(self, video_outline, student_profile, keywords):
        """Generate dialogue based on the stitched video content"""
        try:
            # Prepare context for the prompt
            student_name = student_profile.get('name', 'Student')
            grade = student_profile.get('grade', 3)
            interests = ", ".join([i['topic'] for i in student_profile.get('interests', [])])
            keywords_str = ", ".join(keywords) if keywords else "general education"
            
            # Create a more detailed prompt that asks Gemini to analyze the video
            prompt = f"""This list of clips will be stitched together to make an educational video. 
{video_outline}
                        Create an engaging dialogue between a teacher and {student_name} (a {grade}th grade student) about {keywords_str}.
                        to be converted to audio and overlayed on the video compilation.
                       
                        Format the dialogue like this:
                        
                        [VIDEO 268685.mp4 should start here.  Duration =  22.5 seconds.  Target WordCount = 22.5 * 2.7]
                        Teacher: [dialogue]
                        {student_name}: [dialogue]
                        Teacher: [dialogue]
                        {student_name}: [dialogue]
                        Teacher: [dialogue]                        
                        [VIDEO 268990.mp4 should start here]
                        ...
                        
                        Guidelines:
                        - Begin the presentation with a short intro casually mentioning the lesson keywords. 
                        - Don't talk about the first video.
                        - Use the socratic method
                        - Match the student's grade level
                        - Be fun, engaging, and educational
                        - The student should speak using the age-appropriate speech patterns
                        - You don't need to describe what's happening in every video.  Prioritize creating a complete educational lesson over descriptiveness.
                        - Don't talk about the last video
                        - During the final video clip wrap up the lesson, summarize the key points, and end with a positive, encouraging statement.
                        
                        IMPORTANT:
                        - The recommendations made in this document about video placement will be used to construct the final video compilation, so adhere to timings to stay in synch.
                        - You can reference a past video, but not a future one.
                        - All 10 video clips must be used once and only once
                        
                        IMPORTANT WORD COUNT GUIDELINES:
                        - Target speaking rate: 2.5-3.0 words per second
                        - For a 20-second video: aim for 50-60 words
                        - For a 15-second video: aim for 37-45 words  
                        - For a 30-second video: aim for 75-90 words
                        - Formula: target_words = video_duration_seconds * 2.7

                        Count your words between each [VIDEO marker] and adjust to match these targets.
"""
            print(f"{prompt}")
            model_name = get_default_model("dialogue")
            response = generate_dialogue_with_gemini(prompt)
            dialogue = response.get("response", "").strip()

            return '\n'.join(line for line in dialogue.split('\n') if line.strip())
        

        except Exception as e:
            self.stderr.write(f"Error generating dialogue: {str(e)}")
            # Fallback to a simple dialogue if there's an error
            return f"Teacher: Let's learn about {keywords_str}.\n{student_name}: That sounds interesting! What should I know?\nTeacher: I'll explain the key concepts in this video."    
    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")
    def parse_video_order_from_dialogue(self, dialogue_text, selected_dir):
        """Parse dialogue text to extract video filenames in recommended order"""
        import re
        
        # Find all video markers in the dialogue
        video_pattern = r'\[VIDEO\s+(\w+\.mp4)\s+should start here'
        video_matches = re.findall(video_pattern, dialogue_text)
        
        self.stdout.write(f"Found {len(video_matches)} video markers in dialogue")
        
        # Convert to Path objects and verify they exist
        ordered_video_paths = []
        for filename in video_matches:
            video_path = selected_dir / filename
            if video_path.exists():
                ordered_video_paths.append(video_path)
                self.stdout.write(f"✅ Found video: {filename}")
            else:
                self.stdout.write(f"⚠️  Video not found: {filename}")
        
        return ordered_video_paths