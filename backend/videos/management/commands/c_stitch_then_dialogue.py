import os
import csv
import subprocess
import time
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips
from videos.models import Video
from profiles.models import StudentProfile
from videos.services.audio_generator.utils import VOICE_ID_MAP
import google.generativeai as genai
import json
import platform
from videos.services.video_generator.ai_analysis import analyze_with_gemini

class Command(BaseCommand):
    help = 'Stitch videos first, then generate dialogue for the entire stitched video'
    
    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process')
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')

    def get_voice_id(self, logical_voice, tts_engine):
        return VOICE_ID_MAP[tts_engine][logical_voice]

    def handle(self, *args, **options):
        s_time = time.time()
        student_id = options['student_id']
        video_id = options['video_id']
        try:
            # Get student profile
            profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
            student_profile = {
                'id': str(profile.id),
                'name': profile.name,
                'grade': profile.grade,
                'interests': [
                    {
                        'topic': interest.topic,
                        'confidence': interest.confidence,
                        'source': interest.source
                    }
                    for interest in profile.interests.all()
                ]
            }
            # Get video object
            video_obj = Video.objects.get(id=video_id)
            self.stdout.write(self.style.SUCCESS(f"Processing video {video_obj.id} for student {student_profile['name']}"))
            # Set up directories
            base_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id)
            selected_dir = base_dir / 'selected'
            output_dir = base_dir / 'final'
            output_dir.mkdir(exist_ok=True)
            # Get list of video files
            video_files = sorted(selected_dir.glob('*.mp4'))
            if not video_files:
                raise CommandError(f"No video files found in {selected_dir}")
            # Stitch videos together
            output_video_path = output_dir / 'stitched_video.mp4'
            processed_videos = []
            csv_path = base_dir / 'video_analysis.csv'
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                for i, row in enumerate(reader):
                    if i >= 10:  # Stop after 10 rows
                        break
                    filename = row.get('Filename', '')
                    video_path = selected_dir / filename
                    with VideoFileClip(str(video_path)) as clip:
                            duration = clip.duration
                    video_data = {
                                    'filename': row.get('Filename', ''),
                                    'visual_description': row.get('Visual Description', ''),
                                    'overall_score': float(row.get('Overall Score', 0) or 0),
                                    'video_id': row.get('Video ID', ''),
                                    'duration': duration,
                                    'start_time': 0.0,  # Will be calculated below
                                    'end_time': 0.0     # Will be calculated
                                }
                    print(f"video data: {video_data}")
                    processed_videos.append(video_data)
            processed_videos = processed_videos[:10]
            current_time = 0.0
            for video in processed_videos:
                video['start_time'] = current_time
                video['end_time'] = current_time + video['duration']
                current_time = video['end_time']
            # Create timed outline for the prompt
            outline_parts = []
            for video in processed_videos:
                start_time = video['start_time']
                end_time = video['end_time']
                outline_parts.append(f"{start_time:.1f}-{end_time:.1f}s: {video['visual_description']}")
            video_outline = "\n".join(outline_parts)
            self.write_timing_to_csv(csv_path, processed_videos)
            # Extract file paths from processed_videos in CSV order
            video_paths = []
            for video_data in processed_videos:
                filename = video_data['filename']
                video_path = selected_dir / filename
                if video_path.exists():
                    video_paths.append(video_path)

            # Stitch videos in CSV order
            if video_paths:
                self.stitch_videos(video_paths, output_video_path)
            else:
                raise CommandError(f"No valid video files found from CSV order")
            # Get total duration of stitched video
            with VideoFileClip(str(output_video_path)) as video:
                total_duration = int(video.duration)            # Generate dialogue for the entire video
            dialogue = self.generate_dialogue(
                video_outline=video_outline,
                video_file=output_video_path,
                student_profile=student_profile,
                total_duration=total_duration,
                keywords=options.get('keywords', '').split(',') if options.get('keywords') else video_obj.keywords
            )
            # Save dialogue to file
            dialogue_path = output_dir / 'dialogue.txt'
            with open(dialogue_path, 'w') as f:
                f.write(dialogue)
            # Update video object status
            video_obj.status = 'processed'
            video_obj.save()
            # Print completion message
            execution_time = str(timedelta(seconds=int(time.time() - s_time)))
            self.stdout.write(self.style.SUCCESS(
                f"✅ Successfully processed video in {execution_time}"
            ))
            self.play_completion_sound()
        except Exception as e:
            if 'video_obj' in locals():
                video_obj.status = 'failed'
                video_obj.save()
            raise CommandError(f"Error processing video: {str(e)}")
        
    def stitch_videos(self, video_paths, output_path):
        """Concatenate multiple videos into one"""
        clips = []
        for video_path in video_paths:
            clip = VideoFileClip(str(video_path))
            clips.append(clip)
        
        final_clip = concatenate_videoclips(clips)
        final_clip.write_videofile(
            str(output_path),
            codec='libx264',
            audio_codec='aac',
            temp_audiofile=str(output_path.parent / 'temp-audio.m4a'),
            remove_temp=True,
            logger=None
        )
        
        for clip in clips:
            clip.close()
    def write_timing_to_csv(self, csv_path, processed_videos):
        """Write calculated timing data back to CSV"""
        try:
            # Read existing CSV
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                rows = list(reader)
                fieldnames = reader.fieldnames
            
            # Add timing columns if they don't exist
            if 'start_time' not in fieldnames:
                fieldnames.extend(['start_time', 'end_time', 'duration_seconds'])
            
            # Update rows with timing data
            for i, row in enumerate(rows[:len(processed_videos)]):
                video_data = processed_videos[i]
                row['start_time'] = f"{video_data['start_time']:.2f}"
                row['end_time'] = f"{video_data['end_time']:.2f}"
                row['duration_seconds'] = f"{video_data['duration']:.2f}"
            
            # Write back to CSV
            with open(csv_path, 'w', newline='') as file:
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
                
            self.stdout.write(f"✅ Updated CSV with timing data: {csv_path}")
            
        except Exception as e:
            self.stdout.write(f"Warning: Could not write timing data to CSV: {e}")
            
    def generate_dialogue(self, video_outline, video_file, student_profile, total_duration, keywords):
        """Generate dialogue based on the stitched video content"""
        try:
            # Prepare context for the prompt
            student_name = student_profile.get('name', 'Student')
            grade = student_profile.get('grade', 3)
            interests = ", ".join([i['topic'] for i in student_profile.get('interests', [])])
            keywords_str = ", ".join(keywords) if keywords else "general education"
            
            # Create a more detailed prompt that asks Gemini to analyze the video
            prompt = f"""Analyze this educational video and create an engaging dialogue between a teacher and {student_name} (a {grade}th grade student).

                        Video Context:
                        - Duration: {total_duration} seconds
                        - Educational Topics: {keywords_str}

                        Student Profile:
                        - Grade: {grade}
                        - Interests: {interests}

                        This output will be converted to audio and overlayed on this video.
                        This is an outline of the video clips that make up this compilation.
                        {video_outline}
                        
                        Format the dialogue like this:
                        Teacher: [dialogue]
                        {student_name}: [dialogue]
                        Teacher: [dialogue]
                        
                        Guidelines:
                        - Begin the presentation with a short intro casually mentioning the lesson keywords
                     - If current dialogue time exceeds the current segment, STOP and transition
                        - Use the socratic method
                        - Match the student's grade level
                        - Be fun, engaging, and educational
                        - Do not include any markers or formatting - just the dialogue
                        - Do not say filenames.
                        - The student should speak using the age-appropriate speech patterns
                        - During the final video clip wrap up the lesson, summarize the key points, and end with a positive, encouraging statement.
"""
            print(f"{prompt}")
            # Generate the dialogue using analyze_with_gemini with the stitched video
            response = analyze_with_gemini(str(video_file), prompt)
            
            # Handle the response format
            if hasattr(response, 'get') and 'response' in response:
                return response['response']
            return str(response)
            
        except Exception as e:
            self.stderr.write(f"Error generating dialogue: {str(e)}")
            # Fallback to a simple dialogue if there's an error
            return f"Teacher: Let's learn about {keywords_str}.\n{student_name}: That sounds interesting! What should I know?\nTeacher: I'll explain the key concepts in this video."    
    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")
