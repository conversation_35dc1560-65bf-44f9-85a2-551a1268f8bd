import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from pathlib import Path
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.video_generator.video_csv_tracker import VideoCSVTracker
from videos.services.video_generator import external_video_sites
from videos.services.audio_generator.utils import text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video



class Command(BaseCommand):
    help = 'Download 30 second clips from best videos'
    video_progress = {}
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,  # Minimum video segment duration
                'max_speed_factor': 2.0,  # Maximum speed up factor
            }
        }
        self.student_name = None  # Will be set in handle method

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process') 
        parser.add_argument('--keywords', type=str, required=True, help='Comma-separated keywords')


    def handle(self, *args, **options):
           # Configure logging with larger buffer
        import logging
        import sys
        from logging.handlers import MemoryHandler
        from tqdm import tqdm
        s_time = time.time()
        # Disable tqdm progress bars
        tqdm.monitor_interval = 0
        tqdm.get_lock().locks = []
        tqdm.disable = True
        os.environ['MOVIEPY_VERBOSE'] = '0'
        # Create a custom handler that filters out tqdm output
        class TqdmFilter(logging.Filter):
            def filter(self, record):
                return not record.getMessage().startswith('t:')
        
        # Create a custom handler with larger buffer
        memory_handler = MemoryHandler(
            capacity=10000,  # Increase buffer size (default is usually 100)
            target=logging.StreamHandler(sys.stdout)
        )
        
        # Add filter to memory handler
        memory_handler.addFilter(TqdmFilter())
        
        # Configure the logger
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(memory_handler)
        
        # Add filter to all existing handlers
        for handler in logger.handlers:
            handler.addFilter(TqdmFilter())
         # Disable progress bars in other libraries
        logging.getLogger('moviepy').setLevel(logging.WARNING)
        logging.getLogger('PIL').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        # Use logger instead of self.stdout.write
        logger.info("Starting video processing...")

        video_obj = None
        open('debug.log', 'w').close()
        try:
            start_time = time.time()
            # Get student name from command arguments
            student_id = options['student_id']
            video_id = options['video_id']
            keywords = options['keywords'].split(',')
            try:
                profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
                student_profile = {
                    'id': str(profile.id),
                    'name': profile.name,
                    'grade': profile.grade,
                    'interests': [
                        {
                            'topic': interest.topic,
                            'confidence': interest.confidence,
                            'source': interest.source
                        }
                        for interest in profile.interests.all()
                    ]
                }
            except StudentProfile.DoesNotExist:
                raise CommandError(f"Student profile with ID {student_id} not found")


            try:
                video_obj = Video.objects.get(id=video_id) 
                logger.info(f"Retrieved Video object with ID: {video_obj.id}")
            except Video.DoesNotExist:
                # Raise CommandError if the Video object is not found
                raise CommandError(f"Video object with ID {video_id} not found.") 

            base_dir = Path(settings.MEDIA_ROOT) / 'students' / student_id / str(video_obj.id)

            # Set up directories
            selected_dir = base_dir / 'selected'
            cropped_dir = base_dir / 'cropped'
            processed_dir = base_dir / 'processed'
            compressed_dir = base_dir / 'compressed'
            unused_dir = base_dir / 'unused'
            downloads_dir = base_dir / 'downloads'
            audio_dir = base_dir / 'audio_segments'
            # Create all necessary directories
            for dir_path in [selected_dir, cropped_dir, processed_dir, compressed_dir, unused_dir, downloads_dir, audio_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)            

            # Initialize timing report
            timing_report = []

            tracker = VideoCSVTracker('video_analysis.csv', ['Index', 'Filename','Search Term', 'Subject', 'Overall Score', 'Visual Description',
                'Educational Value', 'Age Appropriate', 'Motion', 'Relevance','Start Time','Video URL'])

            # Initialize processor
            processor = ScriptProcessor(student_profile)
            processor.download_dir = downloads_dir
            processor.compressed_dir = compressed_dir
            processor.cropped_dir = cropped_dir
            processor.processed_dir = processed_dir 
            processor.base_dir = base_dir
            processor.selected_dir = selected_dir
            processor.unused_dir = unused_dir
            
                        # Read processed videos from CSV
            processed_videos = []
            # csv_path = Path(__file__).resolve().parents[3] / 'video_analysis.csv'
            csv_path = base_dir / 'video_analysis.csv'
            if not csv_path.exists():
                raise CommandError(f"CSV file not found at {csv_path}")
                
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    subject_str = row.get('Subject', '')
                    subject_list = [s.strip() for s in subject_str.split(',') if s.strip()]
                    video_data = {
                        'analysis': {
                            'visual_description': row['Visual Description'],
                            'subject': subject_list,  # <-- Add this line
                        },
                        'local_path': str(downloads_dir / 'temp' / row['Filename']),
                        'video_url': row['Video URL'],
                        'video_id': row.get('Video ID') or row.get('video_id') or Path(row['Filename']).stem,
                        'start_time': float(row.get('Start Time', 0)), 
                    }
                    processed_videos.append(video_data)

            selected_videos = processed_videos[:10]
            # print("\nGrouping...")
            # from itertools import combinations

            # # Build a mapping from filename to subject set
            # video_subjects = {Path(v['local_path']).name: set(v['analysis']['subject']) for v in selected_videos}

            # paired = set()
            # side_by_side_pairs = []

            # for v1, v2 in combinations(selected_videos, 2):
            #     s1 = set(v1['analysis']['subject'])
            #     s2 = set(v2['analysis']['subject'])
            #     if len(s1 & s2) >= 2:
            #         # Only pair if neither is already paired
            #         fname1 = Path(v1['local_path']).name
            #         fname2 = Path(v2['local_path']).name
            #         if fname1 not in paired and fname2 not in paired:
            #             side_by_side_pairs.append((v1, v2))
            #             paired.add(fname1)
            #             paired.add(fname2)

            # # Handle unpaired videos
            # unpaired = [v for v in selected_videos if Path(v['local_path']).name not in paired]

            # print("Side-by-side pairs:")
            # for v1, v2 in side_by_side_pairs:
            #     print(f"{Path(v1['local_path']).name} <-> {Path(v2['local_path']).name} (common: {set(v1['analysis']['subject']) & set(v2['analysis']['subject'])})")

            # print("Unpaired videos:")
            # for v in unpaired:
            #     print(Path(v['local_path']).name)
            # print("\nDownloading full versions of top 10 videos...")
            
            top_videos_full = []
            for video_data in selected_videos:  # Assuming top_videos is your sorted list
                print(f"video data::{video_data}")
                analysis = video_data.get('analysis', {})
                print(f"Processing video {analysis.get('video_id')}")
                print(f"Video URL: {video_data.get('video_url')}")
                print(f"Start Time: {video_data.get('start_time')}")
                print(f"Analysis: {analysis}")
                video_id = video_data.get('video_id')  # You'll need to store this during analysis
                video_url = video_data.get('video_url')
                start_time = video_data.get('start_time')
                # Download full version
                full_video_path = selected_dir / f"{video_id}.mp4"
                if video_url:
                        # Directly process and speed up in one step
                    external_video_sites.speed_up_and_process_video(
                        video_url, 
                        str(full_video_path), 
                        speed_factor=1.5, 
                        start_time=start_time,      # or your actual start time
                        duration=30,       # or your actual duration
                        scale="1280:720",  # or your desired scale
                        keep_audio=False   # or True if you want audio
                    )
                    top_videos_full.append({
                        'full_path': str(full_video_path),
                        'analysis': analysis,
                        'video_id': video_id
                    })
            e_time = time.time()
            execution_time = e_time - s_time
            formatted_time = str(timedelta(seconds=int(execution_time)))
            
            self.stdout.write(self.style.SUCCESS(
                f"\n✨ Processing completed in {formatted_time} ✨"
            ))
            self.play_completion_sound()
        except Exception as e:
            logger.error(f"Error processing video: {str(e)}")
            raise CommandError(f"Video processing failed: {str(e)}")
     

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)  # 440Hz for 1 second
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")


