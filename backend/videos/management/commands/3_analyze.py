import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from pathlib import Path
from django.conf import settings
from videos.services.video_generator.ai_analysis import analyze_video_content
from videos.services.video_generator.video_csv_tracker import VideoCSVTracker
from moviepy import VideoF<PERSON><PERSON>lip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.audio_generator.utils import text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video



class Command(BaseCommand):
    video_progress = {}
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,  # Minimum video segment duration
                'max_speed_factor': 2.0,  # Maximum speed up factor
            }
        }
        self.student_name = None  # Will be set in handle method

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process') 
        parser.add_argument('--keywords', type=str, required=True, help='Comma-separated keywords')


    def handle(self, *args, **options):
           # Configure logging with larger buffer
        # import logging
        # import sys
        # from logging.handlers import MemoryHandler
        # from tqdm import tqdm
    
        # # Disable tqdm progress bars
        # tqdm.monitor_interval = 0
        # tqdm.get_lock().locks = []
        # tqdm.disable = True
        # os.environ['MOVIEPY_VERBOSE'] = '0'
        # # Create a custom handler that filters out tqdm output
        # class TqdmFilter(logging.Filter):
        #     def filter(self, record):
        #         return not record.getMessage().startswith('t:')
        
        # # Create a custom handler with larger buffer
        # memory_handler = MemoryHandler(
        #     capacity=10000,  # Increase buffer size (default is usually 100)
        #     target=logging.StreamHandler(sys.stdout)
        # )
        
        # # Add filter to memory handler
        # memory_handler.addFilter(TqdmFilter())
        
        # # Configure the logger
        # logger = logging.getLogger(__name__)
        # logger.setLevel(logging.INFO)
        # logger.addHandler(memory_handler)
        
        # # Add filter to all existing handlers
        # for handler in logger.handlers:
        #     handler.addFilter(TqdmFilter())
        #  # Disable progress bars in other libraries
        # logging.getLogger('moviepy').setLevel(logging.WARNING)
        # logging.getLogger('PIL').setLevel(logging.WARNING)
        # logging.getLogger('urllib3').setLevel(logging.WARNING)
        # # Use logger instead of self.stdout.write
        # logger.info("Starting video processing...")

        video_obj = None
        
        try:
            start_time = time.time()
            # Get student name from command arguments
            student_id = options['student_id']
            video_id = options['video_id']
            keywords = options['keywords'].split(',')
            try:
                profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
                student_profile = {
                    'id': str(profile.id),
                    'name': profile.name,
                    'grade': profile.grade,
                    'interests': [
                        {
                            'topic': interest.topic,
                            'confidence': interest.confidence,
                            'source': interest.source
                        }
                        for interest in profile.interests.all()
                    ]
                }
            except StudentProfile.DoesNotExist:
                raise CommandError(f"Student profile with ID {student_id} not found")


            try:
                video_obj = Video.objects.get(id=video_id) 
                print(f"Retrieved Video object with ID: {video_obj.id}")
            except Video.DoesNotExist:
                # Raise CommandError if the Video object is not found
                raise CommandError(f"Video object with ID {video_id} not found.") 

            base_dir = Path(settings.MEDIA_ROOT) / 'students' / student_id / str(video_obj.id)

            # Set up directories
            selected_dir = base_dir / 'selected'
            cropped_dir = base_dir / 'cropped'
            processed_dir = base_dir / 'processed'
            compressed_dir = base_dir / 'compressed'
            unused_dir = base_dir / 'unused'
            downloads_dir = base_dir / 'downloads'
            audio_dir = base_dir / 'audio_segments'
            # Create all necessary directories
            for dir_path in [selected_dir, cropped_dir, processed_dir, compressed_dir, unused_dir, downloads_dir, audio_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)            

            # Initialize timing report
            timing_report = []

            tracker = VideoCSVTracker('video_analysis.csv', ['Index', 'Filename','Search Term', 'Subject', 'Overall Score', 'Visual Description',
                'Educational Value', 'Age Appropriate', 'Motion', 'Relevance','Start Time','Video URL'])

            # Initialize processor
            processor = ScriptProcessor(student_profile)
            processor.download_dir = downloads_dir
            processor.compressed_dir = compressed_dir
            processor.cropped_dir = cropped_dir
            processor.processed_dir = processed_dir 
            processor.base_dir = base_dir
            processor.selected_dir = selected_dir
            processor.unused_dir = unused_dir
            
      

            processed_videos = []
            tiny_temp_dir = downloads_dir / 'temp'
            for video in tiny_temp_dir.iterdir():
                if video.is_file() and video.suffix == '.mp4':
                    video_path = video
                    print(f"path {video_path}")
                    processed_path = processed_dir / f"{video_path.name}"
                    analysis = analyze_video_content(
                        str(video_path), 
                        dest=str(processed_path),
                        student_profile=student_profile,
                        keywords=keywords,
                        model_name="gemini",
                        video_id=None,
                        original_url=None
                    )           
                    video_data = {'analysis': analysis, 'local_path': str(processed_path)}
                    processed_videos.append(video_data)
                        
            # csv_data = []
            # for i, video in enumerate(processed_videos):
            #     analysis = video.get('analysis', {})
            #     filename = Path(video.get('local_path', '')).name
            #     csv_data.append({
            #         'filename': filename,
            #         'visual_description': analysis.get('analysis', {}).get('visual_description', ''),
            #         'order': i  # Add order information
            #     })
            
            # # Sort top_videos_analysis by original order
            # csv_data.sort(key=lambda x: x['order'])
            
            # Generate CSV report
            print(f"update CSV report...")
            # processed_videos.sort(key=lambda x: x.get('analysis', {}).get('scores', {}).get('overall_score', 0), reverse=True)
            # processor.generate_csv_report(processed_videos, base_dir, student_profile['interests'])
            for i, video_data in enumerate(processed_videos, 1):
                analysis = video_data.get('analysis', {})
                filename = Path(video_data.get('local_path', '')).name
                short_filename = filename.split('_')[-1]
                subject_list = analysis.get('subject', [])
                subject = ', '.join(subject_list) if subject_list else ''                
                scores = analysis.get('scores', {})
                visual_description = analysis.get('visual_description', 'N/A')
                print(f"Updating CSV report for {short_filename} with subject {subject} and scores {scores}")
                tracker.update_row(
                    'Filename',  # match_field
                    short_filename,    # match_value
                    {           # update_dict
                        'Index': i,
                        'Subject': subject,
                        'Overall Score': scores.get('overall_score', 'N/A'),
                        'Visual Description': visual_description,
                        'Educational Value': scores.get('educational_value', 'N/A'),
                        'Age Appropriate': scores.get('age_appropriate', 'N/A'),
                        'Motion': scores.get('motion', 'N/A'),
                        'Relevance': scores.get('relevance_to_at_least_one_keyword', 'N/A'),
                    }
                )
            csv_path = 'video_analysis.csv'
            
            # Sort the CSV file by 'Overall Score' in descending order
            fieldnames = ['Index', 'Filename','Search Term', 'Subject', 'Overall Score', 'Visual Description',
                        'Educational Value', 'Age Appropriate', 'Motion', 'Relevance', 'Video URL', 'Start Time']

            # 1. Read all rows
            with open(csv_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)
            rows.sort(key=lambda r: float(r.get('Overall Score', 0) or 0), reverse=True)
            with open(csv_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for i, row in enumerate(rows, 1):
                    row['Index'] = i
                    writer.writerow(row)
            end_time = time.time()
            execution_time = end_time - start_time
            formatted_time = str(timedelta(seconds=int(execution_time)))
            self.stdout.write(self.style.SUCCESS(
                f"\n✨ Processing completed in {formatted_time} ✨"
            ))
            # Play completion sound
            self.play_completion_sound()
        except Exception as e:
            print(f"Error processing video: {str(e)}")
            raise CommandError(f"Video processing failed: {str(e)}")
     

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)  # 440Hz for 1 second
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")


