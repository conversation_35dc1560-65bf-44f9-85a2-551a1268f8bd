# test_indextts.py

from django.core.management.base import BaseCommand
from indextts.infer import IndexTTS
import os

class Command(BaseCommand):
    help = 'Test IndexTTS with 4 reference voices (adult male/female, child male/female) and output sample audio files.'

    def add_arguments(self, parser):
        parser.add_argument('--text', type=str, default=None, help='Text to synthesize (default: a test phrase per voice)')

    def handle(self, *args, **options):
        MODEL_DIR = "checkpoints"
        CONFIG_PATH = "checkpoints/config.yaml"
        VOICES = {
            "adult_male": "voices/adult_male.wav",
            "adult_female": "voices/adult_female.wav",
            "child_male": "voices/child_male.wav",
            "child_female": "voices/child_female.wav",
        }
        default_text = "Hello, this is a test of the {label} voice."
        test_text = options['text'] if options['text'] else None

        tts = IndexTTS(model_dir=MODEL_DIR, cfg_path=CONFIG_PATH)

        for label, wav_path in VOICES.items():
            if not os.path.exists(wav_path):
                self.stdout.write(self.style.WARNING(f"Reference file not found: {wav_path}"))
                continue
            phrase = test_text if test_text else default_text.format(label=label.replace('_', ' '))
            output_path = f"output_{label}.wav"
            self.stdout.write(f"Synthesizing for {label}...")
            tts.infer(wav_path, phrase, output_path)
            self.stdout.write(self.style.SUCCESS(f"Generated: {output_path}"))