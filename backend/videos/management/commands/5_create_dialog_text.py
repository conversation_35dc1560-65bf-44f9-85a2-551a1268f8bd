import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
# import google.generativeai as genai
from pathlib import Path
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.video_generator.video_stitcher import stitch
from videos.services.video_generator.dialog_generation import generate_dialogue
from videos.services.audio_generator.utils import VOICE_ID_MAP, text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video

class Command(BaseCommand):
    help = 'Create final video from dialogue and selected videos'
    video_progress = {}
    
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,
                'max_speed_factor': 2.0,
            }
        }

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process')
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')
    def get_voice_id(self, logical_voice, tts_engine):
        return VOICE_ID_MAP[tts_engine][logical_voice]
    def handle(self, *args, **options):        
        print("Starting video processing...")
        start_time = time.time()
        student_id = options['student_id']
        video_id = options['video_id']
        try:
            profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
            student_profile = {
                'id': str(profile.id),
                'name': profile.name,
                'grade': profile.grade,
                'interests': [
                    {
                        'topic': interest.topic,
                        'confidence': interest.confidence,
                        'source': interest.source
                    }
                    for interest in profile.interests.all()
                ]
            }
        except StudentProfile.DoesNotExist:
            raise CommandError(f"Student profile with ID {student_id} not found")
        try:
            video_obj = Video.objects.get(id=video_id)
            print(f"Retrieved Video object with ID: {video_obj.id}")
        except Video.DoesNotExist:
            raise CommandError(f"Video object with ID {video_id} not found")
        base_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_obj.id)
        selected_dir = base_dir / 'selected'
        downloads_dir = base_dir / 'downloads'
        audio_dir = base_dir / 'audio_segments'
        dialogue_path = base_dir / 'lesson_dialogue.txt'
        for dir_path in [selected_dir, audio_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        video_obj.status = 'processing'
        video_obj.save()
        processor = ScriptProcessor(student_profile)
        processor.base_dir = base_dir
        processor.selected_dir = selected_dir
        processed_videos = []
        # csv_path = Path(__file__).resolve().parents[3] / 'video_analysis.csv'
        csv_path = base_dir / 'video_analysis.csv'
        if not csv_path.exists():
            raise CommandError(f"CSV file not found at {csv_path}")
            
        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if not row.get('Visual Description') or row['Visual Description'].strip() == 'N/A':
                        continue
                subject_str = row.get('Subject', '')
                subject_list = [s.strip() for s in subject_str.split(',') if s.strip()]
                video_data = {
                        'analysis': {
                            'visual_description': row['Visual Description'],
                            'subject': subject_list,  # <-- Add this line
                        },
                        'local_path': str(downloads_dir / 'temp' / row['Filename']),
                        'video_url': row['Video URL'],
                        'video_id': row.get('Video ID') or row.get('video_id') or Path(row['Filename']).stem,
                        'start_time': float(row.get('Start Time', 0)),
                    }
                processed_videos.append(video_data)

        if not processed_videos:
            raise CommandError("No videos found in the CSV file")

        print(f"Found {len(processed_videos)} videos in CSV")

        keywords = options.get('keywords')
        if keywords:
            keywords = keywords.split(',') # Split comma-separated string into a list
        else:
            keywords = video_obj.keywords # Assume video_obj.keywords is already a list
        
        top_videos =processed_videos[:10]
        generate_dialogue(
            top_videos,
            student_profile,
            base_dir,
            seconds_per_clip=20,
            keywords=keywords,
            total_duration_seconds=200,
        )
        end_time = time.time()
        execution_time = end_time - start_time
        formatted_time = str(timedelta(seconds=int(execution_time)))
        
        self.stdout.write(self.style.SUCCESS(
            f"\n✨ Processing completed in {formatted_time} ✨"
        ))
        self.play_completion_sound()

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")