import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
# import google.generativeai as genai
from pathlib import Path
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.video_generator.video_stitcher import stitch
from videos.services.video_generator.dialog_generation import generate_dialogue
from videos.services.audio_generator.utils import VOICE_ID_MAP, text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video

class Command(BaseCommand):
    help = 'Create final video from dialogue and selected videos'
    video_progress = {}
    
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,
                'max_speed_factor': 2.0,
            }
        }

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process')
    def get_voice_id(self, logical_voice, tts_engine):
        return VOICE_ID_MAP[tts_engine][logical_voice]
    def handle(self, *args, **options):        
        print("Starting stitch...")
        start_time = time.time()
        student_id = options['student_id']
        video_id = options['video_id']
        try:
            profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
            student_profile = {
                'id': str(profile.id),
                'name': profile.name,
                'grade': profile.grade,
                'interests': [
                    {
                        'topic': interest.topic,
                        'confidence': interest.confidence,
                        'source': interest.source
                    }
                    for interest in profile.interests.all()
                ]
            }
        except StudentProfile.DoesNotExist:
            raise CommandError(f"Student profile with ID {student_id} not found")
        try:
            video_obj = Video.objects.get(id=video_id)
            print(f"Retrieved Video object with ID: {video_obj.id}")
        except Video.DoesNotExist:
            raise CommandError(f"Video object with ID {video_id} not found")
        base_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_obj.id)
        selected_dir = base_dir / 'selected'
        audio_dir = base_dir / 'audio_segments'
        dialogue_path = base_dir / 'lesson_dialogue.txt'
        for dir_path in [selected_dir, audio_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        video_obj.status = 'processing'
        video_obj.save()
        processor = ScriptProcessor(student_profile)
        processor.base_dir = base_dir
        processor.selected_dir = selected_dir
        processed_videos = []
        # csv_path = Path(__file__).resolve().parents[3] / 'video_analysis.csv'
        csv_path = base_dir / 'video_analysis.csv'
        if not csv_path.exists():
            raise CommandError(f"CSV file not found at {csv_path}")
            
        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                video_data = {
                    'local_path': str(selected_dir / row['Filename']),
                    'visual_description': row.get('Visual Description', ''),
                    'duration': '20'

                }
                processed_videos.append(video_data)

        top_videos = sorted(
            processed_videos,
            key=lambda v: float(v.get('Overall Score', 0) or 0),
            reverse=True
        )[:10]

        tts_engine = "typecast"  # "kokoro"
        teacher_voice_id = self.get_voice_id("adult_male", tts_engine)
        student_voice_id = self.get_voice_id("child_female", tts_engine)
        print(f"DEBUG: About to call stitch with teacher_voice_id='{teacher_voice_id}', student_voice_id='{student_voice_id}'")
        stitch(
            tts_engine=tts_engine,
            dialogue_path=dialogue_path,
            base_dir=base_dir,
            processed_videos=top_videos,
            teacher_voice_id=teacher_voice_id,
            student_voice_id=student_voice_id
        )
        end_time = time.time()
        execution_time = end_time - start_time
        formatted_time = str(timedelta(seconds=int(execution_time)))
        
        self.stdout.write(self.style.SUCCESS(
            f"\n✨ Processing completed in {formatted_time} ✨"
        ))
        self.play_completion_sound()

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")