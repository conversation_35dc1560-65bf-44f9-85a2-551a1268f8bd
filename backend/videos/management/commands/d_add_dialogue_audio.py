import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from django.conf import settings
from moviepy import <PERSON>File<PERSON>lip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from lms_project.ai_config import get_default_model
from videos.services.video_generator.video_stitcher import stitch
from videos.services.video_generator.dialog_generation import generate_dialogue
from videos.services.video_generator.ai_analysis import analyze_video_content
from videos.services.video_generator import external_video_sites
from videos.services.audio_generator.utils import VOICE_ID_MAP, generate_dialogue_audio, text_to_speech, combine_audio_segments
import os
import numpy as np
from videos.services.video_generator.video_csv_tracker import VideoCSVTracker
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video
# Suppress FFmpeg and MoviePy output
os.environ['FFMPEG_LOG_LEVEL'] = 'error'
os.environ['MOVIEPY_VERBOSE'] = '0'
import logging
logging.getLogger('moviepy').setLevel(logging.WARNING)

class Command(BaseCommand):
    help = 'Add dialogue audio to the stitched video'
    
    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object')
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')
        parser.add_argument('--video-outline', type=str, required=False, help='Video outline for sync analysis')

    def handle(self, *args, **options):
        student_id = options['student_id']
        video_id = options['video_id']
        tts_engine = get_default_model('tts')
        s_time = time.time()
        
        try:
            # Set up paths
            base_dir = Path(settings.MEDIA_ROOT) / 'students' / str(student_id) / str(video_id)
            video_path = base_dir / 'final' / 'stitched_video.mp4'
            dialogue_path = base_dir / 'final' / 'dialogue.txt'
            output_path = base_dir / 'final' / 'final_video_with_audio.mp4'

            # Verify files exist
            if not video_path.exists():
                raise CommandError(f"Stitched video not found at {video_path}")
            if not dialogue_path.exists():
                raise CommandError(f"Dialogue file not found at {dialogue_path}")
            
            # Read dialogue
            with open(dialogue_path, 'r') as f:
                dialogue_text = f.read()
            
            # Get voice IDs
            teacher_voice_id = VOICE_ID_MAP[tts_engine]['adult_male']
            student_voice_id = VOICE_ID_MAP[tts_engine]['child_female']
            
            # Generate dialogue audio
            self.stdout.write(f"Generating dialogue audio using {tts_engine}...")
            audio_output = base_dir / 'dialogue_audio.wav'
            generate_dialogue_audio(
                api=tts_engine,
                dialogue_text=dialogue_text,
                teacher_voice_id=teacher_voice_id,
                student_voice_id=student_voice_id,
                output_path=str(audio_output)
            )
            
            # Load video and audio (remove duplicate)
            self.stdout.write("Loading video and audio...")
            video = VideoFileClip(str(video_path))
            audio = AudioFileClip(str(audio_output))
            #here, we should send the audio to ai along with the dialog.txt.   
            # it listen to the audio while reading the dialogue text and write 
            # timestamps in the dialog when it switches subjects
            # and output another document with dialog_snip1 timestamp1 dialog_snip2 timestamp2...,
            # then we can edit video to end vid1 @ timestamp1, vid2 @ timestamp2....
           # Analyze audio and dialogue to get precise topic transition timestamps
            self.stdout.write("Analyzing audio-dialogue synchronization...")
            dialogue_timestamps = self.analyze_audio_dialogue_sync(
                audio_output, 
                dialogue_text,
                base_dir
            )

            # Apply precise video speed corrections based on audio analysis
            if dialogue_timestamps:
                self.stdout.write("Applying video speed corrections...")
                output_path = self.apply_audio_based_speed_corrections(
                    video_path,
                    dialogue_timestamps,
                    base_dir
                )

            # Combine audio with video (mute original video audio)
            self.stdout.write("Combining video and audio...")
            final_video = video.with_audio(audio)
            
            # Write output
            self.stdout.write(f"Writing output to {output_path}...")
            final_video.write_videofile(
                str(output_path),
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(base_dir / 'temp_audio.m4a'),
                remove_temp=True,
                threads=4
            )
            # # Read video outline from CSV
            # csv_path = base_dir / 'video_analysis.csv'
            # video_outline = self.create_video_outline_from_csv(csv_path)
            
            # # Apply synchronization corrections
            # if video_outline:
            #     self.stdout.write("Analyzing video synchronization...")
            #     synced_path = self.fix_video_audio_sync(
            #         output_path, 
            #         video_outline,
            #         base_dir
            #     )
            #     if synced_path != output_path:
            #         output_path = synced_path
            #         self.stdout.write(f"✅ Applied synchronization corrections: {output_path}")
            # Clean up
            video.close()
            audio.close()
            final_video.close()
            self.stdout.write(self.style.SUCCESS(f"✅ Successfully added dialogue audio to video: {output_path}"))
            
            # Move timing code here (fix unreachable code)
            e_time = time.time()
            execution_time = e_time - s_time
            formatted_time = str(timedelta(seconds=int(execution_time)))
            
            self.stdout.write(self.style.SUCCESS(
                f"\n✨ Processing completed in {formatted_time} ✨"
            ))
            self.play_completion_sound()
            
        except Exception as e:
            raise CommandError(f"Error processing video: {str(e)}")
        finally:
            # Clean up temporary files
            temp_files = [
                base_dir / 'temp_audio.m4a',
                base_dir / 'dialogue_audio.wav'
            ]
            for temp_file in temp_files:
                if temp_file.exists():
                    try:
                        os.remove(temp_file)
                    except:
                        pass

    def fix_video_audio_sync(self, video_path, video_outline, base_dir):
        """Analyze final video and apply synchronization corrections"""
        try:
            from videos.services.video_generator.ai_analysis import analyze_with_gemini
            
            prompt = f"""
            Analyze this video with audio and identify synchronization issues.
            
            Video Outline (target timing):
            {video_outline}
            
            Watch the video and listen to the audio. Identify where the dialogue content 
            doesn't match the video content timing.
            
            Return a JSON analysis:
            {{
                "sync_issues": [
                    {{
                        "segment": "time range",
                        "problem": "description of issue",
                        "speed_adjustment": 1.2,
                        "reason": "why correction needed"
                    }}
                ],
                "recommended_actions": [
                    {{
                        "time_range": "start-end",
                        "speed_multiplier": 1.2,
                        "expected_duration": 36.5
                    }}
                ]
            }}
            """
            
            # Use AI to analyze the video
            analysis = analyze_with_gemini(str(video_path), prompt)
            
            if analysis:
                try:
                    sync_data = json.loads(analysis)
                    
                    # Apply corrections if any
                    if sync_data.get('recommended_actions'):
                        return self.apply_speed_corrections(video_path, sync_data['recommended_actions'], base_dir)
                    
                except json.JSONDecodeError:
                    self.stdout.write("Warning: Could not parse sync analysis, skipping corrections")
            
            return video_path
            
        except Exception as e:
            self.stdout.write(f"Warning: Sync analysis failed: {e}")
            return video_path

    def apply_speed_corrections(self, video_path, corrections, base_dir):
        """Apply speed corrections to specific time ranges"""
        try:
            video = VideoFileClip(str(video_path))
            segments = []
            current_time = 0
            
            for correction in corrections:
                start_time = float(correction['time_range'].split('-')[0])
                end_time = float(correction['time_range'].split('-')[1])
                speed_mult = correction['speed_multiplier']
                
                # Add uncorrected segment before this correction
                if start_time > current_time:
                    segments.append(video.subclip(current_time, start_time))
                
                # Apply speed correction to this segment
                segment = video.subclip(start_time, end_time)
                if speed_mult != 1.0:
                    segment = segment.speedx(speed_mult)
                    self.stdout.write(f"Applied {speed_mult:.2f}x speed to {start_time:.1f}-{end_time:.1f}s")
                
                segments.append(segment)
                current_time = end_time
            
            # Add remaining segment
            if current_time < video.duration:
                segments.append(video.subclip(current_time, video.duration))
            
            # Combine segments
            final_video = concatenate_videoclips(segments)
            synced_path = video_path.replace('.mp4', '_synced.mp4')
            
            final_video.write_videofile(
                str(synced_path),
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(base_dir / 'temp_sync.m4a'),
                remove_temp=True,
                threads=4
            )
            
            video.close()
            final_video.close()
            
            return synced_path
            
        except Exception as e:
            self.stdout.write(f"Warning: Speed correction failed: {e}")
            return video_path
       
    def analyze_audio_dialogue_sync(self, audio_file, dialogue_text, base_dir):
        """Send audio and dialogue to AI for precise topic transition analysis"""
        try:
            from videos.services.video_generator.ai_analysis import analyze_with_gemini
            
            prompt = f"""
            Listen to this audio file and read this dialogue text simultaneously.
            
            DIALOGUE TEXT:
            {dialogue_text}
            
            CRITICAL: You must respond with VALID JSON only. No explanations, no markdown, just pure JSON.
            
            Your task: Identify the EXACT timestamps where the spoken dialogue switches between different topics or video segments.
            
            Return a JSON response with:
            1. Topic segments with their exact start/end times
            2. Dialogue snippets that correspond to each segment
            3. Recommended video speed adjustments
            
            IMPORTANT: Calculate speed_multiplier using this formula:
            speed_multiplier = video_segment_duration / audio_duration

            Example: If video segment is 19.7s but audio only takes 15.3s, 
            then speed_multiplier = 19.7/15.3 = 1.29
            
            Format:
            {{
                "topic_segments": [
                    {{
                        "segment_id": 0,
                        "start_time": 0.0,
                        "end_time": 15.3,
                        "dialogue_snippet": "Teacher: Let's learn about...",
                        "video_end_time": 19.7
                    }},
                    {{
                        "segment_id": 1, 
                        "topic": "next topic",
                        "start_time": 15.3,
                        "end_time": 28.7,
                        "dialogue_snippet": "Teacher: Now let's talk about...",
                        "video_end_time": 30.2
                    }}
                ],
                "speed_corrections": [
                    {{
                        "video_segment": "0-19.7",
                        "audio_duration": 15.3,
                        "speed_multiplier": 1.28,
                        "reason": "Audio topic ends at 15.3s but video continues to 19.7s.
                        Formula: 19.7/15.3 = 1.29"
                    }}
                ]
            }}
            """
            
            # Send audio file and prompt to AI
            analysis = analyze_with_gemini(str(audio_file), prompt)
            
            if analysis: 
                raw_response = ""
                json_string = ""
                try:
                    json_string = analysis.get("response", "").strip()
                    json_string = json_string.strip()
                    self.stdout.write(f"Raw AI response: '{json_string}...'")

                    result = json.loads(json_string)
                    self.stdout.write(f"✅ Audio analysis complete: {len(result.get('topic_segments', []))} segments identified")
                    
                    # Save analysis for debugging
                    analysis_file = base_dir / 'final' / 'audio_analysis.json'
                    with open(analysis_file, 'w') as f:
                        json.dump(result, f, indent=2)
                    
                    return result
                    
                except json.JSONDecodeError as e:
                    self.stdout.write(f"Warning: Could not parse audio analysis: {e}")
                    self.stdout.write(f"JSON string length: {len(json_string)}")
                    # Save raw response for debugging
                    debug_file = base_dir / 'final' / 'raw_ai_response.txt'
                    with open(debug_file, 'w') as f:
                        f.write(raw_response)
                    self.stdout.write(f"Saved raw response to: {debug_file}")
                    return None
            else:
                self.stdout.write("Warning: No audio analysis received")
                return None
                
        except Exception as e:
            self.stdout.write(f"Warning: Audio-dialogue analysis failed: {e}")
            return None

    def apply_audio_based_speed_corrections(self, video_path, analysis_data, base_dir):
        """Apply speed corrections to video based on audio analysis"""
        try:
            if not analysis_data.get('speed_corrections'):
                self.stdout.write("No speed corrections needed")
                return str(video_path)
                
            video = VideoFileClip(str(video_path))
            segments = []
            current_time = 0
            
            for correction in analysis_data['speed_corrections']:
                # Parse time range
                time_range = correction['video_segment']
                start_time = float(time_range.split('-')[0])
                end_time = float(time_range.split('-')[1])
                speed_mult = correction['speed_multiplier']
                
                # Add uncorrected segment before this correction
                if start_time > current_time:
                    segments.append(video.subclip(current_time, start_time))
                
                # Apply speed correction to this segment
                segment = video.subclip(start_time, end_time)
                if speed_mult != 1.0:
                    segment = segment.speedx(speed_mult)
                    self.stdout.write(f"Applied {speed_mult:.2f}x speed to {start_time:.1f}-{end_time:.1f}s")
                
                segments.append(segment)
                current_time = end_time
            
            # Add remaining segment
            if current_time < video.duration:
                segments.append(video.subclip(current_time, video.duration))
            
            # Combine segments
            final_video = concatenate_videoclips(segments)
            synced_path = base_dir / 'final' / 'final_video_synced.mp4'
            
            final_video.write_videofile(
                str(synced_path),
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(base_dir / 'temp_sync.m4a'),
                remove_temp=True,
                threads=4
            ) 
            
            video.close()
            final_video.close()
            
            self.stdout.write(f"✅ Created synchronized video: {synced_path}")
            return str(synced_path)
            
        except Exception as e:
            self.stdout.write(f"Warning: Speed correction failed: {e}")
            return str(video_path)  
        
    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)  # 440Hz for 1 second
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")
            
    def create_video_outline_from_csv(self, csv_path):
        """Create video outline from CSV timing data"""
        try:
            if not csv_path.exists():
                self.stdout.write("Warning: CSV not found, skipping sync analysis")
                return None
                
            outline_parts = []
            
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    # Skip rows without timing data
                    if not row.get('start_time') or not row.get('end_time'):
                        continue
                        
                    start_time = float(row['start_time'])
                    end_time = float(row['end_time'])
                    description = row.get('Visual Description', 'No description')
                    
                    outline_parts.append(f"{start_time:.1f}-{end_time:.1f}s: {description}")
            
            if outline_parts:
                video_outline = "\n".join(outline_parts)
                self.stdout.write(f"Created video outline from CSV ({len(outline_parts)} segments)")
                return video_outline
            else:
                self.stdout.write("Warning: No timing data found in CSV")
                return None
                
        except Exception as e:
            self.stdout.write(f"Warning: Could not create video outline from CSV: {e}")
            return None

    def speed_up_video_clip(self, video_clip, target_duration):
        """Speed up a video clip to match target duration using MoviePy"""
        try:
            original_duration = video_clip.duration
            speed_factor = original_duration / target_duration
            
            # Use MoviePy's speedx method
            sped_up_clip = video_clip.speedx(speed_factor)
            return sped_up_clip
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Failed to speed up video: {e}, using original"))
            return video_clip

    def speed_up_audio_clip(self, audio_clip, target_duration, output_path):
        """Speed up an audio clip to match target duration using pydub"""
        try:
            from pydub import AudioSegment
            
            original_duration = audio_clip.duration
            speed_factor = original_duration / target_duration
            
            # Export current audio to temp file
            temp_audio_path = str(output_path).replace('.wav', '_temp.wav')
            audio_clip.write_audiofile(temp_audio_path, logger=None)
            audio_clip.close()
            
            # Load with pydub and speed up
            audio_segment = AudioSegment.from_wav(temp_audio_path)
            sped_up_audio = audio_segment.speedup(playback_speed=speed_factor)
            
            # Export sped up audio
            sped_up_audio.export(output_path, format="wav")
            
            # Clean up temp file
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)
            
            # Reload with MoviePy
            return AudioFileClip(str(output_path))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Failed to speed up audio: {e}, using original"))
            return audio_clip
        

        """Apply speed corrections to specific time ranges"""
        
        video = VideoFileClip(video_path)
        corrected_segments = []
        
        for correction in corrections:
            start_time = correction['time_range'].split('-')[0]
            end_time = correction['time_range'].split('-')[1]
            speed_mult = correction['speed_multiplier']
            
            # Extract segment
            segment = video.subclip(float(start_time), float(end_time))
            
            # Apply speed change
            if speed_mult != 1.0:
                segment = segment.speedx(speed_mult)
                
            corrected_segments.append(segment)
        
        # Recombine
        final_video = concatenate_videoclips(corrected_segments)
        output_path = video_path.replace('.mp4', '_synced.mp4')
        final_video.write_videofile(output_path, ...)
        
        return output_path