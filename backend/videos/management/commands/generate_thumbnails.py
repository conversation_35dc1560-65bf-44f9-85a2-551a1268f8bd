import os
import cv2
import numpy as np
from django.core.management.base import BaseCommand
from django.conf import settings
from pathlib import Path

class Command(BaseCommand):
    help = 'Generate thumbnails for all videos in grade folders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--size',
            type=int,
            default=500,
            help='Size of the thumbnail (default: 500 pixels)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of existing thumbnails'
        )

    def handle(self, *args, **options):
        size = options['size']
        force = options['force']
        
        # Base directory for videos
        videos_base_dir = Path(settings.MEDIA_ROOT) / 'videos'
        
        if not videos_base_dir.exists():
            self.stdout.write(
                self.style.ERROR(f'Videos directory not found: {videos_base_dir}')
            )
            return
            
        # Process each grade folder
        for grade in range(1, 9):
            grade_dir = videos_base_dir / f'grade_{grade}'
            
            if not grade_dir.exists():
                self.stdout.write(
                    self.style.WARNING(f'Grade directory not found: {grade_dir}')
                )
                continue
                
            self.stdout.write(
                self.style.SUCCESS(f'Processing grade {grade} directory: {grade_dir}')
            )
            
            # Process each video file in the grade directory
            video_files = list(grade_dir.glob('*.mp4'))
            
            if not video_files:
                self.stdout.write(
                    self.style.WARNING(f'No video files found in {grade_dir}')
                )
                continue
                
            processed_count = 0
            skipped_count = 0
            
            for video_path in video_files:
                # Generate thumbnail path
                thumbnail_path = video_path.with_suffix('.jpg')
                
                # Skip if thumbnail already exists and force is not specified
                if thumbnail_path.exists() and not force:
                    skipped_count += 1
                    continue
                
                # Generate thumbnail
                if self.generate_thumbnail(video_path, thumbnail_path, size):
                    processed_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Generated thumbnail for {video_path.name}')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'Failed to generate thumbnail for {video_path.name}')
                    )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Grade {grade}: {processed_count} thumbnails generated, {skipped_count} skipped'
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS('Thumbnail generation completed!')
        )

    def generate_thumbnail(self, video_path, thumbnail_path, size):
        """Generate a thumbnail for a video file"""
        try:
            # Open video file
            cap = cv2.VideoCapture(str(video_path))
            
            # Check if video was opened successfully
            if not cap.isOpened():
                return False
                
            # Get video properties
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # Calculate frame to capture (10% into the video)
            frame_number = int(frame_count * 0.1)
            
            # Set the frame position
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read the frame
            ret, frame = cap.read()
            
            # Release the video capture
            cap.release()
            
            if not ret:
                return False
                
            # Resize the frame to the specified size (maintaining aspect ratio)
            height, width = frame.shape[:2]
            if width > height:
                new_width = size
                new_height = int(height * (size / width))
            else:
                new_height = size
                new_width = int(width * (size / height))
                
            resized_frame = cv2.resize(frame, (new_width, new_height))
            
            # If the resized image is smaller than size, pad it
            if new_width < size or new_height < size:
                # Create a black image of the target size
                padded_frame = np.zeros((size, size, 3), dtype=np.uint8)
                
                # Calculate position to center the resized image
                y_offset = (size - new_height) // 2
                x_offset = (size - new_width) // 2
                
                # Place the resized image in the center
                padded_frame[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized_frame
                resized_frame = padded_frame
            
            # Save the thumbnail
            success = cv2.imwrite(str(thumbnail_path), resized_frame)
            return success
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating thumbnail for {video_path.name}: {str(e)}')
            )
            return False