import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
# from videos.services.audio_generator.utils import text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video
from videos.services.video_generator.video_csv_tracker import VideoCSVTracker
from videos.services.video_generator.external_video_sites import (
    get_tiny_videos_from_pexels,
    get_tiny_videos_from_pixabay,
    get_tiny_videos_from_freepik,
    get_best_pexels_video_url,
    get_best_pixabay_video_url,
    get_best_freepik_video_url,
    search_sites,
)


class Command(BaseCommand):
    help = 'download videos'
    sites = ["pexels","pixabay"] # "coverr", "pexels","pixabay"

    video_progress = {}
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,  # Minimum video segment duration
                'max_speed_factor': 2.0,  # Maximum speed up factor
            }
        }
        self.student_name = None  # Will be set in handle method

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process') 
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')


    def handle(self, *args, **options):
           # Configure logging with larger buffer
        # import logging
        # import sys
        # from logging.handlers import MemoryHandler
        # from tqdm import tqdm
    
        # # Disable tqdm progress bars
        # tqdm.monitor_interval = 0
        # tqdm.get_lock().locks = []
        # tqdm.disable = True
        # os.environ['MOVIEPY_VERBOSE'] = '0'
        # # Create a custom handler that filters out tqdm output
        # class TqdmFilter(logging.Filter):
        #     def filter(self, record):
        #         return not record.getMessage().startswith('t:')
        
        # # Create a custom handler with larger buffer
        # memory_handler = MemoryHandler(
        #     capacity=10000,  # Increase buffer size (default is usually 100)
        #     target=logging.StreamHandler(sys.stdout)
        # )
        
        # # Add filter to memory handler
        # memory_handler.addFilter(TqdmFilter())
        
        # # Configure the logger
        # logger = logging.getLogger(__name__)
        # logger.setLevel(logging.INFO)
        # logger.addHandler(memory_handler)
        
        # # Add filter to all existing handlers
        # for handler in logger.handlers:
        #     handler.addFilter(TqdmFilter())
        #  # Disable progress bars in other libraries
        # logging.getLogger('moviepy').setLevel(logging.WARNING)
        # logging.getLogger('PIL').setLevel(logging.WARNING)
        # logging.getLogger('urllib3').setLevel(logging.WARNING)
        # # Use logger instead of self.stdout.write
        # logger.info("Starting video processing...")

        video_obj = None
        # open('debug.log', 'w').close()

        start_time = time.time()
        # Get student name from command arguments
        student_id = options['student_id']
        video_id = options['video_id']
        try:
            profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
            student_profile = {
                'id': str(profile.id),
                'name': profile.name,
                'grade': profile.grade,
                'interests': [
                    {
                        'topic': interest.topic,
                        'confidence': interest.confidence,
                        'source': interest.source
                    }
                    for interest in profile.interests.all()
                ]
            }
        except StudentProfile.DoesNotExist:
            raise CommandError(f"Student profile with ID {student_id} not found")


        # Get parameters
        # videos_per_keyword = options.get('videos_per_keyword', 5)
        # total_duration_seconds = options.get('total_duration_seconds', 60)
        # Retrieve the Video object using the numeric ID
        try:
            video_obj = Video.objects.get(id=video_id) 
            print(f"Retrieved Video object with ID: {video_obj.id}")
        except Video.DoesNotExist:
            video_obj = Video.objects.create(
                id=video_id,  # Explicitly set the ID
                student_id=student_id,
                keywords=options.get('keywords', '').split(',') if options.get('keywords') else [],
                videos_per_keyword=options.get('videos_per_keyword', 5),
                duration_seconds=200,
                teacher_voice='am_puck',  # Default teacher voice
                student_voice='af_jessica',  # Default student voice
                status='pending'
            )
            print(f"Created new Video object with ID: {video_obj.id}")


        base_dir = Path(settings.MEDIA_ROOT) / 'students' / student_id / str(video_obj.id)
        keywords = video_obj.keywords # If keywords are stored on the Video object
        videos_per_keyword = options.get('videos_per_keyword', 5) # If from options
        # videos_per_keyword = video_obj.videos_per_keyword # If from video_obj
        total_duration_seconds = 200


        # Set up directories
        selected_dir = base_dir / 'selected'
        cropped_dir = base_dir / 'cropped'
        processed_dir = base_dir / 'processed'
        compressed_dir = base_dir / 'compressed'
        unused_dir = base_dir / 'unused'
        downloads_dir = base_dir / 'downloads'
        audio_dir = base_dir / 'audio_segments'
        # Create all necessary directories
        for dir_path in [selected_dir, cropped_dir, processed_dir, compressed_dir, unused_dir, downloads_dir, audio_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)            

        # Initialize timing report
        timing_report = []
        # Update video status to processing after directories are set up
        video_obj.status = 'processing'
        video_obj.save()
        tracker = VideoCSVTracker('video_analysis.csv', ['Index', 'Filename','Search Term','Subject', 'Overall Score', 'Visual Description',
            'Educational Value', 'Age Appropriate', 'Motion', 'Relevance','Video URL', 'Start Time'])
        processor = ScriptProcessor(student_profile)
        processor.download_dir = downloads_dir
        processor.compressed_dir = compressed_dir
        processor.cropped_dir = cropped_dir
        processor.processed_dir = processed_dir 
        processor.base_dir = base_dir
        processor.selected_dir = selected_dir
        processor.unused_dir = unused_dir
        
        # keywords = self.generate_keywords(lesson_id)
        # print(f"\nGenerated keywords: {', '.join(keywords)}")
        keywords = []

        processed_videos = []
        already_downloaded = []
        # download for one keyword at a time.  on the next iteration, we have video ids to not download. 
        keywords_str = options.get('keywords', '')
        if keywords_str:
            keywords = keywords_str.split(',')
            print(f"\nUsing keywords from UI: {', '.join(keywords)}")
        else:
            # Only fall back to generating keywords if none provided
            # keywords = self.generate_keywords(options['lesson_id'])
            print(f"\nNo keywords provided, generated: {', '.join(keywords)}")
        
        # ----------------------------------------
        # Search for videos on external sites
        search_sites(
            keywords=keywords,
            download_dir=str(downloads_dir),
            results_per_keyword=videos_per_keyword,
            sites=self.sites,
            tracker=tracker
        )

        end_time = time.time()
        execution_time = end_time - start_time
        formatted_time = str(timedelta(seconds=int(execution_time)))
        # video_progress[lesson_id] = {
        #     'progress': 100,
        #     'status': 'completed'
        # }
        self.stdout.write(self.style.SUCCESS(
            f"\n✨ Processing completed in {formatted_time} ✨"
        ))
        
        # Play completion sound
        self.play_completion_sound()
        # finally:
        #     # Flush any remaining logs
        #     memory_handler.flush()
        #     logger.removeHandler(memory_handler)

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)  # 440Hz for 1 second
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")


