import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from pathlib import Path
from django.conf import settings
from moviepy import VideoFileClip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.audio_generator.utils import text_to_speech, combine_audio_segments
import os
import numpy as np
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video
from keybert import KeyBERT
from sentence_transformers import SentenceTransformer
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize

class Command(BaseCommand):
    help = 'Extract keywords from a lesson plan name'

    def add_arguments(self, parser):
        parser.add_argument('--lesson-plan', type=str, required=True, help='Name of the lesson plan to extract keywords from')
        parser.add_argument('--total-duration', type=int, default=200, help='Total duration in seconds')

    def handle(self, *args, **options):
        lesson_plan = options['lesson_plan']
        total_duration = options['total_duration']
        
        # First check if it's comma-separated
        if ',' in lesson_plan:
            keywords = [term.strip() for term in lesson_plan.split(',')]
            self.stdout.write(f"Using explicit comma-separated terms: {', '.join(keywords)}")
        else:
            # Download required NLTK data if not already downloaded
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt')
            try:
                nltk.data.find('corpora/stopwords')
            except LookupError:
                nltk.download('stopwords')

            # Get English stopwords
            stop_words = set(stopwords.words('english'))
            # Add additional common words to exclude
            stop_words.update(['world', 'wide', 'big', 'small', 'large', 'great', 'way', 'thing', 'stuff', 'something'])

            # Tokenize and filter
            tokens = word_tokenize(lesson_plan.lower())
            # Filter out stopwords and short words
            keywords = [word for word in tokens if word not in stop_words and len(word) > 2]

            self.stdout.write(f"Extracted keywords: {', '.join(keywords)}")
        
        # Calculate video distribution
        num_terms = len(keywords)
        min_segment_duration = 15
        max_segment_duration = 30
        
        # Calculate how many videos we can fit in the total duration
        max_videos = total_duration // min_segment_duration
        min_videos = total_duration // max_segment_duration
        
        # Distribute videos across terms
        videos_per_term = max(1, min_videos // num_terms)
        if videos_per_term * num_terms > max_videos:
            videos_per_term = max_videos // num_terms
        
        # Calculate actual duration per video
        duration_per_video = total_duration / (videos_per_term * num_terms)
        
        self.stdout.write(self.style.SUCCESS("\nVideo Distribution Strategy:"))
        self.stdout.write(f"Total Duration: {total_duration} seconds")
        self.stdout.write(f"Number of Search Terms: {num_terms}")
        self.stdout.write(f"Videos per Term: {videos_per_term}")
        self.stdout.write(f"Duration per Video: {duration_per_video:.1f} seconds")
        self.stdout.write(f"Total Videos: {videos_per_term * num_terms}")