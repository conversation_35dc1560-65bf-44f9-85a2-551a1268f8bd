import csv
import shutil
from django.core.management.base import BaseCommand, CommandError
from pathlib import Path
import google.generativeai as genai
from django.conf import settings
from moviepy import <PERSON>File<PERSON>lip, concatenate_videoclips, TextClip, CompositeVideoClip, ColorClip, AudioFileClip
import re
import json
from videos.services.video_generator.video_stitcher import stitch
from videos.services.video_generator.dialog_generation import generate_dialogue
from videos.services.video_generator.ai_analysis import analyze_video_content
from videos.services.video_generator import external_video_sites
from videos.services.audio_generator.utils import VOICE_ID_MAP, text_to_speech, combine_audio_segments
import os
import numpy as np
from videos.services.video_generator.video_csv_tracker import VideoCSVTracker
from profiles.models import StudentProfile
import math
from datetime import datetime
from videos.services.video_generator.script_processor import ScriptProcessor
import time
from datetime import timedelta
import subprocess
import platform
from videos.models import Video
from videos.services.video_generator.external_video_sites import (
    get_tiny_videos_from_pexels,
    get_tiny_videos_from_pixabay,
    get_tiny_videos_from_freepik,
    get_best_pexels_video_url,
    get_best_pixabay_video_url,
    get_best_freepik_video_url,
    get_a_clip,
    search_sites,
)



class Command(BaseCommand):
    help = 'Create final video from dialogue and selected videos'
    sites = ["pexels","pixabay" ] #, "coverr"
    video_progress = {}
    def __init__(self):
        super().__init__()
        self.config = {
            'video': {
                'width': 1280,
                'height': 720,
                'fps': 30,
                'min_duration': 1,  # Minimum video segment duration
                'max_speed_factor': 2.0,  # Maximum speed up factor
            }
        }
        self.student_name = None  # Will be set in handle method

    def add_arguments(self, parser):
        parser.add_argument('--student-id', type=str, required=True, help='ID of the student')
        parser.add_argument('--video-id', type=int, required=True, help='ID of the Video object to process')
        parser.add_argument('--keywords', type=str, required=False, help='Comma-separated keywords')
    
    def handle(self, *args, **options):
        print("Starting video processing...")
        video_obj = None
        open('debug.log', 'w').close()
        try:
            start_time = time.time()
            student_id = options['student_id']
            video_id = options['video_id']
            videos_per_keyword = 5
            total_duration_seconds = 200
            try:
                profile = StudentProfile.objects.prefetch_related('interests').get(id=student_id)
                student_profile = {
                    'id': str(profile.id),
                    'name': profile.name,
                    'grade': profile.grade,
                    'interests': [
                        {
                            'topic': interest.topic,
                            'confidence': interest.confidence,
                            'source': interest.source
                        }
                        for interest in profile.interests.all()
                    ]
                }
            except StudentProfile.DoesNotExist:
                raise CommandError(f"Student profile with ID {student_id} not found")
            try:
                video_obj = Video.objects.get(id=video_id) 
                print(f"Retrieved Video object with ID: {video_obj.id}")
            except Video.DoesNotExist:
                video_obj = Video.objects.create(
                    id=video_id,  # Explicitly set the ID
                    student_id=student_id,
                    keywords=options.get('keywords', '').split(',') if options.get('keywords') else [],
                    videos_per_keyword=options.get('videos_per_keyword', 5),
                    duration_seconds=200,
                    teacher_voice='af_jessica',  # Default teacher voice
                    student_voice='am_puck',  # Default student voice
                    status='pending'
                )
                print(f"Created new Video object with ID: {video_obj.id}")
            base_dir = Path(settings.MEDIA_ROOT) / 'students' / student_id / str(video_obj.id)
            # keywords = video_obj.keywords # If keywords are stored on the Video object
            videos_per_keyword = 5 # If from options
            selected_dir = base_dir / 'selected'
            cropped_dir = base_dir / 'cropped'
            processed_dir = base_dir / 'processed'
            compressed_dir = base_dir / 'compressed'
            unused_dir = base_dir / 'unused'
            downloads_dir = base_dir / 'downloads'
            audio_dir = base_dir / 'audio_segments'
            for dir_path in [selected_dir, cropped_dir, processed_dir, compressed_dir, unused_dir, downloads_dir, audio_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)            
            dialogue_path = base_dir / 'lesson_dialogue.txt'
            timing_report = []
            video_obj.status = 'processing'
            video_obj.save()
            csv_path = base_dir / 'video_analysis.csv'
            tracker = VideoCSVTracker(csv_path, ['Index', 'Filename','Search Term', 'Subject', 'Overall Score', 'Visual Description',
                'Educational Value', 'Age Appropriate', 'Motion', 'Relevance','Start Time','Video URL'])
            processor = ScriptProcessor(student_profile)
            processor.download_dir = downloads_dir
            processor.compressed_dir = compressed_dir
            processor.cropped_dir = cropped_dir
            processor.processed_dir = processed_dir 
            processor.base_dir = base_dir
            processor.selected_dir = selected_dir
            processor.unused_dir = unused_dir
            
            keywords = []
            already_downloaded = []
            keywords_str = options.get('keywords', '')
            if keywords_str:
                keywords = keywords_str.split(',')
                print(f"\nUsing keywords from UI: {', '.join(keywords)}")
            else:
                print(f"\nNo keywords provided, generated: {', '.join(keywords)}")

            # At the start of your handle method, add these trackers:
            site_index = 0
            keyword_index = 0
            processed_videos = []
            summary_word_sets = set()
            attempts = 0
            max_attempts = 100  # Prevent infinite loops
            videos_per_keyword_site = 5
            # Initialize a dictionary to track videos per keyword and site
            keyword_site_counts = {(site, kw): 0 for site in self.sites for kw in keywords}
            while any(count < videos_per_keyword_site for count in keyword_site_counts.values()) and attempts < max_attempts:
                current_site = self.sites[site_index % len(self.sites)]
                current_keyword = keywords[keyword_index % len(keywords)]
                current_key = (current_site, current_keyword)
                
                if keyword_site_counts[current_key] >= videos_per_keyword_site:
                    site_index += 1  # Move to next site
                    if site_index >= len(self.sites):
                        site_index = 0
                        keyword_index = (keyword_index + 1) % len(keywords)
                    attempts += 1
                    continue
                print(f"\n--- Attempt {attempts + 1} for {current_site}, {current_keyword}")
                print(f"Already downloaded videos: {already_downloaded}")
                print(f"Current counts: {keyword_site_counts}")
                try:
                    # Search only the current site with current keyword
                    video_data = get_a_clip(
                        keyword=current_keyword,
                        download_dir=str(downloads_dir),
                        results_per_keyword=1,
                        sites=[current_site],  # Only search one site at a time
                        tracker=tracker,
                        already_downloaded=already_downloaded
                    )
                    
                    if video_data:
                        video_id = str(video_data[0]['id'])
                        already_downloaded.append(video_id)
                        video_path = Path(downloads_dir) / 'temp' / f"temp_{current_site}_{video_id}.mp4"
                        
                        # Process the video
                        analysis = analyze_video_content(
                            str(video_path),
                            student_profile=student_profile,
                            keywords=current_keyword,
                            video_id=None,
                            original_url=None
                        )
                        
                        video_keywords = set(analysis.get('subject', []))
                        print(f"Found keywords: {video_keywords}")
                        
                        # Check if video is unique enough
                        is_similar = any(len(video_keywords.intersection(seen)) >= 3 
                                    for seen in summary_word_sets)
                        
                        if not is_similar and len(video_keywords) >= 3:
                            # Add to processed videos
                            processed_videos.append({
                                'analysis': analysis,
                                'local_path': str(video_path),
                                'file_name': f"temp_{current_site}_{video_id}.mp4"
                            })
                            summary_word_sets.add(frozenset(video_keywords))
                            keyword_site_counts[(current_site, current_keyword)] += 1
                            print(f"✅ Added video {video_id}. Total: {len(processed_videos)}")
                            # Reset attempts counter when we successfully add a video
                            attempts = 0
                        else:
                            print("❌ Video too similar or not enough keywords")
                            # Clean up the temp file
                            if video_path.exists():
                                video_path.unlink()
                                print(f"🗑️  Removed temporary file: {video_path.name}")
                    site_index += 1
                    if site_index >= len(self.sites):
                        site_index = 0
                        keyword_index = (keyword_index + 1) % len(keywords)
                        
                except Exception as e:
                    print(f"Error processing video: {str(e)}")
                
                attempts += 1
                time.sleep(1)  
            print(f"\n--- Collection Complete ---")
            print(f"Total videos collected: {len(processed_videos)}")
            for (site, kw), count in keyword_site_counts.items():
                print(f"- {site}/{kw}: {count} videos")    
 
            if not processed_videos:
                raise CommandError("Failed to collect enough unique videos after multiple attempts")                    
            print(f"Analysis phase completed. Processed {len(processed_videos)} videos") 
            #eliminate videos with 3 of 5 keywords are the same
            processed_videos = [v for v in processed_videos if len(set(v['analysis']['subject'])) >= 3]
            print(f"After eliminating videos with 3 of 5 keywords are the same: {len(processed_videos)} videos")
            print("\n..................... Sorting and organizing videos...")
            for i, video_data in enumerate(processed_videos, 1):
                # print(f"video data: {video_data}")
                analysis = video_data.get('analysis', {})
                filename = Path(video_data.get('local_path', '')).name
                short_filename = filename.split('_')[-1]
                subject_list = analysis.get('subject', [])
                subject = ', '.join(subject_list) if subject_list else ''                
                scores = analysis.get('scores', {})
                visual_description = analysis.get('visual_description', 'N/A')
                print(f"Updating CSV report for {short_filename} with subject {subject} and scores {scores}")
                tracker.update_row(
                    'Filename',  # match_field
                    short_filename,    # match_value
                    {           # update_dict
                        'Index': i,
                        # 'Search Term': keywords,
                        'Subject': subject,
                        'Overall Score': scores.get('overall_score', 'N/A'),
                        'Visual Description': visual_description,
                        'Educational Value': scores.get('educational_value', 'N/A'),
                        'Age Appropriate': scores.get('age_appropriate', 'N/A'),
                        'Motion': scores.get('motion', 'N/A'),
                        'Relevance': scores.get('relevance_to_at_least_one_keyword', 'N/A'),
                    }
                )
            
            fieldnames = ['Index', 'Filename', 'Search Term', 'Subject', 'Overall Score', 'Visual Description',
                        'Educational Value', 'Age Appropriate', 'Motion', 'Relevance', 'Video URL', 'Start Time']
            with open(csv_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)
            rows.sort(key=lambda r: float(r.get('Overall Score', 0) or 0), reverse=True)
            with open(csv_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for i, row in enumerate(rows, 1):
                    row['Index'] = i
                    writer.writerow(row)
                    
            processed_videos = []
            # csv_path = Path(__file__).resolve().parents[3] / 'video_analysis.csv'
            csv_path = base_dir / 'video_analysis.csv'
            if not csv_path.exists():
                raise CommandError(f"CSV file not found at {csv_path}")
                
            with open(csv_path, 'r') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    subject_str = row.get('Subject', '')
                    subject_list = [s.strip() for s in subject_str.split(',') if s.strip()]
                    video_data = {
                        'analysis': {
                            'visual_description': row['Visual Description'],
                            'subject': subject_list,  # <-- Add this line
                        },
                        'local_path': str(downloads_dir / 'temp' / row['Filename']),
                        'video_url': row['Video URL'],
                        'video_id': row.get('Video ID') or row.get('video_id') or Path(row['Filename']).stem,
                        'start_time': float(row.get('Start Time', 0)), 
                    }
                    processed_videos.append(video_data)
     
            ####################################################
            selected_videos = processed_videos[:10]
            # Group similar videos based on at least 2 matching subject words
            print("\n.....................Grouping similar videos...") 
            print(f"Total videos to process: {len(processed_videos)}")

            # Function to normalize and clean subject words
            def clean_subjects(subjects):
                if not isinstance(subjects, (list, set)):
                    return set()
                return {str(s).strip().lower() for s in subjects if s and str(s).strip()}

            # Select top N videos for grouping
            selected_videos = processed_videos[:10]
            paired = set()
            pairs = []

            # First pass: Find all possible valid pairs
            for i, v1 in enumerate(selected_videos):
                if Path(v1['local_path']).name in paired:
                    continue
                    
                s1 = clean_subjects(v1['analysis'].get('subject', []))
                if not s1:
                    print(f"Skipping {Path(v1['local_path']).name}: No subjects found")
                    continue
                    
                best_match = None
                best_score = 1  # Require at least 2 matches
                
                # Find best match for current video
                for j in range(i + 1, len(selected_videos)):
                    v2 = selected_videos[j]
                    if Path(v2['local_path']).name in paired:
                        continue
                        
                    s2 = clean_subjects(v2['analysis'].get('subject', []))
                    if not s2:
                        continue
                        
                    # Calculate number of matching subjects
                    common = len(s1 & s2)
                    if common > best_score:
                        best_score = common
                        best_match = v2
                
                # If we found a good match, create a pair
                if best_match:
                    v2 = best_match
                    paired.add(Path(v1['local_path']).name)
                    paired.add(Path(v2['local_path']).name)
                    pairs.append((v1, v2, best_score))
                    print(f"Paired: {Path(v1['local_path']).name} <-> {Path(v2['local_path']).name} "
                        f"(common subjects: {best_score})")

            # Get unpaired videos
            unpaired = [v for v in selected_videos if Path(v['local_path']).name not in paired]

            # Print results
            print("\n=== Video Pairs ===")
            for v1, v2, score in pairs:
                s1 = clean_subjects(v1['analysis'].get('subject', []))
                s2 = clean_subjects(v2['analysis'].get('subject', []))
                common = s1 & s2
                print(f"Pair (score: {score}):")
                print(f"  {Path(v1['local_path']).name}: {v1['analysis'].get('subject', [])}")
                print(f"  {Path(v2['local_path']).name}: {v2['analysis'].get('subject', [])}")
                print(f"  Common subjects: {common}\n")

            print("\n=== Unpaired Videos ===")
            for v in unpaired:
                print(f"{Path(v['local_path']).name}: {v['analysis'].get('subject', [])}")

            # Update the CSV with pair information
            # csv_path1 = 'video_analysis.csv'
            # csv_path2 = 'grouped_video_analysis.csv'
            fieldnames = ['Index', 'Filename', 'Search Term', 'Subject', 'Overall Score', 
                        'Visual Description', 'Educational Value', 'Age Appropriate', 
                        'Motion', 'Relevance', 'Video URL', 'Start Time', 'Pair ID']

            # Read existing rows
            rows = []
            with open(csv_path, 'r') as csvfile:
                reader = csv.DictReader(csvfile)
                rows = list(reader)

            # Add pair information
            pair_id = 1
            filename_to_pair = {}
            for i, (v1, v2, _) in enumerate(pairs, 1):
                filename_to_pair[Path(v1['local_path']).name] = i
                filename_to_pair[Path(v2['local_path']).name] = i

            # Update rows with pair information and prepare for sorting
            for row in rows:
                row['Pair ID'] = str(filename_to_pair.get(row['Filename'], ''))

            # Sort rows to keep paired videos together
            # Paired videos come first, sorted by pair ID, then unpaired videos
            def sort_key(row):
                pair_id = row.get('Pair ID')
                if pair_id and pair_id.isdigit():
                    return (0, int(pair_id))  # Paired videos first, sorted by pair ID
                return (1, 0)  # Unpaired videos after

            # Sort the rows
            rows.sort(key=sort_key)

            # Write updated CSV
            with open(csv_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for i, row in enumerate(rows, 1):
                    row['Index'] = i
                    writer.writerow(row)

            print(f"\nGrouping complete. Results written to {csv_path}")
###########################
            video_obj.status = 'completed'
            video_obj.progress = 100
            video_obj.status_message = "Video generation completed"
            video_obj.save()
            
            print("Video generation completed successfully")
            
        except Exception as e:
            print(f"Error in video generation: {str(e)}")
            if video_obj:
                video_obj.status = 'failed'
                video_obj.status_message = f"Video generation failed: {str(e)}"
                video_obj.save()
            raise CommandError(f"Video generation failed: {str(e)}")

    def play_completion_sound(self):
        """Play a completion sound based on the operating system."""
        try:
            system = platform.system()
            if system == 'Darwin':  # macOS
                subprocess.run(['afplay', '/System/Library/Sounds/Glass.aiff'])
            elif system == 'Linux':
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/complete.oga'])
            elif system == 'Windows':
                import winsound
                winsound.Beep(440, 1000)  # 440Hz for 1 second
        except Exception as e:
            self.stderr.write(f"Could not play completion sound: {str(e)}")
