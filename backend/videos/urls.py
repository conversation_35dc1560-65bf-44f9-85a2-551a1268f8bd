from django.urls import path

from .services.student_profiler import views as profiler_views
from . import views 

urlpatterns = [

    # Profile endpoints
        path('list_all_filenames/', views.list_video_filenames, name='list_all_filenames'),
    path('profile/questionnaire/submit/', profiler_views.submit_questionnaire, name='submit_questionnaire'),
    path('profile/youtube/analyze/', profiler_views.analyze_youtube, name='analyze_youtube'),
    path('profile/combine/', profiler_views.combine_profiles, name='combine_profiles'),
        # New video generation endpoints
    path('generate-video/', views.generate_video, name='generate_video'),
    path('<str:student_id>/', views.get_student_videos, name='get_student_videos'),
    path('<int:video_id>/progress/', views.get_video_progress, name='get_video_progress'),
    # path('student-by-video/<int:video_id>/', views.get_student_profile_by_video_id, name='get_student_profile_by_video_id'),
    path('api/manual/download-videos/', views.manual_download_videos, name='manual_download_videos'),
    path('api/manual/process-analyze/', views.manual_process_analyze, name='manual_process_analyze'),
    path('api/manual/create-final-video/', views.manual_create_final_video, name='manual_create_final_video'),
    path('video-attempts/', views.list_video_attempts, name='list_video_attempts'),
    path('video-attempts/<int:video_id>/', views.video_attempt_details, name='video_attempt_details'),
    path('voices/typecast/', views.get_available_typecast_voices, name='get_available_typecast_voices'), # New line for Typecast voices
    # path('voices/one-shot/', views.get_available_one_shot_voices, name='get_available_one_shot_voices'),
    path('api/videos/list_grade_filenames/<int:grade>/', views.list_grade_filenames, name='list_grade_filenames'),
    # Get videos for a specific grade
    path('grade/<int:grade>/', views.get_grade_videos, name='grade_videos'),
    
    # Get all videos for all grades
    path('api/videos/all/', views.get_all_videos, name='all_videos'),


]