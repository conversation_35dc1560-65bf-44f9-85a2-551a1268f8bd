# Generated by Django 5.1.4 on 2025-05-07 20:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('profiles', '0002_alter_studentprofile_age_alter_studentprofile_grade'),
    ]

    operations = [
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_file', models.FileField(blank=True, null=True, upload_to='videos/')),
                ('keywords', models.J<PERSON>NField(default=list)),
                ('videos_per_keyword', models.IntegerField(default=5)),
                ('duration_minutes', models.FloatField(default=1.0)),
                ('teacher_voice', models.CharField(default='tc_67b6985d4d5d632d97478263', max_length=50)),
                ('student_voice', models.Char<PERSON><PERSON>(default='tc_62fb679683a541c351dc7c3a', max_length=50)),
                ('total_videos_downloaded', models.IntegerField(blank=True, null=True)),
                ('total_videos_used', models.IntegerField(blank=True, null=True)),
                ('total_duration_seconds', models.FloatField(blank=True, null=True)),
                ('status', models.CharField(default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='profiles.studentprofile')),
            ],
        ),
    ]
