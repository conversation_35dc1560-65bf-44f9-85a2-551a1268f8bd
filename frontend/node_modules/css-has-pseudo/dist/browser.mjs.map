{"version": 3, "file": "browser.mjs", "sources": ["../src/encode/decode.mjs", "../src/encode/extract.mjs", "../src/encode/encode.mjs", "../src/browser.js", "../../../node_modules/@mrhenry/core-web/modules/~element-qsa-has.js"], "sourcesContent": ["\n/** Decodes an identifier back into a CSS selector */\nexport default function decodeCSS(value) {\n\tif (value.slice(0, 13) !== 'csstools-has-') {\n\t\treturn '';\n\t}\n\n\tvalue = value.slice(13);\n\tlet values = value.split('-');\n\n\tlet result = '';\n\tfor (let i = 0; i < values.length; i++) {\n\t\tresult += String.fromCharCode(parseInt(values[i], 36));\n\t}\n\n\treturn result;\n}\n", "import decodeCSS from './decode.mjs';\n\n/** Extract encoded selectors out of attribute selectors */\nexport default function extractEncodedSelectors(value) {\n\tlet out = [];\n\n\tlet depth = 0;\n\tlet candidate;\n\n\tlet quoted = false;\n\tlet quotedMark;\n\n\tlet containsUnescapedUnquotedHasAtDepth1 = false;\n\n\t// Stryker disable next-line EqualityOperator\n\tfor (let i = 0; i < value.length; i++) {\n\t\tconst char = value[i];\n\n\t\tswitch (char) {\n\t\t\tcase '[':\n\t\t\t\tif (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (depth === 0) {\n\t\t\t\t\tcandidate = '';\n\t\t\t\t} else {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t}\n\n\t\t\t\tdepth++;\n\t\t\t\tcontinue;\n\t\t\tcase ']':\n\t\t\t\tif (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t{\n\t\t\t\t\tdepth--;\n\t\t\t\t\tif (depth === 0) {\n\t\t\t\t\t\tconst decoded = decodeCSS(candidate);\n\t\t\t\t\t\tif (containsUnescapedUnquotedHasAtDepth1) {\n\t\t\t\t\t\t\tout.push(decoded);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcandidate += char;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcontinue;\n\t\t\tcase '\\\\':\n\t\t\t\tcandidate += value[i];\n\t\t\t\tcandidate += value[i+1];\n\t\t\t\ti++;\n\t\t\t\tcontinue;\n\n\t\t\tcase '\"':\n\t\t\tcase '\\'':\n\t\t\t\tif (quoted && char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tquoted = true;\n\t\t\t\tquotedMark = char;\n\t\t\t\tcontinue;\n\n\t\t\tdefault:\n\t\t\t\tif (candidate === '' && depth === 1 && (value.slice(i, i + 13) === 'csstools-has-')) {\n\t\t\t\t\tcontainsUnescapedUnquotedHasAtDepth1 = true;\n\t\t\t\t}\n\n\t\t\t\tcandidate += char;\n\t\t\t\tcontinue;\n\t\t}\n\t}\n\n\tconst unique = [];\n\tfor (let i = 0; i < out.length; i++) {\n\t\tif (unique.indexOf(out[i]) === -1) {\n\t\t\tunique.push(out[i]);\n\t\t}\n\t}\n\n\treturn unique;\n}\n", "\n/** Returns the string as an encoded CSS identifier. */\nexport default function encodeCSS(value) {\n\tif (value === '') {\n\t\treturn '';\n\t}\n\n\tlet hex;\n\tlet result = '';\n\tfor (let i = 0; i < value.length; i++) {\n\t\thex = value.charCodeAt(i).toString(36);\n\t\tif (i === 0) {\n\t\t\tresult += hex;\n\t\t} else {\n\t\t\tresult += '-' + hex;\n\t\t}\n\t}\n\n\treturn 'csstools-has-' + result;\n}\n", "import '@mrhenry/core-web/modules/~element-qsa-has.js';\nimport extractEncodedSelectors from './encode/extract.mjs';\nimport encodeCSS from './encode/encode.mjs';\n\nfunction hasNativeSupport() {\n\ttry {\n\t\tif (!('CSS' in self) || !('supports' in self.CSS) || !self.CSS.supports('selector(:has(div))')) {\n\t\t\treturn false;\n\t\t}\n\n\t} catch (_) {\n\t\treturn false;\n\t}\n\n\treturn true;\n}\n\nexport default function cssHasPseudo(document, options) {\n\t// OPTIONS\n\t{\n\t\tif (!options) {\n\t\t\toptions = {};\n\t\t}\n\n\t\toptions = {\n\t\t\thover: (!!options.hover) || false,\n\t\t\tdebug: (!!options.debug) || false,\n\t\t\tobservedAttributes: options.observedAttributes || [],\n\t\t\tforcePolyfill: (!!options.forcePolyfill) || false,\n\t\t};\n\n\t\toptions.mustPolyfill = options.forcePolyfill || !hasNativeSupport();\n\n\t\tif (!Array.isArray(options.observedAttributes)) {\n\t\t\toptions.observedAttributes = [];\n\t\t}\n\n\t\toptions.observedAttributes = options.observedAttributes.filter(function(x) {\n\t\t\treturn (typeof x === 'string');\n\t\t});\n\n\t\t// https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes\n\t\t// `data-*` and `style` were omitted\n\t\toptions.observedAttributes = options.observedAttributes.concat(['accept', 'accept-charset', 'accesskey', 'action', 'align', 'allow', 'alt', 'async', 'autocapitalize', 'autocomplete', 'autofocus', 'autoplay', 'buffered', 'capture', 'challenge', 'charset', 'checked', 'cite', 'class', 'code', 'codebase', 'cols', 'colspan', 'content', 'contenteditable', 'contextmenu', 'controls', 'coords', 'crossorigin', 'csp', 'data', 'datetime', 'decoding', 'default', 'defer', 'dir', 'dirname', 'disabled', 'download', 'draggable', 'enctype', 'enterkeyhint', 'for', 'form', 'formaction', 'formenctype', 'formmethod', 'formnovalidate', 'formtarget', 'headers', 'hidden', 'high', 'href', 'hreflang', 'http-equiv', 'icon', 'id', 'importance', 'integrity', 'intrinsicsize', 'inputmode', 'ismap', 'itemprop', 'keytype', 'kind', 'label', 'lang', 'language', 'list', 'loop', 'low', 'manifest', 'max', 'maxlength', 'minlength', 'media', 'method', 'min', 'multiple', 'muted', 'name', 'novalidate', 'open', 'optimum', 'pattern', 'ping', 'placeholder', 'poster', 'preload', 'radiogroup', 'readonly', 'referrerpolicy', 'rel', 'required', 'reversed', 'rows', 'rowspan', 'sandbox', 'scope', 'scoped', 'selected', 'shape', 'size', 'sizes', 'slot', 'span', 'spellcheck', 'src', 'srcdoc', 'srclang', 'srcset', 'start', 'step', 'summary', 'tabindex', 'target', 'title', 'translate', 'type', 'usemap', 'value', 'width', 'wrap']);\n\t}\n\n\tconst observedItems = [];\n\n\t// document.createAttribute() doesn't support `:` in the name. innerHTML does\n\tconst attributeElement = document.createElement('x');\n\n\t// walk all stylesheets to collect observed css rules\n\t[].forEach.call(document.styleSheets, walkStyleSheet);\n\tif (!options.mustPolyfill) {\n\t\t// Cleanup of rules will have happened in `walkStyleSheet`\n\t\t// Native support will take over from here\n\t\treturn;\n\t}\n\n\ttransformObservedItemsThrottled();\n\n\t// observe DOM modifications that affect selectors\n\tif ('MutationObserver' in self) {\n\t\tconst mutationObserver = new MutationObserver(function(mutationsList) {\n\t\t\tmutationsList.forEach(function(mutation) {\n\t\t\t\t[].forEach.call(mutation.addedNodes || [], function(node) {\n\t\t\t\t\t// walk stylesheets to collect observed css rules\n\t\t\t\t\tif (node.nodeType !== 1) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (node.sheet) {\n\t\t\t\t\t\twalkStyleSheet(node.sheet);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tnode.addEventListener('load', function (e) {\n\t\t\t\t\t\tif (e.target && e.target.sheet) {\n\t\t\t\t\t\t\twalkStyleSheet(e.target.sheet);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\t// transform observed css rules\n\t\t\t\tcleanupObservedCssRules();\n\t\t\t\ttransformObservedItemsThrottled();\n\t\t\t});\n\t\t});\n\n\t\tmutationObserver.observe(document, { childList: true, subtree: true, attributes: true, attributeFilter: options.observedAttributes });\n\t}\n\n\t// observe DOM events that affect pseudo-selectors\n\tdocument.addEventListener('focus', transformObservedItemsThrottled, true);\n\tdocument.addEventListener('blur', transformObservedItemsThrottled, true);\n\tdocument.addEventListener('input', transformObservedItemsThrottled);\n\tdocument.addEventListener('change', transformObservedItemsThrottled, true);\n\n\tif (options.hover) {\n\t\tif ('onpointerenter' in document) {\n\t\t\tdocument.addEventListener('pointerenter', transformObservedItemsThrottled, true);\n\t\t\tdocument.addEventListener('pointerleave', transformObservedItemsThrottled, true);\n\t\t} else {\n\t\t\tdocument.addEventListener('mouseover', transformObservedItemsThrottled, true);\n\t\t\tdocument.addEventListener('mouseout', transformObservedItemsThrottled, true);\n\t\t}\n\t}\n\n\t// observe Javascript setters that effect pseudo-selectors\n\tif ('defineProperty' in Object && 'getOwnPropertyDescriptor' in Object && 'hasOwnProperty' in Object) {\n\t\ttry {\n\t\t\tfunction observeProperty(proto, property) {\n\t\t\t\t// eslint-disable-next-line no-prototype-builtins\n\t\t\t\tif (proto.hasOwnProperty(property)) {\n\t\t\t\t\tconst descriptor = Object.getOwnPropertyDescriptor(proto, property);\n\t\t\t\t\tif (descriptor && descriptor.configurable && 'set' in descriptor) {\n\t\t\t\t\t\tObject.defineProperty(proto, property, {\n\t\t\t\t\t\t\tconfigurable: descriptor.configurable,\n\t\t\t\t\t\t\tenumerable: descriptor.enumerable,\n\t\t\t\t\t\t\tget: function () {\n\t\t\t\t\t\t\t\treturn descriptor.get.apply(this, arguments);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tset: function () {\n\t\t\t\t\t\t\t\tdescriptor.set.apply(this, arguments);\n\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\ttransformObservedItemsThrottled();\n\t\t\t\t\t\t\t\t} catch (_) {\n\t\t\t\t\t\t\t\t\t// should never happen as there is an inner try/catch\n\t\t\t\t\t\t\t\t\t// but just in case\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ('HTMLElement' in self && HTMLElement.prototype) {\n\t\t\t\tobserveProperty(HTMLElement.prototype, 'disabled');\n\t\t\t}\n\n\t\t\t// Not all of these elements have all of these properties.\n\t\t\t// But the code above checks if they exist first.\n\t\t\t['checked', 'selected', 'readOnly', 'required'].forEach(function(property) {\n\t\t\t\t[\n\t\t\t\t\t'HTMLButtonElement',\n\t\t\t\t\t'HTMLFieldSetElement',\n\t\t\t\t\t'HTMLInputElement',\n\t\t\t\t\t'HTMLMeterElement',\n\t\t\t\t\t'HTMLOptGroupElement',\n\t\t\t\t\t'HTMLOptionElement',\n\t\t\t\t\t'HTMLOutputElement',\n\t\t\t\t\t'HTMLProgressElement',\n\t\t\t\t\t'HTMLSelectElement',\n\t\t\t\t\t'HTMLTextAreaElement',\n\t\t\t\t].forEach(function(elementName) {\n\t\t\t\t\tif (elementName in self && self[elementName].prototype) {\n\t\t\t\t\t\tobserveProperty(self[elementName].prototype, property);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tif (options.debug) {\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}\n\t}\n\n\tlet transformObservedItemsThrottledBusy = false;\n\tfunction transformObservedItemsThrottled() {\n\t\tif (transformObservedItemsThrottledBusy) {\n\t\t\tcancelAnimationFrame(transformObservedItemsThrottledBusy);\n\t\t}\n\n\t\ttransformObservedItemsThrottledBusy = requestAnimationFrame(function() {\n\t\t\ttransformObservedItems();\n\t\t});\n\t}\n\n\t// transform observed css rules\n\tfunction transformObservedItems() {\n\t\tobservedItems.forEach(function(item) {\n\t\t\tconst nodes = [];\n\n\t\t\tlet matches = [];\n\t\t\tif (item.selector) {\n\t\t\t\ttry {\n\t\t\t\t\tmatches = document.querySelectorAll(item.selector);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (options.debug) {\n\t\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t[].forEach.call(matches, function(element) {\n\t\t\t\t// memorize the node\n\t\t\t\tnodes.push(element);\n\n\t\t\t\t// set an attribute with an irregular attribute name\n\t\t\t\t// document.createAttribute() doesn't support special characters\n\t\t\t\tattributeElement.innerHTML = '<x ' + item.attributeName + '>';\n\n\t\t\t\telement.setAttributeNode(attributeElement.children[0].attributes[0].cloneNode());\n\n\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t});\n\n\t\t\t// remove the encoded attribute from all nodes that no longer match them\n\t\t\titem.nodes.forEach(function(node) {\n\t\t\t\tif (nodes.indexOf(node) === -1) {\n\t\t\t\t\tnode.removeAttribute(item.attributeName);\n\n\t\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// update the\n\t\t\titem.nodes = nodes;\n\t\t});\n\t}\n\n\t// remove any observed cssrules that no longer apply\n\tfunction cleanupObservedCssRules() {\n\t\t[].push.apply(\n\t\t\tobservedItems,\n\t\t\tobservedItems.splice(0).filter(function(item) {\n\t\t\t\treturn item.rule.parentStyleSheet &&\n\t\t\t\t\titem.rule.parentStyleSheet.ownerNode &&\n\t\t\t\t\tdocument.documentElement.contains(item.rule.parentStyleSheet.ownerNode);\n\t\t\t}),\n\t\t);\n\t}\n\n\t// walk a stylesheet to collect observed css rules\n\tfunction walkStyleSheet(styleSheet) {\n\t\ttry {\n\t\t\t// walk a css rule to collect observed css rules\n\t\t\tfor (let i = (styleSheet.cssRules.length - 1); i >= 0; i--) {\n\t\t\t\tlet rule = styleSheet.cssRules[i];\n\n\t\t\t\tif (rule.selectorText) {\n\t\t\t\t\trule.selectorText = rule.selectorText.replace(/\\.js-has-pseudo\\s/g, '');\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// decode the selector text in all browsers to:\n\t\t\t\t\t\tconst hasSelectors = extractEncodedSelectors(rule.selectorText.toString());\n\t\t\t\t\t\tif (hasSelectors.length === 0) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!options.mustPolyfill) {\n\t\t\t\t\t\t\tstyleSheet.deleteRule(i);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tfor (let j = 0; j < hasSelectors.length; j++) {\n\t\t\t\t\t\t\tconst hasSelector = hasSelectors[j];\n\t\t\t\t\t\t\tif (hasSelector) {\n\t\t\t\t\t\t\t\tobservedItems.push({\n\t\t\t\t\t\t\t\t\trule: rule,\n\t\t\t\t\t\t\t\t\tselector: hasSelector,\n\t\t\t\t\t\t\t\t\tattributeName: encodeCSS(hasSelector),\n\t\t\t\t\t\t\t\t\tnodes: [],\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tif (options.debug) {\n\t\t\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\twalkStyleSheet(rule);\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (options.debug) {\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/* eslint-disable */\n(function (global) {\n\ttry {\n\t\t// test for has support\n\t\tglobal.document.querySelector(':has(*, :does-not-exist, > *)');\n\n\t\tif (\n\t\t\t!global.document.querySelector(':has(:scope *)') &&\n\t\t\tCSS.supports('selector(:has(div))')\n\t\t) {\n\t\t\treturn;\n\t\t}\n\t} catch (_) { }\n\n\t// ELEMENT\n\t// polyfill Element#querySelector\n\tvar querySelectorWithHasElement = polyfill(global.Element.prototype.querySelector);\n\n\tglobal.Element.prototype.querySelector = function querySelector(selectors) {\n\t\treturn querySelectorWithHasElement.apply(this, arguments);\n\t};\n\n\t// polyfill Element#querySelectorAll\n\tvar querySelectorAllWithHasElement = polyfill(global.Element.prototype.querySelectorAll);\n\n\tglobal.Element.prototype.querySelectorAll = function querySelectorAll(selectors) {\n\t\treturn querySelectorAllWithHasElement.apply(this, arguments);\n\t};\n\n\t// polyfill Element#matches\n\tif (global.Element.prototype.matches) {\n\t\tvar matchesWithHasElement = polyfill(global.Element.prototype.matches);\n\n\t\tglobal.Element.prototype.matches = function matches(selectors) {\n\t\t\treturn matchesWithHasElement.apply(this, arguments);\n\t\t};\n\t}\n\n\t// polyfill Element#closest\n\tif (global.Element.prototype.closest) {\n\t\tvar closestWithHasElement = polyfill(global.Element.prototype.closest);\n\n\t\tglobal.Element.prototype.closest = function closest(selectors) {\n\t\t\treturn closestWithHasElement.apply(this, arguments);\n\t\t};\n\t}\n\n\t// DOCUMENT\n\tif ('Document' in global && 'prototype' in global.Document) {\n\t\t// polyfill Document#querySelector\n\t\tvar querySelectorWithHasDocument = polyfill(global.Document.prototype.querySelector);\n\n\t\tglobal.Document.prototype.querySelector = function querySelector(selectors) {\n\t\t\treturn querySelectorWithHasDocument.apply(this, arguments);\n\t\t};\n\n\t\t// polyfill Document#querySelectorAll\n\t\tvar querySelectorAllWithHasDocument = polyfill(global.Document.prototype.querySelectorAll);\n\n\t\tglobal.Document.prototype.querySelectorAll = function querySelectorAll(selectors) {\n\t\t\treturn querySelectorAllWithHasDocument.apply(this, arguments);\n\t\t};\n\n\t\t// polyfill Document#matches\n\t\tif (global.Document.prototype.matches) {\n\t\t\tvar matchesWithHasDocument = polyfill(global.Document.prototype.matches);\n\n\t\t\tglobal.Document.prototype.matches = function matches(selectors) {\n\t\t\t\treturn matchesWithHasDocument.apply(this, arguments);\n\t\t\t};\n\t\t}\n\n\t\t// polyfill Document#closest\n\t\tif (global.Document.prototype.closest) {\n\t\t\tvar closestWithHasDocument = polyfill(global.Document.prototype.closest);\n\n\t\t\tglobal.Document.prototype.closest = function closest(selectors) {\n\t\t\t\treturn closestWithHasDocument.apply(this, arguments);\n\t\t\t};\n\t\t}\n\t}\n\n\tfunction pseudoClassHasInnerQuery(query) {\n\t\tvar current = '';\n\t\tvar start = 0;\n\t\tvar depth = 0;\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar inHas = false;\n\n\t\tvar bracketed = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (quoted) {\n\t\t\t\tif (char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t}\n\n\t\t\t\tcurrent += char;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (current.toLowerCase() === ':has(' && !inHas) {\n\t\t\t\tinHas = true;\n\t\t\t\tstart = i;\n\t\t\t\tcurrent = '';\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ':':\n\t\t\t\t\tif (!inHas) {\n\t\t\t\t\t\tcurrent = '';\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '(':\n\t\t\t\t\tif (inHas) {\n\t\t\t\t\t\tdepth++;\n\t\t\t\t\t}\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase ')':\n\t\t\t\t\tif (inHas) {\n\t\t\t\t\t\tif (depth === 0) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tinnerQuery: current,\n\t\t\t\t\t\t\t\tstart: start,\n\t\t\t\t\t\t\t\tend: i-1\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tdepth--;\n\t\t\t\t\t}\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '[':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tbracketed++;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase \"]\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tif (bracketed > 0) {\n\t\t\t\t\t\tbracketed--\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tfunction replaceScopeWithAttr(query, attr) {\n\t\tvar parts = [];\n\t\tvar current = '';\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar bracketed = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (quoted) {\n\t\t\t\tif (char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t}\n\n\t\t\t\tcurrent += char;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (current.toLowerCase() === ':scope' && !bracketed && (/^[\\[\\.\\:\\\\\"\\s|+>~#&,)]/.test(char || ''))) {\n\t\t\t\tparts.push(current.slice(0, current.length - 6));\n\t\t\t\tparts.push('[' + attr + ']');\n\t\t\t\tcurrent = '';\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ':':\n\t\t\t\t\tparts.push(current);\n\t\t\t\t\tcurrent = '';\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '[':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tbracketed++;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase \"]\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tif (bracketed > 0) {\n\t\t\t\t\t\tbracketed--\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\tif (current.toLowerCase() === ':scope') {\n\t\t\tparts.push(current.slice(0, current.length - 6));\n\t\t\tparts.push('[' + attr + ']');\n\t\t\tcurrent = '';\n\t\t}\n\n\t\tif (parts.length === 0) {\n\t\t\treturn query;\n\t\t}\n\n\t\treturn parts.join('') + current;\n\t}\n\n\tfunction charIsNestedMarkMirror(char, mark) {\n\t\tif (mark === '(' && char === ')') {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (mark === '[' && char === ']') {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tfunction splitSelector(query) {\n\t\tvar selectors = [];\n\t\tvar current = '';\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar nestedMark = false;\n\t\tvar nestedDepth = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ',':\n\t\t\t\t\tif (quoted) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (nestedDepth > 0) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tselectors.push(current);\n\t\t\t\t\tcurrent = '';\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tif (quoted && char === quotedMark) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tquoted = false;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '(':\n\t\t\t\tcase ')':\n\t\t\t\tcase '[':\n\t\t\t\tcase ']':\n\t\t\t\t\tif (quoted) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (charIsNestedMarkMirror(char, nestedMark)) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tnestedDepth--;\n\n\t\t\t\t\t\tif (nestedDepth === 0) {\n\t\t\t\t\t\t\tnestedMark = false;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (char === nestedMark) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tnestedDepth++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tnestedDepth++;\n\t\t\t\t\tnestedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t}\n\t\t}\n\n\t\tselectors.push(current);\n\n\t\treturn selectors;\n\t}\n\n\tfunction replaceAllWithTempAttr(query, nested, callback) {\n\t\tvar inner = pseudoClassHasInnerQuery(query);\n\t\tif (!inner) {\n\t\t\treturn query;\n\t\t}\n\n\t\tif (nested) {\n\t\t\treturn false;\n\t\t}\n\n\t\tvar innerQuery = inner.innerQuery;\n\t\tvar attr = 'q-has' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\tvar innerReplacement = '[' + attr + ']';\n\n\t\tvar x = query;\n\n\t\tif (inner.innerQuery.toLowerCase().indexOf(':has(') > -1) {\n\t\t\tvar innerParts = splitSelector(inner.innerQuery);\n\t\t\tvar newInnerParts = [];\n\t\t\tfor (var i = 0; i < innerParts.length; i++) {\n\t\t\t\tvar innerPart = innerParts[i];\n\n\t\t\t\t// Nested has is not supported.\n\t\t\t\t// If a recursive/nested call returns \"false\" we throw\n\t\t\t\tvar innerPartReplaced = replaceAllWithTempAttr(innerPart, true, function () { });\n\t\t\t\tif (!innerPartReplaced) {\n\t\t\t\t\tthrow new Error(\"Nested :has() is not supported\")\n\t\t\t\t} else {\n\t\t\t\t\tnewInnerParts.push(innerPart);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar _prefix = x.substring(0, inner.start - 5); // ':has('.length === 5\n\t\t\tvar _suffix = x.substring(inner.end + 2); // ')'.length === 1\n\n\t\t\treturn _prefix + newInnerParts.join(', ') + _suffix;\n\t\t}\n\n\t\tvar _prefix = x.substring(0, inner.start - 5); // ':has('.length === 5\n\t\tvar _suffix = x.substring(inner.end + 2); // ')'.length === 1\n\n\t\tx = _prefix + innerReplacement + _suffix;\n\n\t\tcallback(innerQuery, attr);\n\t\tif (x.toLowerCase().indexOf(':has(') > -1) {\n\t\t\tvar y = replaceAllWithTempAttr(x, false, callback);\n\t\t\tif (y) {\n\t\t\t\treturn y;\n\t\t\t}\n\t\t}\n\n\t\treturn x;\n\t}\n\n\tfunction walkNode(rootNode, callback) {\n\t\tif (('setAttribute' in (rootNode)) && ('querySelector' in (rootNode))) {\n\t\t\tcallback(rootNode);\n\t\t}\n\n\t\tif (rootNode.hasChildNodes()) {\n\t\t\tvar nodes = rootNode.childNodes;\n\t\t\tfor (var i = 0; i < nodes.length; ++i) {\n\t\t\t\twalkNode(nodes[i], callback);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction polyfill(qsa) {\n\t\treturn function (selectors) {\n\t\t\tif (!selectors) {\n\t\t\t\treturn qsa.apply(this, arguments);\n\t\t\t}\n\n\t\t\tvar selectorsString = String(selectors);\n\t\t\tif (!selectorsString || (selectorsString.toLowerCase().indexOf(':has(') === -1) || !pseudoClassHasInnerQuery(selectorsString)) {\n\t\t\t\treturn qsa.apply(this, arguments);\n\t\t\t}\n\n\t\t\tvar rootNode;\n\t\t\tif ('getRootNode' in this) {\n\t\t\t\trootNode = this.getRootNode();\n\t\t\t} else {\n\t\t\t\tvar r = this;\n\t\t\t\twhile (r) {\n\t\t\t\t\trootNode = r;\n\t\t\t\t\tr = r.parentNode;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar _focus = this;\n\t\t\tif (_focus === global.document) {\n\t\t\t\t_focus = global.document.documentElement;\n\t\t\t}\n\n\t\t\tvar scopeAttr = 'q-has-scope' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t_focus.setAttribute(scopeAttr, '');\n\n\t\t\ttry {\n\t\t\t\tselectorsString = replaceScopeWithAttr(selectorsString, scopeAttr);\n\n\t\t\t\tvar attrs = [scopeAttr];\n\t\t\t\tvar newQuery = replaceAllWithTempAttr(selectorsString, false, function (inner, attr) {\n\t\t\t\t\tattrs.push(attr);\n\n\t\t\t\t\tvar selectorParts = splitSelector(inner);\n\t\t\t\t\tfor (var x = 0; x < selectorParts.length; x++) {\n\t\t\t\t\t\tvar selectorPart = selectorParts[x].trim();\n\t\t\t\t\t\tvar absoluteSelectorPart = selectorPart;\n\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tselectorPart[0] === '>' ||\n\t\t\t\t\t\t\tselectorPart[0] === '+' ||\n\t\t\t\t\t\t\tselectorPart[0] === '~'\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tabsoluteSelectorPart = selectorPart.slice(1).trim();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tabsoluteSelectorPart = ':scope ' + selectorPart;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\twalkNode(rootNode, function (node) {\n\t\t\t\t\t\t\tif (!(node.querySelector(absoluteSelectorPart))) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tswitch (selectorPart[0]) {\n\t\t\t\t\t\t\t\tcase '~':\n\t\t\t\t\t\t\t\tcase '+':\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tvar siblings = node.childNodes;\n\t\t\t\t\t\t\t\t\t\tfor (var i = 0; i < siblings.length; i++) {\n\t\t\t\t\t\t\t\t\t\t\tvar sibling = siblings[i];\n\t\t\t\t\t\t\t\t\t\t\tif (!('setAttribute' in sibling)) {\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tvar idAttr = 'q-has-id' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t\t\t\t\t\t\t\t\tsibling.setAttribute(idAttr, '');\n\n\t\t\t\t\t\t\t\t\t\t\tif (node.querySelector(':scope [' + idAttr + ']' + ' ' + selectorPart)) {\n\t\t\t\t\t\t\t\t\t\t\t\tsibling.setAttribute(attr, '');\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tsibling.removeAttribute(idAttr);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tcase '>':\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tvar idAttr = 'q-has-id' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t\t\t\t\t\t\t\tnode.setAttribute(idAttr, '');\n\n\t\t\t\t\t\t\t\t\t\tif (node.querySelector(':scope[' + idAttr + ']' + ' ' + selectorPart)) {\n\t\t\t\t\t\t\t\t\t\t\tnode.setAttribute(attr, '');\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tnode.removeAttribute(idAttr);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\tnode.setAttribute(attr, '');\n\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\targuments[0] = newQuery;\n\n\t\t\t\t// results of the qsa\n\t\t\t\tvar elementOrNodeList = qsa.apply(this, arguments);\n\n\t\t\t\t_focus.removeAttribute(scopeAttr);\n\n\t\t\t\tif (attrs.length > 0) {\n\t\t\t\t\t// remove the fallback attribute\n\t\t\t\t\tvar attrsForQuery = [];\n\t\t\t\t\tfor (var j = 0; j < attrs.length; j++) {\n\t\t\t\t\t\tattrsForQuery.push('[' + attrs[j] + ']');\n\t\t\t\t\t}\n\n\t\t\t\t\tvar elements = global.document.querySelectorAll(attrsForQuery.join(','));\n\t\t\t\t\tfor (var k = 0; k < elements.length; k++) {\n\t\t\t\t\t\tvar element = elements[k];\n\t\t\t\t\t\tfor (var l = 0; l < attrs.length; l++) {\n\t\t\t\t\t\t\telement.removeAttribute(attrs[l]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// return the results of the qsa\n\t\t\t\treturn elementOrNodeList;\n\t\t\t} catch (err) {\n\t\t\t\t_focus.removeAttribute(scopeAttr);\n\n\t\t\t\tif (attrs.length > 0) {\n\t\t\t\t\t// remove the fallback attribute\n\t\t\t\t\tvar attrsForQuery = [];\n\t\t\t\t\tfor (var j = 0; j < attrs.length; j++) {\n\t\t\t\t\t\tattrsForQuery.push('[' + attrs[j] + ']');\n\t\t\t\t\t}\n\n\t\t\t\t\tvar elements = global.document.querySelectorAll(attrsForQuery.join(','));\n\t\t\t\t\tfor (var k = 0; k < elements.length; k++) {\n\t\t\t\t\t\tvar element = elements[k];\n\t\t\t\t\t\tfor (var l = 0; l < attrs.length; l++) {\n\t\t\t\t\t\t\telement.removeAttribute(attrs[l]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar errorMessage = '';\n\t\t\t\ttry {\n\t\t\t\t\tqsa.apply(this, [':core-web-does-not-exist']);\n\t\t\t\t} catch (dummyError) {\n\t\t\t\t\terrorMessage = dummyError.message;\n\t\t\t\t\tif (errorMessage) {\n\t\t\t\t\t\terrorMessage = errorMessage.replace(':core-web-does-not-exist', selectorsString);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (!errorMessage) {\n\t\t\t\t\terrorMessage = \"Failed to execute 'querySelector' on 'Document': '\" + selectorsString + \"' is not a valid selector.\";\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new DOMException(errorMessage);\n\t\t\t\t} catch (_) {\n\t\t\t\t\tthrow new Error(errorMessage);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n})(self);\n"], "names": ["decodeCSS", "value", "slice", "values", "split", "result", "i", "length", "String", "fromCharCode", "parseInt", "extractEncodedSelectors", "candidate", "quotedMark", "out", "depth", "quoted", "containsUnescapedUnquotedHasAtDepth1", "char", "decoded", "push", "unique", "indexOf", "encodeCSS", "hex", "charCodeAt", "toString", "hasNativeSupport", "self", "CSS", "supports", "_", "cssHasPseudo", "document", "options", "hover", "debug", "observedAttributes", "forcePolyfill", "mustPolyfill", "Array", "isArray", "filter", "x", "concat", "observedItems", "attributeElement", "createElement", "for<PERSON>ach", "call", "styleSheets", "walkStyleSheet", "transformObservedItemsThrottled", "mutationObserver", "MutationObserver", "mutationsList", "mutation", "addedNodes", "node", "nodeType", "sheet", "addEventListener", "e", "target", "apply", "splice", "item", "rule", "parentStyleSheet", "ownerNode", "documentElement", "contains", "observe", "childList", "subtree", "attributes", "attributeFilter", "Object", "observeProperty", "proto", "property", "hasOwnProperty", "descriptor", "getOwnPropertyDescriptor", "configurable", "defineProperty", "enumerable", "get", "this", "arguments", "set", "HTMLElement", "prototype", "elementName", "console", "error", "transformObservedItemsThrottledBusy", "cancelAnimationFrame", "requestAnimationFrame", "nodes", "matches", "selector", "querySelectorAll", "element", "innerHTML", "attributeName", "setAttributeNode", "children", "cloneNode", "style", "zoom", "removeAttribute", "styleSheet", "cssRules", "selectorText", "replace", "hasSelectors", "deleteRule", "j", "hasSelector", "global", "querySelector", "querySelectorWithHasElement", "polyfill", "Element", "selectors", "querySelectorAllWithHasElement", "matchesWithHasElement", "closest", "closestWithHasElement", "Document", "querySelectorWithHasDocument", "querySelectorAllWithHasDocument", "matchesWithHasDocument", "closestWithHasDocument", "pseudoClassHasInnerQuery", "query", "current", "start", "escaped", "inHas", "toLowerCase", "innerQuery", "end", "replaceScopeWithAttr", "attr", "parts", "bracketed", "test", "join", "charIsNestedMarkMirror", "mark", "splitSelector", "nestedMark", "nested<PERSON><PERSON><PERSON>", "replaceAllWithTempAttr", "nested", "callback", "inner", "Math", "floor", "random", "innerReplacement", "innerParts", "newInnerParts", "innerPart", "Error", "_prefix", "substring", "_suffix", "y", "walkNode", "rootNode", "hasChildNodes", "childNodes", "qsa", "selectorsString", "getRootNode", "r", "parentNode", "_focus", "scopeAttr", "setAttribute", "attrs", "<PERSON><PERSON><PERSON><PERSON>", "selectorParts", "selector<PERSON><PERSON>", "trim", "absoluteSelectorPart", "siblings", "sibling", "idAttr", "elementOrNodeList", "attrsFor<PERSON><PERSON>y", "elements", "k", "l", "err", "errorMessage", "dummy<PERSON><PERSON>r", "message", "DOMException"], "mappings": "AAEe,SAASA,UAAUC,GACjC,GAA2B,kBAAvBA,EAAMC,MAAM,EAAG,IAClB,MAAO,GAOR,IAHA,IAAIC,GADJF,EAAQA,EAAMC,MAAM,KACDE,MAAM,KAErBC,EAAS,GACJC,EAAI,EAAGA,EAAIH,EAAOI,OAAQD,IAClCD,GAAUG,OAAOC,aAAaC,SAASP,EAAOG,GAAI,KAGnD,OAAOD,CACR,CCbe,SAASM,wBAAwBV,GAY/C,IAXA,IAGIW,EAGAC,EANAC,EAAM,GAENC,EAAQ,EAGRC,GAAS,EAGTC,GAAuC,EAGlCX,EAAI,EAAGA,EAAIL,EAAMM,OAAQD,IAAK,CACtC,IAAMY,EAAOjB,EAAMK,GAEnB,OAAQY,GACP,IAAK,IACJ,GAAIF,EAAQ,CACXJ,GAAaM,EACb,QACD,CAEc,IAAVH,EACHH,EAAY,GAEZA,GAAaM,EAGdH,IACA,SACD,IAAK,IACJ,GAAIC,EAAQ,CACXJ,GAAaM,EACb,QACD,CAIC,GAAc,MADdH,EACiB,CAChB,IAAMI,EAAUnB,UAAUY,GACtBK,GACHH,EAAIM,KAAKD,EAEX,MACCP,GAAaM,EAIf,SACD,IAAK,KACJN,GAAaX,EAAMK,GACnBM,GAAaX,EAAMK,EAAE,GACrBA,IACA,SAED,IAAK,IACL,IAAK,IACJ,GAAIU,GAAUE,IAASL,EAAY,CAClCG,GAAS,EACT,QACD,CAAO,GAAIA,EAAQ,CAClBJ,GAAaM,EACb,QACD,CAEAF,GAAS,EACTH,EAAaK,EACb,SAED,QACmB,KAAdN,GAA8B,IAAVG,GAA2C,kBAA3Bd,EAAMC,MAAMI,EAAGA,EAAI,MAC1DW,GAAuC,GAGxCL,GAAaM,EACb,SAEH,CAGA,IADA,IAAMG,EAAS,GACNf,EAAI,EAAGA,EAAIQ,EAAIP,OAAQD,KACA,IAA3Be,EAAOC,QAAQR,EAAIR,KACtBe,EAAOD,KAAKN,EAAIR,IAIlB,OAAOe,CACR,CCxFe,SAASE,UAAUtB,GACjC,GAAc,KAAVA,EACH,MAAO,GAKR,IAFA,IAAIuB,EACAnB,EAAS,GACJC,EAAI,EAAGA,EAAIL,EAAMM,OAAQD,IACjCkB,EAAMvB,EAAMwB,WAAWnB,GAAGoB,SAAS,IAElCrB,GADS,IAANC,EACOkB,EAEA,IAAMA,EAIlB,MAAO,gBAAkBnB,CAC1B,CCfA,SAASsB,mBACR,IACC,KAAM,QAASC,SAAW,aAAcA,KAAKC,OAASD,KAAKC,IAAIC,SAAS,uBACvE,OAAO,CAGT,CAAE,MAAOC,GACR,OAAO,CACR,CAEA,OAAO,CACR,CAEe,SAASC,aAAaC,EAAUC,GAGxCA,IACJA,EAAU,CAAA,IAGXA,EAAU,CACTC,QAAUD,EAAQC,QAAU,EAC5BC,QAAUF,EAAQE,QAAU,EAC5BC,mBAAoBH,EAAQG,oBAAsB,GAClDC,gBAAkBJ,EAAQI,gBAAkB,IAGrCC,aAAeL,EAAQI,gBAAkBX,mBAE5Ca,MAAMC,QAAQP,EAAQG,sBAC1BH,EAAQG,mBAAqB,IAG9BH,EAAQG,mBAAqBH,EAAQG,mBAAmBK,OAAO,SAASC,GACvE,MAAqB,iBAANA,CAChB,GAIAT,EAAQG,mBAAqBH,EAAQG,mBAAmBO,OAAO,CAAC,SAAU,iBAAkB,YAAa,SAAU,QAAS,QAAS,MAAO,QAAS,iBAAkB,eAAgB,YAAa,WAAY,WAAY,UAAW,YAAa,UAAW,UAAW,OAAQ,QAAS,OAAQ,WAAY,OAAQ,UAAW,UAAW,kBAAmB,cAAe,WAAY,SAAU,cAAe,MAAO,OAAQ,WAAY,WAAY,UAAW,QAAS,MAAO,UAAW,WAAY,WAAY,YAAa,UAAW,eAAgB,MAAO,OAAQ,aAAc,cAAe,aAAc,iBAAkB,aAAc,UAAW,SAAU,OAAQ,OAAQ,WAAY,aAAc,OAAQ,KAAM,aAAc,YAAa,gBAAiB,YAAa,QAAS,WAAY,UAAW,OAAQ,QAAS,OAAQ,WAAY,OAAQ,OAAQ,MAAO,WAAY,MAAO,YAAa,YAAa,QAAS,SAAU,MAAO,WAAY,QAAS,OAAQ,aAAc,OAAQ,UAAW,UAAW,OAAQ,cAAe,SAAU,UAAW,aAAc,WAAY,iBAAkB,MAAO,WAAY,WAAY,OAAQ,UAAW,UAAW,QAAS,SAAU,WAAY,QAAS,OAAQ,QAAS,OAAQ,OAAQ,aAAc,MAAO,SAAU,UAAW,SAAU,QAAS,OAAQ,UAAW,WAAY,SAAU,QAAS,YAAa,OAAQ,SAAU,QAAS,QAAS,SAG52C,IAAMC,EAAgB,GAGhBC,EAAmBb,EAASc,cAAc,KAIhD,GADA,GAAGC,QAAQC,KAAKhB,EAASiB,YAAaC,gBACjCjB,EAAQK,aAAb,CASA,GAHAa,kCAGI,qBAAsBxB,KAAM,CAC/B,IAAMyB,EAAmB,IAAIC,iBAAiB,SAASC,GACtDA,EAAcP,QAAQ,SAASQ,GAC9B,GAAGR,QAAQC,KAAKO,EAASC,YAAc,GAAI,SAASC,GAE7B,IAAlBA,EAAKC,WAILD,EAAKE,MACRT,eAAeO,EAAKE,OAIrBF,EAAKG,iBAAiB,OAAQ,SAAUC,GACnCA,EAAEC,QAAUD,EAAEC,OAAOH,OACxBT,eAAeW,EAAEC,OAAOH,MAE1B,GACD,GAoJF,GAAGxC,KAAK4C,MACPnB,EACAA,EAAcoB,OAAO,GAAGvB,OAAO,SAASwB,GACvC,OAAOA,EAAKC,KAAKC,kBAChBF,EAAKC,KAAKC,iBAAiBC,WAC3BpC,EAASqC,gBAAgBC,SAASL,EAAKC,KAAKC,iBAAiBC,UAC/D,IAtJCjB,iCACD,EACD,GAEAC,EAAiBmB,QAAQvC,EAAU,CAAEwC,WAAW,EAAMC,SAAS,EAAMC,YAAY,EAAMC,gBAAiB1C,EAAQG,oBACjH,CAmBA,GAhBAJ,EAAS4B,iBAAiB,QAAST,iCAAiC,GACpEnB,EAAS4B,iBAAiB,OAAQT,iCAAiC,GACnEnB,EAAS4B,iBAAiB,QAAST,iCACnCnB,EAAS4B,iBAAiB,SAAUT,iCAAiC,GAEjElB,EAAQC,QACP,mBAAoBF,GACvBA,EAAS4B,iBAAiB,eAAgBT,iCAAiC,GAC3EnB,EAAS4B,iBAAiB,eAAgBT,iCAAiC,KAE3EnB,EAAS4B,iBAAiB,YAAaT,iCAAiC,GACxEnB,EAAS4B,iBAAiB,WAAYT,iCAAiC,KAKrE,mBAAoByB,QAAU,6BAA8BA,QAAU,mBAAoBA,OAC7F,IAAI,IACMC,EAAT,SAASA,gBAAgBC,EAAOC,GAE/B,GAAID,EAAME,eAAeD,GAAW,CACnC,IAAME,EAAaL,OAAOM,yBAAyBJ,EAAOC,GACtDE,GAAcA,EAAWE,cAAgB,QAASF,GACrDL,OAAOQ,eAAeN,EAAOC,EAAU,CACtCI,aAAcF,EAAWE,aACzBE,WAAYJ,EAAWI,WACvBC,IAAK,SAALA,MACC,OAAOL,EAAWK,IAAIvB,MAAMwB,KAAMC,UACnC,EACAC,IAAK,SAALA,MACCR,EAAWQ,IAAI1B,MAAMwB,KAAMC,WAE3B,IACCrC,iCACD,CAAE,MAAOrB,GAER,CAEF,GAGH,CACD,EAEI,gBAAiBH,MAAQ+D,YAAYC,WACxCd,EAAgBa,YAAYC,UAAW,YAKxC,CAAC,UAAW,WAAY,WAAY,YAAY5C,QAAQ,SAASgC,GAChE,CACC,oBACA,sBACA,mBACA,mBACA,sBACA,oBACA,oBACA,sBACA,oBACA,uBACChC,QAAQ,SAAS6C,GACdA,KAAejE,MAAQA,KAAKiE,GAAaD,WAC5Cd,EAAgBlD,KAAKiE,GAAaD,UAAWZ,EAE/C,EACD,EACD,CAAE,MAAOlB,GACJ5B,EAAQE,OAEX0D,QAAQC,MAAMjC,EAEhB,CAGD,IAAIkC,GAAsC,CAhH1C,CAiHA,SAAS5C,kCACJ4C,GACHC,qBAAqBD,GAGtBA,EAAsCE,sBAAsB,WAO5DrD,EAAcG,QAAQ,SAASkB,GAC9B,IAAMiC,EAAQ,GAEVC,EAAU,GACd,GAAIlC,EAAKmC,SACR,IACCD,EAAUnE,EAASqE,iBAAiBpC,EAAKmC,SAC1C,CAAE,MAAOvC,GAKR,YAJI5B,EAAQE,OAEX0D,QAAQC,MAAMjC,GAGhB,CAGD,GAAGd,QAAQC,KAAKmD,EAAS,SAASG,GAEjCJ,EAAM/E,KAAKmF,GAIXzD,EAAiB0D,UAAY,MAAQtC,EAAKuC,cAAgB,IAE1DF,EAAQG,iBAAiB5D,EAAiB6D,SAAS,GAAGhC,WAAW,GAAGiC,aAGpE3E,EAASqC,gBAAgBuC,MAAMC,KAAO,EAAG7E,EAASqC,gBAAgBuC,MAAMC,KAAO,IAChF,GAGA5C,EAAKiC,MAAMnD,QAAQ,SAASU,IACC,IAAxByC,EAAM7E,QAAQoC,KACjBA,EAAKqD,gBAAgB7C,EAAKuC,eAG1BxE,EAASqC,gBAAgBuC,MAAMC,KAAO,EAAG7E,EAASqC,gBAAgBuC,MAAMC,KAAO,KAEjF,GAGA5C,EAAKiC,MAAQA,CACd,EA/CA,EACD,CA8DA,SAAShD,eAAe6D,GACvB,IAEC,IAAK,IAAI1G,EAAK0G,EAAWC,SAAS1G,OAAS,EAAID,GAAK,EAAGA,IAAK,CAC3D,IAAI6D,EAAO6C,EAAWC,SAAS3G,GAE/B,GAAI6D,EAAK+C,aAAc,CACtB/C,EAAK+C,aAAe/C,EAAK+C,aAAaC,QAAQ,qBAAsB,IAEpE,IAEC,IAAMC,EAAezG,wBAAwBwD,EAAK+C,aAAaxF,YAC/D,GAA4B,IAAxB0F,EAAa7G,OAChB,SAGD,IAAK2B,EAAQK,aAAc,CAC1ByE,EAAWK,WAAW/G,GACtB,QACD,CAEA,IAAK,IAAIgH,EAAI,EAAGA,EAAIF,EAAa7G,OAAQ+G,IAAK,CAC7C,IAAMC,EAAcH,EAAaE,GAC7BC,GACH1E,EAAczB,KAAK,CAClB+C,KAAMA,EACNkC,SAAUkB,EACVd,cAAelF,UAAUgG,GACzBpB,MAAO,IAGV,CACD,CAAE,MAAOrC,GACJ5B,EAAQE,OAEX0D,QAAQC,MAAMjC,EAEhB,CACD,MACCX,eAAegB,EAEjB,CACD,CAAE,MAAOL,GACJ5B,EAAQE,OAEX0D,QAAQC,MAAMjC,EAEhB,CACD,CACD,EChSA,SAAW0D,GACV,IAIC,GAFAA,EAAOvF,SAASwF,cAAc,kCAG5BD,EAAOvF,SAASwF,cAAc,mBAC/B5F,IAAIC,SAAS,uBAEb,MAEF,CAAE,MAAOC,GAAK,CAId,IAAI2F,EAA8BC,SAASH,EAAOI,QAAQhC,UAAU6B,eAEpED,EAAOI,QAAQhC,UAAU6B,cAAgB,SAASA,cAAcI,GAC/D,OAAOH,EAA4B1D,MAAMwB,KAAMC,UAChD,EAGA,IAAIqC,EAAiCH,SAASH,EAAOI,QAAQhC,UAAUU,kBAOvE,GALAkB,EAAOI,QAAQhC,UAAUU,iBAAmB,SAASA,iBAAiBuB,GACrE,OAAOC,EAA+B9D,MAAMwB,KAAMC,UACnD,EAGI+B,EAAOI,QAAQhC,UAAUQ,QAAS,CACrC,IAAI2B,EAAwBJ,SAASH,EAAOI,QAAQhC,UAAUQ,SAE9DoB,EAAOI,QAAQhC,UAAUQ,QAAU,SAASA,QAAQyB,GACnD,OAAOE,EAAsB/D,MAAMwB,KAAMC,UAC1C,CACD,CAGA,GAAI+B,EAAOI,QAAQhC,UAAUoC,QAAS,CACrC,IAAIC,EAAwBN,SAASH,EAAOI,QAAQhC,UAAUoC,SAE9DR,EAAOI,QAAQhC,UAAUoC,QAAU,SAASA,QAAQH,GACnD,OAAOI,EAAsBjE,MAAMwB,KAAMC,UAC1C,CACD,CAGA,GAAI,aAAc+B,GAAU,cAAeA,EAAOU,SAAU,CAE3D,IAAIC,EAA+BR,SAASH,EAAOU,SAAStC,UAAU6B,eAEtED,EAAOU,SAAStC,UAAU6B,cAAgB,SAASA,cAAcI,GAChE,OAAOM,EAA6BnE,MAAMwB,KAAMC,UACjD,EAGA,IAAI2C,EAAkCT,SAASH,EAAOU,SAAStC,UAAUU,kBAOzE,GALAkB,EAAOU,SAAStC,UAAUU,iBAAmB,SAASA,iBAAiBuB,GACtE,OAAOO,EAAgCpE,MAAMwB,KAAMC,UACpD,EAGI+B,EAAOU,SAAStC,UAAUQ,QAAS,CACtC,IAAIiC,EAAyBV,SAASH,EAAOU,SAAStC,UAAUQ,SAEhEoB,EAAOU,SAAStC,UAAUQ,QAAU,SAASA,QAAQyB,GACpD,OAAOQ,EAAuBrE,MAAMwB,KAAMC,UAC3C,CACD,CAGA,GAAI+B,EAAOU,SAAStC,UAAUoC,QAAS,CACtC,IAAIM,EAAyBX,SAASH,EAAOU,SAAStC,UAAUoC,SAEhER,EAAOU,SAAStC,UAAUoC,QAAU,SAASA,QAAQH,GACpD,OAAOS,EAAuBtE,MAAMwB,KAAMC,UAC3C,CACD,CACD,CAEA,SAAS8C,yBAAyBC,GAcjC,IAbA,IAAIC,EAAU,GACVC,EAAQ,EACR3H,EAAQ,EAER4H,GAAU,EAEV3H,GAAS,EACTH,GAAa,EAEb+H,GAAQ,EAIHtI,EAAI,EAAGA,EAAIkI,EAAMjI,OAAQD,IAAK,CACtC,IAAIY,EAAOsH,EAAMlI,GAEjB,GAAIqI,EACHF,GAAWvH,EACXyH,GAAU,OAIX,GAAI3H,EACCE,IAASL,IACZG,GAAS,GAGVyH,GAAWvH,OAUZ,OAN8B,UAA1BuH,EAAQI,eAA8BD,IACzCA,GAAQ,EACRF,EAAQpI,EACRmI,EAAU,IAGHvH,GACP,IAAK,IACC0H,IACJH,EAAU,IAGXA,GAAWvH,EACX,SAED,IAAK,IACA0H,GACH7H,IAED0H,GAAWvH,EACX,SAED,IAAK,IACJ,GAAI0H,EAAO,CACV,GAAc,IAAV7H,EACH,MAAO,CACN+H,WAAYL,EACZC,MAAOA,EACPK,IAAKzI,EAAE,GAITS,GACD,CACA0H,GAAWvH,EACX,SAED,IAAK,KACJuH,GAAWvH,EACXyH,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJF,GAAWvH,EACXF,GAAS,EACTH,EAAaK,EACb,SAeD,QACCuH,GAAWvH,EACX,SAEH,CAEA,OAAO,CACR,CAEA,SAAS8H,qBAAqBR,EAAOS,GAWpC,IAVA,IAAIC,EAAQ,GACRT,EAAU,GAEVE,GAAU,EAEV3H,GAAS,EACTH,GAAa,EAEbsI,EAAY,EAEP7I,EAAI,EAAGA,EAAIkI,EAAMjI,OAAQD,IAAK,CACtC,IAAIY,EAAOsH,EAAMlI,GAEjB,GAAIqI,EACHF,GAAWvH,EACXyH,GAAU,OAIX,GAAI3H,EACCE,IAASL,IACZG,GAAS,GAGVyH,GAAWvH,OAUZ,OAN8B,WAA1BuH,EAAQI,gBAA+BM,GAAc,yBAAyBC,KAAKlI,GAAQ,MAC9FgI,EAAM9H,KAAKqH,EAAQvI,MAAM,EAAGuI,EAAQlI,OAAS,IAC7C2I,EAAM9H,KAAK,IAAM6H,EAAO,KACxBR,EAAU,IAGHvH,GACP,IAAK,IACJgI,EAAM9H,KAAKqH,GACXA,EAAU,GACVA,GAAWvH,EACX,SAED,IAAK,KACJuH,GAAWvH,EACXyH,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJF,GAAWvH,EACXF,GAAS,EACTH,EAAaK,EACb,SAED,IAAK,IACJuH,GAAWvH,EACXiI,IACA,SAED,IAAK,IACJV,GAAWvH,EACPiI,EAAY,GACfA,IAGD,SAED,QACCV,GAAWvH,EACX,SAEH,CAQA,MAN8B,WAA1BuH,EAAQI,gBACXK,EAAM9H,KAAKqH,EAAQvI,MAAM,EAAGuI,EAAQlI,OAAS,IAC7C2I,EAAM9H,KAAK,IAAM6H,EAAO,KACxBR,EAAU,IAGU,IAAjBS,EAAM3I,OACFiI,EAGDU,EAAMG,KAAK,IAAMZ,CACzB,CAEA,SAASa,uBAAuBpI,EAAMqI,GACrC,MAAa,MAATA,GAAyB,MAATrI,GAIP,MAATqI,GAAyB,MAATrI,CAKrB,CAEA,SAASsI,cAAchB,GAYtB,IAXA,IAAIX,EAAY,GACZY,EAAU,GAEVE,GAAU,EAEV3H,GAAS,EACTH,GAAa,EAEb4I,GAAa,EACbC,EAAc,EAETpJ,EAAI,EAAGA,EAAIkI,EAAMjI,OAAQD,IAAK,CACtC,IAAIY,EAAOsH,EAAMlI,GAEjB,GAAIqI,EACHF,GAAWvH,EACXyH,GAAU,OAIX,OAAQzH,GACP,IAAK,IACJ,GAAIF,EAAQ,CACXyH,GAAWvH,EACX,QACD,CAEA,GAAIwI,EAAc,EAAG,CACpBjB,GAAWvH,EACX,QACD,CAEA2G,EAAUzG,KAAKqH,GACfA,EAAU,GACV,SAED,IAAK,KACJA,GAAWvH,EACXyH,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJ,GAAI3H,GAAUE,IAASL,EAAY,CAClC4H,GAAWvH,EACXF,GAAS,EACT,QACD,CAEAyH,GAAWvH,EACXF,GAAS,EACTH,EAAaK,EACb,SAED,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACJ,GAAIF,EAAQ,CACXyH,GAAWvH,EACX,QACD,CAEA,GAAIoI,uBAAuBpI,EAAMuI,GAAa,CAC7ChB,GAAWvH,EAGS,MAFpBwI,IAGCD,GAAa,GAGd,QACD,CAEA,GAAIvI,IAASuI,EAAY,CACxBhB,GAAWvH,EACXwI,IACA,QACD,CAEAjB,GAAWvH,EACXwI,IACAD,EAAavI,EACb,SAED,QACCuH,GAAWvH,EACX,SAGH,CAIA,OAFA2G,EAAUzG,KAAKqH,GAERZ,CACR,CAEA,SAAS8B,uBAAuBnB,EAAOoB,EAAQC,GAC9C,IAAIC,EAAQvB,yBAAyBC,GACrC,IAAKsB,EACJ,OAAOtB,EAGR,GAAIoB,EACH,OAAO,EAGR,IAAId,EAAagB,EAAMhB,WACnBG,EAAO,SAAWc,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACxDC,EAAmB,IAAMjB,EAAO,IAEhCtG,EAAI6F,EAER,GAAIsB,EAAMhB,WAAWD,cAAcvH,QAAQ,YAAe,CAGzD,IAFA,IAAI6I,EAAaX,cAAcM,EAAMhB,YACjCsB,EAAgB,GACX9J,EAAI,EAAGA,EAAI6J,EAAW5J,OAAQD,IAAK,CAC3C,IAAI+J,EAAYF,EAAW7J,GAK3B,IADwBqJ,uBAAuBU,GAAW,EAAM,WAAc,GAE7E,MAAM,IAAIC,MAAM,kCAEhBF,EAAchJ,KAAKiJ,EAErB,CAEA,IAAIE,EAAU5H,EAAE6H,UAAU,EAAGV,EAAMpB,MAAQ,GACvC+B,EAAU9H,EAAE6H,UAAUV,EAAMf,IAAM,GAEtC,OAAOwB,EAAUH,EAAcf,KAAK,MAAQoB,CAC7C,CAEIF,EAAU5H,EAAE6H,UAAU,EAAGV,EAAMpB,MAAQ,GACvC+B,EAAU9H,EAAE6H,UAAUV,EAAMf,IAAM,GAKtC,GAHApG,EAAI4H,EAAUL,EAAmBO,EAEjCZ,EAASf,EAAYG,GACjBtG,EAAEkG,cAAcvH,QAAQ,UAAW,EAAI,CAC1C,IAAIoJ,EAAIf,uBAAuBhH,GAAG,EAAOkH,GACzC,GAAIa,EACH,OAAOA,CAET,CAEA,OAAO/H,CACR,CAEA,SAASgI,SAASC,EAAUf,GAK3B,GAJK,iBAAmBe,GAAe,kBAAoBA,GAC1Df,EAASe,GAGNA,EAASC,gBAEZ,IADA,IAAI1E,EAAQyE,EAASE,WACZxK,EAAI,EAAGA,EAAI6F,EAAM5F,SAAUD,EACnCqK,SAASxE,EAAM7F,GAAIuJ,EAGtB,CAEA,SAASlC,SAASoD,GACjB,OAAO,SAAUlD,GAChB,IAAKA,EACJ,OAAOkD,EAAI/G,MAAMwB,KAAMC,WAGxB,IAKImF,EALAI,EAAkBxK,OAAOqH,GAC7B,IAAKmD,IAAuE,IAAnDA,EAAgBnC,cAAcvH,QAAQ,WAAqBiH,yBAAyByC,GAC5G,OAAOD,EAAI/G,MAAMwB,KAAMC,WAIxB,GAAI,gBAAiBD,KACpBoF,EAAWpF,KAAKyF,mBAGhB,IADA,IAAIC,EAAI1F,KACD0F,GACNN,EAAWM,EACXA,EAAIA,EAAEC,WAIR,IAAIC,EAAS5F,KACT4F,IAAW5D,EAAOvF,WACrBmJ,EAAS5D,EAAOvF,SAASqC,iBAG1B,IAAI+G,EAAY,eAAiBtB,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACvEmB,EAAOE,aAAaD,EAAW,IAE/B,IACCL,EAAkBhC,qBAAqBgC,EAAiBK,GAExD,IAAIE,EAAQ,CAACF,GACTG,EAAW7B,uBAAuBqB,GAAiB,EAAO,SAAUlB,EAAOb,GAC9EsC,EAAMnK,KAAK6H,GAGX,IADA,IAAIwC,EAAgBjC,cAAcM,GACzBnH,EAAI,EAAGA,EAAI8I,EAAclL,OAAQoC,IAAK,CAC9C,IAAI+I,EAAeD,EAAc9I,GAAGgJ,OAChCC,EAAuBF,EAO1BE,EAJoB,MAApBF,EAAa,IACO,MAApBA,EAAa,IACO,MAApBA,EAAa,GAEUA,EAAaxL,MAAM,GAAGyL,OAEtB,UAAYD,EAGpCf,SAASC,EAAU,SAAUlH,GAC5B,GAAMA,EAAK+D,cAAcmE,GAIzB,OAAQF,EAAa,IACpB,IAAK,IACL,IAAK,IAGH,IADA,IAAIG,EAAWnI,EAAKoH,WACXxK,EAAI,EAAGA,EAAIuL,EAAStL,OAAQD,IAAK,CACzC,IAAIwL,EAAUD,EAASvL,GACvB,GAAM,iBAAkBwL,EAAxB,CAIA,IAAIC,EAAS,YAAchC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACjE6B,EAAQR,aAAaS,EAAQ,IAEzBrI,EAAK+D,cAAc,WAAasE,EAAb,KAAkCL,IACxDI,EAAQR,aAAarC,EAAM,IAG5B6C,EAAQ/E,gBAAgBgF,EATxB,CAUD,CAED,MAED,IAAK,IAECA,EAAS,YAAchC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACjEvG,EAAK4H,aAAaS,EAAQ,IAEtBrI,EAAK+D,cAAc,UAAYsE,EAAZ,KAAiCL,IACvDhI,EAAK4H,aAAarC,EAAM,IAGzBvF,EAAKqD,gBAAgBgF,GAEtB,MAED,QACCrI,EAAK4H,aAAarC,EAAM,IAI3B,EACD,CACD,GAEAxD,UAAU,GAAK+F,EAGf,IAAIQ,EAAoBjB,EAAI/G,MAAMwB,KAAMC,WAIxC,GAFA2F,EAAOrE,gBAAgBsE,GAEnBE,EAAMhL,OAAS,EAAG,CAGrB,IADA,IAAI0L,EAAgB,GACX3E,EAAI,EAAGA,EAAIiE,EAAMhL,OAAQ+G,IACjC2E,EAAc7K,KAAK,IAAMmK,EAAMjE,GAAK,KAIrC,IADA,IAAI4E,EAAW1E,EAAOvF,SAASqE,iBAAiB2F,EAAc5C,KAAK,MAC1D8C,EAAI,EAAGA,EAAID,EAAS3L,OAAQ4L,IAEpC,IADA,IAAI5F,EAAU2F,EAASC,GACdC,EAAI,EAAGA,EAAIb,EAAMhL,OAAQ6L,IACjC7F,EAAQQ,gBAAgBwE,EAAMa,GAGjC,CAGA,OAAOJ,CACR,CAAE,MAAOK,GAGR,GAFAjB,EAAOrE,gBAAgBsE,GAEnBE,EAAMhL,OAAS,EAAG,CAGrB,IADI0L,EAAgB,GACX3E,EAAI,EAAGA,EAAIiE,EAAMhL,OAAQ+G,IACjC2E,EAAc7K,KAAK,IAAMmK,EAAMjE,GAAK,KAIrC,IADI4E,EAAW1E,EAAOvF,SAASqE,iBAAiB2F,EAAc5C,KAAK,MAC1D8C,EAAI,EAAGA,EAAID,EAAS3L,OAAQ4L,IAEpC,IADI5F,EAAU2F,EAASC,GACdC,EAAI,EAAGA,EAAIb,EAAMhL,OAAQ6L,IACjC7F,EAAQQ,gBAAgBwE,EAAMa,GAGjC,CAEA,IAAIE,EAAe,GACnB,IACCvB,EAAI/G,MAAMwB,KAAM,CAAC,4BAClB,CAAE,MAAO+G,GACRD,EAAeC,EAAWC,QACtBF,IACHA,EAAeA,EAAanF,QAAQ,2BAA4B6D,GAElE,CAEKsB,IACJA,EAAe,qDAAuDtB,EAAkB,8BAGzF,IACC,MAAM,IAAIyB,aAAaH,EACxB,CAAE,MAAOvK,GACR,MAAM,IAAIuI,MAAMgC,EACjB,CACD,CACD,CACD,CACA,CAvmBD,CAumBG1K", "x_google_ignoreList": [4]}