{"version": 3, "file": "browser.cjs", "sources": ["../src/encode/decode.mjs", "../src/encode/extract.mjs", "../src/encode/encode.mjs", "../src/browser.js", "../../../node_modules/@mrhenry/core-web/modules/~element-qsa-has.js"], "sourcesContent": ["\n/** Decodes an identifier back into a CSS selector */\nexport default function decodeCSS(value) {\n\tif (value.slice(0, 13) !== 'csstools-has-') {\n\t\treturn '';\n\t}\n\n\tvalue = value.slice(13);\n\tlet values = value.split('-');\n\n\tlet result = '';\n\tfor (let i = 0; i < values.length; i++) {\n\t\tresult += String.fromCharCode(parseInt(values[i], 36));\n\t}\n\n\treturn result;\n}\n", "import decodeCSS from './decode.mjs';\n\n/** Extract encoded selectors out of attribute selectors */\nexport default function extractEncodedSelectors(value) {\n\tlet out = [];\n\n\tlet depth = 0;\n\tlet candidate;\n\n\tlet quoted = false;\n\tlet quotedMark;\n\n\tlet containsUnescapedUnquotedHasAtDepth1 = false;\n\n\t// Stryker disable next-line EqualityOperator\n\tfor (let i = 0; i < value.length; i++) {\n\t\tconst char = value[i];\n\n\t\tswitch (char) {\n\t\t\tcase '[':\n\t\t\t\tif (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (depth === 0) {\n\t\t\t\t\tcandidate = '';\n\t\t\t\t} else {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t}\n\n\t\t\t\tdepth++;\n\t\t\t\tcontinue;\n\t\t\tcase ']':\n\t\t\t\tif (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t{\n\t\t\t\t\tdepth--;\n\t\t\t\t\tif (depth === 0) {\n\t\t\t\t\t\tconst decoded = decodeCSS(candidate);\n\t\t\t\t\t\tif (containsUnescapedUnquotedHasAtDepth1) {\n\t\t\t\t\t\t\tout.push(decoded);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcandidate += char;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcontinue;\n\t\t\tcase '\\\\':\n\t\t\t\tcandidate += value[i];\n\t\t\t\tcandidate += value[i+1];\n\t\t\t\ti++;\n\t\t\t\tcontinue;\n\n\t\t\tcase '\"':\n\t\t\tcase '\\'':\n\t\t\t\tif (quoted && char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (quoted) {\n\t\t\t\t\tcandidate += char;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tquoted = true;\n\t\t\t\tquotedMark = char;\n\t\t\t\tcontinue;\n\n\t\t\tdefault:\n\t\t\t\tif (candidate === '' && depth === 1 && (value.slice(i, i + 13) === 'csstools-has-')) {\n\t\t\t\t\tcontainsUnescapedUnquotedHasAtDepth1 = true;\n\t\t\t\t}\n\n\t\t\t\tcandidate += char;\n\t\t\t\tcontinue;\n\t\t}\n\t}\n\n\tconst unique = [];\n\tfor (let i = 0; i < out.length; i++) {\n\t\tif (unique.indexOf(out[i]) === -1) {\n\t\t\tunique.push(out[i]);\n\t\t}\n\t}\n\n\treturn unique;\n}\n", "\n/** Returns the string as an encoded CSS identifier. */\nexport default function encodeCSS(value) {\n\tif (value === '') {\n\t\treturn '';\n\t}\n\n\tlet hex;\n\tlet result = '';\n\tfor (let i = 0; i < value.length; i++) {\n\t\thex = value.charCodeAt(i).toString(36);\n\t\tif (i === 0) {\n\t\t\tresult += hex;\n\t\t} else {\n\t\t\tresult += '-' + hex;\n\t\t}\n\t}\n\n\treturn 'csstools-has-' + result;\n}\n", "import '@mrhenry/core-web/modules/~element-qsa-has.js';\nimport extractEncodedSelectors from './encode/extract.mjs';\nimport encodeCSS from './encode/encode.mjs';\n\nfunction hasNativeSupport() {\n\ttry {\n\t\tif (!('CSS' in self) || !('supports' in self.CSS) || !self.CSS.supports('selector(:has(div))')) {\n\t\t\treturn false;\n\t\t}\n\n\t} catch (_) {\n\t\treturn false;\n\t}\n\n\treturn true;\n}\n\nexport default function cssHasPseudo(document, options) {\n\t// OPTIONS\n\t{\n\t\tif (!options) {\n\t\t\toptions = {};\n\t\t}\n\n\t\toptions = {\n\t\t\thover: (!!options.hover) || false,\n\t\t\tdebug: (!!options.debug) || false,\n\t\t\tobservedAttributes: options.observedAttributes || [],\n\t\t\tforcePolyfill: (!!options.forcePolyfill) || false,\n\t\t};\n\n\t\toptions.mustPolyfill = options.forcePolyfill || !hasNativeSupport();\n\n\t\tif (!Array.isArray(options.observedAttributes)) {\n\t\t\toptions.observedAttributes = [];\n\t\t}\n\n\t\toptions.observedAttributes = options.observedAttributes.filter(function(x) {\n\t\t\treturn (typeof x === 'string');\n\t\t});\n\n\t\t// https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes\n\t\t// `data-*` and `style` were omitted\n\t\toptions.observedAttributes = options.observedAttributes.concat(['accept', 'accept-charset', 'accesskey', 'action', 'align', 'allow', 'alt', 'async', 'autocapitalize', 'autocomplete', 'autofocus', 'autoplay', 'buffered', 'capture', 'challenge', 'charset', 'checked', 'cite', 'class', 'code', 'codebase', 'cols', 'colspan', 'content', 'contenteditable', 'contextmenu', 'controls', 'coords', 'crossorigin', 'csp', 'data', 'datetime', 'decoding', 'default', 'defer', 'dir', 'dirname', 'disabled', 'download', 'draggable', 'enctype', 'enterkeyhint', 'for', 'form', 'formaction', 'formenctype', 'formmethod', 'formnovalidate', 'formtarget', 'headers', 'hidden', 'high', 'href', 'hreflang', 'http-equiv', 'icon', 'id', 'importance', 'integrity', 'intrinsicsize', 'inputmode', 'ismap', 'itemprop', 'keytype', 'kind', 'label', 'lang', 'language', 'list', 'loop', 'low', 'manifest', 'max', 'maxlength', 'minlength', 'media', 'method', 'min', 'multiple', 'muted', 'name', 'novalidate', 'open', 'optimum', 'pattern', 'ping', 'placeholder', 'poster', 'preload', 'radiogroup', 'readonly', 'referrerpolicy', 'rel', 'required', 'reversed', 'rows', 'rowspan', 'sandbox', 'scope', 'scoped', 'selected', 'shape', 'size', 'sizes', 'slot', 'span', 'spellcheck', 'src', 'srcdoc', 'srclang', 'srcset', 'start', 'step', 'summary', 'tabindex', 'target', 'title', 'translate', 'type', 'usemap', 'value', 'width', 'wrap']);\n\t}\n\n\tconst observedItems = [];\n\n\t// document.createAttribute() doesn't support `:` in the name. innerHTML does\n\tconst attributeElement = document.createElement('x');\n\n\t// walk all stylesheets to collect observed css rules\n\t[].forEach.call(document.styleSheets, walkStyleSheet);\n\tif (!options.mustPolyfill) {\n\t\t// Cleanup of rules will have happened in `walkStyleSheet`\n\t\t// Native support will take over from here\n\t\treturn;\n\t}\n\n\ttransformObservedItemsThrottled();\n\n\t// observe DOM modifications that affect selectors\n\tif ('MutationObserver' in self) {\n\t\tconst mutationObserver = new MutationObserver(function(mutationsList) {\n\t\t\tmutationsList.forEach(function(mutation) {\n\t\t\t\t[].forEach.call(mutation.addedNodes || [], function(node) {\n\t\t\t\t\t// walk stylesheets to collect observed css rules\n\t\t\t\t\tif (node.nodeType !== 1) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (node.sheet) {\n\t\t\t\t\t\twalkStyleSheet(node.sheet);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tnode.addEventListener('load', function (e) {\n\t\t\t\t\t\tif (e.target && e.target.sheet) {\n\t\t\t\t\t\t\twalkStyleSheet(e.target.sheet);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\n\t\t\t\t// transform observed css rules\n\t\t\t\tcleanupObservedCssRules();\n\t\t\t\ttransformObservedItemsThrottled();\n\t\t\t});\n\t\t});\n\n\t\tmutationObserver.observe(document, { childList: true, subtree: true, attributes: true, attributeFilter: options.observedAttributes });\n\t}\n\n\t// observe DOM events that affect pseudo-selectors\n\tdocument.addEventListener('focus', transformObservedItemsThrottled, true);\n\tdocument.addEventListener('blur', transformObservedItemsThrottled, true);\n\tdocument.addEventListener('input', transformObservedItemsThrottled);\n\tdocument.addEventListener('change', transformObservedItemsThrottled, true);\n\n\tif (options.hover) {\n\t\tif ('onpointerenter' in document) {\n\t\t\tdocument.addEventListener('pointerenter', transformObservedItemsThrottled, true);\n\t\t\tdocument.addEventListener('pointerleave', transformObservedItemsThrottled, true);\n\t\t} else {\n\t\t\tdocument.addEventListener('mouseover', transformObservedItemsThrottled, true);\n\t\t\tdocument.addEventListener('mouseout', transformObservedItemsThrottled, true);\n\t\t}\n\t}\n\n\t// observe Javascript setters that effect pseudo-selectors\n\tif ('defineProperty' in Object && 'getOwnPropertyDescriptor' in Object && 'hasOwnProperty' in Object) {\n\t\ttry {\n\t\t\tfunction observeProperty(proto, property) {\n\t\t\t\t// eslint-disable-next-line no-prototype-builtins\n\t\t\t\tif (proto.hasOwnProperty(property)) {\n\t\t\t\t\tconst descriptor = Object.getOwnPropertyDescriptor(proto, property);\n\t\t\t\t\tif (descriptor && descriptor.configurable && 'set' in descriptor) {\n\t\t\t\t\t\tObject.defineProperty(proto, property, {\n\t\t\t\t\t\t\tconfigurable: descriptor.configurable,\n\t\t\t\t\t\t\tenumerable: descriptor.enumerable,\n\t\t\t\t\t\t\tget: function () {\n\t\t\t\t\t\t\t\treturn descriptor.get.apply(this, arguments);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tset: function () {\n\t\t\t\t\t\t\t\tdescriptor.set.apply(this, arguments);\n\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\ttransformObservedItemsThrottled();\n\t\t\t\t\t\t\t\t} catch (_) {\n\t\t\t\t\t\t\t\t\t// should never happen as there is an inner try/catch\n\t\t\t\t\t\t\t\t\t// but just in case\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ('HTMLElement' in self && HTMLElement.prototype) {\n\t\t\t\tobserveProperty(HTMLElement.prototype, 'disabled');\n\t\t\t}\n\n\t\t\t// Not all of these elements have all of these properties.\n\t\t\t// But the code above checks if they exist first.\n\t\t\t['checked', 'selected', 'readOnly', 'required'].forEach(function(property) {\n\t\t\t\t[\n\t\t\t\t\t'HTMLButtonElement',\n\t\t\t\t\t'HTMLFieldSetElement',\n\t\t\t\t\t'HTMLInputElement',\n\t\t\t\t\t'HTMLMeterElement',\n\t\t\t\t\t'HTMLOptGroupElement',\n\t\t\t\t\t'HTMLOptionElement',\n\t\t\t\t\t'HTMLOutputElement',\n\t\t\t\t\t'HTMLProgressElement',\n\t\t\t\t\t'HTMLSelectElement',\n\t\t\t\t\t'HTMLTextAreaElement',\n\t\t\t\t].forEach(function(elementName) {\n\t\t\t\t\tif (elementName in self && self[elementName].prototype) {\n\t\t\t\t\t\tobserveProperty(self[elementName].prototype, property);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t} catch (e) {\n\t\t\tif (options.debug) {\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}\n\t}\n\n\tlet transformObservedItemsThrottledBusy = false;\n\tfunction transformObservedItemsThrottled() {\n\t\tif (transformObservedItemsThrottledBusy) {\n\t\t\tcancelAnimationFrame(transformObservedItemsThrottledBusy);\n\t\t}\n\n\t\ttransformObservedItemsThrottledBusy = requestAnimationFrame(function() {\n\t\t\ttransformObservedItems();\n\t\t});\n\t}\n\n\t// transform observed css rules\n\tfunction transformObservedItems() {\n\t\tobservedItems.forEach(function(item) {\n\t\t\tconst nodes = [];\n\n\t\t\tlet matches = [];\n\t\t\tif (item.selector) {\n\t\t\t\ttry {\n\t\t\t\t\tmatches = document.querySelectorAll(item.selector);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (options.debug) {\n\t\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t[].forEach.call(matches, function(element) {\n\t\t\t\t// memorize the node\n\t\t\t\tnodes.push(element);\n\n\t\t\t\t// set an attribute with an irregular attribute name\n\t\t\t\t// document.createAttribute() doesn't support special characters\n\t\t\t\tattributeElement.innerHTML = '<x ' + item.attributeName + '>';\n\n\t\t\t\telement.setAttributeNode(attributeElement.children[0].attributes[0].cloneNode());\n\n\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t});\n\n\t\t\t// remove the encoded attribute from all nodes that no longer match them\n\t\t\titem.nodes.forEach(function(node) {\n\t\t\t\tif (nodes.indexOf(node) === -1) {\n\t\t\t\t\tnode.removeAttribute(item.attributeName);\n\n\t\t\t\t\t// trigger a style refresh in IE and Edge\n\t\t\t\t\tdocument.documentElement.style.zoom = 1; document.documentElement.style.zoom = null;\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t// update the\n\t\t\titem.nodes = nodes;\n\t\t});\n\t}\n\n\t// remove any observed cssrules that no longer apply\n\tfunction cleanupObservedCssRules() {\n\t\t[].push.apply(\n\t\t\tobservedItems,\n\t\t\tobservedItems.splice(0).filter(function(item) {\n\t\t\t\treturn item.rule.parentStyleSheet &&\n\t\t\t\t\titem.rule.parentStyleSheet.ownerNode &&\n\t\t\t\t\tdocument.documentElement.contains(item.rule.parentStyleSheet.ownerNode);\n\t\t\t}),\n\t\t);\n\t}\n\n\t// walk a stylesheet to collect observed css rules\n\tfunction walkStyleSheet(styleSheet) {\n\t\ttry {\n\t\t\t// walk a css rule to collect observed css rules\n\t\t\tfor (let i = (styleSheet.cssRules.length - 1); i >= 0; i--) {\n\t\t\t\tlet rule = styleSheet.cssRules[i];\n\n\t\t\t\tif (rule.selectorText) {\n\t\t\t\t\trule.selectorText = rule.selectorText.replace(/\\.js-has-pseudo\\s/g, '');\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// decode the selector text in all browsers to:\n\t\t\t\t\t\tconst hasSelectors = extractEncodedSelectors(rule.selectorText.toString());\n\t\t\t\t\t\tif (hasSelectors.length === 0) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!options.mustPolyfill) {\n\t\t\t\t\t\t\tstyleSheet.deleteRule(i);\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tfor (let j = 0; j < hasSelectors.length; j++) {\n\t\t\t\t\t\t\tconst hasSelector = hasSelectors[j];\n\t\t\t\t\t\t\tif (hasSelector) {\n\t\t\t\t\t\t\t\tobservedItems.push({\n\t\t\t\t\t\t\t\t\trule: rule,\n\t\t\t\t\t\t\t\t\tselector: hasSelector,\n\t\t\t\t\t\t\t\t\tattributeName: encodeCSS(hasSelector),\n\t\t\t\t\t\t\t\t\tnodes: [],\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tif (options.debug) {\n\t\t\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\twalkStyleSheet(rule);\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (options.debug) {\n\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}\n\t}\n}\n", "/* eslint-disable */\n(function (global) {\n\ttry {\n\t\t// test for has support\n\t\tglobal.document.querySelector(':has(*, :does-not-exist, > *)');\n\n\t\tif (\n\t\t\t!global.document.querySelector(':has(:scope *)') &&\n\t\t\tCSS.supports('selector(:has(div))')\n\t\t) {\n\t\t\treturn;\n\t\t}\n\t} catch (_) { }\n\n\t// ELEMENT\n\t// polyfill Element#querySelector\n\tvar querySelectorWithHasElement = polyfill(global.Element.prototype.querySelector);\n\n\tglobal.Element.prototype.querySelector = function querySelector(selectors) {\n\t\treturn querySelectorWithHasElement.apply(this, arguments);\n\t};\n\n\t// polyfill Element#querySelectorAll\n\tvar querySelectorAllWithHasElement = polyfill(global.Element.prototype.querySelectorAll);\n\n\tglobal.Element.prototype.querySelectorAll = function querySelectorAll(selectors) {\n\t\treturn querySelectorAllWithHasElement.apply(this, arguments);\n\t};\n\n\t// polyfill Element#matches\n\tif (global.Element.prototype.matches) {\n\t\tvar matchesWithHasElement = polyfill(global.Element.prototype.matches);\n\n\t\tglobal.Element.prototype.matches = function matches(selectors) {\n\t\t\treturn matchesWithHasElement.apply(this, arguments);\n\t\t};\n\t}\n\n\t// polyfill Element#closest\n\tif (global.Element.prototype.closest) {\n\t\tvar closestWithHasElement = polyfill(global.Element.prototype.closest);\n\n\t\tglobal.Element.prototype.closest = function closest(selectors) {\n\t\t\treturn closestWithHasElement.apply(this, arguments);\n\t\t};\n\t}\n\n\t// DOCUMENT\n\tif ('Document' in global && 'prototype' in global.Document) {\n\t\t// polyfill Document#querySelector\n\t\tvar querySelectorWithHasDocument = polyfill(global.Document.prototype.querySelector);\n\n\t\tglobal.Document.prototype.querySelector = function querySelector(selectors) {\n\t\t\treturn querySelectorWithHasDocument.apply(this, arguments);\n\t\t};\n\n\t\t// polyfill Document#querySelectorAll\n\t\tvar querySelectorAllWithHasDocument = polyfill(global.Document.prototype.querySelectorAll);\n\n\t\tglobal.Document.prototype.querySelectorAll = function querySelectorAll(selectors) {\n\t\t\treturn querySelectorAllWithHasDocument.apply(this, arguments);\n\t\t};\n\n\t\t// polyfill Document#matches\n\t\tif (global.Document.prototype.matches) {\n\t\t\tvar matchesWithHasDocument = polyfill(global.Document.prototype.matches);\n\n\t\t\tglobal.Document.prototype.matches = function matches(selectors) {\n\t\t\t\treturn matchesWithHasDocument.apply(this, arguments);\n\t\t\t};\n\t\t}\n\n\t\t// polyfill Document#closest\n\t\tif (global.Document.prototype.closest) {\n\t\t\tvar closestWithHasDocument = polyfill(global.Document.prototype.closest);\n\n\t\t\tglobal.Document.prototype.closest = function closest(selectors) {\n\t\t\t\treturn closestWithHasDocument.apply(this, arguments);\n\t\t\t};\n\t\t}\n\t}\n\n\tfunction pseudoClassHasInnerQuery(query) {\n\t\tvar current = '';\n\t\tvar start = 0;\n\t\tvar depth = 0;\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar inHas = false;\n\n\t\tvar bracketed = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (quoted) {\n\t\t\t\tif (char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t}\n\n\t\t\t\tcurrent += char;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (current.toLowerCase() === ':has(' && !inHas) {\n\t\t\t\tinHas = true;\n\t\t\t\tstart = i;\n\t\t\t\tcurrent = '';\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ':':\n\t\t\t\t\tif (!inHas) {\n\t\t\t\t\t\tcurrent = '';\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '(':\n\t\t\t\t\tif (inHas) {\n\t\t\t\t\t\tdepth++;\n\t\t\t\t\t}\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase ')':\n\t\t\t\t\tif (inHas) {\n\t\t\t\t\t\tif (depth === 0) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tinnerQuery: current,\n\t\t\t\t\t\t\t\tstart: start,\n\t\t\t\t\t\t\t\tend: i-1\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tdepth--;\n\t\t\t\t\t}\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '[':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tbracketed++;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase \"]\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tif (bracketed > 0) {\n\t\t\t\t\t\tbracketed--\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tfunction replaceScopeWithAttr(query, attr) {\n\t\tvar parts = [];\n\t\tvar current = '';\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar bracketed = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (quoted) {\n\t\t\t\tif (char === quotedMark) {\n\t\t\t\t\tquoted = false;\n\t\t\t\t}\n\n\t\t\t\tcurrent += char;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (current.toLowerCase() === ':scope' && !bracketed && (/^[\\[\\.\\:\\\\\"\\s|+>~#&,)]/.test(char || ''))) {\n\t\t\t\tparts.push(current.slice(0, current.length - 6));\n\t\t\t\tparts.push('[' + attr + ']');\n\t\t\t\tcurrent = '';\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ':':\n\t\t\t\t\tparts.push(current);\n\t\t\t\t\tcurrent = '';\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '[':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tbracketed++;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase \"]\":\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tif (bracketed > 0) {\n\t\t\t\t\t\tbracketed--\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\t\t\t}\n\t\t}\n\n\t\tif (current.toLowerCase() === ':scope') {\n\t\t\tparts.push(current.slice(0, current.length - 6));\n\t\t\tparts.push('[' + attr + ']');\n\t\t\tcurrent = '';\n\t\t}\n\n\t\tif (parts.length === 0) {\n\t\t\treturn query;\n\t\t}\n\n\t\treturn parts.join('') + current;\n\t}\n\n\tfunction charIsNestedMarkMirror(char, mark) {\n\t\tif (mark === '(' && char === ')') {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (mark === '[' && char === ']') {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tfunction splitSelector(query) {\n\t\tvar selectors = [];\n\t\tvar current = '';\n\n\t\tvar escaped = false;\n\n\t\tvar quoted = false;\n\t\tvar quotedMark = false;\n\n\t\tvar nestedMark = false;\n\t\tvar nestedDepth = 0;\n\n\t\tfor (var i = 0; i < query.length; i++) {\n\t\t\tvar char = query[i];\n\n\t\t\tif (escaped) {\n\t\t\t\tcurrent += char;\n\t\t\t\tescaped = false;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tswitch (char) {\n\t\t\t\tcase ',':\n\t\t\t\t\tif (quoted) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (nestedDepth > 0) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tselectors.push(current);\n\t\t\t\t\tcurrent = '';\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\\\\':\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tescaped = true;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '\"':\n\t\t\t\tcase \"'\":\n\t\t\t\t\tif (quoted && char === quotedMark) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tquoted = false;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tquoted = true;\n\t\t\t\t\tquotedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tcase '(':\n\t\t\t\tcase ')':\n\t\t\t\tcase '[':\n\t\t\t\tcase ']':\n\t\t\t\t\tif (quoted) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (charIsNestedMarkMirror(char, nestedMark)) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tnestedDepth--;\n\n\t\t\t\t\t\tif (nestedDepth === 0) {\n\t\t\t\t\t\t\tnestedMark = false;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (char === nestedMark) {\n\t\t\t\t\t\tcurrent += char;\n\t\t\t\t\t\tnestedDepth++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tnestedDepth++;\n\t\t\t\t\tnestedMark = char;\n\t\t\t\t\tcontinue;\n\n\t\t\t\tdefault:\n\t\t\t\t\tcurrent += char;\n\t\t\t\t\tcontinue;\n\n\t\t\t}\n\t\t}\n\n\t\tselectors.push(current);\n\n\t\treturn selectors;\n\t}\n\n\tfunction replaceAllWithTempAttr(query, nested, callback) {\n\t\tvar inner = pseudoClassHasInnerQuery(query);\n\t\tif (!inner) {\n\t\t\treturn query;\n\t\t}\n\n\t\tif (nested) {\n\t\t\treturn false;\n\t\t}\n\n\t\tvar innerQuery = inner.innerQuery;\n\t\tvar attr = 'q-has' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\tvar innerReplacement = '[' + attr + ']';\n\n\t\tvar x = query;\n\n\t\tif (inner.innerQuery.toLowerCase().indexOf(':has(') > -1) {\n\t\t\tvar innerParts = splitSelector(inner.innerQuery);\n\t\t\tvar newInnerParts = [];\n\t\t\tfor (var i = 0; i < innerParts.length; i++) {\n\t\t\t\tvar innerPart = innerParts[i];\n\n\t\t\t\t// Nested has is not supported.\n\t\t\t\t// If a recursive/nested call returns \"false\" we throw\n\t\t\t\tvar innerPartReplaced = replaceAllWithTempAttr(innerPart, true, function () { });\n\t\t\t\tif (!innerPartReplaced) {\n\t\t\t\t\tthrow new Error(\"Nested :has() is not supported\")\n\t\t\t\t} else {\n\t\t\t\t\tnewInnerParts.push(innerPart);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar _prefix = x.substring(0, inner.start - 5); // ':has('.length === 5\n\t\t\tvar _suffix = x.substring(inner.end + 2); // ')'.length === 1\n\n\t\t\treturn _prefix + newInnerParts.join(', ') + _suffix;\n\t\t}\n\n\t\tvar _prefix = x.substring(0, inner.start - 5); // ':has('.length === 5\n\t\tvar _suffix = x.substring(inner.end + 2); // ')'.length === 1\n\n\t\tx = _prefix + innerReplacement + _suffix;\n\n\t\tcallback(innerQuery, attr);\n\t\tif (x.toLowerCase().indexOf(':has(') > -1) {\n\t\t\tvar y = replaceAllWithTempAttr(x, false, callback);\n\t\t\tif (y) {\n\t\t\t\treturn y;\n\t\t\t}\n\t\t}\n\n\t\treturn x;\n\t}\n\n\tfunction walkNode(rootNode, callback) {\n\t\tif (('setAttribute' in (rootNode)) && ('querySelector' in (rootNode))) {\n\t\t\tcallback(rootNode);\n\t\t}\n\n\t\tif (rootNode.hasChildNodes()) {\n\t\t\tvar nodes = rootNode.childNodes;\n\t\t\tfor (var i = 0; i < nodes.length; ++i) {\n\t\t\t\twalkNode(nodes[i], callback);\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction polyfill(qsa) {\n\t\treturn function (selectors) {\n\t\t\tif (!selectors) {\n\t\t\t\treturn qsa.apply(this, arguments);\n\t\t\t}\n\n\t\t\tvar selectorsString = String(selectors);\n\t\t\tif (!selectorsString || (selectorsString.toLowerCase().indexOf(':has(') === -1) || !pseudoClassHasInnerQuery(selectorsString)) {\n\t\t\t\treturn qsa.apply(this, arguments);\n\t\t\t}\n\n\t\t\tvar rootNode;\n\t\t\tif ('getRootNode' in this) {\n\t\t\t\trootNode = this.getRootNode();\n\t\t\t} else {\n\t\t\t\tvar r = this;\n\t\t\t\twhile (r) {\n\t\t\t\t\trootNode = r;\n\t\t\t\t\tr = r.parentNode;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tvar _focus = this;\n\t\t\tif (_focus === global.document) {\n\t\t\t\t_focus = global.document.documentElement;\n\t\t\t}\n\n\t\t\tvar scopeAttr = 'q-has-scope' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t_focus.setAttribute(scopeAttr, '');\n\n\t\t\ttry {\n\t\t\t\tselectorsString = replaceScopeWithAttr(selectorsString, scopeAttr);\n\n\t\t\t\tvar attrs = [scopeAttr];\n\t\t\t\tvar newQuery = replaceAllWithTempAttr(selectorsString, false, function (inner, attr) {\n\t\t\t\t\tattrs.push(attr);\n\n\t\t\t\t\tvar selectorParts = splitSelector(inner);\n\t\t\t\t\tfor (var x = 0; x < selectorParts.length; x++) {\n\t\t\t\t\t\tvar selectorPart = selectorParts[x].trim();\n\t\t\t\t\t\tvar absoluteSelectorPart = selectorPart;\n\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tselectorPart[0] === '>' ||\n\t\t\t\t\t\t\tselectorPart[0] === '+' ||\n\t\t\t\t\t\t\tselectorPart[0] === '~'\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tabsoluteSelectorPart = selectorPart.slice(1).trim();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tabsoluteSelectorPart = ':scope ' + selectorPart;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\twalkNode(rootNode, function (node) {\n\t\t\t\t\t\t\tif (!(node.querySelector(absoluteSelectorPart))) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tswitch (selectorPart[0]) {\n\t\t\t\t\t\t\t\tcase '~':\n\t\t\t\t\t\t\t\tcase '+':\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tvar siblings = node.childNodes;\n\t\t\t\t\t\t\t\t\t\tfor (var i = 0; i < siblings.length; i++) {\n\t\t\t\t\t\t\t\t\t\t\tvar sibling = siblings[i];\n\t\t\t\t\t\t\t\t\t\t\tif (!('setAttribute' in sibling)) {\n\t\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tvar idAttr = 'q-has-id' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t\t\t\t\t\t\t\t\tsibling.setAttribute(idAttr, '');\n\n\t\t\t\t\t\t\t\t\t\t\tif (node.querySelector(':scope [' + idAttr + ']' + ' ' + selectorPart)) {\n\t\t\t\t\t\t\t\t\t\t\t\tsibling.setAttribute(attr, '');\n\t\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\t\tsibling.removeAttribute(idAttr);\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tcase '>':\n\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\tvar idAttr = 'q-has-id' + (Math.floor(Math.random() * 9000000) + 1000000);\n\t\t\t\t\t\t\t\t\t\tnode.setAttribute(idAttr, '');\n\n\t\t\t\t\t\t\t\t\t\tif (node.querySelector(':scope[' + idAttr + ']' + ' ' + selectorPart)) {\n\t\t\t\t\t\t\t\t\t\t\tnode.setAttribute(attr, '');\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tnode.removeAttribute(idAttr);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\tnode.setAttribute(attr, '');\n\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\targuments[0] = newQuery;\n\n\t\t\t\t// results of the qsa\n\t\t\t\tvar elementOrNodeList = qsa.apply(this, arguments);\n\n\t\t\t\t_focus.removeAttribute(scopeAttr);\n\n\t\t\t\tif (attrs.length > 0) {\n\t\t\t\t\t// remove the fallback attribute\n\t\t\t\t\tvar attrsForQuery = [];\n\t\t\t\t\tfor (var j = 0; j < attrs.length; j++) {\n\t\t\t\t\t\tattrsForQuery.push('[' + attrs[j] + ']');\n\t\t\t\t\t}\n\n\t\t\t\t\tvar elements = global.document.querySelectorAll(attrsForQuery.join(','));\n\t\t\t\t\tfor (var k = 0; k < elements.length; k++) {\n\t\t\t\t\t\tvar element = elements[k];\n\t\t\t\t\t\tfor (var l = 0; l < attrs.length; l++) {\n\t\t\t\t\t\t\telement.removeAttribute(attrs[l]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// return the results of the qsa\n\t\t\t\treturn elementOrNodeList;\n\t\t\t} catch (err) {\n\t\t\t\t_focus.removeAttribute(scopeAttr);\n\n\t\t\t\tif (attrs.length > 0) {\n\t\t\t\t\t// remove the fallback attribute\n\t\t\t\t\tvar attrsForQuery = [];\n\t\t\t\t\tfor (var j = 0; j < attrs.length; j++) {\n\t\t\t\t\t\tattrsForQuery.push('[' + attrs[j] + ']');\n\t\t\t\t\t}\n\n\t\t\t\t\tvar elements = global.document.querySelectorAll(attrsForQuery.join(','));\n\t\t\t\t\tfor (var k = 0; k < elements.length; k++) {\n\t\t\t\t\t\tvar element = elements[k];\n\t\t\t\t\t\tfor (var l = 0; l < attrs.length; l++) {\n\t\t\t\t\t\t\telement.removeAttribute(attrs[l]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar errorMessage = '';\n\t\t\t\ttry {\n\t\t\t\t\tqsa.apply(this, [':core-web-does-not-exist']);\n\t\t\t\t} catch (dummyError) {\n\t\t\t\t\terrorMessage = dummyError.message;\n\t\t\t\t\tif (errorMessage) {\n\t\t\t\t\t\terrorMessage = errorMessage.replace(':core-web-does-not-exist', selectorsString);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (!errorMessage) {\n\t\t\t\t\terrorMessage = \"Failed to execute 'querySelector' on 'Document': '\" + selectorsString + \"' is not a valid selector.\";\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new DOMException(errorMessage);\n\t\t\t\t} catch (_) {\n\t\t\t\t\tthrow new Error(errorMessage);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t}\n})(self);\n"], "names": ["decodeCSS", "value", "slice", "values", "split", "result", "i", "length", "String", "fromCharCode", "parseInt", "extractEncodedSelectors", "candidate", "quotedMark", "out", "depth", "quoted", "containsUnescapedUnquotedHasAtDepth1", "char", "decoded", "push", "unique", "indexOf", "encodeCSS", "hex", "charCodeAt", "toString", "hasNativeSupport", "self", "CSS", "supports", "_", "global", "document", "querySelector", "querySelectorWithHasElement", "polyfill", "Element", "prototype", "selectors", "apply", "this", "arguments", "querySelectorAllWithHasElement", "querySelectorAll", "matches", "matchesWithHasElement", "closest", "closestWithHasElement", "Document", "querySelectorWithHasDocument", "querySelectorAllWithHasDocument", "matchesWithHasDocument", "closestWithHasDocument", "pseudoClassHasInnerQuery", "query", "current", "start", "escaped", "inHas", "toLowerCase", "innerQuery", "end", "replaceScopeWithAttr", "attr", "parts", "bracketed", "test", "join", "charIsNestedMarkMirror", "mark", "splitSelector", "nestedMark", "nested<PERSON><PERSON><PERSON>", "replaceAllWithTempAttr", "nested", "callback", "inner", "Math", "floor", "random", "innerReplacement", "x", "innerParts", "newInnerParts", "innerPart", "Error", "_prefix", "substring", "_suffix", "y", "walkNode", "rootNode", "hasChildNodes", "nodes", "childNodes", "qsa", "selectorsString", "getRootNode", "r", "parentNode", "_focus", "documentElement", "scopeAttr", "setAttribute", "attrs", "<PERSON><PERSON><PERSON><PERSON>", "selectorParts", "selector<PERSON><PERSON>", "trim", "absoluteSelectorPart", "node", "siblings", "sibling", "idAttr", "removeAttribute", "elementOrNodeList", "attrsFor<PERSON><PERSON>y", "j", "elements", "k", "element", "l", "err", "errorMessage", "dummy<PERSON><PERSON>r", "message", "replace", "DOMException", "cssHasPseudo", "options", "hover", "debug", "observedAttributes", "forcePolyfill", "mustPolyfill", "Array", "isArray", "filter", "concat", "observedItems", "attributeElement", "createElement", "for<PERSON>ach", "call", "styleSheets", "walkStyleSheet", "transformObservedItemsThrottled", "mutationObserver", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "sheet", "addEventListener", "e", "target", "splice", "item", "rule", "parentStyleSheet", "ownerNode", "contains", "observe", "childList", "subtree", "attributes", "attributeFilter", "Object", "observeProperty", "proto", "property", "hasOwnProperty", "descriptor", "getOwnPropertyDescriptor", "configurable", "defineProperty", "enumerable", "get", "set", "HTMLElement", "elementName", "console", "error", "transformObservedItemsThrottledBusy", "cancelAnimationFrame", "requestAnimationFrame", "selector", "innerHTML", "attributeName", "setAttributeNode", "children", "cloneNode", "style", "zoom", "styleSheet", "cssRules", "selectorText", "hasSelectors", "deleteRule", "hasSelector"], "mappings": "AAEe,SAASA,UAAUC,GACjC,GAA2B,kBAAvBA,EAAMC,MAAM,EAAG,IAClB,MAAO,GAOR,IAHA,IAAIC,GADJF,EAAQA,EAAMC,MAAM,KACDE,MAAM,KAErBC,EAAS,GACJC,EAAI,EAAGA,EAAIH,EAAOI,OAAQD,IAClCD,GAAUG,OAAOC,aAAaC,SAASP,EAAOG,GAAI,KAGnD,OAAOD,CACR,CCbe,SAASM,wBAAwBV,GAY/C,IAXA,IAGIW,EAGAC,EANAC,EAAM,GAENC,EAAQ,EAGRC,GAAS,EAGTC,GAAuC,EAGlCX,EAAI,EAAGA,EAAIL,EAAMM,OAAQD,IAAK,CACtC,IAAMY,EAAOjB,EAAMK,GAEnB,OAAQY,GACP,IAAK,IACJ,GAAIF,EAAQ,CACXJ,GAAaM,EACb,QACD,CAEc,IAAVH,EACHH,EAAY,GAEZA,GAAaM,EAGdH,IACA,SACD,IAAK,IACJ,GAAIC,EAAQ,CACXJ,GAAaM,EACb,QACD,CAIC,GAAc,MADdH,EACiB,CAChB,IAAMI,EAAUnB,UAAUY,GACtBK,GACHH,EAAIM,KAAKD,EAEX,MACCP,GAAaM,EAIf,SACD,IAAK,KACJN,GAAaX,EAAMK,GACnBM,GAAaX,EAAMK,EAAE,GACrBA,IACA,SAED,IAAK,IACL,IAAK,IACJ,GAAIU,GAAUE,IAASL,EAAY,CAClCG,GAAS,EACT,QACD,CAAO,GAAIA,EAAQ,CAClBJ,GAAaM,EACb,QACD,CAEAF,GAAS,EACTH,EAAaK,EACb,SAED,QACmB,KAAdN,GAA8B,IAAVG,GAA2C,kBAA3Bd,EAAMC,MAAMI,EAAGA,EAAI,MAC1DW,GAAuC,GAGxCL,GAAaM,EACb,SAEH,CAGA,IADA,IAAMG,EAAS,GACNf,EAAI,EAAGA,EAAIQ,EAAIP,OAAQD,KACA,IAA3Be,EAAOC,QAAQR,EAAIR,KACtBe,EAAOD,KAAKN,EAAIR,IAIlB,OAAOe,CACR,CCxFe,SAASE,UAAUtB,GACjC,GAAc,KAAVA,EACH,MAAO,GAKR,IAFA,IAAIuB,EACAnB,EAAS,GACJC,EAAI,EAAGA,EAAIL,EAAMM,OAAQD,IACjCkB,EAAMvB,EAAMwB,WAAWnB,GAAGoB,SAAS,IAElCrB,GADS,IAANC,EACOkB,EAEA,IAAMA,EAIlB,MAAO,gBAAkBnB,CAC1B,CCfA,SAASsB,mBACR,IACC,KAAM,QAASC,SAAW,aAAcA,KAAKC,OAASD,KAAKC,IAAIC,SAAS,uBACvE,OAAO,CAGT,CAAE,MAAOC,GACR,OAAO,CACR,CAEA,OAAO,CACR,ECdA,SAAWC,GACV,IAIC,GAFAA,EAAOC,SAASC,cAAc,kCAG5BF,EAAOC,SAASC,cAAc,mBAC/BL,IAAIC,SAAS,uBAEb,MAEF,CAAE,MAAOC,GAAK,CAId,IAAII,EAA8BC,SAASJ,EAAOK,QAAQC,UAAUJ,eAEpEF,EAAOK,QAAQC,UAAUJ,cAAgB,SAASA,cAAcK,GAC/D,OAAOJ,EAA4BK,MAAMC,KAAMC,UAChD,EAGA,IAAIC,EAAiCP,SAASJ,EAAOK,QAAQC,UAAUM,kBAOvE,GALAZ,EAAOK,QAAQC,UAAUM,iBAAmB,SAASA,iBAAiBL,GACrE,OAAOI,EAA+BH,MAAMC,KAAMC,UACnD,EAGIV,EAAOK,QAAQC,UAAUO,QAAS,CACrC,IAAIC,EAAwBV,SAASJ,EAAOK,QAAQC,UAAUO,SAE9Db,EAAOK,QAAQC,UAAUO,QAAU,SAASA,QAAQN,GACnD,OAAOO,EAAsBN,MAAMC,KAAMC,UAC1C,CACD,CAGA,GAAIV,EAAOK,QAAQC,UAAUS,QAAS,CACrC,IAAIC,EAAwBZ,SAASJ,EAAOK,QAAQC,UAAUS,SAE9Df,EAAOK,QAAQC,UAAUS,QAAU,SAASA,QAAQR,GACnD,OAAOS,EAAsBR,MAAMC,KAAMC,UAC1C,CACD,CAGA,GAAI,aAAcV,GAAU,cAAeA,EAAOiB,SAAU,CAE3D,IAAIC,EAA+Bd,SAASJ,EAAOiB,SAASX,UAAUJ,eAEtEF,EAAOiB,SAASX,UAAUJ,cAAgB,SAASA,cAAcK,GAChE,OAAOW,EAA6BV,MAAMC,KAAMC,UACjD,EAGA,IAAIS,EAAkCf,SAASJ,EAAOiB,SAASX,UAAUM,kBAOzE,GALAZ,EAAOiB,SAASX,UAAUM,iBAAmB,SAASA,iBAAiBL,GACtE,OAAOY,EAAgCX,MAAMC,KAAMC,UACpD,EAGIV,EAAOiB,SAASX,UAAUO,QAAS,CACtC,IAAIO,EAAyBhB,SAASJ,EAAOiB,SAASX,UAAUO,SAEhEb,EAAOiB,SAASX,UAAUO,QAAU,SAASA,QAAQN,GACpD,OAAOa,EAAuBZ,MAAMC,KAAMC,UAC3C,CACD,CAGA,GAAIV,EAAOiB,SAASX,UAAUS,QAAS,CACtC,IAAIM,EAAyBjB,SAASJ,EAAOiB,SAASX,UAAUS,SAEhEf,EAAOiB,SAASX,UAAUS,QAAU,SAASA,QAAQR,GACpD,OAAOc,EAAuBb,MAAMC,KAAMC,UAC3C,CACD,CACD,CAEA,SAASY,yBAAyBC,GAcjC,IAbA,IAAIC,EAAU,GACVC,EAAQ,EACR1C,EAAQ,EAER2C,GAAU,EAEV1C,GAAS,EACTH,GAAa,EAEb8C,GAAQ,EAIHrD,EAAI,EAAGA,EAAIiD,EAAMhD,OAAQD,IAAK,CACtC,IAAIY,EAAOqC,EAAMjD,GAEjB,GAAIoD,EACHF,GAAWtC,EACXwC,GAAU,OAIX,GAAI1C,EACCE,IAASL,IACZG,GAAS,GAGVwC,GAAWtC,OAUZ,OAN8B,UAA1BsC,EAAQI,eAA8BD,IACzCA,GAAQ,EACRF,EAAQnD,EACRkD,EAAU,IAGHtC,GACP,IAAK,IACCyC,IACJH,EAAU,IAGXA,GAAWtC,EACX,SAED,IAAK,IACAyC,GACH5C,IAEDyC,GAAWtC,EACX,SAED,IAAK,IACJ,GAAIyC,EAAO,CACV,GAAc,IAAV5C,EACH,MAAO,CACN8C,WAAYL,EACZC,MAAOA,EACPK,IAAKxD,EAAE,GAITS,GACD,CACAyC,GAAWtC,EACX,SAED,IAAK,KACJsC,GAAWtC,EACXwC,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJF,GAAWtC,EACXF,GAAS,EACTH,EAAaK,EACb,SAeD,QACCsC,GAAWtC,EACX,SAEH,CAEA,OAAO,CACR,CAEA,SAAS6C,qBAAqBR,EAAOS,GAWpC,IAVA,IAAIC,EAAQ,GACRT,EAAU,GAEVE,GAAU,EAEV1C,GAAS,EACTH,GAAa,EAEbqD,EAAY,EAEP5D,EAAI,EAAGA,EAAIiD,EAAMhD,OAAQD,IAAK,CACtC,IAAIY,EAAOqC,EAAMjD,GAEjB,GAAIoD,EACHF,GAAWtC,EACXwC,GAAU,OAIX,GAAI1C,EACCE,IAASL,IACZG,GAAS,GAGVwC,GAAWtC,OAUZ,OAN8B,WAA1BsC,EAAQI,gBAA+BM,GAAc,yBAAyBC,KAAKjD,GAAQ,MAC9F+C,EAAM7C,KAAKoC,EAAQtD,MAAM,EAAGsD,EAAQjD,OAAS,IAC7C0D,EAAM7C,KAAK,IAAM4C,EAAO,KACxBR,EAAU,IAGHtC,GACP,IAAK,IACJ+C,EAAM7C,KAAKoC,GACXA,EAAU,GACVA,GAAWtC,EACX,SAED,IAAK,KACJsC,GAAWtC,EACXwC,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJF,GAAWtC,EACXF,GAAS,EACTH,EAAaK,EACb,SAED,IAAK,IACJsC,GAAWtC,EACXgD,IACA,SAED,IAAK,IACJV,GAAWtC,EACPgD,EAAY,GACfA,IAGD,SAED,QACCV,GAAWtC,EACX,SAEH,CAQA,MAN8B,WAA1BsC,EAAQI,gBACXK,EAAM7C,KAAKoC,EAAQtD,MAAM,EAAGsD,EAAQjD,OAAS,IAC7C0D,EAAM7C,KAAK,IAAM4C,EAAO,KACxBR,EAAU,IAGU,IAAjBS,EAAM1D,OACFgD,EAGDU,EAAMG,KAAK,IAAMZ,CACzB,CAEA,SAASa,uBAAuBnD,EAAMoD,GACrC,MAAa,MAATA,GAAyB,MAATpD,GAIP,MAAToD,GAAyB,MAATpD,CAKrB,CAEA,SAASqD,cAAchB,GAYtB,IAXA,IAAIhB,EAAY,GACZiB,EAAU,GAEVE,GAAU,EAEV1C,GAAS,EACTH,GAAa,EAEb2D,GAAa,EACbC,EAAc,EAETnE,EAAI,EAAGA,EAAIiD,EAAMhD,OAAQD,IAAK,CACtC,IAAIY,EAAOqC,EAAMjD,GAEjB,GAAIoD,EACHF,GAAWtC,EACXwC,GAAU,OAIX,OAAQxC,GACP,IAAK,IACJ,GAAIF,EAAQ,CACXwC,GAAWtC,EACX,QACD,CAEA,GAAIuD,EAAc,EAAG,CACpBjB,GAAWtC,EACX,QACD,CAEAqB,EAAUnB,KAAKoC,GACfA,EAAU,GACV,SAED,IAAK,KACJA,GAAWtC,EACXwC,GAAU,EACV,SAED,IAAK,IACL,IAAK,IACJ,GAAI1C,GAAUE,IAASL,EAAY,CAClC2C,GAAWtC,EACXF,GAAS,EACT,QACD,CAEAwC,GAAWtC,EACXF,GAAS,EACTH,EAAaK,EACb,SAED,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACJ,GAAIF,EAAQ,CACXwC,GAAWtC,EACX,QACD,CAEA,GAAImD,uBAAuBnD,EAAMsD,GAAa,CAC7ChB,GAAWtC,EAGS,MAFpBuD,IAGCD,GAAa,GAGd,QACD,CAEA,GAAItD,IAASsD,EAAY,CACxBhB,GAAWtC,EACXuD,IACA,QACD,CAEAjB,GAAWtC,EACXuD,IACAD,EAAatD,EACb,SAED,QACCsC,GAAWtC,EACX,SAGH,CAIA,OAFAqB,EAAUnB,KAAKoC,GAERjB,CACR,CAEA,SAASmC,uBAAuBnB,EAAOoB,EAAQC,GAC9C,IAAIC,EAAQvB,yBAAyBC,GACrC,IAAKsB,EACJ,OAAOtB,EAGR,GAAIoB,EACH,OAAO,EAGR,IAAId,EAAagB,EAAMhB,WACnBG,EAAO,SAAWc,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACxDC,EAAmB,IAAMjB,EAAO,IAEhCkB,EAAI3B,EAER,GAAIsB,EAAMhB,WAAWD,cAActC,QAAQ,YAAe,CAGzD,IAFA,IAAI6D,EAAaZ,cAAcM,EAAMhB,YACjCuB,EAAgB,GACX9E,EAAI,EAAGA,EAAI6E,EAAW5E,OAAQD,IAAK,CAC3C,IAAI+E,EAAYF,EAAW7E,GAK3B,IADwBoE,uBAAuBW,GAAW,EAAM,WAAc,GAE7E,MAAM,IAAIC,MAAM,kCAEhBF,EAAchE,KAAKiE,EAErB,CAEA,IAAIE,EAAUL,EAAEM,UAAU,EAAGX,EAAMpB,MAAQ,GACvCgC,EAAUP,EAAEM,UAAUX,EAAMf,IAAM,GAEtC,OAAOyB,EAAUH,EAAchB,KAAK,MAAQqB,CAC7C,CAEIF,EAAUL,EAAEM,UAAU,EAAGX,EAAMpB,MAAQ,GACvCgC,EAAUP,EAAEM,UAAUX,EAAMf,IAAM,GAKtC,GAHAoB,EAAIK,EAAUN,EAAmBQ,EAEjCb,EAASf,EAAYG,GACjBkB,EAAEtB,cAActC,QAAQ,UAAW,EAAI,CAC1C,IAAIoE,EAAIhB,uBAAuBQ,GAAG,EAAON,GACzC,GAAIc,EACH,OAAOA,CAET,CAEA,OAAOR,CACR,CAEA,SAASS,SAASC,EAAUhB,GAK3B,GAJK,iBAAmBgB,GAAe,kBAAoBA,GAC1DhB,EAASgB,GAGNA,EAASC,gBAEZ,IADA,IAAIC,EAAQF,EAASG,WACZzF,EAAI,EAAGA,EAAIwF,EAAMvF,SAAUD,EACnCqF,SAASG,EAAMxF,GAAIsE,EAGtB,CAEA,SAASxC,SAAS4D,GACjB,OAAO,SAAUzD,GAChB,IAAKA,EACJ,OAAOyD,EAAIxD,MAAMC,KAAMC,WAGxB,IAKIkD,EALAK,EAAkBzF,OAAO+B,GAC7B,IAAK0D,IAAuE,IAAnDA,EAAgBrC,cAActC,QAAQ,WAAqBgC,yBAAyB2C,GAC5G,OAAOD,EAAIxD,MAAMC,KAAMC,WAIxB,GAAI,gBAAiBD,KACpBmD,EAAWnD,KAAKyD,mBAGhB,IADA,IAAIC,EAAI1D,KACD0D,GACNP,EAAWO,EACXA,EAAIA,EAAEC,WAIR,IAAIC,EAAS5D,KACT4D,IAAWrE,EAAOC,WACrBoE,EAASrE,EAAOC,SAASqE,iBAG1B,IAAIC,EAAY,eAAiBzB,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACvEqB,EAAOG,aAAaD,EAAW,IAE/B,IACCN,EAAkBlC,qBAAqBkC,EAAiBM,GAExD,IAAIE,EAAQ,CAACF,GACTG,EAAWhC,uBAAuBuB,GAAiB,EAAO,SAAUpB,EAAOb,GAC9EyC,EAAMrF,KAAK4C,GAGX,IADA,IAAI2C,EAAgBpC,cAAcM,GACzBK,EAAI,EAAGA,EAAIyB,EAAcpG,OAAQ2E,IAAK,CAC9C,IAAI0B,EAAeD,EAAczB,GAAG2B,OAChCC,EAAuBF,EAO1BE,EAJoB,MAApBF,EAAa,IACO,MAApBA,EAAa,IACO,MAApBA,EAAa,GAEUA,EAAa1G,MAAM,GAAG2G,OAEtB,UAAYD,EAGpCjB,SAASC,EAAU,SAAUmB,GAC5B,GAAMA,EAAK7E,cAAc4E,GAIzB,OAAQF,EAAa,IACpB,IAAK,IACL,IAAK,IAGH,IADA,IAAII,EAAWD,EAAKhB,WACXzF,EAAI,EAAGA,EAAI0G,EAASzG,OAAQD,IAAK,CACzC,IAAI2G,EAAUD,EAAS1G,GACvB,GAAM,iBAAkB2G,EAAxB,CAIA,IAAIC,EAAS,YAAcpC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACjEiC,EAAQT,aAAaU,EAAQ,IAEzBH,EAAK7E,cAAc,WAAagF,EAAb,KAAkCN,IACxDK,EAAQT,aAAaxC,EAAM,IAG5BiD,EAAQE,gBAAgBD,EATxB,CAUD,CAED,MAED,IAAK,IAECA,EAAS,YAAcpC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,KACjE+B,EAAKP,aAAaU,EAAQ,IAEtBH,EAAK7E,cAAc,UAAYgF,EAAZ,KAAiCN,IACvDG,EAAKP,aAAaxC,EAAM,IAGzB+C,EAAKI,gBAAgBD,GAEtB,MAED,QACCH,EAAKP,aAAaxC,EAAM,IAI3B,EACD,CACD,GAEAtB,UAAU,GAAKgE,EAGf,IAAIU,EAAoBpB,EAAIxD,MAAMC,KAAMC,WAIxC,GAFA2D,EAAOc,gBAAgBZ,GAEnBE,EAAMlG,OAAS,EAAG,CAGrB,IADA,IAAI8G,EAAgB,GACXC,EAAI,EAAGA,EAAIb,EAAMlG,OAAQ+G,IACjCD,EAAcjG,KAAK,IAAMqF,EAAMa,GAAK,KAIrC,IADA,IAAIC,EAAWvF,EAAOC,SAASW,iBAAiByE,EAAcjD,KAAK,MAC1DoD,EAAI,EAAGA,EAAID,EAAShH,OAAQiH,IAEpC,IADA,IAAIC,EAAUF,EAASC,GACdE,EAAI,EAAGA,EAAIjB,EAAMlG,OAAQmH,IACjCD,EAAQN,gBAAgBV,EAAMiB,GAGjC,CAGA,OAAON,CACR,CAAE,MAAOO,GAGR,GAFAtB,EAAOc,gBAAgBZ,GAEnBE,EAAMlG,OAAS,EAAG,CAGrB,IADI8G,EAAgB,GACXC,EAAI,EAAGA,EAAIb,EAAMlG,OAAQ+G,IACjCD,EAAcjG,KAAK,IAAMqF,EAAMa,GAAK,KAIrC,IADIC,EAAWvF,EAAOC,SAASW,iBAAiByE,EAAcjD,KAAK,MAC1DoD,EAAI,EAAGA,EAAID,EAAShH,OAAQiH,IAEpC,IADIC,EAAUF,EAASC,GACdE,EAAI,EAAGA,EAAIjB,EAAMlG,OAAQmH,IACjCD,EAAQN,gBAAgBV,EAAMiB,GAGjC,CAEA,IAAIE,EAAe,GACnB,IACC5B,EAAIxD,MAAMC,KAAM,CAAC,4BAClB,CAAE,MAAOoF,GACRD,EAAeC,EAAWC,QACtBF,IACHA,EAAeA,EAAaG,QAAQ,2BAA4B9B,GAElE,CAEK2B,IACJA,EAAe,qDAAuD3B,EAAkB,8BAGzF,IACC,MAAM,IAAI+B,aAAaJ,EACxB,CAAE,MAAO7F,GACR,MAAM,IAAIuD,MAAMsC,EACjB,CACD,CACD,CACD,CACA,CAvmBD,CAumBGhG,qBDvlBY,SAASqG,aAAahG,EAAUiG,GAGxCA,IACJA,EAAU,CAAA,IAGXA,EAAU,CACTC,QAAUD,EAAQC,QAAU,EAC5BC,QAAUF,EAAQE,QAAU,EAC5BC,mBAAoBH,EAAQG,oBAAsB,GAClDC,gBAAkBJ,EAAQI,gBAAkB,IAGrCC,aAAeL,EAAQI,gBAAkB3G,mBAE5C6G,MAAMC,QAAQP,EAAQG,sBAC1BH,EAAQG,mBAAqB,IAG9BH,EAAQG,mBAAqBH,EAAQG,mBAAmBK,OAAO,SAASxD,GACvE,MAAqB,iBAANA,CAChB,GAIAgD,EAAQG,mBAAqBH,EAAQG,mBAAmBM,OAAO,CAAC,SAAU,iBAAkB,YAAa,SAAU,QAAS,QAAS,MAAO,QAAS,iBAAkB,eAAgB,YAAa,WAAY,WAAY,UAAW,YAAa,UAAW,UAAW,OAAQ,QAAS,OAAQ,WAAY,OAAQ,UAAW,UAAW,kBAAmB,cAAe,WAAY,SAAU,cAAe,MAAO,OAAQ,WAAY,WAAY,UAAW,QAAS,MAAO,UAAW,WAAY,WAAY,YAAa,UAAW,eAAgB,MAAO,OAAQ,aAAc,cAAe,aAAc,iBAAkB,aAAc,UAAW,SAAU,OAAQ,OAAQ,WAAY,aAAc,OAAQ,KAAM,aAAc,YAAa,gBAAiB,YAAa,QAAS,WAAY,UAAW,OAAQ,QAAS,OAAQ,WAAY,OAAQ,OAAQ,MAAO,WAAY,MAAO,YAAa,YAAa,QAAS,SAAU,MAAO,WAAY,QAAS,OAAQ,aAAc,OAAQ,UAAW,UAAW,OAAQ,cAAe,SAAU,UAAW,aAAc,WAAY,iBAAkB,MAAO,WAAY,WAAY,OAAQ,UAAW,UAAW,QAAS,SAAU,WAAY,QAAS,OAAQ,QAAS,OAAQ,OAAQ,aAAc,MAAO,SAAU,UAAW,SAAU,QAAS,OAAQ,UAAW,WAAY,SAAU,QAAS,YAAa,OAAQ,SAAU,QAAS,QAAS,SAG52C,IAAMC,EAAgB,GAGhBC,EAAmB5G,EAAS6G,cAAc,KAIhD,GADA,GAAGC,QAAQC,KAAK/G,EAASgH,YAAaC,gBACjChB,EAAQK,aAAb,CASA,GAHAY,kCAGI,qBAAsBvH,KAAM,CAC/B,IAAMwH,EAAmB,IAAIC,iBAAiB,SAASC,GACtDA,EAAcP,QAAQ,SAASQ,GAC9B,GAAGR,QAAQC,KAAKO,EAASC,YAAc,GAAI,SAASzC,GAE7B,IAAlBA,EAAK0C,WAIL1C,EAAK2C,MACRR,eAAenC,EAAK2C,OAIrB3C,EAAK4C,iBAAiB,OAAQ,SAAUC,GACnCA,EAAEC,QAAUD,EAAEC,OAAOH,OACxBR,eAAeU,EAAEC,OAAOH,MAE1B,GACD,GAoJF,GAAGtI,KAAKoB,MACPoG,EACAA,EAAckB,OAAO,GAAGpB,OAAO,SAASqB,GACvC,OAAOA,EAAKC,KAAKC,kBAChBF,EAAKC,KAAKC,iBAAiBC,WAC3BjI,EAASqE,gBAAgB6D,SAASJ,EAAKC,KAAKC,iBAAiBC,UAC/D,IAtJCf,iCACD,EACD,GAEAC,EAAiBgB,QAAQnI,EAAU,CAAEoI,WAAW,EAAMC,SAAS,EAAMC,YAAY,EAAMC,gBAAiBtC,EAAQG,oBACjH,CAmBA,GAhBApG,EAAS0H,iBAAiB,QAASR,iCAAiC,GACpElH,EAAS0H,iBAAiB,OAAQR,iCAAiC,GACnElH,EAAS0H,iBAAiB,QAASR,iCACnClH,EAAS0H,iBAAiB,SAAUR,iCAAiC,GAEjEjB,EAAQC,QACP,mBAAoBlG,GACvBA,EAAS0H,iBAAiB,eAAgBR,iCAAiC,GAC3ElH,EAAS0H,iBAAiB,eAAgBR,iCAAiC,KAE3ElH,EAAS0H,iBAAiB,YAAaR,iCAAiC,GACxElH,EAAS0H,iBAAiB,WAAYR,iCAAiC,KAKrE,mBAAoBsB,QAAU,6BAA8BA,QAAU,mBAAoBA,OAC7F,IAAI,IACMC,EAAT,SAASA,gBAAgBC,EAAOC,GAE/B,GAAID,EAAME,eAAeD,GAAW,CACnC,IAAME,EAAaL,OAAOM,yBAAyBJ,EAAOC,GACtDE,GAAcA,EAAWE,cAAgB,QAASF,GACrDL,OAAOQ,eAAeN,EAAOC,EAAU,CACtCI,aAAcF,EAAWE,aACzBE,WAAYJ,EAAWI,WACvBC,IAAK,SAALA,MACC,OAAOL,EAAWK,IAAI3I,MAAMC,KAAMC,UACnC,EACA0I,IAAK,SAALA,MACCN,EAAWM,IAAI5I,MAAMC,KAAMC,WAE3B,IACCyG,iCACD,CAAE,MAAOpH,GAER,CAEF,GAGH,CACD,EAEI,gBAAiBH,MAAQyJ,YAAY/I,WACxCoI,EAAgBW,YAAY/I,UAAW,YAKxC,CAAC,UAAW,WAAY,WAAY,YAAYyG,QAAQ,SAAS6B,GAChE,CACC,oBACA,sBACA,mBACA,mBACA,sBACA,oBACA,oBACA,sBACA,oBACA,uBACC7B,QAAQ,SAASuC,GACdA,KAAe1J,MAAQA,KAAK0J,GAAahJ,WAC5CoI,EAAgB9I,KAAK0J,GAAahJ,UAAWsI,EAE/C,EACD,EACD,CAAE,MAAOhB,GACJ1B,EAAQE,OAEXmD,QAAQC,MAAM5B,EAEhB,CAGD,IAAI6B,GAAsC,CAhH1C,CAiHA,SAAStC,kCACJsC,GACHC,qBAAqBD,GAGtBA,EAAsCE,sBAAsB,WAO5D/C,EAAcG,QAAQ,SAASgB,GAC9B,IAAMjE,EAAQ,GAEVjD,EAAU,GACd,GAAIkH,EAAK6B,SACR,IACC/I,EAAUZ,EAASW,iBAAiBmH,EAAK6B,SAC1C,CAAE,MAAOhC,GAKR,YAJI1B,EAAQE,OAEXmD,QAAQC,MAAM5B,GAGhB,CAGD,GAAGb,QAAQC,KAAKnG,EAAS,SAAS4E,GAEjC3B,EAAM1E,KAAKqG,GAIXoB,EAAiBgD,UAAY,MAAQ9B,EAAK+B,cAAgB,IAE1DrE,EAAQsE,iBAAiBlD,EAAiBmD,SAAS,GAAGzB,WAAW,GAAG0B,aAGpEhK,EAASqE,gBAAgB4F,MAAMC,KAAO,EAAGlK,EAASqE,gBAAgB4F,MAAMC,KAAO,IAChF,GAGApC,EAAKjE,MAAMiD,QAAQ,SAAShC,IACC,IAAxBjB,EAAMxE,QAAQyF,KACjBA,EAAKI,gBAAgB4C,EAAK+B,eAG1B7J,EAASqE,gBAAgB4F,MAAMC,KAAO,EAAGlK,EAASqE,gBAAgB4F,MAAMC,KAAO,KAEjF,GAGApC,EAAKjE,MAAQA,CACd,EA/CA,EACD,CA8DA,SAASoD,eAAekD,GACvB,IAEC,IAAK,IAAI9L,EAAK8L,EAAWC,SAAS9L,OAAS,EAAID,GAAK,EAAGA,IAAK,CAC3D,IAAI0J,EAAOoC,EAAWC,SAAS/L,GAE/B,GAAI0J,EAAKsC,aAAc,CACtBtC,EAAKsC,aAAetC,EAAKsC,aAAavE,QAAQ,qBAAsB,IAEpE,IAEC,IAAMwE,EAAe5L,wBAAwBqJ,EAAKsC,aAAa5K,YAC/D,GAA4B,IAAxB6K,EAAahM,OAChB,SAGD,IAAK2H,EAAQK,aAAc,CAC1B6D,EAAWI,WAAWlM,GACtB,QACD,CAEA,IAAK,IAAIgH,EAAI,EAAGA,EAAIiF,EAAahM,OAAQ+G,IAAK,CAC7C,IAAMmF,EAAcF,EAAajF,GAC7BmF,GACH7D,EAAcxH,KAAK,CAClB4I,KAAMA,EACN4B,SAAUa,EACVX,cAAevK,UAAUkL,GACzB3G,MAAO,IAGV,CACD,CAAE,MAAO8D,GACJ1B,EAAQE,OAEXmD,QAAQC,MAAM5B,EAEhB,CACD,MACCV,eAAec,EAEjB,CACD,CAAE,MAAOJ,GACJ1B,EAAQE,OAEXmD,QAAQC,MAAM5B,EAEhB,CACD,CACD", "x_google_ignoreList": [4]}