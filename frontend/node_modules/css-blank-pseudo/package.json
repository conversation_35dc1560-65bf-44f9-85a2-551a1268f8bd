{"name": "css-blank-pseudo", "description": "Style form elements when they are empty", "version": "7.0.1", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}, "./browser": {"import": "./dist/browser.mjs", "require": "./dist/browser.cjs", "default": "./dist/browser.mjs"}, "./browser-global": {"default": "./dist/browser-global.js"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-selector-parser": "^7.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/css-blank-pseudo#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/css-blank-pseudo"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["a11y", "accessibility", "blank", "css", "empty", "input", "javascript", "js", "polyfill", "postcss", "postcss-plugin", "pseudo", "select", "selectors", "textarea"]}