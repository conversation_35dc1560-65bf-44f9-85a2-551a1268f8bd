{"version": 3, "file": "browser.mjs", "sources": ["../src/is-valid-replacement.mjs", "../src/browser.js"], "sourcesContent": ["const INVALID_SELECTOR_CHAR = [\n\t' ', // Can't use child selector\n\t'>', // Can't use direct child selector\n\t'~', // Can't use sibling selector\n\t':', // Can't use pseudo selector\n\t'+', // Can't use adjacent selector\n\t'@', // Can't use at\n\t'#', // Can't use id selector\n\t'(', // Can't use parenthesis\n\t')', // Can't use parenthesis\n];\n\nexport default function isValidReplacement(selector) {\n\tlet isValid = true;\n\n\t// Purposely archaic so it's interoperable in old browsers\n\tfor (let i = 0, length = INVALID_SELECTOR_CHAR.length; i < length && isValid; i++) {\n\t\tif (selector.indexOf(INVALID_SELECTOR_CHAR[i]) > -1) {\n\t\t\tisValid = false;\n\t\t}\n\t}\n\n\treturn isValid;\n}\n", "import isValidReplacement from './is-valid-replacement.mjs';\n\nconst CSS_CLASS_LOADED = 'js-blank-pseudo';\n\n// form control elements selector\nfunction isFormControlElement(element) {\n\tif (element.nodeName === 'INPUT' || element.nodeName === 'SELECT' || element.nodeName === 'TEXTAREA') {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\nfunction createNewEvent(eventName) {\n\tlet event;\n\n\tif (typeof(Event) === 'function') {\n\t\tevent = new Event(eventName, { bubbles: true });\n\t} else {\n\t\tevent = document.createEvent('Event');\n\t\tevent.initEvent(eventName, true, false);\n\t}\n\n\treturn event;\n}\n\nfunction generateHandler(replaceWith) {\n\tlet selector;\n\tlet remove;\n\tlet add;\n\n\tif (replaceWith[0] === '.') {\n\t\tselector = replaceWith.slice(1);\n\t\tremove = (el) => el.classList.remove(selector);\n\t\tadd = (el) => el.classList.add(selector);\n\t} else {\n\t\t// A bit naive\n\t\tselector = replaceWith.slice(1, -1);\n\t\tremove = (el) => el.removeAttribute(selector, '');\n\t\tadd = (el) => el.setAttribute(selector, '');\n\t}\n\n\treturn function handleInputOrChangeEvent(event) {\n\t\tconst element = event.target;\n\t\tif (!isFormControlElement(element)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst isSelect = element.nodeName === 'SELECT';\n\t\tconst hasValue = isSelect\n\t\t\t? !!element.options[element.selectedIndex]?.value\n\t\t\t: !!element.value;\n\n\t\tif (hasValue) {\n\t\t\tremove(element);\n\t\t} else {\n\t\t\tadd(element);\n\t\t}\n\t};\n}\n\n// observe changes to the \"selected\" property on an HTML Element\nfunction observeSelectedOfHTMLElement(HTMLElement) {\n\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'selected');\n\tconst nativeSet = descriptor.set;\n\n\tdescriptor.set = function set(value) { // eslint-disable-line no-unused-vars\n\t\tnativeSet.apply(this, arguments);\n\n\t\tconst event = createNewEvent('change');\n\t\tthis.parentElement.dispatchEvent(event);\n\t};\n\n\tObject.defineProperty(HTMLElement.prototype, 'selected', descriptor);\n}\n\n// observe changes to the \"value\" property on an HTML Element\nfunction observeValueOfHTMLElement(HTMLElement, handler) {\n\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'value');\n\tconst nativeSet = descriptor.set;\n\n\tdescriptor.set = function set() {\n\t\tnativeSet.apply(this, arguments);\n\t\thandler({ target: this });\n\t};\n\n\tObject.defineProperty(HTMLElement.prototype, 'value', descriptor);\n}\n\nexport default function cssBlankPseudoInit(opts) {\n\t// configuration\n\tconst options = {\n\t\tforce: false,\n\t\treplaceWith: '[blank]',\n\t};\n\n\tif (typeof opts !== 'undefined' && 'force' in opts) {\n\t\toptions.force = opts.force;\n\t}\n\n\tif (typeof opts !== 'undefined' && 'replaceWith' in opts) {\n\t\toptions.replaceWith = opts.replaceWith;\n\t}\n\n\tif (!isValidReplacement(options.replaceWith)) {\n\t\tthrow new Error(`${options.replaceWith} is not a valid replacement since it can't be applied to single elements.`);\n\t}\n\n\ttry {\n\t\tdocument.querySelector(':blank');\n\n\t\tif (!options.force) {\n\t\t\treturn;\n\t\t}\n\t} catch (_) {}\n\n\tconst handler = generateHandler(options.replaceWith);\n\tconst bindEvents = () => {\n\t\tif (document.body) {\n\t\t\tdocument.body.addEventListener('change', handler);\n\t\t\tdocument.body.addEventListener('input', handler);\n\t\t}\n\t};\n\tconst updateAllCandidates = () => {\n\t\tArray.prototype.forEach.call(\n\t\t\tdocument.querySelectorAll('input, select, textarea'),\n\t\t\tnode => {\n\t\t\t\thandler({ target: node });\n\t\t\t},\n\t\t);\n\t};\n\n\tif (document.body) {\n\t\tbindEvents();\n\t} else {\n\t\twindow.addEventListener('load', bindEvents);\n\t}\n\n\tif (document.documentElement.className.indexOf(CSS_CLASS_LOADED) === -1) {\n\t\tdocument.documentElement.className += ` ${CSS_CLASS_LOADED}`;\n\t}\n\n\tobserveValueOfHTMLElement(self.HTMLInputElement, handler);\n\tobserveValueOfHTMLElement(self.HTMLSelectElement, handler);\n\tobserveValueOfHTMLElement(self.HTMLTextAreaElement, handler);\n\tobserveSelectedOfHTMLElement(self.HTMLOptionElement);\n\n\t// conditionally update all form control elements\n\tupdateAllCandidates();\n\n\tif (typeof self.MutationObserver !== 'undefined') {\n\t\t// conditionally observe added or unobserve removed form control elements\n\t\tnew MutationObserver(mutationsList => {\n\t\t\tmutationsList.forEach(mutation => {\n\t\t\t\tArray.prototype.forEach.call(\n\t\t\t\t\tmutation.addedNodes || [],\n\t\t\t\t\tnode => {\n\t\t\t\t\t\tif (node.nodeType === 1 && isFormControlElement(node)) {\n\t\t\t\t\t\t\thandler({ target: node });\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t);\n\t\t\t});\n\t\t}).observe(document, { childList: true, subtree: true });\n\t} else {\n\t\tconst handleOnLoad = () => updateAllCandidates();\n\n\t\twindow.addEventListener('load', handleOnLoad);\n\t\twindow.addEventListener('DOMContentLoaded', handleOnLoad);\n\t}\n}\n"], "names": ["INVALID_SELECTOR_CHAR", "isValidReplacement", "selector", "<PERSON><PERSON><PERSON><PERSON>", "i", "length", "indexOf", "CSS_CLASS_LOADED", "isFormControlElement", "element", "nodeName", "createNewEvent", "eventName", "event", "Event", "bubbles", "document", "createEvent", "initEvent", "observeValueOfHTMLElement", "HTMLElement", "handler", "descriptor", "Object", "getOwnPropertyDescriptor", "prototype", "nativeSet", "set", "apply", "this", "arguments", "target", "defineProperty", "cssBlankPseudoInit", "opts", "options", "force", "replaceWith", "Error", "querySelector", "_", "remove", "add", "slice", "el", "classList", "removeAttribute", "setAttribute", "handleInputOrChangeEvent", "_element$options$elem", "selectedIndex", "value", "bindEvents", "body", "addEventListener", "updateAllCandidates", "Array", "for<PERSON>ach", "call", "querySelectorAll", "node", "window", "documentElement", "className", "self", "HTMLInputElement", "HTMLSelectElement", "HTMLTextAreaElement", "HTMLOptionElement", "parentElement", "dispatchEvent", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "observe", "childList", "subtree", "handleOnLoad"], "mappings": "AAAA,IAAMA,EAAwB,CAC7B,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAGc,SAASC,mBAAmBC,GAI1C,IAHA,IAAIC,GAAU,EAGLC,EAAI,EAAGC,EAASL,EAAsBK,OAAQD,EAAIC,GAAUF,EAASC,IACzEF,EAASI,QAAQN,EAAsBI,KAAO,IACjDD,GAAU,GAIZ,OAAOA,CACR,CCrBA,IAAMI,EAAmB,kBAGzB,SAASC,qBAAqBC,GAC7B,MAAyB,UAArBA,EAAQC,UAA6C,WAArBD,EAAQC,UAA8C,aAArBD,EAAQC,QAK9E,CAEA,SAASC,eAAeC,GACvB,IAAIC,EASJ,MAPsB,mBAAXC,MACVD,EAAQ,IAAIC,MAAMF,EAAW,CAAEG,SAAS,KAExCF,EAAQG,SAASC,YAAY,UACvBC,UAAUN,GAAW,GAAM,GAG3BC,CACR,CAqDA,SAASM,0BAA0BC,EAAaC,GAC/C,IAAMC,EAAaC,OAAOC,yBAAyBJ,EAAYK,UAAW,SACpEC,EAAYJ,EAAWK,IAE7BL,EAAWK,IAAM,SAASA,MACzBD,EAAUE,MAAMC,KAAMC,WACtBT,EAAQ,CAAEU,OAAQF,QAGnBN,OAAOS,eAAeZ,EAAYK,UAAW,QAASH,EACvD,CAEe,SAASW,mBAAmBC,GAE1C,IAAMC,EAAU,CACfC,OAAO,EACPC,YAAa,WAWd,QARoB,IAATH,GAAwB,UAAWA,IAC7CC,EAAQC,MAAQF,EAAKE,YAGF,IAATF,GAAwB,gBAAiBA,IACnDC,EAAQE,YAAcH,EAAKG,cAGvBpC,mBAAmBkC,EAAQE,aAC/B,MAAM,IAAIC,MAASH,EAAQE,yFAG5B,IAGC,GAFArB,SAASuB,cAAc,WAElBJ,EAAQC,MACZ,MAEF,CAAE,MAAOI,GAAI,CAEb,IA1FwBH,EACpBnC,EACAuC,EACAC,EAiCiCtB,EAC/BE,EACAI,EAoDAL,GArFiB,OALCgB,EA0FQF,EAAQE,aArFxB,IACfnC,EAAWmC,EAAYM,MAAM,GAC7BF,EAAS,SAATA,OAAUG,GAAE,OAAKA,EAAGC,UAAUJ,OAAOvC,EAAS,EAC9CwC,EAAM,SAANA,IAAOE,GAAE,OAAKA,EAAGC,UAAUH,IAAIxC,EAAS,IAGxCA,EAAWmC,EAAYM,MAAM,GAAI,GACjCF,EAAS,SAATA,OAAUG,GAAE,OAAKA,EAAGE,gBAAgB5C,EAAU,GAAG,EACjDwC,EAAM,SAANA,IAAOE,GAAE,OAAKA,EAAGG,aAAa7C,EAAU,GAAG,GAGrC,SAAS8C,yBAAyBnC,GAAO,IAAAoC,EACzCxC,EAAUI,EAAMkB,OACjBvB,qBAAqBC,MAIY,WAArBA,EAAQC,SAEkB,OAAvCuC,EAACxC,EAAQ0B,QAAQ1B,EAAQyC,iBAAxBD,EAAwCE,MACxC1C,EAAQ0C,OAGZV,EAAOhC,GAEPiC,EAAIjC,MA6DA2C,EAAa,SAAbA,aACDpC,SAASqC,OACZrC,SAASqC,KAAKC,iBAAiB,SAAUjC,GACzCL,SAASqC,KAAKC,iBAAiB,QAASjC,KAGpCkC,EAAsB,SAAtBA,sBACLC,MAAM/B,UAAUgC,QAAQC,KACvB1C,SAAS2C,iBAAiB,4BAC1B,SAAAC,GACCvC,EAAQ,CAAEU,OAAQ6B,GACnB,KAsBF,GAlBI5C,SAASqC,KACZD,IAEAS,OAAOP,iBAAiB,OAAQF,IAGqC,IAAlEpC,SAAS8C,gBAAgBC,UAAUzD,QAAQC,KAC9CS,SAAS8C,gBAAgBC,eAAiBxD,GAG3CY,0BAA0B6C,KAAKC,iBAAkB5C,GACjDF,0BAA0B6C,KAAKE,kBAAmB7C,GAClDF,0BAA0B6C,KAAKG,oBAAqB9C,GAlFfD,EAmFR4C,KAAKI,kBAlF5B9C,EAAaC,OAAOC,yBAAyBJ,EAAYK,UAAW,YACpEC,EAAYJ,EAAWK,IAE7BL,EAAWK,IAAM,SAASA,IAAIwB,GAC7BzB,EAAUE,MAAMC,KAAMC,WAEtB,IAAMjB,EAAQF,eAAe,UAC7BkB,KAAKwC,cAAcC,cAAczD,IAGlCU,OAAOS,eAAeZ,EAAYK,UAAW,WAAYH,GA2EzDiC,IAEqC,oBAA1BS,KAAKO,iBAEf,IAAIA,kBAAiB,SAAAC,GACpBA,EAAcf,SAAQ,SAAAgB,GACrBjB,MAAM/B,UAAUgC,QAAQC,KACvBe,EAASC,YAAc,IACvB,SAAAd,GACuB,IAAlBA,EAAKe,UAAkBnE,qBAAqBoD,IAC/CvC,EAAQ,CAAEU,OAAQ6B,GAEpB,GAEF,GACD,IAAGgB,QAAQ5D,SAAU,CAAE6D,WAAW,EAAMC,SAAS,QAC3C,CACN,IAAMC,EAAe,SAAfA,eAAY,OAASxB,GAAqB,EAEhDM,OAAOP,iBAAiB,OAAQyB,GAChClB,OAAOP,iBAAiB,mBAAoByB,EAC7C,CACD"}