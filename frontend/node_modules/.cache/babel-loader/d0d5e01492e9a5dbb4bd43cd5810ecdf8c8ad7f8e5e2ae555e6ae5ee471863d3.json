{"ast": null, "code": "import{configureStore}from'@reduxjs/toolkit';import{lessonSlice}from'./lessonSlice';export const store=configureStore({reducer:{lessons:lessonSlice.reducer}});", "map": {"version": 3, "names": ["configureStore", "lessonSlice", "store", "reducer", "lessons"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport { lessonSlice } from './lessonSlice';\n\nexport const store = configureStore({\n  reducer: {\n    lessons: lessonSlice.reducer\n  }\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch; "], "mappings": "AAAA,OAASA,cAAc,KAAQ,kBAAkB,CACjD,OAASC,WAAW,KAAQ,eAAe,CAE3C,MAAO,MAAM,CAAAC,KAAK,CAAGF,cAAc,CAAC,CAClCG,OAAO,CAAE,CACPC,OAAO,CAAEH,WAAW,CAACE,OACvB,CACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}