{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/App.tsx\";\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\n// ... existing imports ...\nimport HomePage from './components/Home/HomePage';\nimport { VideoPreview } from './components/VideoPreview/VideoPreview';\nimport { CheckoutPage } from './components/Checkout/CheckoutPage';\nimport { VideoCreationPage } from './components/VideoCreation/CreateVideoPage';\nimport ManualVideoCreationPage from './components/ManualVideoCreation/ManualVideoCreationPage'; // Import the manual creation page component\nimport SamplesPage from './components/Samples/SamplesPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/create-video\",\n          element: /*#__PURE__*/_jsxDEV(VideoCreationPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/checkout\",\n          element: /*#__PURE__*/_jsxDEV(CheckoutPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/preview/:videoId\",\n          element: /*#__PURE__*/_jsxDEV(VideoPreview, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/manual-create-video\",\n          element: /*#__PURE__*/_jsxDEV(ManualVideoCreationPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/samples\",\n          element: /*#__PURE__*/_jsxDEV(SamplesPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Provider", "store", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "HomePage", "VideoPreview", "CheckoutPage", "VideoCreationPage", "ManualVideoCreationPage", "SamplesPage", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\n// ... existing imports ...\nimport  HomePage  from './components/Home/HomePage';\nimport { VideoPreview } from './components/VideoPreview/VideoPreview';\nimport { CheckoutPage } from './components/Checkout/CheckoutPage';\nimport  { VideoCreationPage }  from './components/VideoCreation/CreateVideoPage';\nimport  ManualVideoCreationPage  from './components/ManualVideoCreation/ManualVideoCreationPage'; // Import the manual creation page component\nimport SamplesPage from './components/Samples/SamplesPage';\nimport { ProfilePage } from './components/Profile/ProfilePage';\n\n\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <BrowserRouter>\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/create-video\" element={<VideoCreationPage />} />\n          <Route path=\"/checkout\" element={<CheckoutPage />} />\n          <Route path=\"/preview/:videoId\" element={<VideoPreview />} />\n          <Route path=\"/manual-create-video\" element={<ManualVideoCreationPage />} />\n          <Route path=\"/samples\" element={<SamplesPage />} />\n        </Routes>\n      </BrowserRouter>\n    </Provider>\n  );\n}\n\nexport default App; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D;AACA,OAAQC,QAAQ,MAAO,4BAA4B;AACnD,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAAUC,iBAAiB,QAAS,4CAA4C;AAChF,OAAQC,uBAAuB,MAAO,0DAA0D,CAAC,CAAC;AAClG,OAAOC,WAAW,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAK3D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACZ,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAa,QAAA,eACrBF,OAAA,CAACV,aAAa;MAAAY,QAAA,eACZF,OAAA,CAACT,MAAM;QAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACP,QAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,eAAe;UAACC,OAAO,eAAEJ,OAAA,CAACJ,iBAAiB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACL,YAAY;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEJ,OAAA,CAACN,YAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DR,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEJ,OAAA,CAACH,uBAAuB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3ER,OAAA,CAACR,KAAK;UAACW,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEJ,OAAA,CAACF,WAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEf;AAACC,EAAA,GAfQR,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}