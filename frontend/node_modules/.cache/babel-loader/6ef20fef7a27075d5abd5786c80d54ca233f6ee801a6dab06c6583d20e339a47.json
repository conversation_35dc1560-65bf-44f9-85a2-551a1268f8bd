{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/GyroscopePage.tsx\";\nimport React from 'react';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport './GyroscopePage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GyroscopePage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gyroscope-page\",\n    children: /*#__PURE__*/_jsxDEV(Gyroscope, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = GyroscopePage;\nexport default GyroscopePage;\nvar _c;\n$RefreshReg$(_c, \"GyroscopePage\");", "map": {"version": 3, "names": ["React", "Gyroscope", "jsxDEV", "_jsxDEV", "GyroscopePage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/GyroscopePage.tsx"], "sourcesContent": ["import React from 'react';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport './GyroscopePage.css';\n\nconst GyroscopePage = () => {\n  return (\n    <div className=\"gyroscope-page\">\n      <Gyroscope />\n    </div>\n  );\n};\n\nexport default GyroscopePage;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BH,OAAA,CAACF,SAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACC,EAAA,GANIP,aAAa;AAQnB,eAAeA,aAAa;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}