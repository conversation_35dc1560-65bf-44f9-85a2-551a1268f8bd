{"ast": null, "code": "// HyperOrangeTransition.tsx\nimport React,{useRef,useEffect}from'react';import{useNavigate}from'react-router-dom';import{gsap}from'gsap';import{MotionPathPlugin}from'gsap/MotionPathPlugin';import'./HyperOrangeTransition.css';// Register the plugin\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";gsap.registerPlugin(MotionPathPlugin);const HyperOrangeTransition=_ref=>{let{isActive,to,onComplete,buttonRect}=_ref;const navigate=useNavigate();const ballRef=useRef(null);const lineRef=useRef(null);const starsRef=useRef([]);const animation=useRef(null);const originalOverflow=useRef(document.body.style.overflow);const dropTrail=(originalLine,opacity)=>{if(!originalLine)return;const trail=originalLine.cloneNode(true);trail.classList.add('line-clone','line-trail');trail.style.opacity=opacity.toString();trail.style.position='fixed';trail.style.left=originalLine.style.left;trail.style.transform=originalLine.style.transform;document.body.appendChild(trail);gsap.to(trail,{opacity:0,duration:0.3,delay:0.2,onComplete:()=>{if(trail.parentNode){trail.parentNode.removeChild(trail);}}});};useEffect(()=>{if(isActive&&buttonRect){originalOverflow.current=document.body.style.overflow;document.body.style.overflow='hidden';const startTransition=()=>{const backgroundImage=document.getElementById('background-image');const contentSections=document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');const mainContent=document.querySelector('main, .hero-section, .features-section');gsap.to(contentSections,{opacity:0,duration:0.5,ease:'power2.out',stagger:0.05});if(mainContent){mainContent.style.transition='opacity 0.3s ease';setTimeout(()=>{mainContent.style.opacity='0.3';},500);}// Create stars\nconst createStars=()=>{const stars=[];const numStars=8;// More stars since they're smaller\nfor(let i=0;i<numStars;i++){const star=document.createElement('div');star.className='transition-star';star.style.cssText=\"\\n              position: fixed;\\n              width: 3px;\\n              height: 3px;\\n              background: #ffffff;\\n              border-radius: 50%;\\n              opacity: 0 !important;\\n              visibility: hidden;\\n              z-index: 1002;\\n              box-shadow: \\n                0 0 4px 1px rgba(255, 255, 255, 0.9),\\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\\n            \";// Completely random positioning across the screen\nconst x=Math.random()*window.innerWidth;const y=Math.random()*window.innerHeight*0.6;// Top 60% of screen\nstar.style.left=x+'px';star.style.top=y+'px';star.style.transform='translate(-50%, -50%)';document.body.appendChild(star);stars.push(star);starsRef.current.push(star);// Set GSAP initial state to ensure they're completely hidden\ngsap.set(star,{opacity:0,visibility:'hidden',scale:0});}return stars;};const stars=createStars();const startX=buttonRect?buttonRect.left+buttonRect.width/2:window.innerWidth/2;const startY=buttonRect?buttonRect.top+buttonRect.height/2:window.innerHeight/2;const targetX=window.innerWidth/2;gsap.set(ballRef.current,{x:startX,y:startY,xPercent:-50,yPercent:-50,position:'fixed',opacity:1,scale:1});console.log('Ball starting position:',startX,startY);console.log('Ball target position:',targetX,15);console.log('Ball element:',ballRef.current);// Create firefly path with a graceful loop\nconst createFireflyPath=()=>{const pathWidth=window.innerWidth*0.3;// 30% of screen width for the loop\nconst loopCenterX=startX+(targetX-startX)*0.4;// Loop positioned 40% of the way\nconst loopCenterY=startY-window.innerHeight*0.35;const loopRadius=Math.min(pathWidth*0.3,120);// Reasonable loop size\nreturn[{x:startX,y:startY},// Start at button\n{x:startX+100,y:startY-150},// Rise up and slightly right\n{x:loopCenterX-loopRadius,y:loopCenterY},// Approach loop from left\n{x:loopCenterX,y:loopCenterY-loopRadius},// Top of loop\n{x:loopCenterX+loopRadius,y:loopCenterY},// Right side of loop\n{x:loopCenterX,y:loopCenterY+loopRadius},// Bottom of loop\n{x:loopCenterX-loopRadius*0.5,y:loopCenterY},// Exit loop\n{x:targetX-100,y:15+100},// Approach final position\n{x:targetX,y:15}// Final position at top center\n];};const fireflyPath=createFireflyPath();console.log('Firefly path points:',fireflyPath);gsap.set(lineRef.current,{scaleY:0,transformOrigin:'top center',position:'fixed',left:window.innerWidth/2,top:0,transform:'translateX(-50%) translateY(0%)',// Start at absolute top\nzIndex:10000,opacity:1});// Create carousels container\n// const createCarousels = () => {\n//   const container = document.createElement('div');\n//   container.className = 'carousels-container';\n//   container.style.cssText = `\n//     position: fixed;\n//     top: 0;\n//     left: 0;\n//     width: 100%;\n//     height: 100%;\n//     display: flex;\n//     justify-content: space-between;\n//     padding: 20px;\n//     opacity: 0;\n//     z-index: 1000;\n//   `;\n//   // Create 8 carousels (one for each grade)\n//   for (let i = 1; i <= 8; i++) {\n//     const carousel = document.createElement('div');\n//     carousel.className = `carousel grade-${i}`;\n//     carousel.style.cssText = `\n//       width: calc(100% / 8 - 10px);\n//       height: 100%;\n//       background: rgba(0, 0, 0, 0.2);\n//       border-radius: 8px;\n//       overflow: hidden;\n//       position: relative;\n//       opacity: 0;\n//       transform: translateY(20px);\n//     `;\n//     // Add grade label at the top of each carousel\n//     const label = document.createElement('div');\n//     label.className = 'carousel-label';\n//     label.textContent = `Grade ${i}`;\n//     label.style.cssText = `\n//       position: absolute;\n//       top: 10px;\n//       left: 0;\n//       right: 0;\n//       text-align: center;\n//       color: white;\n//       font-weight: bold;\n//       text-shadow: 0 1px 3px rgba(0,0,0,0.5);\n//       z-index: 2;\n//       opacity: 0;\n//     `;\n//     // Add a placeholder for the video carousel content\n//     const content = document.createElement('div');\n//     content.className = 'carousel-content';\n//     content.style.cssText = `\n//       width: 100%;\n//       height: 100%;\n//       display: flex;\n//       flex-direction: column;\n//       align-items: center;\n//       justify-content: center;\n//       padding-top: 40px;\n//       color: white;\n//       font-size: 14px;\n//     `;\n//     // Add a placeholder for the video thumbnail\n//     const thumbnail = document.createElement('div');\n//     thumbnail.className = 'video-thumbnail';\n//     thumbnail.style.cssText = `\n//       width: 80%;\n//       aspect-ratio: 16/9;\n//       background: rgba(255,255,255,0.1);\n//       margin: 10px 0;\n//       border-radius: 4px;\n//       display: flex;\n//       align-items: center;\n//       justify-content: center;\n//       font-size: 12px;\n//       color: rgba(255,255,255,0.7);\n//     `;\n//     thumbnail.textContent = 'Video Preview';\n//     content.appendChild(thumbnail);\n//     carousel.appendChild(label);\n//     carousel.appendChild(content);\n//     container.appendChild(carousel);\n//   }\n//   document.body.appendChild(container);\n//   return container;\n// };\n// const carouselsContainer = createCarousels();\n// const carousels = Array.from(carouselsContainer.querySelectorAll('.carousel'));\n// Clean up on completion\nanimation.current=gsap.timeline({onComplete:()=>{// Clean up any existing elements\nconst clones=document.querySelectorAll('.line-clone');clones.forEach(clone=>{var _clone$parentNode;return(_clone$parentNode=clone.parentNode)===null||_clone$parentNode===void 0?void 0:_clone$parentNode.removeChild(clone);});// Clean up stars\nstarsRef.current.forEach(star=>{if(star.parentNode){star.parentNode.removeChild(star);}});starsRef.current=[];// Fade in carousels\n//     gsap.to(carouselsContainer, {\n//       opacity: 1,\n//       duration: 0.5,\n//       onComplete: () => {\n//         // Animate in each carousel with a slight delay\n//         carousels.forEach((carousel, index) => {\n//           const label = carousel.querySelector('.carousel-label');\n//           gsap.to(carousel, {\n//             opacity: 1,\n//             y: 0,\n//             duration: 0.5,\n//             delay: index * 0.05,\n//             ease: 'power2.out',\n//             onComplete: () => {\n//               // Animate in the label after the carousel appears\n//               if (label) {\n//                 gsap.to(label, {\n//                   opacity: 1,\n//                   y: 0,\n//                   duration: 0.3,\n//                   ease: 'power2.out'\n//                 });\n//               }\n//             }\n//           });\n//         });\n//         // Navigate after the carousels have appeared\nnavigate(to);//         onComplete?.();\n//       }\n//     });\n}});animation.current// Background movement - split into two parts: movement + scaling, then just movement\n.to(backgroundImage,{// First half: move from -65% to -37.5% AND scale from 1.0 to 0.7\nkeyframes:{\"0%\":{transform:\"translateY(-65%) scale(1.0)\"},\"50%\":{transform:\"translateY(-37.5%) scale(0.68)\"},\"100%\":{transform:\"translateY(-10%) scale(0.68)\"}// Second half: just movement, no more scaling\n},duration:3.5,ease:'power1.inOut',transformOrigin:'center center'},0)// Ball animation with elegant firefly path including loop\n.to(ballRef.current,{motionPath:{path:fireflyPath,curviness:1.5,// Smooth curves but not too dramatic\nautoRotate:false},duration:2.8,// Slightly longer to accommodate the loop\nease:'sine.inOut',scale:0.9,onStart:function(){console.log('Ball animation started with firefly loop path');}},0.3)// Line appears when ball reaches near top\n.to(lineRef.current,{scaleY:1,duration:1.2,ease:'power2.out'},2.9)// Start line animation when ball is near top\n// Line split - starts immediately after line finishes drawing (2.9 + 1.2 = 4.1)\n.add(()=>{if(!lineRef.current)return;const rightLine=lineRef.current.cloneNode(true);rightLine.classList.add('line-clone');rightLine.style.position='absolute';rightLine.style.left='50%';rightLine.style.transform='translateX(-50%) translateY(0%)';document.body.appendChild(rightLine);gsap.to(lineRef.current,{x:-window.innerWidth/2,opacity:0,duration:0.6,ease:'power2.inOut',onUpdate:function(){const progress=this.ratio;if(progress>0.25&&!this._dropped25&&lineRef.current){this._dropped25=true;dropTrail(lineRef.current,0.75);}if(progress>0.5&&!this._dropped50&&lineRef.current){this._dropped50=true;dropTrail(lineRef.current,0.5);}if(progress>0.75&&!this._dropped75&&lineRef.current){this._dropped75=true;dropTrail(lineRef.current,0.25);}}});gsap.to(rightLine,{x:window.innerWidth/2,opacity:0,duration:0.6,ease:'power2.inOut',onUpdate:function(){const progress=this.ratio;if(progress>0.25&&!this._dropped25){this._dropped25=true;dropTrail(rightLine,0.75);}if(progress>0.5&&!this._dropped50){this._dropped50=true;dropTrail(rightLine,0.5);}if(progress>0.75&&!this._dropped75){this._dropped75=true;dropTrail(rightLine,0.25);}},onComplete:()=>{if(rightLine.parentNode){rightLine.parentNode.removeChild(rightLine);}}});},4.1)// Exactly when line drawing finishes\n// Stars appear towards the end of firefly animation\n.to(stars,{opacity:1,visibility:'visible',scale:1,duration:0.4,ease:'back.out(1.7)',stagger:0.08,onStart:function(){console.log('Stars animation started');},onComplete:function(){console.log('Stars animation completed');// Add the CSS animation back after GSAP animation completes\nstars.forEach(star=>{star.style.animation='starTwinkle 2s ease-in-out infinite alternate';});}},2.6)// Near the end of firefly animation (firefly ends around 2.8-3.1)\n// Flash effect - happens during line split\n.to([ballRef.current,lineRef.current],{opacity:0.8,duration:0.1,repeat:6,yoyo:true,ease:'power1.inOut'},4.2)// Final fade out\n.to([ballRef.current,lineRef.current],{opacity:0,duration:0.6,ease:'power2.inOut'},4.8);return()=>{if(animation.current)animation.current.kill();if(mainContent){mainContent.style.opacity='';mainContent.style.transition='';}if(backgroundImage){backgroundImage.style.transform='translateY(-62%)';// Reset to original\n}// Clean up stars\nstarsRef.current.forEach(star=>{if(star.parentNode){star.parentNode.removeChild(star);}});starsRef.current=[];document.body.style.overflow=originalOverflow.current;};};const cleanup=startTransition();return cleanup;}},[isActive,buttonRect,navigate,onComplete,to]);if(!isActive)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"transition-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"transition-ball\",ref:ballRef}),/*#__PURE__*/_jsx(\"div\",{className:\"transition-line\",ref:lineRef})]});};export default HyperOrangeTransition;", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useNavigate", "gsap", "MotionPathPlugin", "jsx", "_jsx", "jsxs", "_jsxs", "registerPlugin", "HyperOrangeTransition", "_ref", "isActive", "to", "onComplete", "buttonRect", "navigate", "ballRef", "lineRef", "starsRef", "animation", "originalOverflow", "document", "body", "style", "overflow", "dropTrail", "originalLine", "opacity", "trail", "cloneNode", "classList", "add", "toString", "position", "left", "transform", "append<PERSON><PERSON><PERSON>", "duration", "delay", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "current", "startTransition", "backgroundImage", "getElementById", "contentSections", "querySelectorAll", "mainContent", "querySelector", "ease", "stagger", "transition", "setTimeout", "createStars", "stars", "numStars", "i", "star", "createElement", "className", "cssText", "x", "Math", "random", "window", "innerWidth", "y", "innerHeight", "top", "push", "set", "visibility", "scale", "startX", "width", "startY", "height", "targetX", "xPercent", "yPercent", "console", "log", "createFireflyPath", "pathWidth", "loopCenterX", "loopCenterY", "loopRadius", "min", "fireflyPath", "scaleY", "transform<PERSON><PERSON>in", "zIndex", "timeline", "clones", "for<PERSON>ach", "clone", "_clone$parentNode", "keyframes", "motionPath", "path", "curviness", "autoRotate", "onStart", "rightLine", "onUpdate", "progress", "ratio", "_dropped25", "_dropped50", "_dropped75", "repeat", "yoyo", "kill", "cleanup", "children", "ref"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx"], "sourcesContent": ["// HyperOrangeTransition.tsx\nimport React, { useRef, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { gsap } from 'gsap';\nimport { MotionPathPlugin } from 'gsap/MotionPathPlugin';\nimport './HyperOrangeTransition.css';\n\n// Register the plugin\ngsap.registerPlugin(MotionPathPlugin);\n\ninterface HyperOrangeTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete?: () => void;\n  buttonRect?: DOMRect | null;\n}\n\nconst HyperOrangeTransition: React.FC<HyperOrangeTransitionProps> = ({\n  isActive,\n  to,\n  onComplete,\n  buttonRect,\n}) => {\n  const navigate = useNavigate();\n  const ballRef = useRef<HTMLDivElement>(null);\n  const lineRef = useRef<HTMLDivElement>(null);\n  const starsRef = useRef<HTMLDivElement[]>([]);\n  const animation = useRef<gsap.core.Timeline | null>(null);\n  const originalOverflow = useRef<string>(document.body.style.overflow);\n\n  const dropTrail = (originalLine: HTMLElement, opacity: number) => {\n    if (!originalLine) return;\n    \n    const trail = originalLine.cloneNode(true) as HTMLElement;\n    trail.classList.add('line-clone', 'line-trail');\n    trail.style.opacity = opacity.toString();\n    trail.style.position = 'fixed';\n    trail.style.left = originalLine.style.left;\n    trail.style.transform = originalLine.style.transform;\n    document.body.appendChild(trail);\n    \n    gsap.to(trail, {\n      opacity: 0,\n      duration: 0.3,\n      delay: 0.2,\n      onComplete: () => {\n        if (trail.parentNode) {\n          trail.parentNode.removeChild(trail);\n        }\n      }\n    });\n  };\n\n  useEffect(() => {\n    if (isActive && buttonRect) {\n      originalOverflow.current = document.body.style.overflow;\n      document.body.style.overflow = 'hidden';\n\n      const startTransition = () => {\n        const backgroundImage = document.getElementById('background-image');\n        const contentSections = document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');\n        const mainContent = document.querySelector('main, .hero-section, .features-section');\n\n        gsap.to(contentSections, {\n          opacity: 0,\n          duration: 0.5,\n          ease: 'power2.out',\n          stagger: 0.05\n        });\n\n        if (mainContent) {\n          (mainContent as HTMLElement).style.transition = 'opacity 0.3s ease';\n          setTimeout(() => {\n            (mainContent as HTMLElement).style.opacity = '0.3';\n          }, 500);\n        }\n\n        // Create stars\n        const createStars = () => {\n          const stars = [];\n          const numStars = 8; // More stars since they're smaller\n          \n          for (let i = 0; i < numStars; i++) {\n            const star = document.createElement('div');\n            star.className = 'transition-star';\n            star.style.cssText = `\n              position: fixed;\n              width: 3px;\n              height: 3px;\n              background: #ffffff;\n              border-radius: 50%;\n              opacity: 0 !important;\n              visibility: hidden;\n              z-index: 1002;\n              box-shadow: \n                0 0 4px 1px rgba(255, 255, 255, 0.9),\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\n            `;\n            \n            // Completely random positioning across the screen\n            const x = Math.random() * window.innerWidth;\n            const y = Math.random() * window.innerHeight * 0.6; // Top 60% of screen\n            \n            star.style.left = x + 'px';\n            star.style.top = y + 'px';\n            star.style.transform = 'translate(-50%, -50%)';\n            \n            document.body.appendChild(star);\n            stars.push(star);\n            starsRef.current.push(star);\n            \n            // Set GSAP initial state to ensure they're completely hidden\n            gsap.set(star, {\n              opacity: 0,\n              visibility: 'hidden',\n              scale: 0\n            });\n          }\n          \n          return stars;\n        };\n\n        const stars = createStars();\n\n        const startX = buttonRect ? buttonRect.left + (buttonRect.width / 2) : window.innerWidth / 2;\n        const startY = buttonRect ? buttonRect.top + (buttonRect.height / 2) : window.innerHeight / 2;\n        const targetX = window.innerWidth / 2;\n\n        gsap.set(ballRef.current, {\n          x: startX,\n          y: startY,\n          xPercent: -50,\n          yPercent: -50,\n          position: 'fixed',\n          opacity: 1,\n          scale: 1\n        });\n\n        console.log('Ball starting position:', startX, startY);\n        console.log('Ball target position:', targetX, 15);\n        console.log('Ball element:', ballRef.current);\n\n        // Create firefly path with a graceful loop\n        const createFireflyPath = () => {\n          const pathWidth = window.innerWidth * 0.3; // 30% of screen width for the loop\n          const loopCenterX = startX + (targetX - startX) * 0.4; // Loop positioned 40% of the way\n          const loopCenterY = startY - window.innerHeight * 0.35;\n          const loopRadius = Math.min(pathWidth * 0.3, 120); // Reasonable loop size\n          \n          return [\n            { x: startX, y: startY }, // Start at button\n            { x: startX + 100, y: startY - 150 }, // Rise up and slightly right\n            { x: loopCenterX - loopRadius, y: loopCenterY }, // Approach loop from left\n            { x: loopCenterX, y: loopCenterY - loopRadius }, // Top of loop\n            { x: loopCenterX + loopRadius, y: loopCenterY }, // Right side of loop\n            { x: loopCenterX, y: loopCenterY + loopRadius }, // Bottom of loop\n            { x: loopCenterX - loopRadius * 0.5, y: loopCenterY }, // Exit loop\n            { x: targetX - 100, y: 15 + 100 }, // Approach final position\n            { x: targetX, y: 15 } // Final position at top center\n          ];\n        };\n\n        const fireflyPath = createFireflyPath();\n        console.log('Firefly path points:', fireflyPath);\n\n        gsap.set(lineRef.current, {\n          scaleY: 0,\n          transformOrigin: 'top center',\n          position: 'fixed',\n          left: window.innerWidth / 2,\n          top: 0,\n          transform: 'translateX(-50%) translateY(0%)', // Start at absolute top\n          zIndex: 10000,\n          opacity: 1\n        });\n\n        // Create carousels container\n        // const createCarousels = () => {\n        //   const container = document.createElement('div');\n        //   container.className = 'carousels-container';\n        //   container.style.cssText = `\n        //     position: fixed;\n        //     top: 0;\n        //     left: 0;\n        //     width: 100%;\n        //     height: 100%;\n        //     display: flex;\n        //     justify-content: space-between;\n        //     padding: 20px;\n        //     opacity: 0;\n        //     z-index: 1000;\n        //   `;\n          \n        //   // Create 8 carousels (one for each grade)\n        //   for (let i = 1; i <= 8; i++) {\n        //     const carousel = document.createElement('div');\n        //     carousel.className = `carousel grade-${i}`;\n        //     carousel.style.cssText = `\n        //       width: calc(100% / 8 - 10px);\n        //       height: 100%;\n        //       background: rgba(0, 0, 0, 0.2);\n        //       border-radius: 8px;\n        //       overflow: hidden;\n        //       position: relative;\n        //       opacity: 0;\n        //       transform: translateY(20px);\n        //     `;\n            \n        //     // Add grade label at the top of each carousel\n        //     const label = document.createElement('div');\n        //     label.className = 'carousel-label';\n        //     label.textContent = `Grade ${i}`;\n        //     label.style.cssText = `\n        //       position: absolute;\n        //       top: 10px;\n        //       left: 0;\n        //       right: 0;\n        //       text-align: center;\n        //       color: white;\n        //       font-weight: bold;\n        //       text-shadow: 0 1px 3px rgba(0,0,0,0.5);\n        //       z-index: 2;\n        //       opacity: 0;\n        //     `;\n            \n        //     // Add a placeholder for the video carousel content\n        //     const content = document.createElement('div');\n        //     content.className = 'carousel-content';\n        //     content.style.cssText = `\n        //       width: 100%;\n        //       height: 100%;\n        //       display: flex;\n        //       flex-direction: column;\n        //       align-items: center;\n        //       justify-content: center;\n        //       padding-top: 40px;\n        //       color: white;\n        //       font-size: 14px;\n        //     `;\n            \n        //     // Add a placeholder for the video thumbnail\n        //     const thumbnail = document.createElement('div');\n        //     thumbnail.className = 'video-thumbnail';\n        //     thumbnail.style.cssText = `\n        //       width: 80%;\n        //       aspect-ratio: 16/9;\n        //       background: rgba(255,255,255,0.1);\n        //       margin: 10px 0;\n        //       border-radius: 4px;\n        //       display: flex;\n        //       align-items: center;\n        //       justify-content: center;\n        //       font-size: 12px;\n        //       color: rgba(255,255,255,0.7);\n        //     `;\n        //     thumbnail.textContent = 'Video Preview';\n            \n        //     content.appendChild(thumbnail);\n        //     carousel.appendChild(label);\n        //     carousel.appendChild(content);\n        //     container.appendChild(carousel);\n        //   }\n          \n        //   document.body.appendChild(container);\n        //   return container;\n        // };\n\n        // const carouselsContainer = createCarousels();\n        // const carousels = Array.from(carouselsContainer.querySelectorAll('.carousel'));\n\n        // Clean up on completion\n        animation.current = gsap.timeline({\n          onComplete: () => {\n            // Clean up any existing elements\n            const clones = document.querySelectorAll('.line-clone');\n            clones.forEach(clone => clone.parentNode?.removeChild(clone));\n            \n            // Clean up stars\n            starsRef.current.forEach(star => {\n              if (star.parentNode) {\n                star.parentNode.removeChild(star);\n              }\n            });\n            starsRef.current = [];\n            \n            // Fade in carousels\n        //     gsap.to(carouselsContainer, {\n        //       opacity: 1,\n        //       duration: 0.5,\n        //       onComplete: () => {\n        //         // Animate in each carousel with a slight delay\n        //         carousels.forEach((carousel, index) => {\n        //           const label = carousel.querySelector('.carousel-label');\n                  \n        //           gsap.to(carousel, {\n        //             opacity: 1,\n        //             y: 0,\n        //             duration: 0.5,\n        //             delay: index * 0.05,\n        //             ease: 'power2.out',\n        //             onComplete: () => {\n        //               // Animate in the label after the carousel appears\n        //               if (label) {\n        //                 gsap.to(label, {\n        //                   opacity: 1,\n        //                   y: 0,\n        //                   duration: 0.3,\n        //                   ease: 'power2.out'\n        //                 });\n        //               }\n        //             }\n        //           });\n        //         });\n                \n        //         // Navigate after the carousels have appeared\n                 navigate(to);\n        //         onComplete?.();\n        //       }\n        //     });\n          }\n       });\n\n        animation.current\n          // Background movement - split into two parts: movement + scaling, then just movement\n          .to(backgroundImage, {\n            // First half: move from -65% to -37.5% AND scale from 1.0 to 0.7\n            keyframes: {\n              \"0%\": { transform: \"translateY(-65%) scale(1.0)\" },\n              \"50%\": { transform: \"translateY(-37.5%) scale(0.68)\" },\n              \"100%\": { transform: \"translateY(-10%) scale(0.68)\" } // Second half: just movement, no more scaling\n            },\n            duration: 3.5,\n            ease: 'power1.inOut',\n            transformOrigin: 'center center',\n          }, 0)\n          // Ball animation with elegant firefly path including loop\n          .to(ballRef.current, {\n            motionPath: {\n              path: fireflyPath,\n              curviness: 1.5, // Smooth curves but not too dramatic\n              autoRotate: false\n            },\n            duration: 2.8, // Slightly longer to accommodate the loop\n            ease: 'sine.inOut',\n            scale: 0.9,\n            onStart: function() {\n              console.log('Ball animation started with firefly loop path');\n            }\n          }, 0.3)\n          // Line appears when ball reaches near top\n          .to(lineRef.current, {\n            scaleY: 1,\n            duration: 1.2,\n            ease: 'power2.out',\n          }, 2.9) // Start line animation when ball is near top\n          // Line split - starts immediately after line finishes drawing (2.9 + 1.2 = 4.1)\n          .add(() => {\n            if (!lineRef.current) return;\n            \n            const rightLine = lineRef.current.cloneNode(true) as HTMLElement;\n            rightLine.classList.add('line-clone');\n            rightLine.style.position = 'absolute';\n            rightLine.style.left = '50%';\n            rightLine.style.transform = 'translateX(-50%) translateY(0%)';\n            document.body.appendChild(rightLine);\n\n            gsap.to(lineRef.current, {\n              x: -window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onUpdate: function() {\n                const progress = this.ratio; \n                if (progress > 0.25 && !this._dropped25 && lineRef.current) {\n                  this._dropped25 = true;\n                  dropTrail(lineRef.current, 0.75);\n                }\n                if (progress > 0.5 && !this._dropped50 && lineRef.current) {\n                  this._dropped50 = true;\n                  dropTrail(lineRef.current, 0.5);\n                }\n                if (progress > 0.75 && !this._dropped75 && lineRef.current) {\n                  this._dropped75 = true;\n                  dropTrail(lineRef.current, 0.25);\n                }\n              }\n            });\n\n            gsap.to(rightLine, {\n              x: window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onUpdate: function() {\n                const progress = this.ratio;\n                if (progress > 0.25 && !this._dropped25) {\n                  this._dropped25 = true;\n                  dropTrail(rightLine, 0.75);\n                }\n                if (progress > 0.5 && !this._dropped50) {\n                  this._dropped50 = true;\n                  dropTrail(rightLine, 0.5);\n                }\n                if (progress > 0.75 && !this._dropped75) {\n                  this._dropped75 = true;\n                  dropTrail(rightLine, 0.25);\n                }\n              },\n              onComplete: () => {\n                if (rightLine.parentNode) {\n                  rightLine.parentNode.removeChild(rightLine);\n                }\n              }\n            });\n          }, 4.1) // Exactly when line drawing finishes\n          // Stars appear towards the end of firefly animation\n          .to(stars, {\n            opacity: 1,\n            visibility: 'visible',\n            scale: 1,\n            duration: 0.4,\n            ease: 'back.out(1.7)',\n            stagger: 0.08,\n            onStart: function() {\n              console.log('Stars animation started');\n            },\n            onComplete: function() {\n              console.log('Stars animation completed');\n              // Add the CSS animation back after GSAP animation completes\n              stars.forEach(star => {\n                star.style.animation = 'starTwinkle 2s ease-in-out infinite alternate';\n              });\n            }\n          }, 2.6) // Near the end of firefly animation (firefly ends around 2.8-3.1)\n          // Flash effect - happens during line split\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0.8,\n            duration: 0.1,\n            repeat: 6,\n            yoyo: true,\n            ease: 'power1.inOut',\n          }, 4.2)\n          // Final fade out\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n          }, 4.8);\n\n        return () => {\n          if (animation.current) animation.current.kill();\n          if (mainContent) {\n            (mainContent as HTMLElement).style.opacity = '';\n            (mainContent as HTMLElement).style.transition = '';\n          }\n          if (backgroundImage) {\n            backgroundImage.style.transform = 'translateY(-62%)'; // Reset to original\n          }\n          // Clean up stars\n          starsRef.current.forEach(star => {\n            if (star.parentNode) {\n              star.parentNode.removeChild(star);\n            }\n          });\n          starsRef.current = [];\n          document.body.style.overflow = originalOverflow.current;\n        };\n      };\n\n      const cleanup = startTransition();\n      return cleanup;\n    }\n  }, [isActive, buttonRect, navigate, onComplete, to]);\n\n  if (!isActive) return null;\n\n  return (\n    <div className=\"transition-container\">\n      <div className=\"transition-ball\" ref={ballRef} />\n      <div className=\"transition-line\" ref={lineRef} />\n    </div>\n  );\n};\n\nexport default HyperOrangeTransition;"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAChD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,IAAI,KAAQ,MAAM,CAC3B,OAASC,gBAAgB,KAAQ,uBAAuB,CACxD,MAAO,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAL,IAAI,CAACM,cAAc,CAACL,gBAAgB,CAAC,CASrC,KAAM,CAAAM,qBAA2D,CAAGC,IAAA,EAK9D,IAL+D,CACnEC,QAAQ,CACRC,EAAE,CACFC,UAAU,CACVC,UACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,OAAO,CAAGjB,MAAM,CAAiB,IAAI,CAAC,CAC5C,KAAM,CAAAkB,OAAO,CAAGlB,MAAM,CAAiB,IAAI,CAAC,CAC5C,KAAM,CAAAmB,QAAQ,CAAGnB,MAAM,CAAmB,EAAE,CAAC,CAC7C,KAAM,CAAAoB,SAAS,CAAGpB,MAAM,CAA4B,IAAI,CAAC,CACzD,KAAM,CAAAqB,gBAAgB,CAAGrB,MAAM,CAASsB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC,CAErE,KAAM,CAAAC,SAAS,CAAGA,CAACC,YAAyB,CAAEC,OAAe,GAAK,CAChE,GAAI,CAACD,YAAY,CAAE,OAEnB,KAAM,CAAAE,KAAK,CAAGF,YAAY,CAACG,SAAS,CAAC,IAAI,CAAgB,CACzDD,KAAK,CAACE,SAAS,CAACC,GAAG,CAAC,YAAY,CAAE,YAAY,CAAC,CAC/CH,KAAK,CAACL,KAAK,CAACI,OAAO,CAAGA,OAAO,CAACK,QAAQ,CAAC,CAAC,CACxCJ,KAAK,CAACL,KAAK,CAACU,QAAQ,CAAG,OAAO,CAC9BL,KAAK,CAACL,KAAK,CAACW,IAAI,CAAGR,YAAY,CAACH,KAAK,CAACW,IAAI,CAC1CN,KAAK,CAACL,KAAK,CAACY,SAAS,CAAGT,YAAY,CAACH,KAAK,CAACY,SAAS,CACpDd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACR,KAAK,CAAC,CAEhC1B,IAAI,CAACU,EAAE,CAACgB,KAAK,CAAE,CACbD,OAAO,CAAE,CAAC,CACVU,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,GAAG,CACVzB,UAAU,CAAEA,CAAA,GAAM,CAChB,GAAIe,KAAK,CAACW,UAAU,CAAE,CACpBX,KAAK,CAACW,UAAU,CAACC,WAAW,CAACZ,KAAK,CAAC,CACrC,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAED5B,SAAS,CAAC,IAAM,CACd,GAAIW,QAAQ,EAAIG,UAAU,CAAE,CAC1BM,gBAAgB,CAACqB,OAAO,CAAGpB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CACvDH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CAEvC,KAAM,CAAAkB,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,eAAe,CAAGtB,QAAQ,CAACuB,cAAc,CAAC,kBAAkB,CAAC,CACnE,KAAM,CAAAC,eAAe,CAAGxB,QAAQ,CAACyB,gBAAgB,CAAC,sEAAsE,CAAC,CACzH,KAAM,CAAAC,WAAW,CAAG1B,QAAQ,CAAC2B,aAAa,CAAC,wCAAwC,CAAC,CAEpF9C,IAAI,CAACU,EAAE,CAACiC,eAAe,CAAE,CACvBlB,OAAO,CAAE,CAAC,CACVU,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,YAAY,CAClBC,OAAO,CAAE,IACX,CAAC,CAAC,CAEF,GAAIH,WAAW,CAAE,CACdA,WAAW,CAAiBxB,KAAK,CAAC4B,UAAU,CAAG,mBAAmB,CACnEC,UAAU,CAAC,IAAM,CACdL,WAAW,CAAiBxB,KAAK,CAACI,OAAO,CAAG,KAAK,CACpD,CAAC,CAAE,GAAG,CAAC,CACT,CAEA;AACA,KAAM,CAAA0B,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,KAAK,CAAG,EAAE,CAChB,KAAM,CAAAC,QAAQ,CAAG,CAAC,CAAE;AAEpB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,QAAQ,CAAEC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAC,IAAI,CAAGpC,QAAQ,CAACqC,aAAa,CAAC,KAAK,CAAC,CAC1CD,IAAI,CAACE,SAAS,CAAG,iBAAiB,CAClCF,IAAI,CAAClC,KAAK,CAACqC,OAAO,gaAYjB,CAED;AACA,KAAM,CAAAC,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGC,MAAM,CAACC,UAAU,CAC3C,KAAM,CAAAC,CAAC,CAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGC,MAAM,CAACG,WAAW,CAAG,GAAG,CAAE;AAEpDV,IAAI,CAAClC,KAAK,CAACW,IAAI,CAAG2B,CAAC,CAAG,IAAI,CAC1BJ,IAAI,CAAClC,KAAK,CAAC6C,GAAG,CAAGF,CAAC,CAAG,IAAI,CACzBT,IAAI,CAAClC,KAAK,CAACY,SAAS,CAAG,uBAAuB,CAE9Cd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACqB,IAAI,CAAC,CAC/BH,KAAK,CAACe,IAAI,CAACZ,IAAI,CAAC,CAChBvC,QAAQ,CAACuB,OAAO,CAAC4B,IAAI,CAACZ,IAAI,CAAC,CAE3B;AACAvD,IAAI,CAACoE,GAAG,CAACb,IAAI,CAAE,CACb9B,OAAO,CAAE,CAAC,CACV4C,UAAU,CAAE,QAAQ,CACpBC,KAAK,CAAE,CACT,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAlB,KAAK,CACd,CAAC,CAED,KAAM,CAAAA,KAAK,CAAGD,WAAW,CAAC,CAAC,CAE3B,KAAM,CAAAoB,MAAM,CAAG3D,UAAU,CAAGA,UAAU,CAACoB,IAAI,CAAIpB,UAAU,CAAC4D,KAAK,CAAG,CAAE,CAAGV,MAAM,CAACC,UAAU,CAAG,CAAC,CAC5F,KAAM,CAAAU,MAAM,CAAG7D,UAAU,CAAGA,UAAU,CAACsD,GAAG,CAAItD,UAAU,CAAC8D,MAAM,CAAG,CAAE,CAAGZ,MAAM,CAACG,WAAW,CAAG,CAAC,CAC7F,KAAM,CAAAU,OAAO,CAAGb,MAAM,CAACC,UAAU,CAAG,CAAC,CAErC/D,IAAI,CAACoE,GAAG,CAACtD,OAAO,CAACyB,OAAO,CAAE,CACxBoB,CAAC,CAAEY,MAAM,CACTP,CAAC,CAAES,MAAM,CACTG,QAAQ,CAAE,CAAC,EAAE,CACbC,QAAQ,CAAE,CAAC,EAAE,CACb9C,QAAQ,CAAE,OAAO,CACjBN,OAAO,CAAE,CAAC,CACV6C,KAAK,CAAE,CACT,CAAC,CAAC,CAEFQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAER,MAAM,CAAEE,MAAM,CAAC,CACtDK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEJ,OAAO,CAAE,EAAE,CAAC,CACjDG,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEjE,OAAO,CAACyB,OAAO,CAAC,CAE7C;AACA,KAAM,CAAAyC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,SAAS,CAAGnB,MAAM,CAACC,UAAU,CAAG,GAAG,CAAE;AAC3C,KAAM,CAAAmB,WAAW,CAAGX,MAAM,CAAG,CAACI,OAAO,CAAGJ,MAAM,EAAI,GAAG,CAAE;AACvD,KAAM,CAAAY,WAAW,CAAGV,MAAM,CAAGX,MAAM,CAACG,WAAW,CAAG,IAAI,CACtD,KAAM,CAAAmB,UAAU,CAAGxB,IAAI,CAACyB,GAAG,CAACJ,SAAS,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AAEnD,MAAO,CACL,CAAEtB,CAAC,CAAEY,MAAM,CAAEP,CAAC,CAAES,MAAO,CAAC,CAAE;AAC1B,CAAEd,CAAC,CAAEY,MAAM,CAAG,GAAG,CAAEP,CAAC,CAAES,MAAM,CAAG,GAAI,CAAC,CAAE;AACtC,CAAEd,CAAC,CAAEuB,WAAW,CAAGE,UAAU,CAAEpB,CAAC,CAAEmB,WAAY,CAAC,CAAE;AACjD,CAAExB,CAAC,CAAEuB,WAAW,CAAElB,CAAC,CAAEmB,WAAW,CAAGC,UAAW,CAAC,CAAE;AACjD,CAAEzB,CAAC,CAAEuB,WAAW,CAAGE,UAAU,CAAEpB,CAAC,CAAEmB,WAAY,CAAC,CAAE;AACjD,CAAExB,CAAC,CAAEuB,WAAW,CAAElB,CAAC,CAAEmB,WAAW,CAAGC,UAAW,CAAC,CAAE;AACjD,CAAEzB,CAAC,CAAEuB,WAAW,CAAGE,UAAU,CAAG,GAAG,CAAEpB,CAAC,CAAEmB,WAAY,CAAC,CAAE;AACvD,CAAExB,CAAC,CAAEgB,OAAO,CAAG,GAAG,CAAEX,CAAC,CAAE,EAAE,CAAG,GAAI,CAAC,CAAE;AACnC,CAAEL,CAAC,CAAEgB,OAAO,CAAEX,CAAC,CAAE,EAAG,CAAE;AAAA,CACvB,CACH,CAAC,CAED,KAAM,CAAAsB,WAAW,CAAGN,iBAAiB,CAAC,CAAC,CACvCF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEO,WAAW,CAAC,CAEhDtF,IAAI,CAACoE,GAAG,CAACrD,OAAO,CAACwB,OAAO,CAAE,CACxBgD,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,YAAY,CAC7BzD,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE8B,MAAM,CAACC,UAAU,CAAG,CAAC,CAC3BG,GAAG,CAAE,CAAC,CACNjC,SAAS,CAAE,iCAAiC,CAAE;AAC9CwD,MAAM,CAAE,KAAK,CACbhE,OAAO,CAAE,CACX,CAAC,CAAC,CAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACAR,SAAS,CAACsB,OAAO,CAAGvC,IAAI,CAAC0F,QAAQ,CAAC,CAChC/E,UAAU,CAAEA,CAAA,GAAM,CAChB;AACA,KAAM,CAAAgF,MAAM,CAAGxE,QAAQ,CAACyB,gBAAgB,CAAC,aAAa,CAAC,CACvD+C,MAAM,CAACC,OAAO,CAACC,KAAK,OAAAC,iBAAA,QAAAA,iBAAA,CAAID,KAAK,CAACxD,UAAU,UAAAyD,iBAAA,iBAAhBA,iBAAA,CAAkBxD,WAAW,CAACuD,KAAK,CAAC,GAAC,CAE7D;AACA7E,QAAQ,CAACuB,OAAO,CAACqD,OAAO,CAACrC,IAAI,EAAI,CAC/B,GAAIA,IAAI,CAAClB,UAAU,CAAE,CACnBkB,IAAI,CAAClB,UAAU,CAACC,WAAW,CAACiB,IAAI,CAAC,CACnC,CACF,CAAC,CAAC,CACFvC,QAAQ,CAACuB,OAAO,CAAG,EAAE,CAErB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACS1B,QAAQ,CAACH,EAAE,CAAC,CACrB;AACA;AACA;AACE,CACH,CAAC,CAAC,CAEDO,SAAS,CAACsB,OACR;AAAA,CACC7B,EAAE,CAAC+B,eAAe,CAAE,CACnB;AACAsD,SAAS,CAAE,CACT,IAAI,CAAE,CAAE9D,SAAS,CAAE,6BAA8B,CAAC,CAClD,KAAK,CAAE,CAAEA,SAAS,CAAE,gCAAiC,CAAC,CACtD,MAAM,CAAE,CAAEA,SAAS,CAAE,8BAA+B,CAAE;AACxD,CAAC,CACDE,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,cAAc,CACpByC,eAAe,CAAE,eACnB,CAAC,CAAE,CAAC,CACJ;AAAA,CACC9E,EAAE,CAACI,OAAO,CAACyB,OAAO,CAAE,CACnByD,UAAU,CAAE,CACVC,IAAI,CAAEX,WAAW,CACjBY,SAAS,CAAE,GAAG,CAAE;AAChBC,UAAU,CAAE,KACd,CAAC,CACDhE,QAAQ,CAAE,GAAG,CAAE;AACfY,IAAI,CAAE,YAAY,CAClBuB,KAAK,CAAE,GAAG,CACV8B,OAAO,CAAE,QAAAA,CAAA,CAAW,CAClBtB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC9D,CACF,CAAC,CAAE,GAAG,CACN;AAAA,CACCrE,EAAE,CAACK,OAAO,CAACwB,OAAO,CAAE,CACnBgD,MAAM,CAAE,CAAC,CACTpD,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,YACR,CAAC,CAAE,GAAG,CAAE;AACR;AAAA,CACClB,GAAG,CAAC,IAAM,CACT,GAAI,CAACd,OAAO,CAACwB,OAAO,CAAE,OAEtB,KAAM,CAAA8D,SAAS,CAAGtF,OAAO,CAACwB,OAAO,CAACZ,SAAS,CAAC,IAAI,CAAgB,CAChE0E,SAAS,CAACzE,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC,CACrCwE,SAAS,CAAChF,KAAK,CAACU,QAAQ,CAAG,UAAU,CACrCsE,SAAS,CAAChF,KAAK,CAACW,IAAI,CAAG,KAAK,CAC5BqE,SAAS,CAAChF,KAAK,CAACY,SAAS,CAAG,iCAAiC,CAC7Dd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACmE,SAAS,CAAC,CAEpCrG,IAAI,CAACU,EAAE,CAACK,OAAO,CAACwB,OAAO,CAAE,CACvBoB,CAAC,CAAE,CAACG,MAAM,CAACC,UAAU,CAAG,CAAC,CACzBtC,OAAO,CAAE,CAAC,CACVU,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,cAAc,CACpBuD,QAAQ,CAAE,QAAAA,CAAA,CAAW,CACnB,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,KAAK,CAC3B,GAAID,QAAQ,CAAG,IAAI,EAAI,CAAC,IAAI,CAACE,UAAU,EAAI1F,OAAO,CAACwB,OAAO,CAAE,CAC1D,IAAI,CAACkE,UAAU,CAAG,IAAI,CACtBlF,SAAS,CAACR,OAAO,CAACwB,OAAO,CAAE,IAAI,CAAC,CAClC,CACA,GAAIgE,QAAQ,CAAG,GAAG,EAAI,CAAC,IAAI,CAACG,UAAU,EAAI3F,OAAO,CAACwB,OAAO,CAAE,CACzD,IAAI,CAACmE,UAAU,CAAG,IAAI,CACtBnF,SAAS,CAACR,OAAO,CAACwB,OAAO,CAAE,GAAG,CAAC,CACjC,CACA,GAAIgE,QAAQ,CAAG,IAAI,EAAI,CAAC,IAAI,CAACI,UAAU,EAAI5F,OAAO,CAACwB,OAAO,CAAE,CAC1D,IAAI,CAACoE,UAAU,CAAG,IAAI,CACtBpF,SAAS,CAACR,OAAO,CAACwB,OAAO,CAAE,IAAI,CAAC,CAClC,CACF,CACF,CAAC,CAAC,CAEFvC,IAAI,CAACU,EAAE,CAAC2F,SAAS,CAAE,CACjB1C,CAAC,CAAEG,MAAM,CAACC,UAAU,CAAG,CAAC,CACxBtC,OAAO,CAAE,CAAC,CACVU,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,cAAc,CACpBuD,QAAQ,CAAE,QAAAA,CAAA,CAAW,CACnB,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,KAAK,CAC3B,GAAID,QAAQ,CAAG,IAAI,EAAI,CAAC,IAAI,CAACE,UAAU,CAAE,CACvC,IAAI,CAACA,UAAU,CAAG,IAAI,CACtBlF,SAAS,CAAC8E,SAAS,CAAE,IAAI,CAAC,CAC5B,CACA,GAAIE,QAAQ,CAAG,GAAG,EAAI,CAAC,IAAI,CAACG,UAAU,CAAE,CACtC,IAAI,CAACA,UAAU,CAAG,IAAI,CACtBnF,SAAS,CAAC8E,SAAS,CAAE,GAAG,CAAC,CAC3B,CACA,GAAIE,QAAQ,CAAG,IAAI,EAAI,CAAC,IAAI,CAACI,UAAU,CAAE,CACvC,IAAI,CAACA,UAAU,CAAG,IAAI,CACtBpF,SAAS,CAAC8E,SAAS,CAAE,IAAI,CAAC,CAC5B,CACF,CAAC,CACD1F,UAAU,CAAEA,CAAA,GAAM,CAChB,GAAI0F,SAAS,CAAChE,UAAU,CAAE,CACxBgE,SAAS,CAAChE,UAAU,CAACC,WAAW,CAAC+D,SAAS,CAAC,CAC7C,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,GAAG,CAAE;AACR;AAAA,CACC3F,EAAE,CAAC0C,KAAK,CAAE,CACT3B,OAAO,CAAE,CAAC,CACV4C,UAAU,CAAE,SAAS,CACrBC,KAAK,CAAE,CAAC,CACRnC,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,eAAe,CACrBC,OAAO,CAAE,IAAI,CACboD,OAAO,CAAE,QAAAA,CAAA,CAAW,CAClBtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACxC,CAAC,CACDpE,UAAU,CAAE,QAAAA,CAAA,CAAW,CACrBmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CACxC;AACA3B,KAAK,CAACwC,OAAO,CAACrC,IAAI,EAAI,CACpBA,IAAI,CAAClC,KAAK,CAACJ,SAAS,CAAG,+CAA+C,CACxE,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,GAAG,CAAE;AACR;AAAA,CACCP,EAAE,CAAC,CAACI,OAAO,CAACyB,OAAO,CAAExB,OAAO,CAACwB,OAAO,CAAC,CAAE,CACtCd,OAAO,CAAE,GAAG,CACZU,QAAQ,CAAE,GAAG,CACbyE,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,IAAI,CACV9D,IAAI,CAAE,cACR,CAAC,CAAE,GAAG,CACN;AAAA,CACCrC,EAAE,CAAC,CAACI,OAAO,CAACyB,OAAO,CAAExB,OAAO,CAACwB,OAAO,CAAC,CAAE,CACtCd,OAAO,CAAE,CAAC,CACVU,QAAQ,CAAE,GAAG,CACbY,IAAI,CAAE,cACR,CAAC,CAAE,GAAG,CAAC,CAET,MAAO,IAAM,CACX,GAAI9B,SAAS,CAACsB,OAAO,CAAEtB,SAAS,CAACsB,OAAO,CAACuE,IAAI,CAAC,CAAC,CAC/C,GAAIjE,WAAW,CAAE,CACdA,WAAW,CAAiBxB,KAAK,CAACI,OAAO,CAAG,EAAE,CAC9CoB,WAAW,CAAiBxB,KAAK,CAAC4B,UAAU,CAAG,EAAE,CACpD,CACA,GAAIR,eAAe,CAAE,CACnBA,eAAe,CAACpB,KAAK,CAACY,SAAS,CAAG,kBAAkB,CAAE;AACxD,CACA;AACAjB,QAAQ,CAACuB,OAAO,CAACqD,OAAO,CAACrC,IAAI,EAAI,CAC/B,GAAIA,IAAI,CAAClB,UAAU,CAAE,CACnBkB,IAAI,CAAClB,UAAU,CAACC,WAAW,CAACiB,IAAI,CAAC,CACnC,CACF,CAAC,CAAC,CACFvC,QAAQ,CAACuB,OAAO,CAAG,EAAE,CACrBpB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAGJ,gBAAgB,CAACqB,OAAO,CACzD,CAAC,CACH,CAAC,CAED,KAAM,CAAAwE,OAAO,CAAGvE,eAAe,CAAC,CAAC,CACjC,MAAO,CAAAuE,OAAO,CAChB,CACF,CAAC,CAAE,CAACtG,QAAQ,CAAEG,UAAU,CAAEC,QAAQ,CAAEF,UAAU,CAAED,EAAE,CAAC,CAAC,CAEpD,GAAI,CAACD,QAAQ,CAAE,MAAO,KAAI,CAE1B,mBACEJ,KAAA,QAAKoD,SAAS,CAAC,sBAAsB,CAAAuD,QAAA,eACnC7G,IAAA,QAAKsD,SAAS,CAAC,iBAAiB,CAACwD,GAAG,CAAEnG,OAAQ,CAAE,CAAC,cACjDX,IAAA,QAAKsD,SAAS,CAAC,iBAAiB,CAACwD,GAAG,CAAElG,OAAQ,CAAE,CAAC,EAC9C,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}