{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport './HomePage.css';\nimport HyperOrangeTransition from '../Xtra/HyperOrangeTransition';\nimport ScaleLetterTransition from '../Xtra/ScaleLetterTransition'; // Add this import\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Homepage = () => {\n  _s();\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [isScaleTransitioning, setIsScaleTransitioning] = useState(false); // Add this state\n  const [buttonRect, setButtonRect] = useState(null);\n  const shopButtonRef = useRef(null);\n  const createButtonRef = useRef(null); // Add this ref\n\n  const handleSamplesClick = e => {\n    e.preventDefault();\n    if (shopButtonRef.current) {\n      const rect = shopButtonRef.current.getBoundingClientRect();\n      setButtonRect(rect);\n      setIsTransitioning(true);\n    }\n  };\n\n  // Add this handler for Create Custom button\n  const handleCreateCustomClick = e => {\n    e.preventDefault();\n    setIsScaleTransitioning(true);\n  };\n  const handleTransitionComplete = () => {\n    setIsTransitioning(false);\n  };\n\n  // Add this handler for scale transition complete\n  const handleScaleTransitionComplete = () => {\n    setIsScaleTransitioning(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"background-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"background-image\",\n        id: \"background-image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this), isTransitioning && buttonRect && /*#__PURE__*/_jsxDEV(HyperOrangeTransition, {\n      isActive: isTransitioning,\n      to: \"/samples\",\n      onComplete: handleTransitionComplete,\n      buttonRect: buttonRect\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this), isScaleTransitioning && /*#__PURE__*/_jsxDEV(ScaleLetterTransition, {\n      isActive: isScaleTransitioning,\n      to: \"/create-video\",\n      onComplete: handleScaleTransitionComplete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: \"Tailored Tutoring presents...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"compliance-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"compliance-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Compliance & Integration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2713 LTI Compliant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2713 SCORM Compliant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2713 xAPI Compliant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Seamlessly Integrated with:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"lms-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDFE2 Canvas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDFE2 Moodle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDFE2 Google Classroom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDFE2 Blackboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\uD83D\\uDFE1 Schoology (Beta)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"more-text\",\n          children: \"...and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"SCaLE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The Science Class Lesson Engine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            ref: createButtonRef,\n            href: \"/create-video\",\n            className: \"cta-button\",\n            onClick: handleCreateCustomClick,\n            children: \"Create Custom\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            ref: shopButtonRef,\n            href: \"/samples\",\n            className: \"cta-button shop-button\",\n            onClick: handleSamplesClick,\n            children: \"Shop Pre-made\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"features-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"What Tailored Tutoring Offers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83C\\uDFA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Auto-generated educational video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDFEB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Teacher-student dialogue overlay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Personalized quiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83C\\uDF10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Private webpage for student access\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '50px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/manual-create-video\",\n          style: {\n            color: '#007bff',\n            textDecoration: 'underline'\n          },\n          children: \"Manual Video Creation (Fallback)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(Homepage, \"H3zw7Zz+Rgp5tZVkTu34Zj2L2O0=\");\n_c = Homepage;\nexport default Homepage;\nvar _c;\n$RefreshReg$(_c, \"Homepage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "HyperOrangeTransition", "ScaleLetterTransition", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Homepage", "_s", "isTransitioning", "setIsTransitioning", "isScaleTransitioning", "setIsScaleTransitioning", "buttonRect", "setButtonRect", "shopButtonRef", "createButtonRef", "handleSamplesClick", "e", "preventDefault", "current", "rect", "getBoundingClientRect", "handleCreateCustomClick", "handleTransitionComplete", "handleScaleTransitionComplete", "children", "className", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isActive", "to", "onComplete", "ref", "href", "onClick", "style", "marginTop", "textAlign", "color", "textDecoration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport './HomePage.css';\nimport HyperOrangeTransition from '../Xtra/HyperOrangeTransition';\nimport ScaleLetterTransition from '../Xtra/ScaleLetterTransition'; // Add this import\n\nconst Homepage: React.FC = () => {\n    const [isTransitioning, setIsTransitioning] = useState(false);\n    const [isScaleTransitioning, setIsScaleTransitioning] = useState(false); // Add this state\n    const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);\n    const shopButtonRef = useRef<HTMLAnchorElement>(null);\n    const createButtonRef = useRef<HTMLAnchorElement>(null); // Add this ref\n\n    const handleSamplesClick = (e: React.MouseEvent) => {\n        e.preventDefault();\n        if (shopButtonRef.current) {\n            const rect = shopButtonRef.current.getBoundingClientRect();\n            setButtonRect(rect);\n            setIsTransitioning(true);\n        }\n    };\n\n    // Add this handler for Create Custom button\n    const handleCreateCustomClick = (e: React.MouseEvent) => {\n        e.preventDefault();\n        setIsScaleTransitioning(true);\n    };\n\n    const handleTransitionComplete = () => {\n        setIsTransitioning(false);\n    };\n\n    // Add this handler for scale transition complete\n    const handleScaleTransitionComplete = () => {\n        setIsScaleTransitioning(false);\n    };\n\n    return (\n        <>\n            {/* Background container - this replaces the body background */}\n            <div className=\"background-container\">\n                <div className=\"background-image\" id=\"background-image\"></div>\n            </div>\n\n            {isTransitioning && buttonRect && (\n                <HyperOrangeTransition\n                    isActive={isTransitioning}\n                    to=\"/samples\"\n                    onComplete={handleTransitionComplete}\n                    buttonRect={buttonRect}\n                />\n            )}\n\n            {/* Add the scale letter transition */}\n            {isScaleTransitioning && (\n                <ScaleLetterTransition\n                    isActive={isScaleTransitioning}\n                    to=\"/create-video\"\n                    onComplete={handleScaleTransitionComplete}\n                />\n            )}\n            \n            <header>\n                <nav>\n                    <div className=\"logo\">Tailored Tutoring presents...</div>\n                </nav>\n            </header>\n\n            <div className=\"compliance-banner\">\n                <div className=\"compliance-content\">\n                    <h3>Compliance & Integration</h3>\n                    <ul>\n                        <li>✓ LTI Compliant</li>\n                        <li>✓ SCORM Compliant</li>\n                        <li>✓ xAPI Compliant</li>\n                    </ul>\n                    <h4>Seamlessly Integrated with:</h4>\n                    <ul className=\"lms-list\">\n                        <li>🟢 Canvas</li>\n                        <li>🟢 Moodle</li>\n                        <li>🟢 Google Classroom</li>\n                        <li>🟢 Blackboard</li>\n                        <li>🟡 Schoology (Beta)</li>\n                    </ul>\n                    <p className=\"more-text\">...and more!</p>\n                </div>\n            </div>\n\n            <section className=\"hero-section\">\n                <div className=\"hero-content\">\n                    <h1>SCaLE</h1>\n                    <p>The Science Class Lesson Engine</p>\n                    <div className=\"hero-buttons\">\n                        {/* Update the Create Custom button */}\n                        <a \n                            ref={createButtonRef}\n                            href=\"/create-video\" \n                            className=\"cta-button\"\n                            onClick={handleCreateCustomClick}\n                        >\n                            Create Custom\n                        </a>\n                        <a \n                            ref={shopButtonRef}\n                            href=\"/samples\" \n                            className=\"cta-button shop-button\"\n                            onClick={handleSamplesClick}\n                        >\n                            Shop Pre-made\n                        </a>\n                    </div>\n                </div>\n            </section>\n\n            <section className=\"features-section\">\n                <h2>What Tailored Tutoring Offers</h2>\n                <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🎥</div>\n                        <p>Auto-generated educational video</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🧑‍🏫</div>\n                        <p>Teacher-student dialogue overlay</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">📝</div>\n                        <p>Personalized quiz</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🌐</div>\n                        <p>Private webpage for student access</p>\n                    </div>\n                </div>            \n                <div style={{ marginTop: '50px', textAlign: 'center' }}>\n                    <Link to=\"/manual-create-video\" style={{ color: '#007bff', textDecoration: 'underline' }}>\n                        Manual Video Creation (Fallback)\n                    </Link>\n                </div>\n            </section>\n\n            <footer>\n                {/* Optional: Add footer content here */}\n            </footer>\n        </>\n    );\n};\n\nexport default Homepage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,gBAAgB;AACvB,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,qBAAqB,MAAM,+BAA+B,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACa,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACzE,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAiB,IAAI,CAAC;EAClE,MAAMiB,aAAa,GAAGhB,MAAM,CAAoB,IAAI,CAAC;EACrD,MAAMiB,eAAe,GAAGjB,MAAM,CAAoB,IAAI,CAAC,CAAC,CAAC;;EAEzD,MAAMkB,kBAAkB,GAAIC,CAAmB,IAAK;IAChDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,aAAa,CAACK,OAAO,EAAE;MACvB,MAAMC,IAAI,GAAGN,aAAa,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC1DR,aAAa,CAACO,IAAI,CAAC;MACnBX,kBAAkB,CAAC,IAAI,CAAC;IAC5B;EACJ,CAAC;;EAED;EACA,MAAMa,uBAAuB,GAAIL,CAAmB,IAAK;IACrDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,uBAAuB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;IACnCd,kBAAkB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMe,6BAA6B,GAAGA,CAAA,KAAM;IACxCb,uBAAuB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,oBACIR,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBAEItB,OAAA;MAAKuB,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACjCtB,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAACC,EAAE,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EAELvB,eAAe,IAAII,UAAU,iBAC1BT,OAAA,CAACH,qBAAqB;MAClBgC,QAAQ,EAAExB,eAAgB;MAC1ByB,EAAE,EAAC,UAAU;MACbC,UAAU,EAAEX,wBAAyB;MACrCX,UAAU,EAAEA;IAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACJ,EAGArB,oBAAoB,iBACjBP,OAAA,CAACF,qBAAqB;MAClB+B,QAAQ,EAAEtB,oBAAqB;MAC/BuB,EAAE,EAAC,eAAe;MAClBC,UAAU,EAAEV;IAA8B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACJ,eAED5B,OAAA;MAAAsB,QAAA,eACItB,OAAA;QAAAsB,QAAA,eACItB,OAAA;UAAKuB,SAAS,EAAC,MAAM;UAAAD,QAAA,EAAC;QAA6B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAET5B,OAAA;MAAKuB,SAAS,EAAC,mBAAmB;MAAAD,QAAA,eAC9BtB,OAAA;QAAKuB,SAAS,EAAC,oBAAoB;QAAAD,QAAA,gBAC/BtB,OAAA;UAAAsB,QAAA,EAAI;QAAwB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC5B,OAAA;UAAAsB,QAAA,gBACItB,OAAA;YAAAsB,QAAA,EAAI;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB5B,OAAA;YAAAsB,QAAA,EAAI;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B5B,OAAA;YAAAsB,QAAA,EAAI;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACL5B,OAAA;UAAAsB,QAAA,EAAI;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpC5B,OAAA;UAAIuB,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACpBtB,OAAA;YAAAsB,QAAA,EAAI;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB5B,OAAA;YAAAsB,QAAA,EAAI;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClB5B,OAAA;YAAAsB,QAAA,EAAI;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B5B,OAAA;YAAAsB,QAAA,EAAI;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB5B,OAAA;YAAAsB,QAAA,EAAI;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACL5B,OAAA;UAAGuB,SAAS,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN5B,OAAA;MAASuB,SAAS,EAAC,cAAc;MAAAD,QAAA,eAC7BtB,OAAA;QAAKuB,SAAS,EAAC,cAAc;QAAAD,QAAA,gBACzBtB,OAAA;UAAAsB,QAAA,EAAI;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACd5B,OAAA;UAAAsB,QAAA,EAAG;QAA+B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtC5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAEzBtB,OAAA;YACIgC,GAAG,EAAEpB,eAAgB;YACrBqB,IAAI,EAAC,eAAe;YACpBV,SAAS,EAAC,YAAY;YACtBW,OAAO,EAAEf,uBAAwB;YAAAG,QAAA,EACpC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ5B,OAAA;YACIgC,GAAG,EAAErB,aAAc;YACnBsB,IAAI,EAAC,UAAU;YACfV,SAAS,EAAC,wBAAwB;YAClCW,OAAO,EAAErB,kBAAmB;YAAAS,QAAA,EAC/B;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEV5B,OAAA;MAASuB,SAAS,EAAC,kBAAkB;MAAAD,QAAA,gBACjCtB,OAAA;QAAAsB,QAAA,EAAI;MAA6B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtC5B,OAAA;QAAKuB,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC1BtB,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzBtB,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5B,OAAA;YAAAsB,QAAA,EAAG;UAAgC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzBtB,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjC5B,OAAA;YAAAsB,QAAA,EAAG;UAAgC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzBtB,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5B,OAAA;YAAAsB,QAAA,EAAG;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN5B,OAAA;UAAKuB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzBtB,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAD,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5B,OAAA;YAAAsB,QAAA,EAAG;UAAkC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5B,OAAA;QAAKmC,KAAK,EAAE;UAAEC,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAf,QAAA,eACnDtB,OAAA,CAACJ,IAAI;UAACkC,EAAE,EAAC,sBAAsB;UAACK,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEC,cAAc,EAAE;UAAY,CAAE;UAAAjB,QAAA,EAAC;QAE1F;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEV5B,OAAA;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEQ,CAAC;EAAA,eACX,CAAC;AAEX,CAAC;AAACxB,EAAA,CA5IID,QAAkB;AAAAqC,EAAA,GAAlBrC,QAAkB;AA8IxB,eAAeA,QAAQ;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}