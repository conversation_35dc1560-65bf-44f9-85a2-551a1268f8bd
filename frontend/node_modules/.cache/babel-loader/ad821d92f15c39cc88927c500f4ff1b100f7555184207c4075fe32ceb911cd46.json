{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/VideoCreationPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './VideoCreationPage.css';\n\n// Voice options based on sex\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VOICE_OPTIONS = {\n  male: [{\n    value: 'david',\n    label: '<PERSON>',\n    description: 'Friendly and clear'\n  }, {\n    value: 'marcus',\n    label: '<PERSON>',\n    description: 'Energetic and engaging'\n  }, {\n    value: 'james',\n    label: '<PERSON>',\n    description: 'Calm and professional'\n  }],\n  female: [{\n    value: 'sarah',\n    label: 'Sarah',\n    description: 'Warm and articulate'\n  }, {\n    value: 'emma',\n    label: 'Emma',\n    description: 'Clear and professional'\n  }, {\n    value: 'lisa',\n    label: '<PERSON>',\n    description: 'Gentle and encouraging'\n  }]\n};\n\n// Grade options\nconst GRADE_OPTIONS = ['Kindergarten', '1st Grade', '2nd Grade', '3rd Grade', '4th Grade', '5th Grade', '6th Grade', '7th Grade', '8th Grade', '9th Grade', '10th Grade', '11th Grade', '12th Grade', 'College', 'Adult'];\nconst VideoCreationPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [currentStep, setCurrentStep] = useState(1);\n\n  // Form state\n  const [formData, setFormData] = useState({\n    lessonName: '',\n    studentName: '',\n    sex: '',\n    grade: '',\n    voice: '',\n    learningObjectives: '',\n    additionalNotes: ''\n  });\n\n  // Handle form input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle voice selection\n  const handleVoiceSelect = voice => {\n    setFormData(prev => ({\n      ...prev,\n      voice\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    console.log('Form submitted:', formData);\n    // TODO: Connect to backend API\n    navigate('/video-preview/123'); // Temporary navigation\n  };\n\n  // Navigate to next step\n  const nextStep = () => {\n    setCurrentStep(prev => Math.min(prev + 1, 3));\n  };\n\n  // Navigate to previous step\n  const prevStep = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  // Get current voice options based on selected sex\n  const getVoiceOptions = () => {\n    return formData.sex === 'male' ? VOICE_OPTIONS.male : VOICE_OPTIONS.female;\n  };\n\n  // Check if current step is valid\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return formData.lessonName.trim() !== '' && formData.studentName.trim() !== '';\n      case 2:\n        return formData.sex !== '' && formData.grade !== '';\n      case 3:\n        return formData.voice !== '';\n      default:\n        return false;\n    }\n  };\n\n  // Render step 1: Basic Information\n  const renderStep1 = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"step-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Let's Get Started\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"step-description\",\n      children: \"Tell us about your lesson and student\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"lessonName\",\n        children: \"Lesson Name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"lessonName\",\n        name: \"lessonName\",\n        value: formData.lessonName,\n        onChange: handleInputChange,\n        placeholder: \"e.g., Introduction to Fractions\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"studentName\",\n        children: \"Student's Name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"studentName\",\n        name: \"studentName\",\n        value: formData.studentName,\n        onChange: handleInputChange,\n        placeholder: \"e.g., Alex\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Learning Objectives\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        name: \"learningObjectives\",\n        value: formData.learningObjectives,\n        onChange: handleInputChange,\n        placeholder: \"What should the student learn from this lesson?\",\n        rows: 4\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n\n  // Render step 2: Student Details\n  const renderStep2 = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"step-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Student Details\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"step-description\",\n      children: \"Help us personalize the learning experience\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Grade Level\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grade-options\",\n        children: GRADE_OPTIONS.map(grade => /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: `grade-option ${formData.grade === grade ? 'selected' : ''}`,\n          onClick: () => setFormData(prev => ({\n            ...prev,\n            grade\n          })),\n          children: grade\n        }, grade, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Voice Preference\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sex-options\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: `sex-option ${formData.sex === 'male' ? 'selected' : ''}`,\n          onClick: () => setFormData(prev => ({\n            ...prev,\n            sex: 'male',\n            voice: ''\n          })),\n          children: \"Male Voice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: `sex-option ${formData.sex === 'female' ? 'selected' : ''}`,\n          onClick: () => setFormData(prev => ({\n            ...prev,\n            sex: 'female',\n            voice: ''\n          })),\n          children: \"Female Voice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), formData.sex && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Additional Notes (Optional)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        name: \"additionalNotes\",\n        value: formData.additionalNotes,\n        onChange: handleInputChange,\n        placeholder: \"Any special instructions or preferences?\",\n        rows: 3\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n\n  // Render step 3: Voice Selection\n  const renderStep3 = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"step-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Choose a Voice\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"step-description\",\n      children: \"Select the voice that best fits your lesson\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"voice-options\",\n      children: getVoiceOptions().map(voice => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `voice-option ${formData.voice === voice.value ? 'selected' : ''}`,\n        onClick: () => handleVoiceSelect(voice.value),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"voice-preview\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"voice-wave\",\n            children: [1, 2, 3, 4, 5].map(i => /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                height: `${Math.random() * 40 + 10}%`,\n                animationDelay: `${i * 0.1}s`\n              }\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"voice-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: voice.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: voice.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"play-button\",\n          onClick: e => {\n            e.stopPropagation();\n            // TODO: Implement voice preview\n          },\n          children: \"\\u25B6\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)]\n      }, voice.value, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Preview Text\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Type something to hear a preview\",\n        className: \"preview-input\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n\n  // Render step indicators\n  const renderStepIndicators = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"step-indicators\",\n    children: [1, 2, 3].map(step => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `step-indicator ${currentStep === step ? 'active' : ''} ${currentStep > step ? 'completed' : ''}`,\n      onClick: () => setCurrentStep(step),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-number\",\n        children: step\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-label\",\n        children: [step === 1 && 'Lesson Info', step === 2 && 'Student Details', step === 3 && 'Voice']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)]\n    }, step, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-creation-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"creation-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Create a Custom Lesson\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), renderStepIndicators(), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"creation-form\",\n        children: [currentStep === 1 && renderStep1(), currentStep === 2 && renderStep2(), currentStep === 3 && renderStep3(), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [currentStep > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"btn btn-secondary\",\n            onClick: prevStep,\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), currentStep < 3 ? /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: `btn btn-primary ${!isStepValid() ? 'disabled' : ''}`,\n            onClick: nextStep,\n            disabled: !isStepValid(),\n            children: \"Continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: `btn btn-primary ${!isStepValid() ? 'disabled' : ''}`,\n            disabled: !isStepValid(),\n            children: \"Create Lesson\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 270,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCreationPage, \"+Wu6o8y5/w2tEug0K8EH8ziY7ok=\", false, function () {\n  return [useNavigate];\n});\n_c = VideoCreationPage;\nexport { VideoCreationPage };\nvar _c;\n$RefreshReg$(_c, \"VideoCreationPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "VOICE_OPTIONS", "male", "value", "label", "description", "female", "GRADE_OPTIONS", "VideoCreationPage", "_s", "navigate", "currentStep", "setCurrentStep", "formData", "setFormData", "lessonName", "studentName", "sex", "grade", "voice", "learningObjectives", "additionalNotes", "handleInputChange", "e", "name", "target", "prev", "handleVoiceSelect", "handleSubmit", "preventDefault", "console", "log", "nextStep", "Math", "min", "prevStep", "max", "getVoiceOptions", "isStepValid", "trim", "renderStep1", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "onChange", "placeholder", "required", "rows", "renderStep2", "map", "onClick", "renderStep3", "i", "style", "height", "random", "animationDelay", "stopPropagation", "renderStepIndicators", "step", "onSubmit", "disabled", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/VideoCreationPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './VideoCreationPage.css';\n\n// Voice options based on sex\nconst VOICE_OPTIONS = {\n  male: [\n    { value: 'david', label: '<PERSON>', description: 'Friendly and clear' },\n    { value: 'marcus', label: '<PERSON>', description: 'Energetic and engaging' },\n    { value: 'james', label: '<PERSON>', description: 'Calm and professional' },\n  ],\n  female: [\n    { value: 'sarah', label: 'Sarah', description: 'Warm and articulate' },\n    { value: 'emma', label: 'Emma', description: 'Clear and professional' },\n    { value: 'lisa', label: 'Lisa', description: 'Gentle and encouraging' },\n  ],\n};\n\n// Grade options\nconst GRADE_OPTIONS = [\n  'Kindergarten', '1st Grade', '2nd Grade', '3rd Grade', '4th Grade',\n  '5th Grade', '6th Grade', '7th Grade', '8th Grade', '9th Grade',\n  '10th Grade', '11th Grade', '12th Grade', 'College', 'Adult'\n];\n\nconst VideoCreationPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [currentStep, setCurrentStep] = useState(1);\n  \n  // Form state\n  const [formData, setFormData] = useState({\n    lessonName: '',\n    studentName: '',\n    sex: '',\n    grade: '',\n    voice: '',\n    learningObjectives: '',\n    additionalNotes: ''\n  });\n\n  // Handle form input changes\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle voice selection\n  const handleVoiceSelect = (voice: string) => {\n    setFormData(prev => ({\n      ...prev,\n      voice\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('Form submitted:', formData);\n    // TODO: Connect to backend API\n    navigate('/video-preview/123'); // Temporary navigation\n  };\n\n  // Navigate to next step\n  const nextStep = () => {\n    setCurrentStep(prev => Math.min(prev + 1, 3));\n  };\n\n  // Navigate to previous step\n  const prevStep = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  // Get current voice options based on selected sex\n  const getVoiceOptions = () => {\n    return formData.sex === 'male' ? VOICE_OPTIONS.male : VOICE_OPTIONS.female;\n  };\n\n  // Check if current step is valid\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return formData.lessonName.trim() !== '' && formData.studentName.trim() !== '';\n      case 2:\n        return formData.sex !== '' && formData.grade !== '';\n      case 3:\n        return formData.voice !== '';\n      default:\n        return false;\n    }\n  };\n\n  // Render step 1: Basic Information\n  const renderStep1 = () => (\n    <div className=\"step-content\">\n      <h2>Let's Get Started</h2>\n      <p className=\"step-description\">Tell us about your lesson and student</p>\n      \n      <div className=\"form-group\">\n        <label htmlFor=\"lessonName\">Lesson Name</label>\n        <input\n          type=\"text\"\n          id=\"lessonName\"\n          name=\"lessonName\"\n          value={formData.lessonName}\n          onChange={handleInputChange}\n          placeholder=\"e.g., Introduction to Fractions\"\n          required\n        />\n      </div>\n      \n      <div className=\"form-group\">\n        <label htmlFor=\"studentName\">Student's Name</label>\n        <input\n          type=\"text\"\n          id=\"studentName\"\n          name=\"studentName\"\n          value={formData.studentName}\n          onChange={handleInputChange}\n          placeholder=\"e.g., Alex\"\n          required\n        />\n      </div>\n      \n      <div className=\"form-group\">\n        <label>Learning Objectives</label>\n        <textarea\n          name=\"learningObjectives\"\n          value={formData.learningObjectives}\n          onChange={handleInputChange}\n          placeholder=\"What should the student learn from this lesson?\"\n          rows={4}\n        />\n      </div>\n    </div>\n  );\n\n  // Render step 2: Student Details\n  const renderStep2 = () => (\n    <div className=\"step-content\">\n      <h2>Student Details</h2>\n      <p className=\"step-description\">Help us personalize the learning experience</p>\n      \n      <div className=\"form-group\">\n        <label>Grade Level</label>\n        <div className=\"grade-options\">\n          {GRADE_OPTIONS.map((grade) => (\n            <button\n              key={grade}\n              type=\"button\"\n              className={`grade-option ${formData.grade === grade ? 'selected' : ''}`}\n              onClick={() => setFormData(prev => ({ ...prev, grade }))}\n            >\n              {grade}\n            </button>\n          ))}\n        </div>\n      </div>\n      \n      <div className=\"form-group\">\n        <label>Voice Preference</label>\n        <div className=\"sex-options\">\n          <button\n            type=\"button\"\n            className={`sex-option ${formData.sex === 'male' ? 'selected' : ''}`}\n            onClick={() => setFormData(prev => ({ ...prev, sex: 'male', voice: '' }))}\n          >\n            Male Voice\n          </button>\n          <button\n            type=\"button\"\n            className={`sex-option ${formData.sex === 'female' ? 'selected' : ''}`}\n            onClick={() => setFormData(prev => ({ ...prev, sex: 'female', voice: '' }))}\n          >\n            Female Voice\n          </button>\n        </div>\n      </div>\n      \n      {formData.sex && (\n        <div className=\"form-group\">\n          <label>Additional Notes (Optional)</label>\n          <textarea\n            name=\"additionalNotes\"\n            value={formData.additionalNotes}\n            onChange={handleInputChange}\n            placeholder=\"Any special instructions or preferences?\"\n            rows={3}\n          />\n        </div>\n      )}\n    </div>\n  );\n\n  // Render step 3: Voice Selection\n  const renderStep3 = () => (\n    <div className=\"step-content\">\n      <h2>Choose a Voice</h2>\n      <p className=\"step-description\">Select the voice that best fits your lesson</p>\n      \n      <div className=\"voice-options\">\n        {getVoiceOptions().map((voice) => (\n          <div\n            key={voice.value}\n            className={`voice-option ${formData.voice === voice.value ? 'selected' : ''}`}\n            onClick={() => handleVoiceSelect(voice.value)}\n          >\n            <div className=\"voice-preview\">\n              <div className=\"voice-wave\">\n                {[1, 2, 3, 4, 5].map((i) => (\n                  <span key={i} style={{\n                    height: `${Math.random() * 40 + 10}%`,\n                    animationDelay: `${i * 0.1}s`\n                  }} />\n                ))}\n              </div>\n            </div>\n            <div className=\"voice-details\">\n              <h3>{voice.label}</h3>\n              <p>{voice.description}</p>\n            </div>\n            <button \n              type=\"button\" \n              className=\"play-button\"\n              onClick={(e) => {\n                e.stopPropagation();\n                // TODO: Implement voice preview\n              }}\n            >\n              ▶️\n            </button>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"form-group\">\n        <label>Preview Text</label>\n        <input\n          type=\"text\"\n          placeholder=\"Type something to hear a preview\"\n          className=\"preview-input\"\n        />\n      </div>\n    </div>\n  );\n\n  // Render step indicators\n  const renderStepIndicators = () => (\n    <div className=\"step-indicators\">\n      {[1, 2, 3].map((step) => (\n        <div\n          key={step}\n          className={`step-indicator ${currentStep === step ? 'active' : ''} ${currentStep > step ? 'completed' : ''}`}\n          onClick={() => setCurrentStep(step)}\n        >\n          <div className=\"step-number\">{step}</div>\n          <div className=\"step-label\">\n            {step === 1 && 'Lesson Info'}\n            {step === 2 && 'Student Details'}\n            {step === 3 && 'Voice'}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n\n  return (\n    <div className=\"video-creation-page\">\n      <div className=\"creation-container\">\n        <h1>Create a Custom Lesson</h1>\n        \n        {renderStepIndicators()}\n        \n        <form onSubmit={handleSubmit} className=\"creation-form\">\n          {currentStep === 1 && renderStep1()}\n          {currentStep === 2 && renderStep2()}\n          {currentStep === 3 && renderStep3()}\n          \n          <div className=\"form-actions\">\n            {currentStep > 1 && (\n              <button\n                type=\"button\"\n                className=\"btn btn-secondary\"\n                onClick={prevStep}\n              >\n                Back\n              </button>\n            )}\n            \n            {currentStep < 3 ? (\n              <button\n                type=\"button\"\n                className={`btn btn-primary ${!isStepValid() ? 'disabled' : ''}`}\n                onClick={nextStep}\n                disabled={!isStepValid()}\n              >\n                Continue\n              </button>\n            ) : (\n              <button\n                type=\"submit\"\n                className={`btn btn-primary ${!isStepValid() ? 'disabled' : ''}`}\n                disabled={!isStepValid()}\n              >\n                Create Lesson\n              </button>\n            )}\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport { VideoCreationPage };"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,yBAAyB;;AAEhC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAG;EACpBC,IAAI,EAAE,CACJ;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAqB,CAAC,EACrE;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAAyB,CAAC,EAC3E;IAAEF,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAwB,CAAC,CACzE;EACDC,MAAM,EAAE,CACN;IAAEH,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAsB,CAAC,EACtE;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,WAAW,EAAE;EAAyB,CAAC,EACvE;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,WAAW,EAAE;EAAyB,CAAC;AAE3E,CAAC;;AAED;AACA,MAAME,aAAa,GAAG,CACpB,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAClE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAC/D,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,CAC7D;AAED,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;;EAEjD;EACA,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,kBAAkB,EAAE,EAAE;IACtBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,iBAAiB,GAAIC,CAAgF,IAAK;IAC9G,MAAM;MAAEC,IAAI;MAAErB;IAAM,CAAC,GAAGoB,CAAC,CAACE,MAAM;IAChCX,WAAW,CAACY,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGrB;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAIR,KAAa,IAAK;IAC3CL,WAAW,CAACY,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPP;IACF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMS,YAAY,GAAIL,CAAkB,IAAK;IAC3CA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElB,QAAQ,CAAC;IACxC;IACAH,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;EAClC,CAAC;;EAED;EACA,MAAMsB,QAAQ,GAAGA,CAAA,KAAM;IACrBpB,cAAc,CAACc,IAAI,IAAIO,IAAI,CAACC,GAAG,CAACR,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMS,QAAQ,GAAGA,CAAA,KAAM;IACrBvB,cAAc,CAACc,IAAI,IAAIO,IAAI,CAACG,GAAG,CAACV,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMW,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOxB,QAAQ,CAACI,GAAG,KAAK,MAAM,GAAGhB,aAAa,CAACC,IAAI,GAAGD,aAAa,CAACK,MAAM;EAC5E,CAAC;;EAED;EACA,MAAMgC,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAQ3B,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOE,QAAQ,CAACE,UAAU,CAACwB,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI1B,QAAQ,CAACG,WAAW,CAACuB,IAAI,CAAC,CAAC,KAAK,EAAE;MAChF,KAAK,CAAC;QACJ,OAAO1B,QAAQ,CAACI,GAAG,KAAK,EAAE,IAAIJ,QAAQ,CAACK,KAAK,KAAK,EAAE;MACrD,KAAK,CAAC;QACJ,OAAOL,QAAQ,CAACM,KAAK,KAAK,EAAE;MAC9B;QACE,OAAO,KAAK;IAChB;EACF,CAAC;;EAED;EACA,MAAMqB,WAAW,GAAGA,CAAA,kBAClBxC,OAAA;IAAKyC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1C,OAAA;MAAA0C,QAAA,EAAI;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1B9C,OAAA;MAAGyC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAqC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEzE9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAO+C,OAAO,EAAC,YAAY;QAAAL,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/C9C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,YAAY;QACfzB,IAAI,EAAC,YAAY;QACjBrB,KAAK,EAAEU,QAAQ,CAACE,UAAW;QAC3BmC,QAAQ,EAAE5B,iBAAkB;QAC5B6B,WAAW,EAAC,iCAAiC;QAC7CC,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAO+C,OAAO,EAAC,aAAa;QAAAL,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnD9C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,aAAa;QAChBzB,IAAI,EAAC,aAAa;QAClBrB,KAAK,EAAEU,QAAQ,CAACG,WAAY;QAC5BkC,QAAQ,EAAE5B,iBAAkB;QAC5B6B,WAAW,EAAC,YAAY;QACxBC,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAA0C,QAAA,EAAO;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClC9C,OAAA;QACEwB,IAAI,EAAC,oBAAoB;QACzBrB,KAAK,EAAEU,QAAQ,CAACO,kBAAmB;QACnC8B,QAAQ,EAAE5B,iBAAkB;QAC5B6B,WAAW,EAAC,iDAAiD;QAC7DE,IAAI,EAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMQ,WAAW,GAAGA,CAAA,kBAClBtD,OAAA;IAAKyC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1C,OAAA;MAAA0C,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxB9C,OAAA;MAAGyC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAE/E9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAA0C,QAAA,EAAO;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1B9C,OAAA;QAAKyC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BnC,aAAa,CAACgD,GAAG,CAAErC,KAAK,iBACvBlB,OAAA;UAEEgD,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAE,gBAAgB5B,QAAQ,CAACK,KAAK,KAAKA,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;UACxEsC,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACY,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAER;UAAM,CAAC,CAAC,CAAE;UAAAwB,QAAA,EAExDxB;QAAK,GALDA,KAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAA0C,QAAA,EAAO;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B9C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA;UACEgD,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAE,cAAc5B,QAAQ,CAACI,GAAG,KAAK,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;UACrEuC,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACY,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAET,GAAG,EAAE,MAAM;YAAEE,KAAK,EAAE;UAAG,CAAC,CAAC,CAAE;UAAAuB,QAAA,EAC3E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9C,OAAA;UACEgD,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAE,cAAc5B,QAAQ,CAACI,GAAG,KAAK,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;UACvEuC,OAAO,EAAEA,CAAA,KAAM1C,WAAW,CAACY,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAET,GAAG,EAAE,QAAQ;YAAEE,KAAK,EAAE;UAAG,CAAC,CAAC,CAAE;UAAAuB,QAAA,EAC7E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELjC,QAAQ,CAACI,GAAG,iBACXjB,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAA0C,QAAA,EAAO;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1C9C,OAAA;QACEwB,IAAI,EAAC,iBAAiB;QACtBrB,KAAK,EAAEU,QAAQ,CAACQ,eAAgB;QAChC6B,QAAQ,EAAE5B,iBAAkB;QAC5B6B,WAAW,EAAC,0CAA0C;QACtDE,IAAI,EAAE;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,kBAClBzD,OAAA;IAAKyC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1C,OAAA;MAAA0C,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvB9C,OAAA;MAAGyC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAE/E9C,OAAA;MAAKyC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BL,eAAe,CAAC,CAAC,CAACkB,GAAG,CAAEpC,KAAK,iBAC3BnB,OAAA;QAEEyC,SAAS,EAAE,gBAAgB5B,QAAQ,CAACM,KAAK,KAAKA,KAAK,CAAChB,KAAK,GAAG,UAAU,GAAG,EAAE,EAAG;QAC9EqD,OAAO,EAAEA,CAAA,KAAM7B,iBAAiB,CAACR,KAAK,CAAChB,KAAK,CAAE;QAAAuC,QAAA,gBAE9C1C,OAAA;UAAKyC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B1C,OAAA;YAAKyC,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACa,GAAG,CAAEG,CAAC,iBACrB1D,OAAA;cAAc2D,KAAK,EAAE;gBACnBC,MAAM,EAAE,GAAG3B,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrCC,cAAc,EAAE,GAAGJ,CAAC,GAAG,GAAG;cAC5B;YAAE,GAHSA,CAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGR,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9C,OAAA;UAAKyC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1C,OAAA;YAAA0C,QAAA,EAAKvB,KAAK,CAACf;UAAK;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtB9C,OAAA;YAAA0C,QAAA,EAAIvB,KAAK,CAACd;UAAW;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN9C,OAAA;UACEgD,IAAI,EAAC,QAAQ;UACbP,SAAS,EAAC,aAAa;UACvBe,OAAO,EAAGjC,CAAC,IAAK;YACdA,CAAC,CAACwC,eAAe,CAAC,CAAC;YACnB;UACF,CAAE;UAAArB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GA3BJ3B,KAAK,CAAChB,KAAK;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4Bb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN9C,OAAA;MAAKyC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1C,OAAA;QAAA0C,QAAA,EAAO;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC3B9C,OAAA;QACEgD,IAAI,EAAC,MAAM;QACXG,WAAW,EAAC,kCAAkC;QAC9CV,SAAS,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMkB,oBAAoB,GAAGA,CAAA,kBAC3BhE,OAAA;IAAKyC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAC7B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACa,GAAG,CAAEU,IAAI,iBAClBjE,OAAA;MAEEyC,SAAS,EAAE,kBAAkB9B,WAAW,KAAKsD,IAAI,GAAG,QAAQ,GAAG,EAAE,IAAItD,WAAW,GAAGsD,IAAI,GAAG,WAAW,GAAG,EAAE,EAAG;MAC7GT,OAAO,EAAEA,CAAA,KAAM5C,cAAc,CAACqD,IAAI,CAAE;MAAAvB,QAAA,gBAEpC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEuB;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzC9C,OAAA;QAAKyC,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxBuB,IAAI,KAAK,CAAC,IAAI,aAAa,EAC3BA,IAAI,KAAK,CAAC,IAAI,iBAAiB,EAC/BA,IAAI,KAAK,CAAC,IAAI,OAAO;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA,GATDmB,IAAI;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUN,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACE9C,OAAA;IAAKyC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClC1C,OAAA;MAAKyC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC1C,OAAA;QAAA0C,QAAA,EAAI;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE9BkB,oBAAoB,CAAC,CAAC,eAEvBhE,OAAA;QAAMkE,QAAQ,EAAEtC,YAAa;QAACa,SAAS,EAAC,eAAe;QAAAC,QAAA,GACpD/B,WAAW,KAAK,CAAC,IAAI6B,WAAW,CAAC,CAAC,EAClC7B,WAAW,KAAK,CAAC,IAAI2C,WAAW,CAAC,CAAC,EAClC3C,WAAW,KAAK,CAAC,IAAI8C,WAAW,CAAC,CAAC,eAEnCzD,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,GAC1B/B,WAAW,GAAG,CAAC,iBACdX,OAAA;YACEgD,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,mBAAmB;YAC7Be,OAAO,EAAErB,QAAS;YAAAO,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,EAEAnC,WAAW,GAAG,CAAC,gBACdX,OAAA;YACEgD,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAE,mBAAmB,CAACH,WAAW,CAAC,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;YACjEkB,OAAO,EAAExB,QAAS;YAClBmC,QAAQ,EAAE,CAAC7B,WAAW,CAAC,CAAE;YAAAI,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET9C,OAAA;YACEgD,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAE,mBAAmB,CAACH,WAAW,CAAC,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;YACjE6B,QAAQ,EAAE,CAAC7B,WAAW,CAAC,CAAE;YAAAI,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CAjSID,iBAA2B;EAAA,QACdV,WAAW;AAAA;AAAAsE,EAAA,GADxB5D,iBAA2B;AAmSjC,SAASA,iBAAiB;AAAG,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}