{"ast": null, "code": "import { ReactReduxContext } from '../components/Context';\nimport { useStore as useDefaultStore, createStoreHook } from './useStore';\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\n\nexport function createDispatchHook() {\n  let context = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ReactReduxContext;\n  const useStore =\n  // @ts-ignore\n  context === ReactReduxContext ? useDefaultStore : createStoreHook(context);\n  return function useDispatch() {\n    const store = useStore(); // @ts-ignore\n\n    return store.dispatch;\n  };\n}\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\n\nexport const useDispatch = /*#__PURE__*/createDispatchHook();", "map": {"version": 3, "names": ["ReactReduxContext", "useStore", "useDefaultStore", "createStoreHook", "createDispatchHook", "context", "arguments", "length", "undefined", "useDispatch", "store", "dispatch"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/hooks/useDispatch.js"], "sourcesContent": ["import { ReactReduxContext } from '../components/Context';\nimport { useStore as useDefaultStore, createStoreHook } from './useStore';\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\n\nexport function createDispatchHook(context = ReactReduxContext) {\n  const useStore = // @ts-ignore\n  context === ReactReduxContext ? useDefaultStore : createStoreHook(context);\n  return function useDispatch() {\n    const store = useStore(); // @ts-ignore\n\n    return store.dispatch;\n  };\n}\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\n\nexport const useDispatch = /*#__PURE__*/createDispatchHook();"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,QAAQ,IAAIC,eAAe,EAAEC,eAAe,QAAQ,YAAY;AACzE;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAAA,EAA8B;EAAA,IAA7BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,iBAAiB;EAC5D,MAAMC,QAAQ;EAAG;EACjBI,OAAO,KAAKL,iBAAiB,GAAGE,eAAe,GAAGC,eAAe,CAACE,OAAO,CAAC;EAC1E,OAAO,SAASI,WAAWA,CAAA,EAAG;IAC5B,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC,CAAC,CAAC;;IAE1B,OAAOS,KAAK,CAACC,QAAQ;EACvB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMF,WAAW,GAAG,aAAaL,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}