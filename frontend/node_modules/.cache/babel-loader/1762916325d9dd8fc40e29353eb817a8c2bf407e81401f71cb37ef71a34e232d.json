{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./CreateVideoPage.css';import Gyroscope from'../Xtra/Gyroscope';import'../Xtra/Gyroscope.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CreateVideoPage=()=>{const[formVisible,setFormVisible]=useState(false);const[currentField,setCurrentField]=useState(0);const[lessonTitle,setLessonTitle]=useState('');const[childFieldsVisible,setChildFieldsVisible]=useState(false);useEffect(()=>{// Delayed entrance for dramatic effect\nsetTimeout(()=>{setFormVisible(true);setCurrentField(1);// Show lesson title\n},800);},[]);useEffect(()=>{if(lessonTitle.length>=3){setChildFieldsVisible(true);}else{setChildFieldsVisible(false);setCurrentField(1);// Reset to show only first\n}},[lessonTitle]);useEffect(()=>{if(formVisible&&childFieldsVisible){const timer=setInterval(()=>{setCurrentField(prev=>prev<formFields.length?prev+1:prev);},600);return()=>clearInterval(timer);}},[formVisible,childFieldsVisible]);const formFields=[{label:\"lesson title\",type:\"text\",placeholder:\"enter your video lesson title...\"},{label:\"student's first name\",type:\"text\",placeholder:\"All Of This Is Optional!!\"},{label:\"student's age\",type:\"select\",options:[\"6\",\"7\",\"8\",\"9\"]},{label:\"teacher's voice\",type:\"select\",options:[\"male\",\"female\",\"clone your own\"]},{label:\"student's voice\",type:\"select\",options:[\"male\",\"female\",\"clone your own\"]}];return/*#__PURE__*/_jsxs(\"div\",{className:\"westworld-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"particles-container\",children:[...Array(20)].map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"floating-particle\",style:{left:\"\".concat(Math.random()*100,\"%\"),top:\"\".concat(Math.random()*100,\"%\"),animationDelay:\"\".concat(Math.random()*3,\"s\"),animationDuration:\"\".concat(2+Math.random()*2,\"s\")}},i))}),/*#__PURE__*/_jsx(\"div\",{className:\"milk-waves\",children:/*#__PURE__*/_jsxs(\"svg\",{width:\"100%\",height:\"100%\",viewBox:\"0 0 1000 1000\",preserveAspectRatio:\"none\",children:[/*#__PURE__*/_jsx(\"defs\",{children:/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"milkGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#ffffff\",stopOpacity:\"0.8\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"50%\",stopColor:\"#f8fafc\",stopOpacity:\"0.6\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#f1f5f9\",stopOpacity:\"0.4\"})]})}),/*#__PURE__*/_jsx(\"path\",{d:\"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\",fill:\"url(#milkGradient)\",className:\"milk-surface\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-wrapper\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-container \".concat(formVisible?'visible':'hidden'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"title-section\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"CREATE\"}),/*#__PURE__*/_jsx(\"div\",{className:\"title-line\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-fields\",children:[/*#__PURE__*/_jsx(_Fragment,{children:formFields.map((field,index)=>{var _field$options2;// Special handling for student's first name and age on same line\nif(index===1){var _formFields$2$options;return/*#__PURE__*/_jsxs(\"div\",{className:\"field-wrapper combined-fields \".concat(childFieldsVisible?currentField>index?'emerged':'emerging':'hidden'),style:{transitionDelay:\"\".concat(index*200,\"ms\")},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"inline-field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"field-label\",children:field.label}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:field.placeholder,className:\"field-input\",value:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"inline-field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"field-label\",children:formFields[2].label}),/*#__PURE__*/_jsxs(\"select\",{className:\"field-select\",children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",formFields[2].label.toLowerCase(),\"...\"]}),(_formFields$2$options=formFields[2].options)===null||_formFields$2$options===void 0?void 0:_formFields$2$options.map((option,optIndex)=>/*#__PURE__*/_jsx(\"option\",{value:option,children:option},optIndex))]})]})]},\"combined-\".concat(index));}// Skip the age field since it's included in the combined container\nif(index===2)return null;// Special handling for teacher's voice and student's voice on same line\nif(index===3){var _field$options,_formFields$4$options;return/*#__PURE__*/_jsxs(\"div\",{className:\"field-wrapper combined-fields \".concat(childFieldsVisible?currentField>index?'emerged':'emerging':'hidden'),style:{transitionDelay:\"\".concat(index*200,\"ms\")},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"inline-field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"field-label\",children:field.label}),/*#__PURE__*/_jsxs(\"select\",{className:\"field-select\",children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",field.label.toLowerCase(),\"...\"]}),(_field$options=field.options)===null||_field$options===void 0?void 0:_field$options.map((option,optIndex)=>/*#__PURE__*/_jsx(\"option\",{value:option,children:option},optIndex))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"inline-field\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"field-label\",children:formFields[4].label}),/*#__PURE__*/_jsxs(\"select\",{className:\"field-select\",children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",formFields[4].label.toLowerCase(),\"...\"]}),(_formFields$4$options=formFields[4].options)===null||_formFields$4$options===void 0?void 0:_formFields$4$options.map((option,optIndex)=>/*#__PURE__*/_jsx(\"option\",{value:option,children:option},optIndex))]})]})]},\"combined-voices-\".concat(index));}// Skip the student's voice field since it's included in the combined container\nif(index===4)return null;// Regular field rendering\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"field-wrapper \".concat(index===0?'emerged':childFieldsVisible?currentField>index?'emerged':'emerging':'hidden'),style:{transitionDelay:\"\".concat(index*200,\"ms\")},children:[/*#__PURE__*/_jsx(\"label\",{className:\"field-label\",children:field.label}),field.type==='text'&&/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:field.placeholder,className:\"field-input\",value:index===0?lessonTitle:'',onChange:index===0?e=>setLessonTitle(e.target.value):undefined}),field.type==='select'&&/*#__PURE__*/_jsxs(\"select\",{className:\"field-select\",children:[/*#__PURE__*/_jsxs(\"option\",{value:\"\",children:[\"Select \",field.label.toLowerCase(),\"...\"]}),(_field$options2=field.options)===null||_field$options2===void 0?void 0:_field$options2.map((option,optIndex)=>/*#__PURE__*/_jsx(\"option\",{value:option,children:option},optIndex))]}),field.type==='textarea'&&/*#__PURE__*/_jsx(\"textarea\",{placeholder:field.placeholder,rows:4,className:\"field-textarea\"})]},index);})}),/*#__PURE__*/_jsx(\"div\",{className:\"gyroscope-wrapper \".concat(childFieldsVisible&&currentField>formFields.length-1?'emerged':'hidden'),children:/*#__PURE__*/_jsx(\"div\",{className:\"gyroscope-test-container\",onClick:()=>console.log('Generate video clicked!'),children:/*#__PURE__*/_jsx(Gyroscope,{})})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"gyroscope-section \".concat(childFieldsVisible&&currentField>formFields.length-1?'visible':''),children:/*#__PURE__*/_jsx(Gyroscope,{})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid-overlay\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid-pattern\",children:[...Array(400)].map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"grid-cell\"},i))})}),/*#__PURE__*/_jsx(\"div\",{className:\"ambient-light\"})]});};export default CreateVideoPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Gyroscope", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CreateVideoPage", "formVisible", "setFormVisible", "current<PERSON><PERSON>", "setCurrentField", "lessonTitle", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childFieldsVisible", "<PERSON><PERSON><PERSON><PERSON>FieldsVisible", "setTimeout", "length", "timer", "setInterval", "prev", "formFields", "clearInterval", "label", "type", "placeholder", "options", "className", "children", "Array", "map", "_", "i", "style", "left", "concat", "Math", "random", "top", "animationDelay", "animationDuration", "width", "height", "viewBox", "preserveAspectRatio", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "d", "fill", "field", "index", "_field$options2", "_formFields$2$options", "transitionDelay", "value", "toLowerCase", "option", "optIndex", "_field$options", "_formFields$4$options", "onChange", "e", "target", "undefined", "rows", "onClick", "console", "log"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport '../Xtra/Gyroscope.css';\n\nconst CreateVideoPage = () => {\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n\n  const formFields = [\n    { label: \"lesson title\", type: \"text\", placeholder: \"enter your video lesson title...\" },\n    { label: \"student's first name\", type: \"text\", placeholder: \"All Of This Is Optional!!\" },\n    { label: \"student's age\", type: \"select\", options: [\"6\", \"7\", \"8\", \"9\"] },\n    { label: \"teacher's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] },\n    { label: \"student's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] }\n  ];\n\n  return (\n    <div className=\"westworld-container\">\n      {/* Floating particles for depth */}\n      <div className=\"particles-container\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"floating-particle\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${2 + Math.random() * 2}s`\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Milk-like surface waves */}\n      <div className=\"milk-waves\">\n        <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1000 1000\" preserveAspectRatio=\"none\">\n          <defs>\n            <linearGradient id=\"milkGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#ffffff\" stopOpacity=\"0.8\" />\n              <stop offset=\"50%\" stopColor=\"#f8fafc\" stopOpacity=\"0.6\" />\n              <stop offset=\"100%\" stopColor=\"#f1f5f9\" stopOpacity=\"0.4\" />\n            </linearGradient>\n          </defs>\n          <path\n            d=\"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\"\n            fill=\"url(#milkGradient)\"\n            className=\"milk-surface\"\n          />\n        </svg>\n      </div>\n\n      {/* Main content */}\n      <div className=\"content-wrapper\">\n        <div className={`form-container ${formVisible ? 'visible' : 'hidden'}`}>\n          {/* Title */}\n          <div className=\"title-section\">\n            <h1 className=\"main-title\">CREATE</h1>\n            <div className=\"title-line\"></div>\n          </div>\n\n          {/* Form */}\n          <div className=\"form-fields\">\n            <>\n              {formFields.map((field, index) => {\n                // Special handling for student's first name and age on same line\n                if (index === 1) {\n                  return (\n                    <div\n                      key={`combined-${index}`}\n                      className={`field-wrapper combined-fields ${\n                        childFieldsVisible\n                          ? currentField > index ? 'emerged' : 'emerging'\n                          : 'hidden'\n                      }`}\n                      style={{ transitionDelay: `${index * 200}ms` }}\n                    >\n                      {/* First Name Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{field.label}</label>\n                        <input\n                          type=\"text\"\n                          placeholder={field.placeholder}\n                          className=\"field-input\"\n                          value=\"\"\n                        />\n                      </div>\n\n                      {/* Age Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{formFields[2].label}</label>\n                        <select className=\"field-select\">\n                          <option value=\"\">Select {formFields[2].label.toLowerCase()}...</option>\n                          {formFields[2].options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n                    </div>\n                  );\n                }\n\n                // Skip the age field since it's included in the combined container\n                if (index === 2) return null;\n\n                // Special handling for teacher's voice and student's voice on same line\n                if (index === 3) {\n                  return (\n                    <div\n                      key={`combined-voices-${index}`}\n                      className={`field-wrapper combined-fields ${\n                        childFieldsVisible\n                          ? currentField > index ? 'emerged' : 'emerging'\n                          : 'hidden'\n                      }`}\n                      style={{ transitionDelay: `${index * 200}ms` }}\n                    >\n                      {/* Teacher's Voice Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{field.label}</label>\n                        <select className=\"field-select\">\n                          <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                          {field.options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n\n                      {/* Student's Voice Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{formFields[4].label}</label>\n                        <select className=\"field-select\">\n                          <option value=\"\">Select {formFields[4].label.toLowerCase()}...</option>\n                          {formFields[4].options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n                    </div>\n                  );\n                }\n\n                // Skip the student's voice field since it's included in the combined container\n                if (index === 4) return null;\n\n                // Regular field rendering\n                return (\n                  <div\n                    key={index}\n                    className={`field-wrapper ${\n                      index === 0\n                        ? 'emerged'\n                        : childFieldsVisible\n                        ? currentField > index ? 'emerged' : 'emerging'\n                        : 'hidden'\n                    }`}\n                    style={{ transitionDelay: `${index * 200}ms` }}\n                  >\n                    <label className=\"field-label\">{field.label}</label>\n                    {field.type === 'text' && (\n                      <input\n                        type=\"text\"\n                        placeholder={field.placeholder}\n                        className=\"field-input\"\n                        value={index === 0 ? lessonTitle : ''}\n                        onChange={index === 0 ? (e) => setLessonTitle(e.target.value) : undefined}\n                      />\n                    )}\n                    {field.type === 'select' && (\n                      <select className=\"field-select\">\n                        <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                        {field.options?.map((option, optIndex) => (\n                          <option key={optIndex} value={option}>{option}</option>\n                        ))}\n                      </select>\n                    )}\n                    {field.type === 'textarea' && (\n                      <textarea\n                        placeholder={field.placeholder}\n                        rows={4}\n                        className=\"field-textarea\"\n                      />\n                    )}\n                  </div>\n                );\n              })}\n            </>\n\n            {/* Gyroscope Generate Button */}\n            <div className={`gyroscope-wrapper ${\n              childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'\n            }`}>\n              <div\n                className=\"gyroscope-test-container\"\n                onClick={() => console.log('Generate video clicked!')}\n              >\n                <Gyroscope />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Gyroscope Section */}\n        <div className={`gyroscope-section ${childFieldsVisible && currentField > formFields.length - 1 ? 'visible' : ''}`}>\n          <Gyroscope />\n        </div>\n      </div>\n\n      {/* Grid overlay and ambient light */}\n      <div className=\"grid-overlay\">\n        <div className=\"grid-pattern\">\n          {[...Array(400)].map((_, i) => <div key={i} className=\"grid-cell\"></div>)}\n        </div>\n      </div>\n      <div className=\"ambient-light\"></div>\n    </div>\n  );\n};\n\nexport default CreateVideoPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,uBAAuB,CAC9B,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE/B,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGb,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACc,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACgB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAEnEC,SAAS,CAAC,IAAM,CACd;AACAiB,UAAU,CAAC,IAAM,CACfP,cAAc,CAAC,IAAI,CAAC,CACpBE,eAAe,CAAC,CAAC,CAAC,CAAE;AACtB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,CAAE,EAAE,CAAC,CAENZ,SAAS,CAAC,IAAM,CACd,GAAIa,WAAW,CAACK,MAAM,EAAI,CAAC,CAAE,CAC3BF,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,IAAM,CACLA,qBAAqB,CAAC,KAAK,CAAC,CAC5BJ,eAAe,CAAC,CAAC,CAAC,CAAE;AACtB,CACF,CAAC,CAAE,CAACC,WAAW,CAAC,CAAC,CAEjBb,SAAS,CAAC,IAAM,CACd,GAAIS,WAAW,EAAIM,kBAAkB,CAAE,CACrC,KAAM,CAAAI,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9BR,eAAe,CAACS,IAAI,EAAIA,IAAI,CAAGC,UAAU,CAACJ,MAAM,CAAGG,IAAI,CAAG,CAAC,CAAGA,IAAI,CAAC,CACrE,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAME,aAAa,CAACJ,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,CAACV,WAAW,CAAEM,kBAAkB,CAAC,CAAC,CAErC,KAAM,CAAAO,UAAU,CAAG,CACjB,CAAEE,KAAK,CAAE,cAAc,CAAEC,IAAI,CAAE,MAAM,CAAEC,WAAW,CAAE,kCAAmC,CAAC,CACxF,CAAEF,KAAK,CAAE,sBAAsB,CAAEC,IAAI,CAAE,MAAM,CAAEC,WAAW,CAAE,2BAA4B,CAAC,CACzF,CAAEF,KAAK,CAAE,eAAe,CAAEC,IAAI,CAAE,QAAQ,CAAEE,OAAO,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,CAAC,CACzE,CAAEH,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAE,QAAQ,CAAEE,OAAO,CAAE,CAAC,MAAM,CAAE,QAAQ,CAAE,gBAAgB,CAAE,CAAC,CAC3F,CAAEH,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAE,QAAQ,CAAEE,OAAO,CAAE,CAAC,MAAM,CAAE,QAAQ,CAAE,gBAAgB,CAAE,CAAC,CAC5F,CAED,mBACEtB,KAAA,QAAKuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAElC1B,IAAA,QAAKyB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CACjC,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAEC,CAAC,gBACvB9B,IAAA,QAEEyB,SAAS,CAAC,mBAAmB,CAC7BM,KAAK,CAAE,CACLC,IAAI,IAAAC,MAAA,CAAKC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/BC,GAAG,IAAAH,MAAA,CAAKC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9BE,cAAc,IAAAJ,MAAA,CAAKC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,KAAG,CACvCG,iBAAiB,IAAAL,MAAA,CAAK,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,KAC7C,CAAE,EAPGL,CAQN,CACF,CAAC,CACC,CAAC,cAGN9B,IAAA,QAAKyB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzBxB,KAAA,QAAKqC,KAAK,CAAC,MAAM,CAACC,MAAM,CAAC,MAAM,CAACC,OAAO,CAAC,eAAe,CAACC,mBAAmB,CAAC,MAAM,CAAAhB,QAAA,eAChF1B,IAAA,SAAA0B,QAAA,cACExB,KAAA,mBAAgByC,EAAE,CAAC,cAAc,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,MAAM,CAAArB,QAAA,eACnE1B,IAAA,SAAMgD,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAACC,WAAW,CAAC,KAAK,CAAE,CAAC,cAC1DlD,IAAA,SAAMgD,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,SAAS,CAACC,WAAW,CAAC,KAAK,CAAE,CAAC,cAC3DlD,IAAA,SAAMgD,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAACC,WAAW,CAAC,KAAK,CAAE,CAAC,EAC9C,CAAC,CACb,CAAC,cACPlD,IAAA,SACEmD,CAAC,CAAC,wDAAwD,CAC1DC,IAAI,CAAC,oBAAoB,CACzB3B,SAAS,CAAC,cAAc,CACzB,CAAC,EACC,CAAC,CACH,CAAC,cAGNvB,KAAA,QAAKuB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxB,KAAA,QAAKuB,SAAS,mBAAAQ,MAAA,CAAoB3B,WAAW,CAAG,SAAS,CAAG,QAAQ,CAAG,CAAAoB,QAAA,eAErExB,KAAA,QAAKuB,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B1B,IAAA,OAAIyB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACtC1B,IAAA,QAAKyB,SAAS,CAAC,YAAY,CAAM,CAAC,EAC/B,CAAC,cAGNvB,KAAA,QAAKuB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B1B,IAAA,CAAAI,SAAA,EAAAsB,QAAA,CACGP,UAAU,CAACS,GAAG,CAAC,CAACyB,KAAK,CAAEC,KAAK,GAAK,KAAAC,eAAA,CAChC;AACA,GAAID,KAAK,GAAK,CAAC,CAAE,KAAAE,qBAAA,CACf,mBACEtD,KAAA,QAEEuB,SAAS,kCAAAQ,MAAA,CACPrB,kBAAkB,CACdJ,YAAY,CAAG8C,KAAK,CAAG,SAAS,CAAG,UAAU,CAC7C,QAAQ,CACX,CACHvB,KAAK,CAAE,CAAE0B,eAAe,IAAAxB,MAAA,CAAKqB,KAAK,CAAG,GAAG,MAAK,CAAE,CAAA5B,QAAA,eAG/CxB,KAAA,QAAKuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1B,IAAA,UAAOyB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE2B,KAAK,CAAChC,KAAK,CAAQ,CAAC,cACpDrB,IAAA,UACEsB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAE8B,KAAK,CAAC9B,WAAY,CAC/BE,SAAS,CAAC,aAAa,CACvBiC,KAAK,CAAC,EAAE,CACT,CAAC,EACC,CAAC,cAGNxD,KAAA,QAAKuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1B,IAAA,UAAOyB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAAQ,CAAC,cAC5DnB,KAAA,WAAQuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC9BxB,KAAA,WAAQwD,KAAK,CAAC,EAAE,CAAAhC,QAAA,EAAC,SAAO,CAACP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACsC,WAAW,CAAC,CAAC,CAAC,KAAG,EAAQ,CAAC,EAAAH,qBAAA,CACtErC,UAAU,CAAC,CAAC,CAAC,CAACK,OAAO,UAAAgC,qBAAA,iBAArBA,qBAAA,CAAuB5B,GAAG,CAAC,CAACgC,MAAM,CAAEC,QAAQ,gBAC3C7D,IAAA,WAAuB0D,KAAK,CAAEE,MAAO,CAAAlC,QAAA,CAAEkC,MAAM,EAAhCC,QAAyC,CACvD,CAAC,EACI,CAAC,EACN,CAAC,eAAA5B,MAAA,CA5BWqB,KAAK,CA6BnB,CAAC,CAEV,CAEA;AACA,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAI,CAE5B;AACA,GAAIA,KAAK,GAAK,CAAC,CAAE,KAAAQ,cAAA,CAAAC,qBAAA,CACf,mBACE7D,KAAA,QAEEuB,SAAS,kCAAAQ,MAAA,CACPrB,kBAAkB,CACdJ,YAAY,CAAG8C,KAAK,CAAG,SAAS,CAAG,UAAU,CAC7C,QAAQ,CACX,CACHvB,KAAK,CAAE,CAAE0B,eAAe,IAAAxB,MAAA,CAAKqB,KAAK,CAAG,GAAG,MAAK,CAAE,CAAA5B,QAAA,eAG/CxB,KAAA,QAAKuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1B,IAAA,UAAOyB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE2B,KAAK,CAAChC,KAAK,CAAQ,CAAC,cACpDnB,KAAA,WAAQuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC9BxB,KAAA,WAAQwD,KAAK,CAAC,EAAE,CAAAhC,QAAA,EAAC,SAAO,CAAC2B,KAAK,CAAChC,KAAK,CAACsC,WAAW,CAAC,CAAC,CAAC,KAAG,EAAQ,CAAC,EAAAG,cAAA,CAC9DT,KAAK,CAAC7B,OAAO,UAAAsC,cAAA,iBAAbA,cAAA,CAAelC,GAAG,CAAC,CAACgC,MAAM,CAAEC,QAAQ,gBACnC7D,IAAA,WAAuB0D,KAAK,CAAEE,MAAO,CAAAlC,QAAA,CAAEkC,MAAM,EAAhCC,QAAyC,CACvD,CAAC,EACI,CAAC,EACN,CAAC,cAGN3D,KAAA,QAAKuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1B,IAAA,UAAOyB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAAQ,CAAC,cAC5DnB,KAAA,WAAQuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC9BxB,KAAA,WAAQwD,KAAK,CAAC,EAAE,CAAAhC,QAAA,EAAC,SAAO,CAACP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACsC,WAAW,CAAC,CAAC,CAAC,KAAG,EAAQ,CAAC,EAAAI,qBAAA,CACtE5C,UAAU,CAAC,CAAC,CAAC,CAACK,OAAO,UAAAuC,qBAAA,iBAArBA,qBAAA,CAAuBnC,GAAG,CAAC,CAACgC,MAAM,CAAEC,QAAQ,gBAC3C7D,IAAA,WAAuB0D,KAAK,CAAEE,MAAO,CAAAlC,QAAA,CAAEkC,MAAM,EAAhCC,QAAyC,CACvD,CAAC,EACI,CAAC,EACN,CAAC,sBAAA5B,MAAA,CA5BkBqB,KAAK,CA6B1B,CAAC,CAEV,CAEA;AACA,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,KAAI,CAE5B;AACA,mBACEpD,KAAA,QAEEuB,SAAS,kBAAAQ,MAAA,CACPqB,KAAK,GAAK,CAAC,CACP,SAAS,CACT1C,kBAAkB,CAClBJ,YAAY,CAAG8C,KAAK,CAAG,SAAS,CAAG,UAAU,CAC7C,QAAQ,CACX,CACHvB,KAAK,CAAE,CAAE0B,eAAe,IAAAxB,MAAA,CAAKqB,KAAK,CAAG,GAAG,MAAK,CAAE,CAAA5B,QAAA,eAE/C1B,IAAA,UAAOyB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE2B,KAAK,CAAChC,KAAK,CAAQ,CAAC,CACnDgC,KAAK,CAAC/B,IAAI,GAAK,MAAM,eACpBtB,IAAA,UACEsB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAE8B,KAAK,CAAC9B,WAAY,CAC/BE,SAAS,CAAC,aAAa,CACvBiC,KAAK,CAAEJ,KAAK,GAAK,CAAC,CAAG5C,WAAW,CAAG,EAAG,CACtCsD,QAAQ,CAAEV,KAAK,GAAK,CAAC,CAAIW,CAAC,EAAKtD,cAAc,CAACsD,CAAC,CAACC,MAAM,CAACR,KAAK,CAAC,CAAGS,SAAU,CAC3E,CACF,CACAd,KAAK,CAAC/B,IAAI,GAAK,QAAQ,eACtBpB,KAAA,WAAQuB,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC9BxB,KAAA,WAAQwD,KAAK,CAAC,EAAE,CAAAhC,QAAA,EAAC,SAAO,CAAC2B,KAAK,CAAChC,KAAK,CAACsC,WAAW,CAAC,CAAC,CAAC,KAAG,EAAQ,CAAC,EAAAJ,eAAA,CAC9DF,KAAK,CAAC7B,OAAO,UAAA+B,eAAA,iBAAbA,eAAA,CAAe3B,GAAG,CAAC,CAACgC,MAAM,CAAEC,QAAQ,gBACnC7D,IAAA,WAAuB0D,KAAK,CAAEE,MAAO,CAAAlC,QAAA,CAAEkC,MAAM,EAAhCC,QAAyC,CACvD,CAAC,EACI,CACT,CACAR,KAAK,CAAC/B,IAAI,GAAK,UAAU,eACxBtB,IAAA,aACEuB,WAAW,CAAE8B,KAAK,CAAC9B,WAAY,CAC/B6C,IAAI,CAAE,CAAE,CACR3C,SAAS,CAAC,gBAAgB,CAC3B,CACF,GAlCI6B,KAmCF,CAAC,CAEV,CAAC,CAAC,CACF,CAAC,cAGHtD,IAAA,QAAKyB,SAAS,sBAAAQ,MAAA,CACZrB,kBAAkB,EAAIJ,YAAY,CAAGW,UAAU,CAACJ,MAAM,CAAG,CAAC,CAAG,SAAS,CAAG,QAAQ,CAChF,CAAAW,QAAA,cACD1B,IAAA,QACEyB,SAAS,CAAC,0BAA0B,CACpC4C,OAAO,CAAEA,CAAA,GAAMC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAE,CAAA7C,QAAA,cAEtD1B,IAAA,CAACF,SAAS,GAAE,CAAC,CACV,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNE,IAAA,QAAKyB,SAAS,sBAAAQ,MAAA,CAAuBrB,kBAAkB,EAAIJ,YAAY,CAAGW,UAAU,CAACJ,MAAM,CAAG,CAAC,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAW,QAAA,cACjH1B,IAAA,CAACF,SAAS,GAAE,CAAC,CACV,CAAC,EACH,CAAC,cAGNE,IAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B1B,IAAA,QAAKyB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B,CAAC,GAAGC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,CAAEC,CAAC,gBAAK9B,IAAA,QAAayB,SAAS,CAAC,WAAW,EAAxBK,CAA8B,CAAC,CAAC,CACtE,CAAC,CACH,CAAC,cACN9B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAM,CAAC,EAClC,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}