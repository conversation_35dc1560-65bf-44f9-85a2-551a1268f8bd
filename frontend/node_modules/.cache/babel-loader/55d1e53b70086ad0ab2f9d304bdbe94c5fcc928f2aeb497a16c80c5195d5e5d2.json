{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './ManualVideoCreationPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ManualVideoCreationPage = () => {\n  _s();\n  const [keywords, setKeywords] = useState('');\n  const [downloadedVideos, setDownloadedVideos] = useState([]);\n  const [selectedVideoIds, setSelectedVideoIds] = useState([]);\n  const [isDownloading, setIsDownloading] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [statusMessage, setStatusMessage] = useState('');\n  const [error, setError] = useState(null);\n  const [generatedDescription, setGeneratedDescription] = useState('');\n  const [attempts, setAttempts] = useState([]);\n  const [selectedAttempt, setSelectedAttempt] = useState(null);\n  const [loadingAttempts, setLoadingAttempts] = useState(true);\n  const [loadingDetails, setLoadingDetails] = useState(false);\n  const [selectedVideos, setSelectedVideos] = useState([]);\n  const [manualDialogue, setManualDialogue] = useState('');\n  const [useManualDialogue, setUseManualDialogue] = useState(false);\n\n  // Fetch video creation attempts when the component mounts\n  useEffect(() => {\n    const fetchAttempts = async () => {\n      try {\n        setLoadingAttempts(true);\n        setError(null); // Clear previous errors\n        // Replace with your actual backend URL for listing attempts\n        const response = await fetch('/api/video-attempts/');\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        setAttempts(data);\n        setLoadingAttempts(false);\n      } catch (err) {\n        // Use any for catch error type for broader compatibility\n        console.error(\"Error fetching video attempts:\", err);\n        setError(`Failed to load video attempts: ${err.message}`);\n        setLoadingAttempts(false);\n      }\n    };\n    fetchAttempts();\n  }, []); // Empty dependency array means this effect runs once on mount\n\n  // Fetch details when an attempt is selected\n  const handleSelectAttempt = async attemptId => {\n    try {\n      setLoadingDetails(true);\n      setError(null); // Clear previous errors\n      // Replace with your actual backend URL for attempt details\n      const response = await fetch(`/api/video-attempts/${attemptId}/`);\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const data = await response.json();\n      setSelectedAttempt(data);\n      // Initialize selected videos with all downloaded videos by default,\n      // or an empty array depending on desired default behavior\n      setSelectedVideos(data.downloaded_videos);\n      setManualDialogue(data.dialogue); // Load existing dialogue for manual tweaking\n      setUseManualDialogue(false); // Default to not using manual dialogue initially\n      setLoadingDetails(false);\n    } catch (err) {\n      // Use any for catch error type for broader compatibility\n      console.error(`Error fetching details for attempt ${attemptId}:`, err);\n      setError(`Failed to load details for attempt ${attemptId}: ${err.message}`);\n      setLoadingDetails(false);\n      setSelectedAttempt(null); // Clear selected attempt on error\n    }\n  };\n\n  // Handle dialogue text area changes\n  const handleDialogueChange = event => {\n    setManualDialogue(event.target.value);\n  };\n\n  // Handle radio button/checkbox for manual dialogue use\n  const handleUseManualDialogueChange = event => {\n    setUseManualDialogue(event.target.checked);\n  };\n\n  // Step 1: Initiate Video Download (Requires new backend endpoint)\n  const handleDownloadVideos = async () => {\n    setIsDownloading(true);\n    setStatusMessage('Downloading videos...');\n    setError(null);\n    setDownloadedVideos([]); // Clear previous downloads\n\n    // TODO: Call your backend endpoint to initiate download\n    // Example: const response = await fetch('/api/manual/download-videos', { method: 'POST', body: JSON.stringify({ keywords }) });\n    // const data = await response.json(); // Assuming backend returns list of downloaded videos metadata\n\n    // Placeholder for simulating download\n    setTimeout(() => {\n      const dummyVideos = [{\n        id: 'vid1',\n        thumbnailUrl: 'placeholder.jpg',\n        localPath: '/path/to/vid1.mp4'\n      }, {\n        id: 'vid2',\n        thumbnailUrl: 'placeholder2.jpg',\n        localPath: '/path/to/vid2.mp4'\n      }\n      // Add more dummy videos or replace with actual backend data\n      ];\n      setDownloadedVideos(dummyVideos);\n      setStatusMessage('Videos downloaded. Please select 10.');\n      setIsDownloading(false);\n    }, 2000); // Simulate network request\n  };\n\n  // Handle checkbox changes for video selection - THIS IS THE CORRECT FUNCTION\n  const handleVideoSelect = (videoPath, isSelected) => {\n    if (isSelected) {\n      setSelectedVideos([...selectedVideos, videoPath]);\n    } else {\n      setSelectedVideos(selectedVideos.filter(path => path !== videoPath));\n    }\n  };\n\n  // Step 2: Process Selected Videos and Get Analysis (Requires new backend endpoint)\n  const handleProcessSelected = async () => {\n    if (selectedVideoIds.length !== 10) {\n      setError('Please select exactly 10 videos.');\n      return;\n    }\n    setIsProcessing(true);\n    setStatusMessage('Processing and analyzing selected videos...');\n    setError(null);\n    setGeneratedDescription(''); // Clear previous description\n\n    // Find the full video metadata for selected IDs\n    const videosToProcess = downloadedVideos.filter(video => selectedVideoIds.includes(video.id));\n\n    // TODO: Call your backend endpoint to process selected videos and get analysis\n    // Example: const response = await fetch('/api/manual/process-analyze', { method: 'POST', body: JSON.stringify({ videos: videosToProcess }) });\n    // const data = await response.json(); // Assuming backend returns generated description\n\n    // Placeholder for simulating processing and analysis\n    setTimeout(() => {\n      setGeneratedDescription('AI-generated visual description based on your selected videos...'); // Replace with actual analysis\n      setStatusMessage('Analysis complete. Review description and create final video.');\n      setIsProcessing(false);\n    }, 3000); // Simulate processing time\n  };\n\n  // Step 3: Create Final Video (Requires new backend endpoint or reuse/modify existing)\n  const handleCreateFinalVideo = async () => {\n    // This step would use the selected videos and the generated description\n    // to proceed with dialogue generation, stitching, etc.\n    setStatusMessage('Creating final video...');\n    // TODO: Call your backend endpoint to create the final video\n    // This might be a modified version of your existing generation endpoint\n\n    // Placeholder\n    setTimeout(() => {\n      setStatusMessage('Final video creation process started.');\n      // Redirect or show link to the final video\n    }, 1000); // Simulate start\n  };\n\n  // Handle the Process button click - implement backend call later\n  const handleProcess = async () => {\n    // Made async to await fetch call\n    if (!selectedAttempt) {\n      alert(\"Please select a video attempt first.\");\n      return;\n    }\n    if (selectedVideos.length === 0) {\n      alert(\"Please select at least one video.\");\n      return;\n    }\n    const dataToSend = {\n      attempt_id: selectedAttempt.attempt_details.id,\n      selected_video_paths: selectedVideos,\n      use_manual_dialogue: useManualDialogue,\n      manual_dialogue_content: useManualDialogue ? manualDialogue : selectedAttempt.dialogue // Send generated dialogue if not manual\n      // We'll also need to send information about which analysis results to keep/regenerate\n      // Based on your requirement, we only need visual description from Gemini for selected videos\n      // This part of backend logic needs to be implemented.\n    };\n    console.log(\"Process button clicked with data:\", dataToSend);\n\n    // TODO: Implement the backend API call to trigger manual processing\n    // Example using fetch:\n    /*\n    try {\n        const response = await fetch('/api/process-manual-video/', { // Replace with your actual endpoint\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                // Include CSRF token for Django if needed\n                // 'X-CSRFToken': getCsrfToken(), // You'll need a function to get the CSRF token\n            },\n            body: JSON.stringify(dataToSend),\n        });\n         if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`);\n        }\n         const result = await response.json();\n        console.log(\"Manual processing initiated:\", result);\n        alert(\"Manual processing initiated successfully!\");\n        // You might want to update the UI to show processing status\n     } catch (err: any) {\n            console.error(\"Error during manual processing:\", err);\n            alert(`Failed to initiate manual processing: ${err.message}`);\n    }\n    */\n    alert(\"Processing logic needs to be implemented on the backend and connected here.\"); // Keep the alert for now\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"manual-creation-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Manual Video Creation Fallback\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 13\n    }, this), loadingAttempts && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Loading video attempts...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 33\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"error-message\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 23\n    }, this), !loadingAttempts && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"attempts-list\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Previous Creation Attempts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 21\n      }, this), attempts.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No previous attempts found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: attempts.map(attempt => /*#__PURE__*/_jsxDEV(\"li\", {\n          onClick: () => handleSelectAttempt(attempt.id),\n          className: (selectedAttempt === null || selectedAttempt === void 0 ? void 0 : selectedAttempt.attempt_details.id) === attempt.id ? 'selected' : '',\n          children: [\"Attempt ID: \", attempt.id, \" - Status: \", attempt.status, \" - Created: \", new Date(attempt.created_at).toLocaleString()]\n        }, attempt.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 33\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 17\n    }, this), loadingDetails && /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Loading attempt details...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 32\n    }, this), selectedAttempt && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"attempt-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"Details for Attempt \", selectedAttempt.attempt_details.id]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"downloaded-videos\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Downloaded Videos (\", selectedVideos.length, \" selected)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 25\n        }, this), \" \", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-thumbnails\",\n          children: selectedAttempt.downloaded_videos.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No downloaded videos found for this attempt.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 33\n          }, this) : selectedAttempt.downloaded_videos.map(videoPath => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `video-thumbnail-item ${selectedVideos.includes(videoPath) ? 'selected-thumbnail' : ''}`,\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `/media/${videoPath}`,\n              alt: `Video ${videoPath}` // More descriptive alt text\n              // You might need a proper thumbnail generation logic\n              // For now, a generic image or a frame from the video could be used\n              // This is a placeholder, replace with actual thumbnail logic if available\n              ,\n              style: {\n                width: '100px',\n                height: 'auto',\n                border: '1px solid #ccc'\n              } // Added border\n              ,\n              onError: e => {\n                e.target.src = '/path/to/placeholder-image.png';\n              } // Add fallback for broken images\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: selectedVideos.includes(videoPath),\n              onChange: e => handleVideoSelect(videoPath, e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: videoPath.split('/').pop()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 41\n            }, this), \" \"]\n          }, videoPath, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 37\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dialogue-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Dialogue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: useManualDialogue,\n            onChange: handleUseManualDialogueChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 30\n          }, this), \"Manually edit dialogue\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 26\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: useManualDialogue ? manualDialogue : selectedAttempt.dialogue,\n          onChange: handleDialogueChange,\n          rows: 10,\n          cols: 80,\n          disabled: !useManualDialogue // Disable if not using manual dialogue\n          ,\n          placeholder: \"Enter manual dialogue here or uncheck to use generated dialogue\" // Added placeholder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 25\n        }, this), !useManualDialogue && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Using automatically generated dialogue.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 49\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleProcess,\n        children: \"Process Manually Selected Videos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 9\n  }, this);\n};\n_s(ManualVideoCreationPage, \"oBHG4Q0/qC4ieAYZq+DGKAUv1Qs=\");\n_c = ManualVideoCreationPage;\nexport default ManualVideoCreationPage;\nvar _c;\n$RefreshReg$(_c, \"ManualVideoCreationPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ManualVideoCreationPage", "_s", "keywords", "setKeywords", "downloadedVideos", "setDownloadedVideos", "selectedVideoIds", "setSelectedVideoIds", "isDownloading", "setIsDownloading", "isProcessing", "setIsProcessing", "statusMessage", "setStatusMessage", "error", "setError", "generatedDescription", "setGeneratedDescription", "attempts", "setAttempts", "selectedAttempt", "setSelectedAttempt", "loadingAttempts", "setLoa<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingDetails", "setLoadingDetails", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideos", "manualDialogue", "setManualDialogue", "useManualDialogue", "setUseManualDialogue", "fetchAttempts", "response", "fetch", "ok", "Error", "status", "data", "json", "err", "console", "message", "handleSelectAttempt", "attemptId", "downloaded_videos", "dialogue", "handleDialogueChange", "event", "target", "value", "handleUseManualDialogueChange", "checked", "handleDownloadVideos", "setTimeout", "dummy<PERSON><PERSON><PERSON>", "id", "thumbnailUrl", "localPath", "handleVideoSelect", "videoPath", "isSelected", "filter", "path", "handleProcessSelected", "length", "videosToProcess", "video", "includes", "handleCreateFinalVideo", "handleProcess", "alert", "dataToSend", "attempt_id", "attempt_details", "selected_video_paths", "use_manual_dialogue", "manual_dialogue_content", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "attempt", "onClick", "Date", "created_at", "toLocaleString", "src", "alt", "style", "width", "height", "border", "onError", "e", "type", "onChange", "split", "pop", "rows", "cols", "disabled", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ManualVideoCreationPage.css';\n\n\ninterface VideoMetadata {\n    id: string; // Assuming a unique ID for the downloaded video\n    thumbnailUrl: string; // URL for displaying the thumbnail\n    localPath: string; // Path to the downloaded video file (backend needs this)\n    // Add other relevant metadata if needed\n}\ninterface VideoAttempt {\n    id: number; // ID from the backend model is a number\n    status: string;\n    created_at: string;\n    // Add other fields from your Video model that you want to display\n    keywords: string[]; // Example based on model\n    status_message: string; // Example\n}\n\ninterface VideoAttemptDetails {\n    attempt_details: VideoAttempt;\n    downloaded_videos: string[]; // List of relative paths to downloaded videos\n    dialogue: string; // The generated dialogue content\n}\nconst ManualVideoCreationPage: React.FC = () => {\n    const [keywords, setKeywords] = useState<string>('');\n    const [downloadedVideos, setDownloadedVideos] = useState<VideoMetadata[]>([]);\n    const [selectedVideoIds, setSelectedVideoIds] = useState<string[]>([]);\n    const [isDownloading, setIsDownloading] = useState(false);\n    const [isProcessing, setIsProcessing] = useState(false);\n    const [statusMessage, setStatusMessage] = useState('');\n    const [error, setError] = useState<string | null>(null);\n    const [generatedDescription, setGeneratedDescription] = useState<string>('');\n\n    const [attempts, setAttempts] = useState<VideoAttempt[]>([]);\n    const [selectedAttempt, setSelectedAttempt] = useState<VideoAttemptDetails | null>(null);\n    const [loadingAttempts, setLoadingAttempts] = useState<boolean>(true);\n    const [loadingDetails, setLoadingDetails] = useState<boolean>(false);\n    const [selectedVideos, setSelectedVideos] = useState<string[]>([]);\n    const [manualDialogue, setManualDialogue] = useState<string>('');\n    const [useManualDialogue, setUseManualDialogue] = useState<boolean>(false);\n\n    // Fetch video creation attempts when the component mounts\n    useEffect(() => {\n        const fetchAttempts = async () => {\n            try {\n                setLoadingAttempts(true);\n                setError(null); // Clear previous errors\n                // Replace with your actual backend URL for listing attempts\n                const response = await fetch('/api/video-attempts/');\n                    if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setAttempts(data);\n                setLoadingAttempts(false);\n            } catch (err: any) { // Use any for catch error type for broader compatibility\n                console.error(\"Error fetching video attempts:\", err);\n                    setError(`Failed to load video attempts: ${err.message}`);\n                setLoadingAttempts(false);\n            }\n        };\n\n        fetchAttempts();\n    }, []); // Empty dependency array means this effect runs once on mount\n\n       // Fetch details when an attempt is selected\n    const handleSelectAttempt = async (attemptId: number) => {\n        try {\n            setLoadingDetails(true);\n            setError(null); // Clear previous errors\n            // Replace with your actual backend URL for attempt details\n            const response = await fetch(`/api/video-attempts/${attemptId}/`);\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n            const data = await response.json();\n            setSelectedAttempt(data);\n            // Initialize selected videos with all downloaded videos by default,\n            // or an empty array depending on desired default behavior\n            setSelectedVideos(data.downloaded_videos);\n            setManualDialogue(data.dialogue); // Load existing dialogue for manual tweaking\n            setUseManualDialogue(false); // Default to not using manual dialogue initially\n            setLoadingDetails(false);\n        } catch (err: any) { // Use any for catch error type for broader compatibility\n            console.error(`Error fetching details for attempt ${attemptId}:`, err);\n            setError(`Failed to load details for attempt ${attemptId}: ${err.message}`);\n            setLoadingDetails(false);\n            setSelectedAttempt(null); // Clear selected attempt on error\n        }\n    };\n\n       // Handle dialogue text area changes\n    const handleDialogueChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n        setManualDialogue(event.target.value);\n    };\n\n    // Handle radio button/checkbox for manual dialogue use\n    const handleUseManualDialogueChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n        setUseManualDialogue(event.target.checked);\n    };\n\n\n    // Step 1: Initiate Video Download (Requires new backend endpoint)\n    const handleDownloadVideos = async () => {\n        setIsDownloading(true);\n        setStatusMessage('Downloading videos...');\n        setError(null);\n        setDownloadedVideos([]); // Clear previous downloads\n\n        // TODO: Call your backend endpoint to initiate download\n        // Example: const response = await fetch('/api/manual/download-videos', { method: 'POST', body: JSON.stringify({ keywords }) });\n        // const data = await response.json(); // Assuming backend returns list of downloaded videos metadata\n\n        // Placeholder for simulating download\n        setTimeout(() => {\n             const dummyVideos: VideoMetadata[] = [\n                 { id: 'vid1', thumbnailUrl: 'placeholder.jpg', localPath: '/path/to/vid1.mp4' },\n                 { id: 'vid2', thumbnailUrl: 'placeholder2.jpg', localPath: '/path/to/vid2.mp4' },\n                 // Add more dummy videos or replace with actual backend data\n             ];\n             setDownloadedVideos(dummyVideos);\n             setStatusMessage('Videos downloaded. Please select 10.');\n             setIsDownloading(false);\n         }, 2000); // Simulate network request\n    };\n\n    // Handle checkbox changes for video selection - THIS IS THE CORRECT FUNCTION\n    const handleVideoSelect = (videoPath: string, isSelected: boolean) => {\n        if (isSelected) {\n            setSelectedVideos([...selectedVideos, videoPath]);\n        } else {\n            setSelectedVideos(selectedVideos.filter(path => path !== videoPath));\n        }\n    };\n\n    // Step 2: Process Selected Videos and Get Analysis (Requires new backend endpoint)\n    const handleProcessSelected = async () => {\n         if (selectedVideoIds.length !== 10) {\n             setError('Please select exactly 10 videos.');\n             return;\n         }\n\n         setIsProcessing(true);\n         setStatusMessage('Processing and analyzing selected videos...');\n         setError(null);\n         setGeneratedDescription(''); // Clear previous description\n\n         // Find the full video metadata for selected IDs\n         const videosToProcess = downloadedVideos.filter(video => selectedVideoIds.includes(video.id));\n\n         // TODO: Call your backend endpoint to process selected videos and get analysis\n         // Example: const response = await fetch('/api/manual/process-analyze', { method: 'POST', body: JSON.stringify({ videos: videosToProcess }) });\n         // const data = await response.json(); // Assuming backend returns generated description\n\n         // Placeholder for simulating processing and analysis\n         setTimeout(() => {\n             setGeneratedDescription('AI-generated visual description based on your selected videos...'); // Replace with actual analysis\n             setStatusMessage('Analysis complete. Review description and create final video.');\n             setIsProcessing(false);\n         }, 3000); // Simulate processing time\n    };\n\n    // Step 3: Create Final Video (Requires new backend endpoint or reuse/modify existing)\n    const handleCreateFinalVideo = async () => {\n        // This step would use the selected videos and the generated description\n        // to proceed with dialogue generation, stitching, etc.\n        setStatusMessage('Creating final video...');\n        // TODO: Call your backend endpoint to create the final video\n        // This might be a modified version of your existing generation endpoint\n\n        // Placeholder\n        setTimeout(() => {\n            setStatusMessage('Final video creation process started.');\n            // Redirect or show link to the final video\n        }, 1000); // Simulate start\n    };\n\n        // Handle the Process button click - implement backend call later\n    const handleProcess = async () => { // Made async to await fetch call\n        if (!selectedAttempt) {\n            alert(\"Please select a video attempt first.\");\n            return;\n        }\n        if (selectedVideos.length === 0) {\n                alert(\"Please select at least one video.\");\n                return;\n        }\n\n        const dataToSend = {\n            attempt_id: selectedAttempt.attempt_details.id,\n            selected_video_paths: selectedVideos,\n            use_manual_dialogue: useManualDialogue,\n            manual_dialogue_content: useManualDialogue ? manualDialogue : selectedAttempt.dialogue, // Send generated dialogue if not manual\n            // We'll also need to send information about which analysis results to keep/regenerate\n            // Based on your requirement, we only need visual description from Gemini for selected videos\n            // This part of backend logic needs to be implemented.\n        };\n\n        console.log(\"Process button clicked with data:\", dataToSend);\n\n        // TODO: Implement the backend API call to trigger manual processing\n        // Example using fetch:\n        /*\n        try {\n            const response = await fetch('/api/process-manual-video/', { // Replace with your actual endpoint\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    // Include CSRF token for Django if needed\n                    // 'X-CSRFToken': getCsrfToken(), // You'll need a function to get the CSRF token\n                },\n                body: JSON.stringify(dataToSend),\n            });\n\n            if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`);\n            }\n\n            const result = await response.json();\n            console.log(\"Manual processing initiated:\", result);\n            alert(\"Manual processing initiated successfully!\");\n            // You might want to update the UI to show processing status\n\n        } catch (err: any) {\n                console.error(\"Error during manual processing:\", err);\n                alert(`Failed to initiate manual processing: ${err.message}`);\n        }\n        */\n            alert(\"Processing logic needs to be implemented on the backend and connected here.\"); // Keep the alert for now\n\n    };\n\n    return (\n        <div className=\"manual-creation-container\">\n            <h1>Manual Video Creation Fallback</h1>\n\n            {loadingAttempts && <p>Loading video attempts...</p>}\n            {error && <p className=\"error-message\">Error: {error}</p>}\n\n            {!loadingAttempts && !error && (\n                <div className=\"attempts-list\">\n                    <h2>Previous Creation Attempts</h2>\n                    {attempts.length === 0 ? (\n                        <p>No previous attempts found.</p>\n                    ) : (\n                        <ul>\n                            {attempts.map(attempt => (\n                                <li key={attempt.id} onClick={() => handleSelectAttempt(attempt.id)} className={selectedAttempt?.attempt_details.id === attempt.id ? 'selected' : ''}>\n                                    Attempt ID: {attempt.id} - Status: {attempt.status} - Created: {new Date(attempt.created_at).toLocaleString()}\n                                    {/* Display other relevant attempt info */}\n                                </li>\n                            ))}\n                        </ul>\n                    )}\n                </div>\n            )}\n\n            {loadingDetails && <p>Loading attempt details...</p>}\n\n            {selectedAttempt && (\n                <div className=\"attempt-details\">\n                    <h2>Details for Attempt {selectedAttempt.attempt_details.id}</h2>\n\n                    <div className=\"downloaded-videos\">\n                        <h3>Downloaded Videos ({selectedVideos.length} selected)</h3> {/* Added selected count */}\n                        <div className=\"video-thumbnails\">\n                            {selectedAttempt.downloaded_videos.length === 0 ? (\n                                <p>No downloaded videos found for this attempt.</p>\n                            ) : (\n                                selectedAttempt.downloaded_videos.map(videoPath => (\n                                    <div key={videoPath} className={`video-thumbnail-item ${selectedVideos.includes(videoPath) ? 'selected-thumbnail' : ''}`}> {/* Added class for styling selected */}\n                                        {/* Construct the URL to the video file */}\n                                        {/* Assuming your Django MEDIA_URL is '/media/' */}\n                                        <img\n                                            src={`/media/${videoPath}`}\n                                            alt={`Video ${videoPath}`} // More descriptive alt text\n                                            // You might need a proper thumbnail generation logic\n                                            // For now, a generic image or a frame from the video could be used\n                                            // This is a placeholder, replace with actual thumbnail logic if available\n                                            style={{ width: '100px', height: 'auto', border: '1px solid #ccc' }} // Added border\n                                            onError={(e) => { (e.target as HTMLImageElement).src = '/path/to/placeholder-image.png'; }} // Add fallback for broken images\n                                        />\n                                        <br />\n                                        <input\n                                            type=\"checkbox\"\n                                            checked={selectedVideos.includes(videoPath)}\n                                            onChange={(e) => handleVideoSelect(videoPath, e.target.checked)}\n                                        />\n                                        {/* Display filename or part of the path */}\n                                        <span>{videoPath.split('/').pop()}</span> {/* Display just the filename */}\n                                    </div>\n                                ))\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"dialogue-section\">\n                        <h3>Dialogue</h3>\n                         <label>\n                             <input\n                                 type=\"checkbox\"\n                                 checked={useManualDialogue}\n                                 onChange={handleUseManualDialogueChange}\n                             />\n                             Manually edit dialogue\n                         </label>\n                        <textarea\n                            value={useManualDialogue ? manualDialogue : selectedAttempt.dialogue}\n                            onChange={handleDialogueChange}\n                            rows={10}\n                            cols={80}\n                            disabled={!useManualDialogue} // Disable if not using manual dialogue\n                            placeholder=\"Enter manual dialogue here or uncheck to use generated dialogue\" // Added placeholder\n                        />\n                         {!useManualDialogue && <p>Using automatically generated dialogue.</p>} {/* Indicate which dialogue is used */}\n                    </div>\n\n                    <button onClick={handleProcess}>Process Manually Selected Videos</button>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default ManualVideoCreationPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBvC,MAAMC,uBAAiC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGR,QAAQ,CAAkB,EAAE,CAAC;EAC7E,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGV,QAAQ,CAAW,EAAE,CAAC;EACtE,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAS,EAAE,CAAC;EAE5E,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAiB,EAAE,CAAC;EAC5D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAA6B,IAAI,CAAC;EACxF,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAU,IAAI,CAAC;EACrE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAU,KAAK,CAAC;EACpE,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAU,KAAK,CAAC;;EAE1E;EACAD,SAAS,CAAC,MAAM;IACZ,MAAMoC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACAT,kBAAkB,CAAC,IAAI,CAAC;QACxBR,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChB;QACA,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsB,CAAC;QAChD,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAClB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC7D;QACA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCpB,WAAW,CAACmB,IAAI,CAAC;QACjBf,kBAAkB,CAAC,KAAK,CAAC;MAC7B,CAAC,CAAC,OAAOiB,GAAQ,EAAE;QAAE;QACjBC,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,EAAE0B,GAAG,CAAC;QAChDzB,QAAQ,CAAC,kCAAkCyB,GAAG,CAACE,OAAO,EAAE,CAAC;QAC7DnB,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IACJ,CAAC;IAEDS,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAEL;EACH,MAAMW,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACrD,IAAI;MACAnB,iBAAiB,CAAC,IAAI,CAAC;MACvBV,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAChB;MACA,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,uBAAuBU,SAAS,GAAG,CAAC;MAC7D,IAAI,CAACX,QAAQ,CAACE,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MAC7D;MACJ,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClClB,kBAAkB,CAACiB,IAAI,CAAC;MACxB;MACA;MACAX,iBAAiB,CAACW,IAAI,CAACO,iBAAiB,CAAC;MACzChB,iBAAiB,CAACS,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC;MAClCf,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7BN,iBAAiB,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC,OAAOe,GAAQ,EAAE;MAAE;MACjBC,OAAO,CAAC3B,KAAK,CAAC,sCAAsC8B,SAAS,GAAG,EAAEJ,GAAG,CAAC;MACtEzB,QAAQ,CAAC,sCAAsC6B,SAAS,KAAKJ,GAAG,CAACE,OAAO,EAAE,CAAC;MAC3EjB,iBAAiB,CAAC,KAAK,CAAC;MACxBJ,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9B;EACJ,CAAC;;EAEE;EACH,MAAM0B,oBAAoB,GAAIC,KAA6C,IAAK;IAC5EnB,iBAAiB,CAACmB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMC,6BAA6B,GAAIH,KAA0C,IAAK;IAClFjB,oBAAoB,CAACiB,KAAK,CAACC,MAAM,CAACG,OAAO,CAAC;EAC9C,CAAC;;EAGD;EACA,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACrC5C,gBAAgB,CAAC,IAAI,CAAC;IACtBI,gBAAgB,CAAC,uBAAuB,CAAC;IACzCE,QAAQ,CAAC,IAAI,CAAC;IACdV,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEzB;IACA;IACA;;IAEA;IACAiD,UAAU,CAAC,MAAM;MACZ,MAAMC,WAA4B,GAAG,CACjC;QAAEC,EAAE,EAAE,MAAM;QAAEC,YAAY,EAAE,iBAAiB;QAAEC,SAAS,EAAE;MAAoB,CAAC,EAC/E;QAAEF,EAAE,EAAE,MAAM;QAAEC,YAAY,EAAE,kBAAkB;QAAEC,SAAS,EAAE;MAAoB;MAC/E;MAAA,CACH;MACDrD,mBAAmB,CAACkD,WAAW,CAAC;MAChC1C,gBAAgB,CAAC,sCAAsC,CAAC;MACxDJ,gBAAgB,CAAC,KAAK,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGA,CAACC,SAAiB,EAAEC,UAAmB,KAAK;IAClE,IAAIA,UAAU,EAAE;MACZlC,iBAAiB,CAAC,CAAC,GAAGD,cAAc,EAAEkC,SAAS,CAAC,CAAC;IACrD,CAAC,MAAM;MACHjC,iBAAiB,CAACD,cAAc,CAACoC,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKH,SAAS,CAAC,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI1D,gBAAgB,CAAC2D,MAAM,KAAK,EAAE,EAAE;MAChClD,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACJ;IAEAJ,eAAe,CAAC,IAAI,CAAC;IACrBE,gBAAgB,CAAC,6CAA6C,CAAC;IAC/DE,QAAQ,CAAC,IAAI,CAAC;IACdE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE7B;IACA,MAAMiD,eAAe,GAAG9D,gBAAgB,CAAC0D,MAAM,CAACK,KAAK,IAAI7D,gBAAgB,CAAC8D,QAAQ,CAACD,KAAK,CAACX,EAAE,CAAC,CAAC;;IAE7F;IACA;IACA;;IAEA;IACAF,UAAU,CAAC,MAAM;MACbrC,uBAAuB,CAAC,kEAAkE,CAAC,CAAC,CAAC;MAC7FJ,gBAAgB,CAAC,+DAA+D,CAAC;MACjFF,eAAe,CAAC,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM0D,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA;IACAxD,gBAAgB,CAAC,yBAAyB,CAAC;IAC3C;IACA;;IAEA;IACAyC,UAAU,CAAC,MAAM;MACbzC,gBAAgB,CAAC,uCAAuC,CAAC;MACzD;IACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACd,CAAC;;EAEG;EACJ,MAAMyD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAAE;IAChC,IAAI,CAAClD,eAAe,EAAE;MAClBmD,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACJ;IACA,IAAI7C,cAAc,CAACuC,MAAM,KAAK,CAAC,EAAE;MACzBM,KAAK,CAAC,mCAAmC,CAAC;MAC1C;IACR;IAEA,MAAMC,UAAU,GAAG;MACfC,UAAU,EAAErD,eAAe,CAACsD,eAAe,CAAClB,EAAE;MAC9CmB,oBAAoB,EAAEjD,cAAc;MACpCkD,mBAAmB,EAAE9C,iBAAiB;MACtC+C,uBAAuB,EAAE/C,iBAAiB,GAAGF,cAAc,GAAGR,eAAe,CAAC0B,QAAQ,CAAE;MACxF;MACA;MACA;IACJ,CAAC;IAEDL,OAAO,CAACqC,GAAG,CAAC,mCAAmC,EAAEN,UAAU,CAAC;;IAE5D;IACA;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAIYD,KAAK,CAAC,6EAA6E,CAAC,CAAC,CAAC;EAE9F,CAAC;EAED,oBACIxE,OAAA;IAAKgF,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACtCjF,OAAA;MAAAiF,QAAA,EAAI;IAA8B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEtC9D,eAAe,iBAAIvB,OAAA;MAAAiF,QAAA,EAAG;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EACnDtE,KAAK,iBAAIf,OAAA;MAAGgF,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,SAAO,EAAClE,KAAK;IAAA;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAExD,CAAC9D,eAAe,IAAI,CAACR,KAAK,iBACvBf,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BjF,OAAA;QAAAiF,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAClClE,QAAQ,CAAC+C,MAAM,KAAK,CAAC,gBAClBlE,OAAA;QAAAiF,QAAA,EAAG;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAElCrF,OAAA;QAAAiF,QAAA,EACK9D,QAAQ,CAACmE,GAAG,CAACC,OAAO,iBACjBvF,OAAA;UAAqBwF,OAAO,EAAEA,CAAA,KAAM5C,mBAAmB,CAAC2C,OAAO,CAAC9B,EAAE,CAAE;UAACuB,SAAS,EAAE,CAAA3D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsD,eAAe,CAAClB,EAAE,MAAK8B,OAAO,CAAC9B,EAAE,GAAG,UAAU,GAAG,EAAG;UAAAwB,QAAA,GAAC,cACtI,EAACM,OAAO,CAAC9B,EAAE,EAAC,aAAW,EAAC8B,OAAO,CAACjD,MAAM,EAAC,cAAY,EAAC,IAAImD,IAAI,CAACF,OAAO,CAACG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;QAAA,GADxGJ,OAAO,CAAC9B,EAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGf,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,EAEA5D,cAAc,iBAAIzB,OAAA;MAAAiF,QAAA,EAAG;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EAEnDhE,eAAe,iBACZrB,OAAA;MAAKgF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BjF,OAAA;QAAAiF,QAAA,GAAI,sBAAoB,EAAC5D,eAAe,CAACsD,eAAe,CAAClB,EAAE;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEjErF,OAAA;QAAKgF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BjF,OAAA;UAAAiF,QAAA,GAAI,qBAAmB,EAACtD,cAAc,CAACuC,MAAM,EAAC,YAAU;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,KAAC,eAC9DrF,OAAA;UAAKgF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5B5D,eAAe,CAACyB,iBAAiB,CAACoB,MAAM,KAAK,CAAC,gBAC3ClE,OAAA;YAAAiF,QAAA,EAAG;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,GAEnDhE,eAAe,CAACyB,iBAAiB,CAACwC,GAAG,CAACzB,SAAS,iBAC3C7D,OAAA;YAAqBgF,SAAS,EAAE,wBAAwBrD,cAAc,CAAC0C,QAAQ,CAACR,SAAS,CAAC,GAAG,oBAAoB,GAAG,EAAE,EAAG;YAAAoB,QAAA,GAAC,GAAC,eAGvHjF,OAAA;cACI4F,GAAG,EAAE,UAAU/B,SAAS,EAAG;cAC3BgC,GAAG,EAAE,SAAShC,SAAS,EAAG,CAAC;cAC3B;cACA;cACA;cAAA;cACAiC,KAAK,EAAE;gBAAEC,KAAK,EAAE,OAAO;gBAAEC,MAAM,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAiB,CAAE,CAAC;cAAA;cACrEC,OAAO,EAAGC,CAAC,IAAK;gBAAGA,CAAC,CAACjD,MAAM,CAAsB0C,GAAG,GAAG,gCAAgC;cAAE,CAAE,CAAC;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACFrF,OAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrF,OAAA;cACIoG,IAAI,EAAC,UAAU;cACf/C,OAAO,EAAE1B,cAAc,CAAC0C,QAAQ,CAACR,SAAS,CAAE;cAC5CwC,QAAQ,EAAGF,CAAC,IAAKvC,iBAAiB,CAACC,SAAS,EAAEsC,CAAC,CAACjD,MAAM,CAACG,OAAO;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEFrF,OAAA;cAAAiF,QAAA,EAAOpB,SAAS,CAACyC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC;YAAC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,KAAC;UAAA,GAnBpCxB,SAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBd,CACR;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENrF,OAAA;QAAKgF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7BjF,OAAA;UAAAiF,QAAA,EAAI;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBrF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YACIoG,IAAI,EAAC,UAAU;YACf/C,OAAO,EAAEtB,iBAAkB;YAC3BsE,QAAQ,EAAEjD;UAA8B;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,0BAEN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACTrF,OAAA;UACImD,KAAK,EAAEpB,iBAAiB,GAAGF,cAAc,GAAGR,eAAe,CAAC0B,QAAS;UACrEsD,QAAQ,EAAErD,oBAAqB;UAC/BwD,IAAI,EAAE,EAAG;UACTC,IAAI,EAAE,EAAG;UACTC,QAAQ,EAAE,CAAC3E,iBAAkB,CAAC;UAAA;UAC9B4E,WAAW,EAAC,iEAAiE,CAAC;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,EACA,CAACtD,iBAAiB,iBAAI/B,OAAA;UAAAiF,QAAA,EAAG;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAAC,GAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAENrF,OAAA;QAAQwF,OAAO,EAAEjB,aAAc;QAAAU,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACnF,EAAA,CA5SID,uBAAiC;AAAA2G,EAAA,GAAjC3G,uBAAiC;AA8SvC,eAAeA,uBAAuB;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}