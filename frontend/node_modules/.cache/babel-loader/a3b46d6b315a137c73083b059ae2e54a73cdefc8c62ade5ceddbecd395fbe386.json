{"ast": null, "code": "/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _doc,\n  _win,\n  _doc<PERSON><PERSON>,\n  _body,\n  _div<PERSON><PERSON>r,\n  _svg<PERSON><PERSON>r,\n  _identityMatrix,\n  _gEl,\n  _transformProp = \"transform\",\n  _transformOriginProp = _transformProp + \"Origin\",\n  _hasOffsetBug,\n  _setDoc = function _setDoc(element) {\n    var doc = element.ownerDocument || element;\n    if (!(_transformProp in element.style) && \"msTransform\" in element.style) {\n      //to improve compatibility with old Microsoft browsers\n      _transformProp = \"msTransform\";\n      _transformOriginProp = _transformProp + \"Origin\";\n    }\n    while (doc.parentNode && (doc = doc.parentNode)) {}\n    _win = window;\n    _identityMatrix = new Matrix2D();\n    if (doc) {\n      _doc = doc;\n      _docElement = doc.documentElement;\n      _body = doc.body;\n      _gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\"); // prevent any existing CSS from transforming it\n\n      _gEl.style.transform = \"none\"; // now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\n      var d1 = doc.createElement(\"div\"),\n        d2 = doc.createElement(\"div\"),\n        root = doc && (doc.body || doc.firstElementChild);\n      if (root && root.appendChild) {\n        root.appendChild(d1);\n        d1.appendChild(d2);\n        d1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n        _hasOffsetBug = d2.offsetParent !== d1;\n        root.removeChild(d1);\n      }\n    }\n    return doc;\n  },\n  _forceNonZeroScale = function _forceNonZeroScale(e) {\n    // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n    var a, cache;\n    while (e && e !== _body) {\n      cache = e._gsap;\n      cache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\n      if (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n        cache.scaleX = cache.scaleY = 1e-4;\n        cache.renderTransform(1, cache);\n        a ? a.push(cache) : a = [cache];\n      }\n      e = e.parentNode;\n    }\n    return a;\n  },\n  // possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n  // _forceDisplay = e => {\n  // \tlet a = [],\n  // \t\tparent;\n  // \twhile (e && e !== _body) {\n  // \t\tparent = e.parentNode;\n  // \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n  // \t\tparent || _body.appendChild(e);\n  // \t\te = parent;\n  // \t}\n  // \treturn a;\n  // },\n  // _revertDisplay = a => {\n  // \tfor (let i = 0; i < a.length; i+=3) {\n  // \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n  // \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n  // \t}\n  // },\n  _svgTemps = [],\n  //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n  _divTemps = [],\n  _getDocScrollTop = function _getDocScrollTop() {\n    return _win.pageYOffset || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0;\n  },\n  _getDocScrollLeft = function _getDocScrollLeft() {\n    return _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0;\n  },\n  _svgOwner = function _svgOwner(element) {\n    return element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null);\n  },\n  _isFixed = function _isFixed(element) {\n    if (_win.getComputedStyle(element).position === \"fixed\") {\n      return true;\n    }\n    element = element.parentNode;\n    if (element && element.nodeType === 1) {\n      // avoid document fragments which will throw an error.\n      return _isFixed(element);\n    }\n  },\n  _createSibling = function _createSibling(element, i) {\n    if (element.parentNode && (_doc || _setDoc(element))) {\n      var svg = _svgOwner(element),\n        ns = svg ? svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\" : \"http://www.w3.org/1999/xhtml\",\n        type = svg ? i ? \"rect\" : \"g\" : \"div\",\n        x = i !== 2 ? 0 : 100,\n        y = i === 3 ? 100 : 0,\n        css = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n        e = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n      if (i) {\n        if (!svg) {\n          if (!_divContainer) {\n            _divContainer = _createSibling(element);\n            _divContainer.style.cssText = css;\n          }\n          e.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n          _divContainer.appendChild(e);\n        } else {\n          _svgContainer || (_svgContainer = _createSibling(element));\n          e.setAttribute(\"width\", 0.01);\n          e.setAttribute(\"height\", 0.01);\n          e.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n          _svgContainer.appendChild(e);\n        }\n      }\n      return e;\n    }\n    throw \"Need document and parent.\";\n  },\n  _consolidate = function _consolidate(m) {\n    // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n    var c = new Matrix2D(),\n      i = 0;\n    for (; i < m.numberOfItems; i++) {\n      c.multiply(m.getItem(i).matrix);\n    }\n    return c;\n  },\n  _getCTM = function _getCTM(svg) {\n    var m = svg.getCTM(),\n      transform;\n    if (!m) {\n      // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n      transform = svg.style[_transformProp];\n      svg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\n      svg.appendChild(_gEl);\n      m = _gEl.getCTM();\n      svg.removeChild(_gEl);\n      transform ? svg.style[_transformProp] = transform : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n    }\n    return m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n  },\n  _placeSiblings = function _placeSiblings(element, adjustGOffset) {\n    var svg = _svgOwner(element),\n      isRootSVG = element === svg,\n      siblings = svg ? _svgTemps : _divTemps,\n      parent = element.parentNode,\n      appendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n      container,\n      m,\n      b,\n      x,\n      y,\n      cs;\n    if (element === _win) {\n      return element;\n    }\n    siblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n    container = svg ? _svgContainer : _divContainer;\n    if (svg) {\n      if (isRootSVG) {\n        b = _getCTM(element);\n        x = -b.e / b.a;\n        y = -b.f / b.d;\n        m = _identityMatrix;\n      } else if (element.getBBox) {\n        b = element.getBBox();\n        m = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\n        m = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\n        x = m.a * b.x + m.c * b.y;\n        y = m.b * b.x + m.d * b.y;\n      } else {\n        // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n        m = new Matrix2D();\n        x = y = 0;\n      }\n      if (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n        x = y = 0;\n      }\n      (isRootSVG ? svg : parent).appendChild(container);\n      container.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n    } else {\n      x = y = 0;\n      if (_hasOffsetBug) {\n        // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n        m = element.offsetParent;\n        b = element;\n        while (b && (b = b.parentNode) && b !== m && b.parentNode) {\n          if ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n            x = b.offsetLeft;\n            y = b.offsetTop;\n            b = 0;\n          }\n        }\n      }\n      cs = _win.getComputedStyle(element);\n      if (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n        m = element.offsetParent;\n        while (parent && parent !== m) {\n          // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n          x += parent.scrollLeft || 0;\n          y += parent.scrollTop || 0;\n          parent = parent.parentNode;\n        }\n      }\n      b = container.style;\n      b.top = element.offsetTop - y + \"px\";\n      b.left = element.offsetLeft - x + \"px\";\n      b[_transformProp] = cs[_transformProp];\n      b[_transformOriginProp] = cs[_transformOriginProp]; // b.border = m.border;\n      // b.borderLeftStyle = m.borderLeftStyle;\n      // b.borderTopStyle = m.borderTopStyle;\n      // b.borderLeftWidth = m.borderLeftWidth;\n      // b.borderTopWidth = m.borderTopWidth;\n\n      b.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n      appendToEl.appendChild(container);\n    }\n    return container;\n  },\n  _setMatrix = function _setMatrix(m, a, b, c, d, e, f) {\n    m.a = a;\n    m.b = b;\n    m.c = c;\n    m.d = d;\n    m.e = e;\n    m.f = f;\n    return m;\n  };\nexport var Matrix2D = /*#__PURE__*/function () {\n  function Matrix2D(a, b, c, d, e, f) {\n    if (a === void 0) {\n      a = 1;\n    }\n    if (b === void 0) {\n      b = 0;\n    }\n    if (c === void 0) {\n      c = 0;\n    }\n    if (d === void 0) {\n      d = 1;\n    }\n    if (e === void 0) {\n      e = 0;\n    }\n    if (f === void 0) {\n      f = 0;\n    }\n    _setMatrix(this, a, b, c, d, e, f);\n  }\n  var _proto = Matrix2D.prototype;\n  _proto.inverse = function inverse() {\n    var a = this.a,\n      b = this.b,\n      c = this.c,\n      d = this.d,\n      e = this.e,\n      f = this.f,\n      determinant = a * d - b * c || 1e-10;\n    return _setMatrix(this, d / determinant, -b / determinant, -c / determinant, a / determinant, (c * f - d * e) / determinant, -(a * f - b * e) / determinant);\n  };\n  _proto.multiply = function multiply(matrix) {\n    var a = this.a,\n      b = this.b,\n      c = this.c,\n      d = this.d,\n      e = this.e,\n      f = this.f,\n      a2 = matrix.a,\n      b2 = matrix.c,\n      c2 = matrix.b,\n      d2 = matrix.d,\n      e2 = matrix.e,\n      f2 = matrix.f;\n    return _setMatrix(this, a2 * a + c2 * c, a2 * b + c2 * d, b2 * a + d2 * c, b2 * b + d2 * d, e + e2 * a + f2 * c, f + e2 * b + f2 * d);\n  };\n  _proto.clone = function clone() {\n    return new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n  };\n  _proto.equals = function equals(matrix) {\n    var a = this.a,\n      b = this.b,\n      c = this.c,\n      d = this.d,\n      e = this.e,\n      f = this.f;\n    return a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f;\n  };\n  _proto.apply = function apply(point, decoratee) {\n    if (decoratee === void 0) {\n      decoratee = {};\n    }\n    var x = point.x,\n      y = point.y,\n      a = this.a,\n      b = this.b,\n      c = this.c,\n      d = this.d,\n      e = this.e,\n      f = this.f;\n    decoratee.x = x * a + y * c + e || 0;\n    decoratee.y = x * b + y * d + f || 0;\n    return decoratee;\n  };\n  return Matrix2D;\n}(); // Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\n\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) {\n  // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n  if (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n    return new Matrix2D();\n  }\n  var zeroScales = _forceNonZeroScale(element),\n    svg = _svgOwner(element),\n    temps = svg ? _svgTemps : _divTemps,\n    container = _placeSiblings(element, adjustGOffset),\n    b1 = temps[0].getBoundingClientRect(),\n    b2 = temps[1].getBoundingClientRect(),\n    b3 = temps[2].getBoundingClientRect(),\n    parent = container.parentNode,\n    isFixed = !includeScrollInFixed && _isFixed(element),\n    m = new Matrix2D((b2.left - b1.left) / 100, (b2.top - b1.top) / 100, (b3.left - b1.left) / 100, (b3.top - b1.top) / 100, b1.left + (isFixed ? 0 : _getDocScrollLeft()), b1.top + (isFixed ? 0 : _getDocScrollTop()));\n  parent.removeChild(container);\n  if (zeroScales) {\n    b1 = zeroScales.length;\n    while (b1--) {\n      b2 = zeroScales[b1];\n      b2.scaleX = b2.scaleY = 0;\n      b2.renderTransform(1, b2);\n    }\n  }\n  return inverse ? m.inverse() : m;\n}\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM }; // export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "map": {"version": 3, "names": ["_doc", "_win", "_doc<PERSON>lement", "_body", "_divContainer", "_svgContainer", "_identityMatrix", "_gEl", "_transformProp", "_transformOriginProp", "_hasOffsetBug", "_setDoc", "element", "doc", "ownerDocument", "style", "parentNode", "window", "Matrix2D", "documentElement", "body", "createElementNS", "transform", "d1", "createElement", "d2", "root", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "setAttribute", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_forceNonZeroScale", "e", "a", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "push", "_svgTemps", "_divTemps", "_getDocScrollTop", "pageYOffset", "scrollTop", "_getDocScrollLeft", "pageXOffset", "scrollLeft", "_svgOwner", "ownerSVGElement", "tagName", "toLowerCase", "_isFixed", "getComputedStyle", "position", "nodeType", "_createSibling", "i", "svg", "ns", "getAttribute", "type", "x", "y", "css", "replace", "cssText", "_consolidate", "m", "c", "numberOfItems", "multiply", "getItem", "matrix", "_getCTM", "getCTM", "removeProperty", "clone", "_placeSiblings", "adjustGOffset", "isRootSVG", "siblings", "parent", "appendToEl", "shadowRoot", "container", "b", "cs", "length", "f", "d", "getBBox", "baseVal", "offsetLeft", "offsetTop", "top", "left", "_setMatrix", "_proto", "prototype", "inverse", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "temps", "b1", "getBoundingClientRect", "b3", "isFixed"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/gsap/utils/matrix.js"], "sourcesContent": ["/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _doc,\n    _win,\n    _doc<PERSON><PERSON>,\n    _body,\n    _div<PERSON><PERSON>r,\n    _svg<PERSON><PERSON>r,\n    _identityMatrix,\n    _gEl,\n    _transformProp = \"transform\",\n    _transformOriginProp = _transformProp + \"Origin\",\n    _hasOffsetBug,\n    _setDoc = function _setDoc(element) {\n  var doc = element.ownerDocument || element;\n\n  if (!(_transformProp in element.style) && \"msTransform\" in element.style) {\n    //to improve compatibility with old Microsoft browsers\n    _transformProp = \"msTransform\";\n    _transformOriginProp = _transformProp + \"Origin\";\n  }\n\n  while (doc.parentNode && (doc = doc.parentNode)) {}\n\n  _win = window;\n  _identityMatrix = new Matrix2D();\n\n  if (doc) {\n    _doc = doc;\n    _docElement = doc.documentElement;\n    _body = doc.body;\n    _gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\"); // prevent any existing CSS from transforming it\n\n    _gEl.style.transform = \"none\"; // now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\n    var d1 = doc.createElement(\"div\"),\n        d2 = doc.createElement(\"div\"),\n        root = doc && (doc.body || doc.firstElementChild);\n\n    if (root && root.appendChild) {\n      root.appendChild(d1);\n      d1.appendChild(d2);\n      d1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n      _hasOffsetBug = d2.offsetParent !== d1;\n      root.removeChild(d1);\n    }\n  }\n\n  return doc;\n},\n    _forceNonZeroScale = function _forceNonZeroScale(e) {\n  // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n  var a, cache;\n\n  while (e && e !== _body) {\n    cache = e._gsap;\n    cache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\n    if (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n      cache.scaleX = cache.scaleY = 1e-4;\n      cache.renderTransform(1, cache);\n      a ? a.push(cache) : a = [cache];\n    }\n\n    e = e.parentNode;\n  }\n\n  return a;\n},\n    // possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n// _forceDisplay = e => {\n// \tlet a = [],\n// \t\tparent;\n// \twhile (e && e !== _body) {\n// \t\tparent = e.parentNode;\n// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n// \t\tparent || _body.appendChild(e);\n// \t\te = parent;\n// \t}\n// \treturn a;\n// },\n// _revertDisplay = a => {\n// \tfor (let i = 0; i < a.length; i+=3) {\n// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n// \t}\n// },\n_svgTemps = [],\n    //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n_divTemps = [],\n    _getDocScrollTop = function _getDocScrollTop() {\n  return _win.pageYOffset || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0;\n},\n    _getDocScrollLeft = function _getDocScrollLeft() {\n  return _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0;\n},\n    _svgOwner = function _svgOwner(element) {\n  return element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null);\n},\n    _isFixed = function _isFixed(element) {\n  if (_win.getComputedStyle(element).position === \"fixed\") {\n    return true;\n  }\n\n  element = element.parentNode;\n\n  if (element && element.nodeType === 1) {\n    // avoid document fragments which will throw an error.\n    return _isFixed(element);\n  }\n},\n    _createSibling = function _createSibling(element, i) {\n  if (element.parentNode && (_doc || _setDoc(element))) {\n    var svg = _svgOwner(element),\n        ns = svg ? svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\" : \"http://www.w3.org/1999/xhtml\",\n        type = svg ? i ? \"rect\" : \"g\" : \"div\",\n        x = i !== 2 ? 0 : 100,\n        y = i === 3 ? 100 : 0,\n        css = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n        e = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\n    if (i) {\n      if (!svg) {\n        if (!_divContainer) {\n          _divContainer = _createSibling(element);\n          _divContainer.style.cssText = css;\n        }\n\n        e.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\n        _divContainer.appendChild(e);\n      } else {\n        _svgContainer || (_svgContainer = _createSibling(element));\n        e.setAttribute(\"width\", 0.01);\n        e.setAttribute(\"height\", 0.01);\n        e.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\n        _svgContainer.appendChild(e);\n      }\n    }\n\n    return e;\n  }\n\n  throw \"Need document and parent.\";\n},\n    _consolidate = function _consolidate(m) {\n  // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n  var c = new Matrix2D(),\n      i = 0;\n\n  for (; i < m.numberOfItems; i++) {\n    c.multiply(m.getItem(i).matrix);\n  }\n\n  return c;\n},\n    _getCTM = function _getCTM(svg) {\n  var m = svg.getCTM(),\n      transform;\n\n  if (!m) {\n    // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n    transform = svg.style[_transformProp];\n    svg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\n    svg.appendChild(_gEl);\n    m = _gEl.getCTM();\n    svg.removeChild(_gEl);\n    transform ? svg.style[_transformProp] = transform : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n  }\n\n  return m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n},\n    _placeSiblings = function _placeSiblings(element, adjustGOffset) {\n  var svg = _svgOwner(element),\n      isRootSVG = element === svg,\n      siblings = svg ? _svgTemps : _divTemps,\n      parent = element.parentNode,\n      appendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n      container,\n      m,\n      b,\n      x,\n      y,\n      cs;\n\n  if (element === _win) {\n    return element;\n  }\n\n  siblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n  container = svg ? _svgContainer : _divContainer;\n\n  if (svg) {\n    if (isRootSVG) {\n      b = _getCTM(element);\n      x = -b.e / b.a;\n      y = -b.f / b.d;\n      m = _identityMatrix;\n    } else if (element.getBBox) {\n      b = element.getBBox();\n      m = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\n      m = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\n      x = m.a * b.x + m.c * b.y;\n      y = m.b * b.x + m.d * b.y;\n    } else {\n      // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n      m = new Matrix2D();\n      x = y = 0;\n    }\n\n    if (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n      x = y = 0;\n    }\n\n    (isRootSVG ? svg : parent).appendChild(container);\n    container.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n  } else {\n    x = y = 0;\n\n    if (_hasOffsetBug) {\n      // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n      m = element.offsetParent;\n      b = element;\n\n      while (b && (b = b.parentNode) && b !== m && b.parentNode) {\n        if ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n          x = b.offsetLeft;\n          y = b.offsetTop;\n          b = 0;\n        }\n      }\n    }\n\n    cs = _win.getComputedStyle(element);\n\n    if (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n      m = element.offsetParent;\n\n      while (parent && parent !== m) {\n        // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n        x += parent.scrollLeft || 0;\n        y += parent.scrollTop || 0;\n        parent = parent.parentNode;\n      }\n    }\n\n    b = container.style;\n    b.top = element.offsetTop - y + \"px\";\n    b.left = element.offsetLeft - x + \"px\";\n    b[_transformProp] = cs[_transformProp];\n    b[_transformOriginProp] = cs[_transformOriginProp]; // b.border = m.border;\n    // b.borderLeftStyle = m.borderLeftStyle;\n    // b.borderTopStyle = m.borderTopStyle;\n    // b.borderLeftWidth = m.borderLeftWidth;\n    // b.borderTopWidth = m.borderTopWidth;\n\n    b.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n    appendToEl.appendChild(container);\n  }\n\n  return container;\n},\n    _setMatrix = function _setMatrix(m, a, b, c, d, e, f) {\n  m.a = a;\n  m.b = b;\n  m.c = c;\n  m.d = d;\n  m.e = e;\n  m.f = f;\n  return m;\n};\n\nexport var Matrix2D = /*#__PURE__*/function () {\n  function Matrix2D(a, b, c, d, e, f) {\n    if (a === void 0) {\n      a = 1;\n    }\n\n    if (b === void 0) {\n      b = 0;\n    }\n\n    if (c === void 0) {\n      c = 0;\n    }\n\n    if (d === void 0) {\n      d = 1;\n    }\n\n    if (e === void 0) {\n      e = 0;\n    }\n\n    if (f === void 0) {\n      f = 0;\n    }\n\n    _setMatrix(this, a, b, c, d, e, f);\n  }\n\n  var _proto = Matrix2D.prototype;\n\n  _proto.inverse = function inverse() {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f,\n        determinant = a * d - b * c || 1e-10;\n    return _setMatrix(this, d / determinant, -b / determinant, -c / determinant, a / determinant, (c * f - d * e) / determinant, -(a * f - b * e) / determinant);\n  };\n\n  _proto.multiply = function multiply(matrix) {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f,\n        a2 = matrix.a,\n        b2 = matrix.c,\n        c2 = matrix.b,\n        d2 = matrix.d,\n        e2 = matrix.e,\n        f2 = matrix.f;\n    return _setMatrix(this, a2 * a + c2 * c, a2 * b + c2 * d, b2 * a + d2 * c, b2 * b + d2 * d, e + e2 * a + f2 * c, f + e2 * b + f2 * d);\n  };\n\n  _proto.clone = function clone() {\n    return new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n  };\n\n  _proto.equals = function equals(matrix) {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f;\n    return a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f;\n  };\n\n  _proto.apply = function apply(point, decoratee) {\n    if (decoratee === void 0) {\n      decoratee = {};\n    }\n\n    var x = point.x,\n        y = point.y,\n        a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f;\n    decoratee.x = x * a + y * c + e || 0;\n    decoratee.y = x * b + y * d + f || 0;\n    return decoratee;\n  };\n\n  return Matrix2D;\n}(); // Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\n\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) {\n  // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n  if (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n    return new Matrix2D();\n  }\n\n  var zeroScales = _forceNonZeroScale(element),\n      svg = _svgOwner(element),\n      temps = svg ? _svgTemps : _divTemps,\n      container = _placeSiblings(element, adjustGOffset),\n      b1 = temps[0].getBoundingClientRect(),\n      b2 = temps[1].getBoundingClientRect(),\n      b3 = temps[2].getBoundingClientRect(),\n      parent = container.parentNode,\n      isFixed = !includeScrollInFixed && _isFixed(element),\n      m = new Matrix2D((b2.left - b1.left) / 100, (b2.top - b1.top) / 100, (b3.left - b1.left) / 100, (b3.top - b1.top) / 100, b1.left + (isFixed ? 0 : _getDocScrollLeft()), b1.top + (isFixed ? 0 : _getDocScrollTop()));\n\n  parent.removeChild(container);\n\n  if (zeroScales) {\n    b1 = zeroScales.length;\n\n    while (b1--) {\n      b2 = zeroScales[b1];\n      b2.scaleX = b2.scaleY = 0;\n      b2.renderTransform(1, b2);\n    }\n  }\n\n  return inverse ? m.inverse() : m;\n}\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM }; // export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIA,IAAI;EACJC,IAAI;EACJC,WAAW;EACXC,KAAK;EACLC,aAAa;EACbC,aAAa;EACbC,eAAe;EACfC,IAAI;EACJC,cAAc,GAAG,WAAW;EAC5BC,oBAAoB,GAAGD,cAAc,GAAG,QAAQ;EAChDE,aAAa;EACbC,OAAO,GAAG,SAASA,OAAOA,CAACC,OAAO,EAAE;IACtC,IAAIC,GAAG,GAAGD,OAAO,CAACE,aAAa,IAAIF,OAAO;IAE1C,IAAI,EAAEJ,cAAc,IAAII,OAAO,CAACG,KAAK,CAAC,IAAI,aAAa,IAAIH,OAAO,CAACG,KAAK,EAAE;MACxE;MACAP,cAAc,GAAG,aAAa;MAC9BC,oBAAoB,GAAGD,cAAc,GAAG,QAAQ;IAClD;IAEA,OAAOK,GAAG,CAACG,UAAU,KAAKH,GAAG,GAAGA,GAAG,CAACG,UAAU,CAAC,EAAE,CAAC;IAElDf,IAAI,GAAGgB,MAAM;IACbX,eAAe,GAAG,IAAIY,QAAQ,CAAC,CAAC;IAEhC,IAAIL,GAAG,EAAE;MACPb,IAAI,GAAGa,GAAG;MACVX,WAAW,GAAGW,GAAG,CAACM,eAAe;MACjChB,KAAK,GAAGU,GAAG,CAACO,IAAI;MAChBb,IAAI,GAAGP,IAAI,CAACqB,eAAe,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC,CAAC;;MAEhEd,IAAI,CAACQ,KAAK,CAACO,SAAS,GAAG,MAAM,CAAC,CAAC;;MAE/B,IAAIC,EAAE,GAAGV,GAAG,CAACW,aAAa,CAAC,KAAK,CAAC;QAC7BC,EAAE,GAAGZ,GAAG,CAACW,aAAa,CAAC,KAAK,CAAC;QAC7BE,IAAI,GAAGb,GAAG,KAAKA,GAAG,CAACO,IAAI,IAAIP,GAAG,CAACc,iBAAiB,CAAC;MAErD,IAAID,IAAI,IAAIA,IAAI,CAACE,WAAW,EAAE;QAC5BF,IAAI,CAACE,WAAW,CAACL,EAAE,CAAC;QACpBA,EAAE,CAACK,WAAW,CAACH,EAAE,CAAC;QAClBF,EAAE,CAACM,YAAY,CAAC,OAAO,EAAE,gDAAgD,CAAC;QAC1EnB,aAAa,GAAGe,EAAE,CAACK,YAAY,KAAKP,EAAE;QACtCG,IAAI,CAACK,WAAW,CAACR,EAAE,CAAC;MACtB;IACF;IAEA,OAAOV,GAAG;EACZ,CAAC;EACGmB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,CAAC,EAAE;IACtD;IACA,IAAIC,CAAC,EAAEC,KAAK;IAEZ,OAAOF,CAAC,IAAIA,CAAC,KAAK9B,KAAK,EAAE;MACvBgC,KAAK,GAAGF,CAAC,CAACG,KAAK;MACfD,KAAK,IAAIA,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,GAAG,CAACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAE7C,IAAIE,KAAK,IAAI,CAACA,KAAK,CAACI,MAAM,IAAI,CAACJ,KAAK,CAACK,MAAM,IAAIL,KAAK,CAACM,eAAe,EAAE;QACpEN,KAAK,CAACI,MAAM,GAAGJ,KAAK,CAACK,MAAM,GAAG,IAAI;QAClCL,KAAK,CAACM,eAAe,CAAC,CAAC,EAAEN,KAAK,CAAC;QAC/BD,CAAC,GAAGA,CAAC,CAACQ,IAAI,CAACP,KAAK,CAAC,GAAGD,CAAC,GAAG,CAACC,KAAK,CAAC;MACjC;MAEAF,CAAC,GAAGA,CAAC,CAACjB,UAAU;IAClB;IAEA,OAAOkB,CAAC;EACV,CAAC;EACG;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAS,SAAS,GAAG,EAAE;EACV;EACJC,SAAS,GAAG,EAAE;EACVC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;IACjD,OAAO5C,IAAI,CAAC6C,WAAW,IAAI9C,IAAI,CAAC+C,SAAS,IAAI7C,WAAW,CAAC6C,SAAS,IAAI5C,KAAK,CAAC4C,SAAS,IAAI,CAAC;EAC5F,CAAC;EACGC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnD,OAAO/C,IAAI,CAACgD,WAAW,IAAIjD,IAAI,CAACkD,UAAU,IAAIhD,WAAW,CAACgD,UAAU,IAAI/C,KAAK,CAAC+C,UAAU,IAAI,CAAC;EAC/F,CAAC;EACGC,SAAS,GAAG,SAASA,SAASA,CAACvC,OAAO,EAAE;IAC1C,OAAOA,OAAO,CAACwC,eAAe,KAAK,CAACxC,OAAO,CAACyC,OAAO,GAAG,EAAE,EAAEC,WAAW,CAAC,CAAC,KAAK,KAAK,GAAG1C,OAAO,GAAG,IAAI,CAAC;EACrG,CAAC;EACG2C,QAAQ,GAAG,SAASA,QAAQA,CAAC3C,OAAO,EAAE;IACxC,IAAIX,IAAI,CAACuD,gBAAgB,CAAC5C,OAAO,CAAC,CAAC6C,QAAQ,KAAK,OAAO,EAAE;MACvD,OAAO,IAAI;IACb;IAEA7C,OAAO,GAAGA,OAAO,CAACI,UAAU;IAE5B,IAAIJ,OAAO,IAAIA,OAAO,CAAC8C,QAAQ,KAAK,CAAC,EAAE;MACrC;MACA,OAAOH,QAAQ,CAAC3C,OAAO,CAAC;IAC1B;EACF,CAAC;EACG+C,cAAc,GAAG,SAASA,cAAcA,CAAC/C,OAAO,EAAEgD,CAAC,EAAE;IACvD,IAAIhD,OAAO,CAACI,UAAU,KAAKhB,IAAI,IAAIW,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MACpD,IAAIiD,GAAG,GAAGV,SAAS,CAACvC,OAAO,CAAC;QACxBkD,EAAE,GAAGD,GAAG,GAAGA,GAAG,CAACE,YAAY,CAAC,OAAO,CAAC,IAAI,4BAA4B,GAAG,8BAA8B;QACrGC,IAAI,GAAGH,GAAG,GAAGD,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,KAAK;QACrCK,CAAC,GAAGL,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;QACrBM,CAAC,GAAGN,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QACrBO,GAAG,GAAG,yEAAyE;QAC/ElC,CAAC,GAAGjC,IAAI,CAACqB,eAAe,GAAGrB,IAAI,CAACqB,eAAe,CAACyC,EAAE,CAACM,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEJ,IAAI,CAAC,GAAGhE,IAAI,CAACwB,aAAa,CAACwC,IAAI,CAAC;MAElH,IAAIJ,CAAC,EAAE;QACL,IAAI,CAACC,GAAG,EAAE;UACR,IAAI,CAACzD,aAAa,EAAE;YAClBA,aAAa,GAAGuD,cAAc,CAAC/C,OAAO,CAAC;YACvCR,aAAa,CAACW,KAAK,CAACsD,OAAO,GAAGF,GAAG;UACnC;UAEAlC,CAAC,CAAClB,KAAK,CAACsD,OAAO,GAAGF,GAAG,GAAG,+BAA+B,GAAGD,CAAC,GAAG,UAAU,GAAGD,CAAC,GAAG,IAAI;UAEnF7D,aAAa,CAACwB,WAAW,CAACK,CAAC,CAAC;QAC9B,CAAC,MAAM;UACL5B,aAAa,KAAKA,aAAa,GAAGsD,cAAc,CAAC/C,OAAO,CAAC,CAAC;UAC1DqB,CAAC,CAACJ,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;UAC7BI,CAAC,CAACJ,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC;UAC9BI,CAAC,CAACJ,YAAY,CAAC,WAAW,EAAE,YAAY,GAAGoC,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,CAAC;UAE7D7D,aAAa,CAACuB,WAAW,CAACK,CAAC,CAAC;QAC9B;MACF;MAEA,OAAOA,CAAC;IACV;IAEA,MAAM,2BAA2B;EACnC,CAAC;EACGqC,YAAY,GAAG,SAASA,YAAYA,CAACC,CAAC,EAAE;IAC1C;IACA,IAAIC,CAAC,GAAG,IAAItD,QAAQ,CAAC,CAAC;MAClB0C,CAAC,GAAG,CAAC;IAET,OAAOA,CAAC,GAAGW,CAAC,CAACE,aAAa,EAAEb,CAAC,EAAE,EAAE;MAC/BY,CAAC,CAACE,QAAQ,CAACH,CAAC,CAACI,OAAO,CAACf,CAAC,CAAC,CAACgB,MAAM,CAAC;IACjC;IAEA,OAAOJ,CAAC;EACV,CAAC;EACGK,OAAO,GAAG,SAASA,OAAOA,CAAChB,GAAG,EAAE;IAClC,IAAIU,CAAC,GAAGV,GAAG,CAACiB,MAAM,CAAC,CAAC;MAChBxD,SAAS;IAEb,IAAI,CAACiD,CAAC,EAAE;MACN;MACAjD,SAAS,GAAGuC,GAAG,CAAC9C,KAAK,CAACP,cAAc,CAAC;MACrCqD,GAAG,CAAC9C,KAAK,CAACP,cAAc,CAAC,GAAG,MAAM,CAAC,CAAC;;MAEpCqD,GAAG,CAACjC,WAAW,CAACrB,IAAI,CAAC;MACrBgE,CAAC,GAAGhE,IAAI,CAACuE,MAAM,CAAC,CAAC;MACjBjB,GAAG,CAAC9B,WAAW,CAACxB,IAAI,CAAC;MACrBe,SAAS,GAAGuC,GAAG,CAAC9C,KAAK,CAACP,cAAc,CAAC,GAAGc,SAAS,GAAGuC,GAAG,CAAC9C,KAAK,CAACgE,cAAc,CAACvE,cAAc,CAAC4D,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACd,WAAW,CAAC,CAAC,CAAC;IACvI;IAEA,OAAOiB,CAAC,IAAIjE,eAAe,CAAC0E,KAAK,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EACGC,cAAc,GAAG,SAASA,cAAcA,CAACrE,OAAO,EAAEsE,aAAa,EAAE;IACnE,IAAIrB,GAAG,GAAGV,SAAS,CAACvC,OAAO,CAAC;MACxBuE,SAAS,GAAGvE,OAAO,KAAKiD,GAAG;MAC3BuB,QAAQ,GAAGvB,GAAG,GAAGlB,SAAS,GAAGC,SAAS;MACtCyC,MAAM,GAAGzE,OAAO,CAACI,UAAU;MAC3BsE,UAAU,GAAGD,MAAM,IAAI,CAACxB,GAAG,IAAIwB,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACE,UAAU,CAAC3D,WAAW,GAAGyD,MAAM,CAACE,UAAU,GAAGF,MAAM;MAC9GG,SAAS;MACTjB,CAAC;MACDkB,CAAC;MACDxB,CAAC;MACDC,CAAC;MACDwB,EAAE;IAEN,IAAI9E,OAAO,KAAKX,IAAI,EAAE;MACpB,OAAOW,OAAO;IAChB;IAEAwE,QAAQ,CAACO,MAAM,IAAIP,QAAQ,CAAC1C,IAAI,CAACiB,cAAc,CAAC/C,OAAO,EAAE,CAAC,CAAC,EAAE+C,cAAc,CAAC/C,OAAO,EAAE,CAAC,CAAC,EAAE+C,cAAc,CAAC/C,OAAO,EAAE,CAAC,CAAC,CAAC;IACpH4E,SAAS,GAAG3B,GAAG,GAAGxD,aAAa,GAAGD,aAAa;IAE/C,IAAIyD,GAAG,EAAE;MACP,IAAIsB,SAAS,EAAE;QACbM,CAAC,GAAGZ,OAAO,CAACjE,OAAO,CAAC;QACpBqD,CAAC,GAAG,CAACwB,CAAC,CAACxD,CAAC,GAAGwD,CAAC,CAACvD,CAAC;QACdgC,CAAC,GAAG,CAACuB,CAAC,CAACG,CAAC,GAAGH,CAAC,CAACI,CAAC;QACdtB,CAAC,GAAGjE,eAAe;MACrB,CAAC,MAAM,IAAIM,OAAO,CAACkF,OAAO,EAAE;QAC1BL,CAAC,GAAG7E,OAAO,CAACkF,OAAO,CAAC,CAAC;QACrBvB,CAAC,GAAG3D,OAAO,CAACU,SAAS,GAAGV,OAAO,CAACU,SAAS,CAACyE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;;QAExDxB,CAAC,GAAG,CAACA,CAAC,CAACE,aAAa,GAAGnE,eAAe,GAAGiE,CAAC,CAACE,aAAa,GAAG,CAAC,GAAGH,YAAY,CAACC,CAAC,CAAC,GAAGA,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;;QAEtGX,CAAC,GAAGM,CAAC,CAACrC,CAAC,GAAGuD,CAAC,CAACxB,CAAC,GAAGM,CAAC,CAACC,CAAC,GAAGiB,CAAC,CAACvB,CAAC;QACzBA,CAAC,GAAGK,CAAC,CAACkB,CAAC,GAAGA,CAAC,CAACxB,CAAC,GAAGM,CAAC,CAACsB,CAAC,GAAGJ,CAAC,CAACvB,CAAC;MAC3B,CAAC,MAAM;QACL;QACAK,CAAC,GAAG,IAAIrD,QAAQ,CAAC,CAAC;QAClB+C,CAAC,GAAGC,CAAC,GAAG,CAAC;MACX;MAEA,IAAIgB,aAAa,IAAItE,OAAO,CAACyC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QAC1DW,CAAC,GAAGC,CAAC,GAAG,CAAC;MACX;MAEA,CAACiB,SAAS,GAAGtB,GAAG,GAAGwB,MAAM,EAAEzD,WAAW,CAAC4D,SAAS,CAAC;MACjDA,SAAS,CAAC3D,YAAY,CAAC,WAAW,EAAE,SAAS,GAAG0C,CAAC,CAACrC,CAAC,GAAG,GAAG,GAAGqC,CAAC,CAACkB,CAAC,GAAG,GAAG,GAAGlB,CAAC,CAACC,CAAC,GAAG,GAAG,GAAGD,CAAC,CAACsB,CAAC,GAAG,GAAG,IAAItB,CAAC,CAACtC,CAAC,GAAGgC,CAAC,CAAC,GAAG,GAAG,IAAIM,CAAC,CAACqB,CAAC,GAAG1B,CAAC,CAAC,GAAG,GAAG,CAAC;IACpI,CAAC,MAAM;MACLD,CAAC,GAAGC,CAAC,GAAG,CAAC;MAET,IAAIxD,aAAa,EAAE;QACjB;QACA6D,CAAC,GAAG3D,OAAO,CAACkB,YAAY;QACxB2D,CAAC,GAAG7E,OAAO;QAEX,OAAO6E,CAAC,KAAKA,CAAC,GAAGA,CAAC,CAACzE,UAAU,CAAC,IAAIyE,CAAC,KAAKlB,CAAC,IAAIkB,CAAC,CAACzE,UAAU,EAAE;UACzD,IAAI,CAACf,IAAI,CAACuD,gBAAgB,CAACiC,CAAC,CAAC,CAACjF,cAAc,CAAC,GAAG,EAAE,EAAEmF,MAAM,GAAG,CAAC,EAAE;YAC9D1B,CAAC,GAAGwB,CAAC,CAACO,UAAU;YAChB9B,CAAC,GAAGuB,CAAC,CAACQ,SAAS;YACfR,CAAC,GAAG,CAAC;UACP;QACF;MACF;MAEAC,EAAE,GAAGzF,IAAI,CAACuD,gBAAgB,CAAC5C,OAAO,CAAC;MAEnC,IAAI8E,EAAE,CAACjC,QAAQ,KAAK,UAAU,IAAIiC,EAAE,CAACjC,QAAQ,KAAK,OAAO,EAAE;QACzDc,CAAC,GAAG3D,OAAO,CAACkB,YAAY;QAExB,OAAOuD,MAAM,IAAIA,MAAM,KAAKd,CAAC,EAAE;UAC7B;UACAN,CAAC,IAAIoB,MAAM,CAACnC,UAAU,IAAI,CAAC;UAC3BgB,CAAC,IAAImB,MAAM,CAACtC,SAAS,IAAI,CAAC;UAC1BsC,MAAM,GAAGA,MAAM,CAACrE,UAAU;QAC5B;MACF;MAEAyE,CAAC,GAAGD,SAAS,CAACzE,KAAK;MACnB0E,CAAC,CAACS,GAAG,GAAGtF,OAAO,CAACqF,SAAS,GAAG/B,CAAC,GAAG,IAAI;MACpCuB,CAAC,CAACU,IAAI,GAAGvF,OAAO,CAACoF,UAAU,GAAG/B,CAAC,GAAG,IAAI;MACtCwB,CAAC,CAACjF,cAAc,CAAC,GAAGkF,EAAE,CAAClF,cAAc,CAAC;MACtCiF,CAAC,CAAChF,oBAAoB,CAAC,GAAGiF,EAAE,CAACjF,oBAAoB,CAAC,CAAC,CAAC;MACpD;MACA;MACA;MACA;;MAEAgF,CAAC,CAAChC,QAAQ,GAAGiC,EAAE,CAACjC,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,UAAU;MAC3D6B,UAAU,CAAC1D,WAAW,CAAC4D,SAAS,CAAC;IACnC;IAEA,OAAOA,SAAS;EAClB,CAAC;EACGY,UAAU,GAAG,SAASA,UAAUA,CAAC7B,CAAC,EAAErC,CAAC,EAAEuD,CAAC,EAAEjB,CAAC,EAAEqB,CAAC,EAAE5D,CAAC,EAAE2D,CAAC,EAAE;IACxDrB,CAAC,CAACrC,CAAC,GAAGA,CAAC;IACPqC,CAAC,CAACkB,CAAC,GAAGA,CAAC;IACPlB,CAAC,CAACC,CAAC,GAAGA,CAAC;IACPD,CAAC,CAACsB,CAAC,GAAGA,CAAC;IACPtB,CAAC,CAACtC,CAAC,GAAGA,CAAC;IACPsC,CAAC,CAACqB,CAAC,GAAGA,CAAC;IACP,OAAOrB,CAAC;EACV,CAAC;AAED,OAAO,IAAIrD,QAAQ,GAAG,aAAa,YAAY;EAC7C,SAASA,QAAQA,CAACgB,CAAC,EAAEuD,CAAC,EAAEjB,CAAC,EAAEqB,CAAC,EAAE5D,CAAC,EAAE2D,CAAC,EAAE;IAClC,IAAI1D,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEA,IAAIuD,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEA,IAAIjB,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEA,IAAIqB,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEA,IAAI5D,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEA,IAAI2D,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC;IACP;IAEAQ,UAAU,CAAC,IAAI,EAAElE,CAAC,EAAEuD,CAAC,EAAEjB,CAAC,EAAEqB,CAAC,EAAE5D,CAAC,EAAE2D,CAAC,CAAC;EACpC;EAEA,IAAIS,MAAM,GAAGnF,QAAQ,CAACoF,SAAS;EAE/BD,MAAM,CAACE,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAIrE,CAAC,GAAG,IAAI,CAACA,CAAC;MACVuD,CAAC,GAAG,IAAI,CAACA,CAAC;MACVjB,CAAC,GAAG,IAAI,CAACA,CAAC;MACVqB,CAAC,GAAG,IAAI,CAACA,CAAC;MACV5D,CAAC,GAAG,IAAI,CAACA,CAAC;MACV2D,CAAC,GAAG,IAAI,CAACA,CAAC;MACVY,WAAW,GAAGtE,CAAC,GAAG2D,CAAC,GAAGJ,CAAC,GAAGjB,CAAC,IAAI,KAAK;IACxC,OAAO4B,UAAU,CAAC,IAAI,EAAEP,CAAC,GAAGW,WAAW,EAAE,CAACf,CAAC,GAAGe,WAAW,EAAE,CAAChC,CAAC,GAAGgC,WAAW,EAAEtE,CAAC,GAAGsE,WAAW,EAAE,CAAChC,CAAC,GAAGoB,CAAC,GAAGC,CAAC,GAAG5D,CAAC,IAAIuE,WAAW,EAAE,EAAEtE,CAAC,GAAG0D,CAAC,GAAGH,CAAC,GAAGxD,CAAC,CAAC,GAAGuE,WAAW,CAAC;EAC9J,CAAC;EAEDH,MAAM,CAAC3B,QAAQ,GAAG,SAASA,QAAQA,CAACE,MAAM,EAAE;IAC1C,IAAI1C,CAAC,GAAG,IAAI,CAACA,CAAC;MACVuD,CAAC,GAAG,IAAI,CAACA,CAAC;MACVjB,CAAC,GAAG,IAAI,CAACA,CAAC;MACVqB,CAAC,GAAG,IAAI,CAACA,CAAC;MACV5D,CAAC,GAAG,IAAI,CAACA,CAAC;MACV2D,CAAC,GAAG,IAAI,CAACA,CAAC;MACVa,EAAE,GAAG7B,MAAM,CAAC1C,CAAC;MACbwE,EAAE,GAAG9B,MAAM,CAACJ,CAAC;MACbmC,EAAE,GAAG/B,MAAM,CAACa,CAAC;MACbhE,EAAE,GAAGmD,MAAM,CAACiB,CAAC;MACbe,EAAE,GAAGhC,MAAM,CAAC3C,CAAC;MACb4E,EAAE,GAAGjC,MAAM,CAACgB,CAAC;IACjB,OAAOQ,UAAU,CAAC,IAAI,EAAEK,EAAE,GAAGvE,CAAC,GAAGyE,EAAE,GAAGnC,CAAC,EAAEiC,EAAE,GAAGhB,CAAC,GAAGkB,EAAE,GAAGd,CAAC,EAAEa,EAAE,GAAGxE,CAAC,GAAGT,EAAE,GAAG+C,CAAC,EAAEkC,EAAE,GAAGjB,CAAC,GAAGhE,EAAE,GAAGoE,CAAC,EAAE5D,CAAC,GAAG2E,EAAE,GAAG1E,CAAC,GAAG2E,EAAE,GAAGrC,CAAC,EAAEoB,CAAC,GAAGgB,EAAE,GAAGnB,CAAC,GAAGoB,EAAE,GAAGhB,CAAC,CAAC;EACvI,CAAC;EAEDQ,MAAM,CAACrB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,OAAO,IAAI9D,QAAQ,CAAC,IAAI,CAACgB,CAAC,EAAE,IAAI,CAACuD,CAAC,EAAE,IAAI,CAACjB,CAAC,EAAE,IAAI,CAACqB,CAAC,EAAE,IAAI,CAAC5D,CAAC,EAAE,IAAI,CAAC2D,CAAC,CAAC;EACrE,CAAC;EAEDS,MAAM,CAACS,MAAM,GAAG,SAASA,MAAMA,CAAClC,MAAM,EAAE;IACtC,IAAI1C,CAAC,GAAG,IAAI,CAACA,CAAC;MACVuD,CAAC,GAAG,IAAI,CAACA,CAAC;MACVjB,CAAC,GAAG,IAAI,CAACA,CAAC;MACVqB,CAAC,GAAG,IAAI,CAACA,CAAC;MACV5D,CAAC,GAAG,IAAI,CAACA,CAAC;MACV2D,CAAC,GAAG,IAAI,CAACA,CAAC;IACd,OAAO1D,CAAC,KAAK0C,MAAM,CAAC1C,CAAC,IAAIuD,CAAC,KAAKb,MAAM,CAACa,CAAC,IAAIjB,CAAC,KAAKI,MAAM,CAACJ,CAAC,IAAIqB,CAAC,KAAKjB,MAAM,CAACiB,CAAC,IAAI5D,CAAC,KAAK2C,MAAM,CAAC3C,CAAC,IAAI2D,CAAC,KAAKhB,MAAM,CAACgB,CAAC;EACjH,CAAC;EAEDS,MAAM,CAACU,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC9C,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC,CAAC;IAChB;IAEA,IAAIhD,CAAC,GAAG+C,KAAK,CAAC/C,CAAC;MACXC,CAAC,GAAG8C,KAAK,CAAC9C,CAAC;MACXhC,CAAC,GAAG,IAAI,CAACA,CAAC;MACVuD,CAAC,GAAG,IAAI,CAACA,CAAC;MACVjB,CAAC,GAAG,IAAI,CAACA,CAAC;MACVqB,CAAC,GAAG,IAAI,CAACA,CAAC;MACV5D,CAAC,GAAG,IAAI,CAACA,CAAC;MACV2D,CAAC,GAAG,IAAI,CAACA,CAAC;IACdqB,SAAS,CAAChD,CAAC,GAAGA,CAAC,GAAG/B,CAAC,GAAGgC,CAAC,GAAGM,CAAC,GAAGvC,CAAC,IAAI,CAAC;IACpCgF,SAAS,CAAC/C,CAAC,GAAGD,CAAC,GAAGwB,CAAC,GAAGvB,CAAC,GAAG2B,CAAC,GAAGD,CAAC,IAAI,CAAC;IACpC,OAAOqB,SAAS;EAClB,CAAC;EAED,OAAO/F,QAAQ;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASgG,eAAeA,CAACtG,OAAO,EAAE2F,OAAO,EAAErB,aAAa,EAAEiC,oBAAoB,EAAE;EACrF;EACA,IAAI,CAACvG,OAAO,IAAI,CAACA,OAAO,CAACI,UAAU,IAAI,CAAChB,IAAI,IAAIW,OAAO,CAACC,OAAO,CAAC,EAAEO,eAAe,KAAKP,OAAO,EAAE;IAC7F,OAAO,IAAIM,QAAQ,CAAC,CAAC;EACvB;EAEA,IAAIkG,UAAU,GAAGpF,kBAAkB,CAACpB,OAAO,CAAC;IACxCiD,GAAG,GAAGV,SAAS,CAACvC,OAAO,CAAC;IACxByG,KAAK,GAAGxD,GAAG,GAAGlB,SAAS,GAAGC,SAAS;IACnC4C,SAAS,GAAGP,cAAc,CAACrE,OAAO,EAAEsE,aAAa,CAAC;IAClDoC,EAAE,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,qBAAqB,CAAC,CAAC;IACrCb,EAAE,GAAGW,KAAK,CAAC,CAAC,CAAC,CAACE,qBAAqB,CAAC,CAAC;IACrCC,EAAE,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,qBAAqB,CAAC,CAAC;IACrClC,MAAM,GAAGG,SAAS,CAACxE,UAAU;IAC7ByG,OAAO,GAAG,CAACN,oBAAoB,IAAI5D,QAAQ,CAAC3C,OAAO,CAAC;IACpD2D,CAAC,GAAG,IAAIrD,QAAQ,CAAC,CAACwF,EAAE,CAACP,IAAI,GAAGmB,EAAE,CAACnB,IAAI,IAAI,GAAG,EAAE,CAACO,EAAE,CAACR,GAAG,GAAGoB,EAAE,CAACpB,GAAG,IAAI,GAAG,EAAE,CAACsB,EAAE,CAACrB,IAAI,GAAGmB,EAAE,CAACnB,IAAI,IAAI,GAAG,EAAE,CAACqB,EAAE,CAACtB,GAAG,GAAGoB,EAAE,CAACpB,GAAG,IAAI,GAAG,EAAEoB,EAAE,CAACnB,IAAI,IAAIsB,OAAO,GAAG,CAAC,GAAGzE,iBAAiB,CAAC,CAAC,CAAC,EAAEsE,EAAE,CAACpB,GAAG,IAAIuB,OAAO,GAAG,CAAC,GAAG5E,gBAAgB,CAAC,CAAC,CAAC,CAAC;EAExNwC,MAAM,CAACtD,WAAW,CAACyD,SAAS,CAAC;EAE7B,IAAI4B,UAAU,EAAE;IACdE,EAAE,GAAGF,UAAU,CAACzB,MAAM;IAEtB,OAAO2B,EAAE,EAAE,EAAE;MACXZ,EAAE,GAAGU,UAAU,CAACE,EAAE,CAAC;MACnBZ,EAAE,CAACnE,MAAM,GAAGmE,EAAE,CAAClE,MAAM,GAAG,CAAC;MACzBkE,EAAE,CAACjE,eAAe,CAAC,CAAC,EAAEiE,EAAE,CAAC;IAC3B;EACF;EAEA,OAAOH,OAAO,GAAGhC,CAAC,CAACgC,OAAO,CAAC,CAAC,GAAGhC,CAAC;AAClC;AACA,SAAS1B,gBAAgB,EAAEG,iBAAiB,EAAErC,OAAO,EAAE4C,QAAQ,EAAEsB,OAAO,GAAG,CAAC;AAC5E;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}