{"ast": null, "code": "import * as React from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nfunction Provider({\n  store,\n  context,\n  children,\n  serverState,\n  stabilityCheck = 'once',\n  noopCheck = 'once'\n}) {\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n      stabilityCheck,\n      noopCheck\n    };\n  }, [store, serverState, stabilityCheck, noopCheck]);\n  const previousState = React.useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nexport default Provider;", "map": {"version": 3, "names": ["React", "ReactReduxContext", "createSubscription", "useIsomorphicLayoutEffect", "Provider", "store", "context", "children", "serverState", "stabilityCheck", "<PERSON>op<PERSON><PERSON><PERSON>", "contextValue", "useMemo", "subscription", "getServerState", "undefined", "previousState", "getState", "onStateChange", "notifyNestedSubs", "trySubscribe", "tryUnsubscribe", "Context", "createElement", "value"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/components/Provider.js"], "sourcesContent": ["import * as React from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider({\n  store,\n  context,\n  children,\n  serverState,\n  stabilityCheck = 'once',\n  noopCheck = 'once'\n}) {\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n      stabilityCheck,\n      noopCheck\n    };\n  }, [store, serverState, stabilityCheck, noopCheck]);\n  const previousState = React.useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nexport default Provider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,WAAW;AAC7C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,QAAQA,CAAC;EAChBC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,WAAW;EACXC,cAAc,GAAG,MAAM;EACvBC,SAAS,GAAG;AACd,CAAC,EAAE;EACD,MAAMC,YAAY,GAAGX,KAAK,CAACY,OAAO,CAAC,MAAM;IACvC,MAAMC,YAAY,GAAGX,kBAAkB,CAACG,KAAK,CAAC;IAC9C,OAAO;MACLA,KAAK;MACLQ,YAAY;MACZC,cAAc,EAAEN,WAAW,GAAG,MAAMA,WAAW,GAAGO,SAAS;MAC3DN,cAAc;MACdC;IACF,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEG,WAAW,EAAEC,cAAc,EAAEC,SAAS,CAAC,CAAC;EACnD,MAAMM,aAAa,GAAGhB,KAAK,CAACY,OAAO,CAAC,MAAMP,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAE,CAACZ,KAAK,CAAC,CAAC;EACpEF,yBAAyB,CAAC,MAAM;IAC9B,MAAM;MACJU;IACF,CAAC,GAAGF,YAAY;IAChBE,YAAY,CAACK,aAAa,GAAGL,YAAY,CAACM,gBAAgB;IAC1DN,YAAY,CAACO,YAAY,CAAC,CAAC;IAE3B,IAAIJ,aAAa,KAAKX,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAE;MACtCJ,YAAY,CAACM,gBAAgB,CAAC,CAAC;IACjC;IAEA,OAAO,MAAM;MACXN,YAAY,CAACQ,cAAc,CAAC,CAAC;MAC7BR,YAAY,CAACK,aAAa,GAAGH,SAAS;IACxC,CAAC;EACH,CAAC,EAAE,CAACJ,YAAY,EAAEK,aAAa,CAAC,CAAC;EACjC,MAAMM,OAAO,GAAGhB,OAAO,IAAIL,iBAAiB,CAAC,CAAC;;EAE9C,OAAO,aAAaD,KAAK,CAACuB,aAAa,CAACD,OAAO,CAAClB,QAAQ,EAAE;IACxDoB,KAAK,EAAEb;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}