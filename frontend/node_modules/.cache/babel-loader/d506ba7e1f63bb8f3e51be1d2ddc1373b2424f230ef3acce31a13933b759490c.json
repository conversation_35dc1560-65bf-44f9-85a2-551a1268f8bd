{"ast": null, "code": "import React from'react';import{Provider}from'react-redux';import{store}from'./store';import{BrowserRouter,Routes,Route}from'react-router-dom';// ... existing imports ...\nimport HomePage from'./components/Home/HomePage';import{VideoPreview}from'./components/VideoPreview/VideoPreview';import{CheckoutPage}from'./components/Checkout/CheckoutPage';import VideoCreationPage from'./components/VideoCreation/CreateVideoPage';import ManualVideoCreationPage from'./components/ManualVideoCreation/ManualVideoCreationPage';// Import the manual creation page component\nimport SamplesPage from'./components/Samples/SamplesPage';import GyroscopePage from'./components/VideoCreation/GyroscopePage';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(Provider,{store:store,children:/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(HomePage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/gyroscope\",element:/*#__PURE__*/_jsx(GyroscopePage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/create-video\",element:/*#__PURE__*/_jsx(VideoCreationPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/checkout\",element:/*#__PURE__*/_jsx(CheckoutPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/preview/:videoId\",element:/*#__PURE__*/_jsx(VideoPreview,{})}),/*#__PURE__*/_jsx(Route,{path:\"/manual-create-video\",element:/*#__PURE__*/_jsx(ManualVideoCreationPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"/samples\",element:/*#__PURE__*/_jsx(SamplesPage,{})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "Provider", "store", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "HomePage", "VideoPreview", "CheckoutPage", "VideoCreationPage", "ManualVideoCreationPage", "SamplesPage", "GyroscopePage", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "path", "element"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from './store';\nimport { BrowserRouter, Routes, Route } from 'react-router-dom';\n// ... existing imports ...\nimport  HomePage  from './components/Home/HomePage';\nimport { VideoPreview } from './components/VideoPreview/VideoPreview';\nimport { CheckoutPage } from './components/Checkout/CheckoutPage';\nimport   VideoCreationPage   from './components/VideoCreation/CreateVideoPage';\nimport  ManualVideoCreationPage  from './components/ManualVideoCreation/ManualVideoCreationPage'; // Import the manual creation page component\nimport SamplesPage from './components/Samples/SamplesPage';\nimport GyroscopePage from './components/VideoCreation/GyroscopePage';\n\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <BrowserRouter>\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/gyroscope\" element={<GyroscopePage />} />\n          <Route path=\"/create-video\" element={<VideoCreationPage />} />\n          <Route path=\"/checkout\" element={<CheckoutPage />} />\n          <Route path=\"/preview/:videoId\" element={<VideoPreview />} />\n          <Route path=\"/manual-create-video\" element={<ManualVideoCreationPage />} />\n          <Route path=\"/samples\" element={<SamplesPage />} />\n        </Routes>\n      </BrowserRouter>\n    </Provider>\n  );\n}\n\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,aAAa,CACtC,OAASC,KAAK,KAAQ,SAAS,CAC/B,OAASC,aAAa,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAC/D;AACA,MAAQ,CAAAC,QAAQ,KAAO,4BAA4B,CACnD,OAASC,YAAY,KAAQ,wCAAwC,CACrE,OAASC,YAAY,KAAQ,oCAAoC,CACjE,MAAS,CAAAC,iBAAiB,KAAQ,4CAA4C,CAC9E,MAAQ,CAAAC,uBAAuB,KAAO,0DAA0D,CAAE;AAClG,MAAO,CAAAC,WAAW,KAAM,kCAAkC,CAC1D,MAAO,CAAAC,aAAa,KAAM,0CAA0C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGrE,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACb,QAAQ,EAACC,KAAK,CAAEA,KAAM,CAAAgB,QAAA,cACrBJ,IAAA,CAACX,aAAa,EAAAe,QAAA,cACZF,KAAA,CAACZ,MAAM,EAAAc,QAAA,eACLJ,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEN,IAAA,CAACR,QAAQ,GAAE,CAAE,CAAE,CAAC,cACzCQ,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEN,IAAA,CAACF,aAAa,GAAE,CAAE,CAAE,CAAC,cACvDE,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEN,IAAA,CAACL,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAC9DK,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEN,IAAA,CAACN,YAAY,GAAE,CAAE,CAAE,CAAC,cACrDM,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEN,IAAA,CAACP,YAAY,GAAE,CAAE,CAAE,CAAC,cAC7DO,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,sBAAsB,CAACC,OAAO,cAAEN,IAAA,CAACJ,uBAAuB,GAAE,CAAE,CAAE,CAAC,cAC3EI,IAAA,CAACT,KAAK,EAACc,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEN,IAAA,CAACH,WAAW,GAAE,CAAE,CAAE,CAAC,EAC7C,CAAC,CACI,CAAC,CACR,CAAC,CAEf,CAEA,cAAe,CAAAM,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}