{"ast": null, "code": "import * as React from 'react';\nconst ContextKey = Symbol.for(`react-redux-context`);\nconst gT = typeof globalThis !== \"undefined\" ? globalThis : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */\n{};\nfunction getContext() {\n  var _gT$ContextKey;\n  if (!React.createContext) return {};\n  const contextMap = (_gT$ContextKey = gT[ContextKey]) != null ? _gT$ContextKey : gT[ContextKey] = new Map();\n  let realContext = contextMap.get(React.createContext);\n  if (!realContext) {\n    realContext = React.createContext(null);\n    if (process.env.NODE_ENV !== 'production') {\n      realContext.displayName = 'ReactRedux';\n    }\n    contextMap.set(React.createContext, realContext);\n  }\n  return realContext;\n}\nexport const ReactReduxContext = /*#__PURE__*/getContext();\nexport default ReactReduxContext;", "map": {"version": 3, "names": ["React", "Context<PERSON>ey", "Symbol", "for", "gT", "globalThis", "getContext", "_gT$ContextKey", "createContext", "contextMap", "Map", "realContext", "get", "process", "env", "NODE_ENV", "displayName", "set", "ReactReduxContext"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/components/Context.js"], "sourcesContent": ["import * as React from 'react';\nconst ContextKey = Symbol.for(`react-redux-context`);\nconst gT = typeof globalThis !== \"undefined\" ? globalThis :\n/* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */\n{};\n\nfunction getContext() {\n  var _gT$ContextKey;\n\n  if (!React.createContext) return {};\n  const contextMap = (_gT$ContextKey = gT[ContextKey]) != null ? _gT$ContextKey : gT[ContextKey] = new Map();\n  let realContext = contextMap.get(React.createContext);\n\n  if (!realContext) {\n    realContext = React.createContext(null);\n\n    if (process.env.NODE_ENV !== 'production') {\n      realContext.displayName = 'ReactRedux';\n    }\n\n    contextMap.set(React.createContext, realContext);\n  }\n\n  return realContext;\n}\n\nexport const ReactReduxContext = /*#__PURE__*/getContext();\nexport default ReactReduxContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,UAAU,GAAGC,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC;AACpD,MAAMC,EAAE,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GACzD;AACA,CAAC,CAAC;AAEF,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAIC,cAAc;EAElB,IAAI,CAACP,KAAK,CAACQ,aAAa,EAAE,OAAO,CAAC,CAAC;EACnC,MAAMC,UAAU,GAAG,CAACF,cAAc,GAAGH,EAAE,CAACH,UAAU,CAAC,KAAK,IAAI,GAAGM,cAAc,GAAGH,EAAE,CAACH,UAAU,CAAC,GAAG,IAAIS,GAAG,CAAC,CAAC;EAC1G,IAAIC,WAAW,GAAGF,UAAU,CAACG,GAAG,CAACZ,KAAK,CAACQ,aAAa,CAAC;EAErD,IAAI,CAACG,WAAW,EAAE;IAChBA,WAAW,GAAGX,KAAK,CAACQ,aAAa,CAAC,IAAI,CAAC;IAEvC,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,WAAW,CAACK,WAAW,GAAG,YAAY;IACxC;IAEAP,UAAU,CAACQ,GAAG,CAACjB,KAAK,CAACQ,aAAa,EAAEG,WAAW,CAAC;EAClD;EAEA,OAAOA,WAAW;AACpB;AAEA,OAAO,MAAMO,iBAAiB,GAAG,aAAaZ,UAAU,CAAC,CAAC;AAC1D,eAAeY,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}