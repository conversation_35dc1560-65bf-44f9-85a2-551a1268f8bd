{"ast": null, "code": "function n(n) {\n  for (var r = arguments.length, t = Array(r > 1 ? r - 1 : 0), e = 1; e < r; e++) t[e - 1] = arguments[e];\n  if (\"production\" !== process.env.NODE_ENV) {\n    var i = Y[n],\n      o = i ? \"function\" == typeof i ? i.apply(null, t) : i : \"unknown error nr: \" + n;\n    throw Error(\"[Immer] \" + o);\n  }\n  throw Error(\"[Immer] minified error nr: \" + n + (t.length ? \" \" + t.map(function (n) {\n    return \"'\" + n + \"'\";\n  }).join(\",\") : \"\") + \". Find the full error at: https://bit.ly/3cXEKWf\");\n}\nfunction r(n) {\n  return !!n && !!n[Q];\n}\nfunction t(n) {\n  var r;\n  return !!n && (function (n) {\n    if (!n || \"object\" != typeof n) return !1;\n    var r = Object.getPrototypeOf(n);\n    if (null === r) return !0;\n    var t = Object.hasOwnProperty.call(r, \"constructor\") && r.constructor;\n    return t === Object || \"function\" == typeof t && Function.toString.call(t) === Z;\n  }(n) || Array.isArray(n) || !!n[L] || !!(null === (r = n.constructor) || void 0 === r ? void 0 : r[L]) || s(n) || v(n));\n}\nfunction e(t) {\n  return r(t) || n(23, t), t[Q].t;\n}\nfunction i(n, r, t) {\n  void 0 === t && (t = !1), 0 === o(n) ? (t ? Object.keys : nn)(n).forEach(function (e) {\n    t && \"symbol\" == typeof e || r(e, n[e], n);\n  }) : n.forEach(function (t, e) {\n    return r(e, t, n);\n  });\n}\nfunction o(n) {\n  var r = n[Q];\n  return r ? r.i > 3 ? r.i - 4 : r.i : Array.isArray(n) ? 1 : s(n) ? 2 : v(n) ? 3 : 0;\n}\nfunction u(n, r) {\n  return 2 === o(n) ? n.has(r) : Object.prototype.hasOwnProperty.call(n, r);\n}\nfunction a(n, r) {\n  return 2 === o(n) ? n.get(r) : n[r];\n}\nfunction f(n, r, t) {\n  var e = o(n);\n  2 === e ? n.set(r, t) : 3 === e ? n.add(t) : n[r] = t;\n}\nfunction c(n, r) {\n  return n === r ? 0 !== n || 1 / n == 1 / r : n != n && r != r;\n}\nfunction s(n) {\n  return X && n instanceof Map;\n}\nfunction v(n) {\n  return q && n instanceof Set;\n}\nfunction p(n) {\n  return n.o || n.t;\n}\nfunction l(n) {\n  if (Array.isArray(n)) return Array.prototype.slice.call(n);\n  var r = rn(n);\n  delete r[Q];\n  for (var t = nn(r), e = 0; e < t.length; e++) {\n    var i = t[e],\n      o = r[i];\n    !1 === o.writable && (o.writable = !0, o.configurable = !0), (o.get || o.set) && (r[i] = {\n      configurable: !0,\n      writable: !0,\n      enumerable: o.enumerable,\n      value: n[i]\n    });\n  }\n  return Object.create(Object.getPrototypeOf(n), r);\n}\nfunction d(n, e) {\n  return void 0 === e && (e = !1), y(n) || r(n) || !t(n) || (o(n) > 1 && (n.set = n.add = n.clear = n.delete = h), Object.freeze(n), e && i(n, function (n, r) {\n    return d(r, !0);\n  }, !0)), n;\n}\nfunction h() {\n  n(2);\n}\nfunction y(n) {\n  return null == n || \"object\" != typeof n || Object.isFrozen(n);\n}\nfunction b(r) {\n  var t = tn[r];\n  return t || n(18, r), t;\n}\nfunction m(n, r) {\n  tn[n] || (tn[n] = r);\n}\nfunction _() {\n  return \"production\" === process.env.NODE_ENV || U || n(0), U;\n}\nfunction j(n, r) {\n  r && (b(\"Patches\"), n.u = [], n.s = [], n.v = r);\n}\nfunction g(n) {\n  O(n), n.p.forEach(S), n.p = null;\n}\nfunction O(n) {\n  n === U && (U = n.l);\n}\nfunction w(n) {\n  return U = {\n    p: [],\n    l: U,\n    h: n,\n    m: !0,\n    _: 0\n  };\n}\nfunction S(n) {\n  var r = n[Q];\n  0 === r.i || 1 === r.i ? r.j() : r.g = !0;\n}\nfunction P(r, e) {\n  e._ = e.p.length;\n  var i = e.p[0],\n    o = void 0 !== r && r !== i;\n  return e.h.O || b(\"ES5\").S(e, r, o), o ? (i[Q].P && (g(e), n(4)), t(r) && (r = M(e, r), e.l || x(e, r)), e.u && b(\"Patches\").M(i[Q].t, r, e.u, e.s)) : r = M(e, i, []), g(e), e.u && e.v(e.u, e.s), r !== H ? r : void 0;\n}\nfunction M(n, r, t) {\n  if (y(r)) return r;\n  var e = r[Q];\n  if (!e) return i(r, function (i, o) {\n    return A(n, e, r, i, o, t);\n  }, !0), r;\n  if (e.A !== n) return r;\n  if (!e.P) return x(n, e.t, !0), e.t;\n  if (!e.I) {\n    e.I = !0, e.A._--;\n    var o = 4 === e.i || 5 === e.i ? e.o = l(e.k) : e.o,\n      u = o,\n      a = !1;\n    3 === e.i && (u = new Set(o), o.clear(), a = !0), i(u, function (r, i) {\n      return A(n, e, o, r, i, t, a);\n    }), x(n, o, !1), t && n.u && b(\"Patches\").N(e, t, n.u, n.s);\n  }\n  return e.o;\n}\nfunction A(e, i, o, a, c, s, v) {\n  if (\"production\" !== process.env.NODE_ENV && c === o && n(5), r(c)) {\n    var p = M(e, c, s && i && 3 !== i.i && !u(i.R, a) ? s.concat(a) : void 0);\n    if (f(o, a, p), !r(p)) return;\n    e.m = !1;\n  } else v && o.add(c);\n  if (t(c) && !y(c)) {\n    if (!e.h.D && e._ < 1) return;\n    M(e, c), i && i.A.l || x(e, c);\n  }\n}\nfunction x(n, r, t) {\n  void 0 === t && (t = !1), !n.l && n.h.D && n.m && d(r, t);\n}\nfunction z(n, r) {\n  var t = n[Q];\n  return (t ? p(t) : n)[r];\n}\nfunction I(n, r) {\n  if (r in n) for (var t = Object.getPrototypeOf(n); t;) {\n    var e = Object.getOwnPropertyDescriptor(t, r);\n    if (e) return e;\n    t = Object.getPrototypeOf(t);\n  }\n}\nfunction k(n) {\n  n.P || (n.P = !0, n.l && k(n.l));\n}\nfunction E(n) {\n  n.o || (n.o = l(n.t));\n}\nfunction N(n, r, t) {\n  var e = s(r) ? b(\"MapSet\").F(r, t) : v(r) ? b(\"MapSet\").T(r, t) : n.O ? function (n, r) {\n    var t = Array.isArray(n),\n      e = {\n        i: t ? 1 : 0,\n        A: r ? r.A : _(),\n        P: !1,\n        I: !1,\n        R: {},\n        l: r,\n        t: n,\n        k: null,\n        o: null,\n        j: null,\n        C: !1\n      },\n      i = e,\n      o = en;\n    t && (i = [e], o = on);\n    var u = Proxy.revocable(i, o),\n      a = u.revoke,\n      f = u.proxy;\n    return e.k = f, e.j = a, f;\n  }(r, t) : b(\"ES5\").J(r, t);\n  return (t ? t.A : _()).p.push(e), e;\n}\nfunction R(e) {\n  return r(e) || n(22, e), function n(r) {\n    if (!t(r)) return r;\n    var e,\n      u = r[Q],\n      c = o(r);\n    if (u) {\n      if (!u.P && (u.i < 4 || !b(\"ES5\").K(u))) return u.t;\n      u.I = !0, e = D(r, c), u.I = !1;\n    } else e = D(r, c);\n    return i(e, function (r, t) {\n      u && a(u.t, r) === t || f(e, r, n(t));\n    }), 3 === c ? new Set(e) : e;\n  }(e);\n}\nfunction D(n, r) {\n  switch (r) {\n    case 2:\n      return new Map(n);\n    case 3:\n      return Array.from(n);\n  }\n  return l(n);\n}\nfunction F() {\n  function t(n, r) {\n    var t = s[n];\n    return t ? t.enumerable = r : s[n] = t = {\n      configurable: !0,\n      enumerable: r,\n      get: function () {\n        var r = this[Q];\n        return \"production\" !== process.env.NODE_ENV && f(r), en.get(r, n);\n      },\n      set: function (r) {\n        var t = this[Q];\n        \"production\" !== process.env.NODE_ENV && f(t), en.set(t, n, r);\n      }\n    }, t;\n  }\n  function e(n) {\n    for (var r = n.length - 1; r >= 0; r--) {\n      var t = n[r][Q];\n      if (!t.P) switch (t.i) {\n        case 5:\n          a(t) && k(t);\n          break;\n        case 4:\n          o(t) && k(t);\n      }\n    }\n  }\n  function o(n) {\n    for (var r = n.t, t = n.k, e = nn(t), i = e.length - 1; i >= 0; i--) {\n      var o = e[i];\n      if (o !== Q) {\n        var a = r[o];\n        if (void 0 === a && !u(r, o)) return !0;\n        var f = t[o],\n          s = f && f[Q];\n        if (s ? s.t !== a : !c(f, a)) return !0;\n      }\n    }\n    var v = !!r[Q];\n    return e.length !== nn(r).length + (v ? 0 : 1);\n  }\n  function a(n) {\n    var r = n.k;\n    if (r.length !== n.t.length) return !0;\n    var t = Object.getOwnPropertyDescriptor(r, r.length - 1);\n    if (t && !t.get) return !0;\n    for (var e = 0; e < r.length; e++) if (!r.hasOwnProperty(e)) return !0;\n    return !1;\n  }\n  function f(r) {\n    r.g && n(3, JSON.stringify(p(r)));\n  }\n  var s = {};\n  m(\"ES5\", {\n    J: function (n, r) {\n      var e = Array.isArray(n),\n        i = function (n, r) {\n          if (n) {\n            for (var e = Array(r.length), i = 0; i < r.length; i++) Object.defineProperty(e, \"\" + i, t(i, !0));\n            return e;\n          }\n          var o = rn(r);\n          delete o[Q];\n          for (var u = nn(o), a = 0; a < u.length; a++) {\n            var f = u[a];\n            o[f] = t(f, n || !!o[f].enumerable);\n          }\n          return Object.create(Object.getPrototypeOf(r), o);\n        }(e, n),\n        o = {\n          i: e ? 5 : 4,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          R: {},\n          l: r,\n          t: n,\n          k: i,\n          o: null,\n          g: !1,\n          C: !1\n        };\n      return Object.defineProperty(i, Q, {\n        value: o,\n        writable: !0\n      }), i;\n    },\n    S: function (n, t, o) {\n      o ? r(t) && t[Q].A === n && e(n.p) : (n.u && function n(r) {\n        if (r && \"object\" == typeof r) {\n          var t = r[Q];\n          if (t) {\n            var e = t.t,\n              o = t.k,\n              f = t.R,\n              c = t.i;\n            if (4 === c) i(o, function (r) {\n              r !== Q && (void 0 !== e[r] || u(e, r) ? f[r] || n(o[r]) : (f[r] = !0, k(t)));\n            }), i(e, function (n) {\n              void 0 !== o[n] || u(o, n) || (f[n] = !1, k(t));\n            });else if (5 === c) {\n              if (a(t) && (k(t), f.length = !0), o.length < e.length) for (var s = o.length; s < e.length; s++) f[s] = !1;else for (var v = e.length; v < o.length; v++) f[v] = !0;\n              for (var p = Math.min(o.length, e.length), l = 0; l < p; l++) o.hasOwnProperty(l) || (f[l] = !0), void 0 === f[l] && n(o[l]);\n            }\n          }\n        }\n      }(n.p[0]), e(n.p));\n    },\n    K: function (n) {\n      return 4 === n.i ? o(n) : a(n);\n    }\n  });\n}\nfunction T() {\n  function e(n) {\n    if (!t(n)) return n;\n    if (Array.isArray(n)) return n.map(e);\n    if (s(n)) return new Map(Array.from(n.entries()).map(function (n) {\n      return [n[0], e(n[1])];\n    }));\n    if (v(n)) return new Set(Array.from(n).map(e));\n    var r = Object.create(Object.getPrototypeOf(n));\n    for (var i in n) r[i] = e(n[i]);\n    return u(n, L) && (r[L] = n[L]), r;\n  }\n  function f(n) {\n    return r(n) ? e(n) : n;\n  }\n  var c = \"add\";\n  m(\"Patches\", {\n    $: function (r, t) {\n      return t.forEach(function (t) {\n        for (var i = t.path, u = t.op, f = r, s = 0; s < i.length - 1; s++) {\n          var v = o(f),\n            p = i[s];\n          \"string\" != typeof p && \"number\" != typeof p && (p = \"\" + p), 0 !== v && 1 !== v || \"__proto__\" !== p && \"constructor\" !== p || n(24), \"function\" == typeof f && \"prototype\" === p && n(24), \"object\" != typeof (f = a(f, p)) && n(15, i.join(\"/\"));\n        }\n        var l = o(f),\n          d = e(t.value),\n          h = i[i.length - 1];\n        switch (u) {\n          case \"replace\":\n            switch (l) {\n              case 2:\n                return f.set(h, d);\n              case 3:\n                n(16);\n              default:\n                return f[h] = d;\n            }\n          case c:\n            switch (l) {\n              case 1:\n                return \"-\" === h ? f.push(d) : f.splice(h, 0, d);\n              case 2:\n                return f.set(h, d);\n              case 3:\n                return f.add(d);\n              default:\n                return f[h] = d;\n            }\n          case \"remove\":\n            switch (l) {\n              case 1:\n                return f.splice(h, 1);\n              case 2:\n                return f.delete(h);\n              case 3:\n                return f.delete(t.value);\n              default:\n                return delete f[h];\n            }\n          default:\n            n(17, u);\n        }\n      }), r;\n    },\n    N: function (n, r, t, e) {\n      switch (n.i) {\n        case 0:\n        case 4:\n        case 2:\n          return function (n, r, t, e) {\n            var o = n.t,\n              s = n.o;\n            i(n.R, function (n, i) {\n              var v = a(o, n),\n                p = a(s, n),\n                l = i ? u(o, n) ? \"replace\" : c : \"remove\";\n              if (v !== p || \"replace\" !== l) {\n                var d = r.concat(n);\n                t.push(\"remove\" === l ? {\n                  op: l,\n                  path: d\n                } : {\n                  op: l,\n                  path: d,\n                  value: p\n                }), e.push(l === c ? {\n                  op: \"remove\",\n                  path: d\n                } : \"remove\" === l ? {\n                  op: c,\n                  path: d,\n                  value: f(v)\n                } : {\n                  op: \"replace\",\n                  path: d,\n                  value: f(v)\n                });\n              }\n            });\n          }(n, r, t, e);\n        case 5:\n        case 1:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.R,\n              u = n.o;\n            if (u.length < i.length) {\n              var a = [u, i];\n              i = a[0], u = a[1];\n              var s = [e, t];\n              t = s[0], e = s[1];\n            }\n            for (var v = 0; v < i.length; v++) if (o[v] && u[v] !== i[v]) {\n              var p = r.concat([v]);\n              t.push({\n                op: \"replace\",\n                path: p,\n                value: f(u[v])\n              }), e.push({\n                op: \"replace\",\n                path: p,\n                value: f(i[v])\n              });\n            }\n            for (var l = i.length; l < u.length; l++) {\n              var d = r.concat([l]);\n              t.push({\n                op: c,\n                path: d,\n                value: f(u[l])\n              });\n            }\n            i.length < u.length && e.push({\n              op: \"replace\",\n              path: r.concat([\"length\"]),\n              value: i.length\n            });\n          }(n, r, t, e);\n        case 3:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.o,\n              u = 0;\n            i.forEach(function (n) {\n              if (!o.has(n)) {\n                var i = r.concat([u]);\n                t.push({\n                  op: \"remove\",\n                  path: i,\n                  value: n\n                }), e.unshift({\n                  op: c,\n                  path: i,\n                  value: n\n                });\n              }\n              u++;\n            }), u = 0, o.forEach(function (n) {\n              if (!i.has(n)) {\n                var o = r.concat([u]);\n                t.push({\n                  op: c,\n                  path: o,\n                  value: n\n                }), e.unshift({\n                  op: \"remove\",\n                  path: o,\n                  value: n\n                });\n              }\n              u++;\n            });\n          }(n, r, t, e);\n      }\n    },\n    M: function (n, r, t, e) {\n      t.push({\n        op: \"replace\",\n        path: [],\n        value: r === H ? void 0 : r\n      }), e.push({\n        op: \"replace\",\n        path: [],\n        value: n\n      });\n    }\n  });\n}\nfunction C() {\n  function r(n, r) {\n    function t() {\n      this.constructor = n;\n    }\n    a(n, r), n.prototype = (t.prototype = r.prototype, new t());\n  }\n  function e(n) {\n    n.o || (n.R = new Map(), n.o = new Map(n.t));\n  }\n  function o(n) {\n    n.o || (n.o = new Set(), n.t.forEach(function (r) {\n      if (t(r)) {\n        var e = N(n.A.h, r, n);\n        n.p.set(r, e), n.o.add(e);\n      } else n.o.add(r);\n    }));\n  }\n  function u(r) {\n    r.g && n(3, JSON.stringify(p(r)));\n  }\n  var a = function (n, r) {\n      return (a = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (n, r) {\n        n.__proto__ = r;\n      } || function (n, r) {\n        for (var t in r) r.hasOwnProperty(t) && (n[t] = r[t]);\n      })(n, r);\n    },\n    f = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 2,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          R: void 0,\n          t: n,\n          k: this,\n          C: !1,\n          g: !1\n        }, this;\n      }\n      r(n, Map);\n      var o = n.prototype;\n      return Object.defineProperty(o, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), o.has = function (n) {\n        return p(this[Q]).has(n);\n      }, o.set = function (n, r) {\n        var t = this[Q];\n        return u(t), p(t).has(n) && p(t).get(n) === r || (e(t), k(t), t.R.set(n, !0), t.o.set(n, r), t.R.set(n, !0)), this;\n      }, o.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), e(r), k(r), r.t.has(n) ? r.R.set(n, !1) : r.R.delete(n), r.o.delete(n), !0;\n      }, o.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (e(n), k(n), n.R = new Map(), i(n.t, function (r) {\n          n.R.set(r, !1);\n        }), n.o.clear());\n      }, o.forEach = function (n, r) {\n        var t = this;\n        p(this[Q]).forEach(function (e, i) {\n          n.call(r, t.get(i), i, t);\n        });\n      }, o.get = function (n) {\n        var r = this[Q];\n        u(r);\n        var i = p(r).get(n);\n        if (r.I || !t(i)) return i;\n        if (i !== r.t.get(n)) return i;\n        var o = N(r.A.h, i, r);\n        return e(r), r.o.set(n, o), o;\n      }, o.keys = function () {\n        return p(this[Q]).keys();\n      }, o.values = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.values();\n        }, n.next = function () {\n          var n = t.next();\n          return n.done ? n : {\n            done: !1,\n            value: r.get(n.value)\n          };\n        }, n;\n      }, o.entries = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.entries();\n        }, n.next = function () {\n          var n = t.next();\n          if (n.done) return n;\n          var e = r.get(n.value);\n          return {\n            done: !1,\n            value: [n.value, e]\n          };\n        }, n;\n      }, o[V] = function () {\n        return this.entries();\n      }, n;\n    }(),\n    c = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 3,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          t: n,\n          k: this,\n          p: new Map(),\n          g: !1,\n          C: !1\n        }, this;\n      }\n      r(n, Set);\n      var t = n.prototype;\n      return Object.defineProperty(t, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), t.has = function (n) {\n        var r = this[Q];\n        return u(r), r.o ? !!r.o.has(n) || !(!r.p.has(n) || !r.o.has(r.p.get(n))) : r.t.has(n);\n      }, t.add = function (n) {\n        var r = this[Q];\n        return u(r), this.has(n) || (o(r), k(r), r.o.add(n)), this;\n      }, t.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), o(r), k(r), r.o.delete(n) || !!r.p.has(n) && r.o.delete(r.p.get(n));\n      }, t.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (o(n), k(n), n.o.clear());\n      }, t.values = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.values();\n      }, t.entries = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.entries();\n      }, t.keys = function () {\n        return this.values();\n      }, t[V] = function () {\n        return this.values();\n      }, t.forEach = function (n, r) {\n        for (var t = this.values(), e = t.next(); !e.done;) n.call(r, e.value, e.value, this), e = t.next();\n      }, n;\n    }();\n  m(\"MapSet\", {\n    F: function (n, r) {\n      return new f(n, r);\n    },\n    T: function (n, r) {\n      return new c(n, r);\n    }\n  });\n}\nfunction J() {\n  F(), C(), T();\n}\nfunction K(n) {\n  return n;\n}\nfunction $(n) {\n  return n;\n}\nvar G,\n  U,\n  W = \"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol(\"x\"),\n  X = \"undefined\" != typeof Map,\n  q = \"undefined\" != typeof Set,\n  B = \"undefined\" != typeof Proxy && void 0 !== Proxy.revocable && \"undefined\" != typeof Reflect,\n  H = W ? Symbol.for(\"immer-nothing\") : ((G = {})[\"immer-nothing\"] = !0, G),\n  L = W ? Symbol.for(\"immer-draftable\") : \"__$immer_draftable\",\n  Q = W ? Symbol.for(\"immer-state\") : \"__$immer_state\",\n  V = \"undefined\" != typeof Symbol && Symbol.iterator || \"@@iterator\",\n  Y = {\n    0: \"Illegal state\",\n    1: \"Immer drafts cannot have computed properties\",\n    2: \"This object has been frozen and should not be mutated\",\n    3: function (n) {\n      return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + n;\n    },\n    4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n    5: \"Immer forbids circular references\",\n    6: \"The first or second argument to `produce` must be a function\",\n    7: \"The third argument to `produce` must be a function or undefined\",\n    8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n    9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n    10: \"The given draft is already finalized\",\n    11: \"Object.defineProperty() cannot be used on an Immer draft\",\n    12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n    13: \"Immer only supports deleting array indices\",\n    14: \"Immer only supports setting array indices and the 'length' property\",\n    15: function (n) {\n      return \"Cannot apply patch, path doesn't resolve: \" + n;\n    },\n    16: 'Sets cannot have \"replace\" patches.',\n    17: function (n) {\n      return \"Unsupported patch operation: \" + n;\n    },\n    18: function (n) {\n      return \"The plugin for '\" + n + \"' has not been loaded into Immer. To enable the plugin, import and call `enable\" + n + \"()` when initializing your application.\";\n    },\n    20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n    21: function (n) {\n      return \"produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '\" + n + \"'\";\n    },\n    22: function (n) {\n      return \"'current' expects a draft, got: \" + n;\n    },\n    23: function (n) {\n      return \"'original' expects a draft, got: \" + n;\n    },\n    24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n  },\n  Z = \"\" + Object.prototype.constructor,\n  nn = \"undefined\" != typeof Reflect && Reflect.ownKeys ? Reflect.ownKeys : void 0 !== Object.getOwnPropertySymbols ? function (n) {\n    return Object.getOwnPropertyNames(n).concat(Object.getOwnPropertySymbols(n));\n  } : Object.getOwnPropertyNames,\n  rn = Object.getOwnPropertyDescriptors || function (n) {\n    var r = {};\n    return nn(n).forEach(function (t) {\n      r[t] = Object.getOwnPropertyDescriptor(n, t);\n    }), r;\n  },\n  tn = {},\n  en = {\n    get: function (n, r) {\n      if (r === Q) return n;\n      var e = p(n);\n      if (!u(e, r)) return function (n, r, t) {\n        var e,\n          i = I(r, t);\n        return i ? \"value\" in i ? i.value : null === (e = i.get) || void 0 === e ? void 0 : e.call(n.k) : void 0;\n      }(n, e, r);\n      var i = e[r];\n      return n.I || !t(i) ? i : i === z(n.t, r) ? (E(n), n.o[r] = N(n.A.h, i, n)) : i;\n    },\n    has: function (n, r) {\n      return r in p(n);\n    },\n    ownKeys: function (n) {\n      return Reflect.ownKeys(p(n));\n    },\n    set: function (n, r, t) {\n      var e = I(p(n), r);\n      if (null == e ? void 0 : e.set) return e.set.call(n.k, t), !0;\n      if (!n.P) {\n        var i = z(p(n), r),\n          o = null == i ? void 0 : i[Q];\n        if (o && o.t === t) return n.o[r] = t, n.R[r] = !1, !0;\n        if (c(t, i) && (void 0 !== t || u(n.t, r))) return !0;\n        E(n), k(n);\n      }\n      return n.o[r] === t && (void 0 !== t || r in n.o) || Number.isNaN(t) && Number.isNaN(n.o[r]) || (n.o[r] = t, n.R[r] = !0), !0;\n    },\n    deleteProperty: function (n, r) {\n      return void 0 !== z(n.t, r) || r in n.t ? (n.R[r] = !1, E(n), k(n)) : delete n.R[r], n.o && delete n.o[r], !0;\n    },\n    getOwnPropertyDescriptor: function (n, r) {\n      var t = p(n),\n        e = Reflect.getOwnPropertyDescriptor(t, r);\n      return e ? {\n        writable: !0,\n        configurable: 1 !== n.i || \"length\" !== r,\n        enumerable: e.enumerable,\n        value: t[r]\n      } : e;\n    },\n    defineProperty: function () {\n      n(11);\n    },\n    getPrototypeOf: function (n) {\n      return Object.getPrototypeOf(n.t);\n    },\n    setPrototypeOf: function () {\n      n(12);\n    }\n  },\n  on = {};\ni(en, function (n, r) {\n  on[n] = function () {\n    return arguments[0] = arguments[0][0], r.apply(this, arguments);\n  };\n}), on.deleteProperty = function (r, t) {\n  return \"production\" !== process.env.NODE_ENV && isNaN(parseInt(t)) && n(13), on.set.call(this, r, t, void 0);\n}, on.set = function (r, t, e) {\n  return \"production\" !== process.env.NODE_ENV && \"length\" !== t && isNaN(parseInt(t)) && n(14), en.set.call(this, r[0], t, e, r[0]);\n};\nvar un = function () {\n    function e(r) {\n      var e = this;\n      this.O = B, this.D = !0, this.produce = function (r, i, o) {\n        if (\"function\" == typeof r && \"function\" != typeof i) {\n          var u = i;\n          i = r;\n          var a = e;\n          return function (n) {\n            var r = this;\n            void 0 === n && (n = u);\n            for (var t = arguments.length, e = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) e[o - 1] = arguments[o];\n            return a.produce(n, function (n) {\n              var t;\n              return (t = i).call.apply(t, [r, n].concat(e));\n            });\n          };\n        }\n        var f;\n        if (\"function\" != typeof i && n(6), void 0 !== o && \"function\" != typeof o && n(7), t(r)) {\n          var c = w(e),\n            s = N(e, r, void 0),\n            v = !0;\n          try {\n            f = i(s), v = !1;\n          } finally {\n            v ? g(c) : O(c);\n          }\n          return \"undefined\" != typeof Promise && f instanceof Promise ? f.then(function (n) {\n            return j(c, o), P(n, c);\n          }, function (n) {\n            throw g(c), n;\n          }) : (j(c, o), P(f, c));\n        }\n        if (!r || \"object\" != typeof r) {\n          if (void 0 === (f = i(r)) && (f = r), f === H && (f = void 0), e.D && d(f, !0), o) {\n            var p = [],\n              l = [];\n            b(\"Patches\").M(r, f, p, l), o(p, l);\n          }\n          return f;\n        }\n        n(21, r);\n      }, this.produceWithPatches = function (n, r) {\n        if (\"function\" == typeof n) return function (r) {\n          for (var t = arguments.length, i = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) i[o - 1] = arguments[o];\n          return e.produceWithPatches(r, function (r) {\n            return n.apply(void 0, [r].concat(i));\n          });\n        };\n        var t,\n          i,\n          o = e.produce(n, r, function (n, r) {\n            t = n, i = r;\n          });\n        return \"undefined\" != typeof Promise && o instanceof Promise ? o.then(function (n) {\n          return [n, t, i];\n        }) : [o, t, i];\n      }, \"boolean\" == typeof (null == r ? void 0 : r.useProxies) && this.setUseProxies(r.useProxies), \"boolean\" == typeof (null == r ? void 0 : r.autoFreeze) && this.setAutoFreeze(r.autoFreeze);\n    }\n    var i = e.prototype;\n    return i.createDraft = function (e) {\n      t(e) || n(8), r(e) && (e = R(e));\n      var i = w(this),\n        o = N(this, e, void 0);\n      return o[Q].C = !0, O(i), o;\n    }, i.finishDraft = function (r, t) {\n      var e = r && r[Q];\n      \"production\" !== process.env.NODE_ENV && (e && e.C || n(9), e.I && n(10));\n      var i = e.A;\n      return j(i, t), P(void 0, i);\n    }, i.setAutoFreeze = function (n) {\n      this.D = n;\n    }, i.setUseProxies = function (r) {\n      r && !B && n(20), this.O = r;\n    }, i.applyPatches = function (n, t) {\n      var e;\n      for (e = t.length - 1; e >= 0; e--) {\n        var i = t[e];\n        if (0 === i.path.length && \"replace\" === i.op) {\n          n = i.value;\n          break;\n        }\n      }\n      e > -1 && (t = t.slice(e + 1));\n      var o = b(\"Patches\").$;\n      return r(n) ? o(n, t) : this.produce(n, function (n) {\n        return o(n, t);\n      });\n    }, e;\n  }(),\n  an = new un(),\n  fn = an.produce,\n  cn = an.produceWithPatches.bind(an),\n  sn = an.setAutoFreeze.bind(an),\n  vn = an.setUseProxies.bind(an),\n  pn = an.applyPatches.bind(an),\n  ln = an.createDraft.bind(an),\n  dn = an.finishDraft.bind(an);\nexport default fn;\nexport { un as Immer, pn as applyPatches, K as castDraft, $ as castImmutable, ln as createDraft, R as current, J as enableAllPlugins, F as enableES5, C as enableMapSet, T as enablePatches, dn as finishDraft, d as freeze, L as immerable, r as isDraft, t as isDraftable, H as nothing, e as original, fn as produce, cn as produceWithPatches, sn as setAutoFreeze, vn as setUseProxies };", "map": {"version": 3, "names": ["n", "r", "arguments", "length", "t", "Array", "e", "process", "env", "NODE_ENV", "i", "Y", "o", "apply", "Error", "map", "join", "Q", "Object", "getPrototypeOf", "hasOwnProperty", "call", "constructor", "Function", "toString", "Z", "isArray", "L", "s", "v", "keys", "nn", "for<PERSON>ach", "u", "has", "prototype", "a", "get", "f", "set", "add", "c", "X", "Map", "q", "Set", "p", "l", "slice", "rn", "writable", "configurable", "enumerable", "value", "create", "d", "y", "clear", "delete", "h", "freeze", "isFrozen", "b", "tn", "m", "_", "U", "j", "g", "O", "S", "w", "P", "M", "x", "H", "A", "I", "k", "N", "R", "concat", "D", "z", "getOwnPropertyDescriptor", "E", "F", "T", "C", "en", "on", "Proxy", "revocable", "revoke", "proxy", "J", "push", "K", "from", "JSON", "stringify", "defineProperty", "Math", "min", "entries", "$", "path", "op", "splice", "unshift", "extendStatics", "setPrototypeOf", "__proto__", "size", "values", "V", "next", "done", "G", "W", "Symbol", "B", "Reflect", "for", "iterator", "ownKeys", "getOwnPropertySymbols", "getOwnPropertyNames", "getOwnPropertyDescriptors", "Number", "isNaN", "deleteProperty", "parseInt", "un", "produce", "Promise", "then", "produceWithPatches", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "an", "fn", "cn", "bind", "sn", "vn", "pn", "ln", "dn", "Immer", "castDraft", "castImmutable", "current", "enableAllPlugins", "enableES5", "enableMapSet", "enablePatches", "immerable", "isDraft", "isDraftable", "nothing", "original"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/utils/errors.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/utils/common.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/utils/plugins.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/core/scope.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/core/finalize.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/core/proxy.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/core/immerClass.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/core/current.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/plugins/es5.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/plugins/patches.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/plugins/mapset.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/plugins/all.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/immer.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/immer/src/utils/env.ts"], "sourcesContent": ["const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n"], "mappings": "SA4CgBA,EAAIA,CAAA;EAAA,SAAAC,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAA+BC,CAAA,GAAAC,KAAA,CAAAJ,CAAA,OAAAA,CAAA,WAAAK,CAAA,MAAAA,CAAA,GAAAL,CAAA,EAAAK,CAAA,IAAAF,CAAA,CAAAE,CAAA,QAAAJ,SAAA,CAAAI,CAAA;EAAA,qBAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA,EACrC;IAAA,IACNC,CAAA,GAAIC,CAAA,CAAOX,CAAA;MACXY,CAAA,GAAOF,CAAA,GAEG,qBAANA,CAAA,GACPA,CAAA,CAAEG,KAAA,CAAM,MAAMT,CAAA,IACdM,CAAA,GAHA,uBAAuBV,CAAA;IAAA,MAIhBc,KAAA,cAAiBF,CAAA;EAAA;EAAA,MAElBE,KAAA,iCACqBd,CAAA,IAC7BI,CAAA,CAAKD,MAAA,GAAS,MAAMC,CAAA,CAAKW,GAAA,CAAI,UAAAf,CAAA;IAAA,aAASA,CAAA;EAAA,GAAMgB,IAAA,CAAK,OAAO;AAAA;AAAA,SCvC3Cf,EAAQD,CAAA;EAAA,SACdA,CAAA,MAAWA,CAAA,CAAMiB,CAAA;AAAA;AAAA,SAKXb,EAAYJ,CAAA;EAAA,IAAAC,CAAA;EAAA,SACtBD,CAAA,eAawBA,CAAA;IAAA,KACxBA,CAAA,IAA0B,mBAAVA,CAAA,EAAoB,QAAO;IAAA,IAC1CC,CAAA,GAAQiB,MAAA,CAAOC,cAAA,CAAenB,CAAA;IAAA,IACtB,SAAVC,CAAA,UACI;IAAA,IAEFG,CAAA,GACLc,MAAA,CAAOE,cAAA,CAAeC,IAAA,CAAKpB,CAAA,EAAO,kBAAkBA,CAAA,CAAMqB,WAAA;IAAA,OAEvDlB,CAAA,KAASc,MAAA,IAGG,qBAARd,CAAA,IACPmB,QAAA,CAASC,QAAA,CAASH,IAAA,CAAKjB,CAAA,MAAUqB,CAAA;EAAA,CAxBjC,CAAczB,CAAA,KACdK,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA,OACZA,CAAA,CAAM2B,CAAA,kBAAA1B,CAAA,GACND,CAAA,CAAMsB,WAAA,gBAAArB,CAAA,YAANA,CAAA,CAAoB0B,CAAA,MACtBC,CAAA,CAAM5B,CAAA,KACN6B,CAAA,CAAM7B,CAAA;AAAA;AAAA,SA0BQM,EAASF,CAAA;EAAA,OACnBH,CAAA,CAAQG,CAAA,KAAQJ,CAAA,CAAI,IAAII,CAAA,GACtBA,CAAA,CAAMa,CAAA,EAAab,CAAA;AAAA;AA8B3B,SAAgBM,EAAKV,CAAA,EAAUC,CAAA,EAAWG,CAAA;EAAA,WAAAA,CAAA,KAAAA,CAAA,IAAiB,UACtDQ,CAAA,CAAYZ,CAAA,KACbI,CAAA,GAAiBc,MAAA,CAAOY,IAAA,GAAOC,EAAA,EAAS/B,CAAA,EAAKgC,OAAA,CAAQ,UAAA1B,CAAA;IACjDF,CAAA,IAAiC,mBAARE,CAAA,IAAkBL,CAAA,CAAKK,CAAA,EAAKN,CAAA,CAAIM,CAAA,GAAMN,CAAA;EAAA,KAGrEA,CAAA,CAAIgC,OAAA,CAAQ,UAAC5B,CAAA,EAAYE,CAAA;IAAA,OAAeL,CAAA,CAAKK,CAAA,EAAOF,CAAA,EAAOJ,CAAA;EAAA;AAAA;AAAA,SAK7CY,EAAYZ,CAAA;EAAA,IAErBC,CAAA,GAAgCD,CAAA,CAAMiB,CAAA;EAAA,OACrChB,CAAA,GACJA,CAAA,CAAMS,CAAA,GAAQ,IACbT,CAAA,CAAMS,CAAA,GAAQ,IACbT,CAAA,CAAMS,CAAA,GACRL,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA,QAEd4B,CAAA,CAAM5B,CAAA,QAEN6B,CAAA,CAAM7B,CAAA;AAAA;AAAA,SAMMiC,EAAIjC,CAAA,EAAYC,CAAA;EAAA,aACxBW,CAAA,CAAYZ,CAAA,IAChBA,CAAA,CAAMkC,GAAA,CAAIjC,CAAA,IACViB,MAAA,CAAOiB,SAAA,CAAUf,cAAA,CAAeC,IAAA,CAAKrB,CAAA,EAAOC,CAAA;AAAA;AAAA,SAIhCmC,EAAIpC,CAAA,EAA2BC,CAAA;EAAA,aAEvCW,CAAA,CAAYZ,CAAA,IAA0BA,CAAA,CAAMqC,GAAA,CAAIpC,CAAA,IAAQD,CAAA,CAAMC,CAAA;AAAA;AAItE,SAAgBqC,EAAItC,CAAA,EAAYC,CAAA,EAA6BG,CAAA;EAAA,IACtDE,CAAA,GAAIM,CAAA,CAAYZ,CAAA;EAAA,MAClBM,CAAA,GAAoBN,CAAA,CAAMuC,GAAA,CAAItC,CAAA,EAAgBG,CAAA,UACzCE,CAAA,GACRN,CAAA,CAAMwC,GAAA,CAAIpC,CAAA,IACJJ,CAAA,CAAMC,CAAA,IAAkBG,CAAA;AAAA;AAAA,SAIhBqC,EAAGzC,CAAA,EAAQC,CAAA;EAAA,OAEtBD,CAAA,KAAMC,CAAA,GACI,MAAND,CAAA,IAAW,IAAIA,CAAA,IAAM,IAAIC,CAAA,GAEzBD,CAAA,IAAMA,CAAA,IAAKC,CAAA,IAAMA,CAAA;AAAA;AAAA,SAKV2B,EAAM5B,CAAA;EAAA,OACd0C,CAAA,IAAU1C,CAAA,YAAkB2C,GAAA;AAAA;AAAA,SAIpBd,EAAM7B,CAAA;EAAA,OACd4C,CAAA,IAAU5C,CAAA,YAAkB6C,GAAA;AAAA;AAAA,SAGpBC,EAAO9C,CAAA;EAAA,OACfA,CAAA,CAAMY,CAAA,IAASZ,CAAA,CAAMI,CAAA;AAAA;AAAA,SAIb2C,EAAY/C,CAAA;EAAA,IACvBK,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA,GAAO,OAAOK,KAAA,CAAM8B,SAAA,CAAUa,KAAA,CAAM3B,IAAA,CAAKrB,CAAA;EAAA,IACrDC,CAAA,GAAcgD,EAAA,CAA0BjD,CAAA;EAAA,OACvCC,CAAA,CAAYgB,CAAA;EAAA,SACfb,CAAA,GAAO2B,EAAA,CAAQ9B,CAAA,GACVK,CAAA,GAAI,GAAGA,CAAA,GAAIF,CAAA,CAAKD,MAAA,EAAQG,CAAA,IAAK;IAAA,IAC/BI,CAAA,GAAWN,CAAA,CAAKE,CAAA;MAChBM,CAAA,GAAOX,CAAA,CAAYS,CAAA;IAAA,CACH,MAAlBE,CAAA,CAAKsC,QAAA,KACRtC,CAAA,CAAKsC,QAAA,IAAW,GAChBtC,CAAA,CAAKuC,YAAA,IAAe,KAKjBvC,CAAA,CAAKyB,GAAA,IAAOzB,CAAA,CAAK2B,GAAA,MACpBtC,CAAA,CAAYS,CAAA,IAAO;MAClByC,YAAA,GAAc;MACdD,QAAA,GAAU;MACVE,UAAA,EAAYxC,CAAA,CAAKwC,UAAA;MACjBC,KAAA,EAAOrD,CAAA,CAAKU,CAAA;IAAA;EAAA;EAAA,OAGRQ,MAAA,CAAOoC,MAAA,CAAOpC,MAAA,CAAOC,cAAA,CAAenB,CAAA,GAAOC,CAAA;AAAA;AAAA,SAWnCsD,EAAUvD,CAAA,EAAUM,CAAA;EAAA,kBAAAA,CAAA,KAAAA,CAAA,IAAgB,IAC/CkD,CAAA,CAASxD,CAAA,KAAQC,CAAA,CAAQD,CAAA,MAASI,CAAA,CAAYJ,CAAA,MAC9CY,CAAA,CAAYZ,CAAA,IAAO,MACtBA,CAAA,CAAIuC,GAAA,GAAMvC,CAAA,CAAIwC,GAAA,GAAMxC,CAAA,CAAIyD,KAAA,GAAQzD,CAAA,CAAI0D,MAAA,GAASC,CAAA,GAE9CzC,MAAA,CAAO0C,MAAA,CAAO5D,CAAA,GACVM,CAAA,IAAMI,CAAA,CAAKV,CAAA,EAAK,UAACA,CAAA,EAAKC,CAAA;IAAA,OAAUsD,CAAA,CAAOtD,CAAA,GAAO;EAAA,IAAO,KALMD,CAAA;AAAA;AAShE,SAAS2D,EAAA;EACR3D,CAAA,CAAI;AAAA;AAAA,SAGWwD,EAASxD,CAAA;EAAA,OACb,QAAPA,CAAA,IAA8B,mBAARA,CAAA,IAEnBkB,MAAA,CAAO2C,QAAA,CAAS7D,CAAA;AAAA;AAAA,SCxKR8D,EACf7D,CAAA;EAAA,IAEMG,CAAA,GAAS2D,EAAA,CAAQ9D,CAAA;EAAA,OAClBG,CAAA,IACJJ,CAAA,CAAI,IAAIC,CAAA,GAGFG,CAAA;AAAA;AAAA,SAGQ4D,EACfhE,CAAA,EACAC,CAAA;EAEK8D,EAAA,CAAQ/D,CAAA,MAAY+D,EAAA,CAAQ/D,CAAA,IAAaC,CAAA;AAAA;AClC/C,SAAgBgE,EAAA;EAAA,wBACX1D,OAAA,CAAAC,GAAA,CAAAC,QAAA,IAAYyD,CAAA,IAAclE,CAAA,CAAI,IAC3BkE,CAAA;AAAA;AAAA,SAkBQC,EACfnE,CAAA,EACAC,CAAA;EAEIA,CAAA,KACH6D,CAAA,CAAU,YACV9D,CAAA,CAAMiC,CAAA,GAAW,IACjBjC,CAAA,CAAM4B,CAAA,GAAkB,IACxB5B,CAAA,CAAM6B,CAAA,GAAiB5B,CAAA;AAAA;AAAA,SAITmE,EAAYpE,CAAA;EAC3BqE,CAAA,CAAWrE,CAAA,GACXA,CAAA,CAAM8C,CAAA,CAAQd,OAAA,CAAQsC,CAAA,GAEtBtE,CAAA,CAAM8C,CAAA,GAAU;AAAA;AAAA,SAGDuB,EAAWrE,CAAA;EACtBA,CAAA,KAAUkE,CAAA,KACbA,CAAA,GAAelE,CAAA,CAAM+C,CAAA;AAAA;AAAA,SAIPwB,EAAWvE,CAAA;EAAA,OAClBkE,CAAA,GArCD;IACNpB,CAAA,EAAS;IACTC,CAAA,EAmCkCmB,CAAA;IAlClCP,CAAA,EAkCgD3D,CAAA;IA/BhDgE,CAAA,GAAgB;IAChBC,CAAA,EAAoB;EAAA;AAAA;AAiCtB,SAASK,EAAYtE,CAAA;EAAA,IACdC,CAAA,GAAoBD,CAAA,CAAMiB,CAAA;EAAA,MAE/BhB,CAAA,CAAMS,CAAA,UACNT,CAAA,CAAMS,CAAA,GAENT,CAAA,CAAMkE,CAAA,KACFlE,CAAA,CAAMmE,CAAA,IAAW;AAAA;AAAA,SC9DPI,EAAcvE,CAAA,EAAaK,CAAA;EAC1CA,CAAA,CAAM2D,CAAA,GAAqB3D,CAAA,CAAMwC,CAAA,CAAQ3C,MAAA;EAAA,IACnCO,CAAA,GAAYJ,CAAA,CAAMwC,CAAA,CAAS;IAC3BlC,CAAA,QAAwB,MAAXX,CAAA,IAAwBA,CAAA,KAAWS,CAAA;EAAA,OACjDJ,CAAA,CAAMqD,CAAA,CAAOU,CAAA,IACjBP,CAAA,CAAU,OAAOQ,CAAA,CAAiBhE,CAAA,EAAOL,CAAA,EAAQW,CAAA,GAC9CA,CAAA,IACCF,CAAA,CAAUO,CAAA,EAAauD,CAAA,KAC1BJ,CAAA,CAAY9D,CAAA,GACZN,CAAA,CAAI,KAEDI,CAAA,CAAYH,CAAA,MAEfA,CAAA,GAASwE,CAAA,CAASnE,CAAA,EAAOL,CAAA,GACpBK,CAAA,CAAMyC,CAAA,IAAS2B,CAAA,CAAYpE,CAAA,EAAOL,CAAA,IAEpCK,CAAA,CAAM2B,CAAA,IACT6B,CAAA,CAAU,WAAWW,CAAA,CACpB/D,CAAA,CAAUO,CAAA,EAAab,CAAA,EACvBH,CAAA,EACAK,CAAA,CAAM2B,CAAA,EACN3B,CAAA,CAAMsB,CAAA,KAKR3B,CAAA,GAASwE,CAAA,CAASnE,CAAA,EAAOI,CAAA,EAAW,KAErC0D,CAAA,CAAY9D,CAAA,GACRA,CAAA,CAAM2B,CAAA,IACT3B,CAAA,CAAMuB,CAAA,CAAgBvB,CAAA,CAAM2B,CAAA,EAAU3B,CAAA,CAAMsB,CAAA,GAEtC3B,CAAA,KAAW0E,CAAA,GAAU1E,CAAA,QAAS;AAAA;AAGtC,SAASwE,EAASzE,CAAA,EAAuBC,CAAA,EAAYG,CAAA;EAAA,IAEhDoD,CAAA,CAASvD,CAAA,GAAQ,OAAOA,CAAA;EAAA,IAEtBK,CAAA,GAAoBL,CAAA,CAAMgB,CAAA;EAAA,KAE3BX,CAAA,SACJI,CAAA,CACCT,CAAA,EACA,UAACS,CAAA,EAAKE,CAAA;IAAA,OACLgE,CAAA,CAAiB5E,CAAA,EAAWM,CAAA,EAAOL,CAAA,EAAOS,CAAA,EAAKE,CAAA,EAAYR,CAAA;EAAA,IAC5D,IAEMH,CAAA;EAAA,IAGJK,CAAA,CAAMsE,CAAA,KAAW5E,CAAA,EAAW,OAAOC,CAAA;EAAA,KAElCK,CAAA,CAAMkE,CAAA,SACVE,CAAA,CAAY1E,CAAA,EAAWM,CAAA,CAAMF,CAAA,GAAO,IAC7BE,CAAA,CAAMF,CAAA;EAAA,KAGTE,CAAA,CAAMuE,CAAA,EAAY;IACtBvE,CAAA,CAAMuE,CAAA,IAAa,GACnBvE,CAAA,CAAMsE,CAAA,CAAOX,CAAA;IAAA,IACPrD,CAAA,SAELN,CAAA,CAAMI,CAAA,UAAiCJ,CAAA,CAAMI,CAAA,GACzCJ,CAAA,CAAMM,CAAA,GAAQmC,CAAA,CAAYzC,CAAA,CAAMwE,CAAA,IACjCxE,CAAA,CAAMM,CAAA;MAKNqB,CAAA,GAAarB,CAAA;MACbwB,CAAA,IAAQ;IAAA,MACR9B,CAAA,CAAMI,CAAA,KACTuB,CAAA,GAAa,IAAIY,GAAA,CAAIjC,CAAA,GACrBA,CAAA,CAAO6C,KAAA,IACPrB,CAAA,IAAQ,IAET1B,CAAA,CAAKuB,CAAA,EAAY,UAAChC,CAAA,EAAKS,CAAA;MAAA,OACtBkE,CAAA,CAAiB5E,CAAA,EAAWM,CAAA,EAAOM,CAAA,EAAQX,CAAA,EAAKS,CAAA,EAAYN,CAAA,EAAMgC,CAAA;IAAA,IAGnEsC,CAAA,CAAY1E,CAAA,EAAWY,CAAA,GAAQ,IAE3BR,CAAA,IAAQJ,CAAA,CAAUiC,CAAA,IACrB6B,CAAA,CAAU,WAAWiB,CAAA,CACpBzE,CAAA,EACAF,CAAA,EACAJ,CAAA,CAAUiC,CAAA,EACVjC,CAAA,CAAU4B,CAAA;EAAA;EAAA,OAINtB,CAAA,CAAMM,CAAA;AAAA;AAGd,SAASgE,EACRtE,CAAA,EACAI,CAAA,EACAE,CAAA,EACAwB,CAAA,EACAK,CAAA,EACAb,CAAA,EACAC,CAAA;EAAA,qBAEItB,OAAA,CAAAC,GAAA,CAAAC,QAAA,IAAWgC,CAAA,KAAe7B,CAAA,IAAcZ,CAAA,CAAI,IAC5CC,CAAA,CAAQwC,CAAA,GAAa;IAAA,IASlBK,CAAA,GAAM2B,CAAA,CAASnE,CAAA,EAAWmC,CAAA,EAP/Bb,CAAA,IACAlB,CAAA,UACAA,CAAA,CAAaA,CAAA,KACZuB,CAAA,CAAKvB,CAAA,CAA8CsE,CAAA,EAAY5C,CAAA,IAC7DR,CAAA,CAAUqD,MAAA,CAAO7C,CAAA,SACjB;IAAA,IAGJE,CAAA,CAAI1B,CAAA,EAAcwB,CAAA,EAAMU,CAAA,IAGpB7C,CAAA,CAAQ6C,CAAA,GAEL;IADNxC,CAAA,CAAU0D,CAAA,IAAiB;EAAA,OAElBnC,CAAA,IACVjB,CAAA,CAAa4B,GAAA,CAAIC,CAAA;EAAA,IAGdrC,CAAA,CAAYqC,CAAA,MAAgBe,CAAA,CAASf,CAAA,GAAa;IAAA,KAChDnC,CAAA,CAAUqD,CAAA,CAAOuB,CAAA,IAAe5E,CAAA,CAAU2D,CAAA,GAAqB;IAQpEQ,CAAA,CAASnE,CAAA,EAAWmC,CAAA,GAEf/B,CAAA,IAAgBA,CAAA,CAAYkE,CAAA,CAAO7B,CAAA,IACvC2B,CAAA,CAAYpE,CAAA,EAAWmC,CAAA;EAAA;AAAA;AAI1B,SAASiC,EAAY1E,CAAA,EAAmBC,CAAA,EAAYG,CAAA;EAAA,WAAAA,CAAA,KAAAA,CAAA,IAAO,KAErDJ,CAAA,CAAM+C,CAAA,IAAW/C,CAAA,CAAM2D,CAAA,CAAOuB,CAAA,IAAelF,CAAA,CAAMgE,CAAA,IACvDT,CAAA,CAAOtD,CAAA,EAAOG,CAAA;AAAA;ACqEhB,SAAS+E,EAAKnF,CAAA,EAAgBC,CAAA;EAAA,IACvBG,CAAA,GAAQJ,CAAA,CAAMiB,CAAA;EAAA,QACLb,CAAA,GAAQ0C,CAAA,CAAO1C,CAAA,IAASJ,CAAA,EACzBC,CAAA;AAAA;AAcf,SAAS4E,EACR7E,CAAA,EACAC,CAAA;EAAA,IAGMA,CAAA,IAAQD,CAAA,WACVI,CAAA,GAAQc,MAAA,CAAOC,cAAA,CAAenB,CAAA,GAC3BI,CAAA,GAAO;IAAA,IACPE,CAAA,GAAOY,MAAA,CAAOkE,wBAAA,CAAyBhF,CAAA,EAAOH,CAAA;IAAA,IAChDK,CAAA,EAAM,OAAOA,CAAA;IACjBF,CAAA,GAAQc,MAAA,CAAOC,cAAA,CAAef,CAAA;EAAA;AAAA;AAAA,SAKhB0E,EAAY9E,CAAA;EACtBA,CAAA,CAAMwE,CAAA,KACVxE,CAAA,CAAMwE,CAAA,IAAY,GACdxE,CAAA,CAAM+C,CAAA,IACT+B,CAAA,CAAY9E,CAAA,CAAM+C,CAAA;AAAA;AAAA,SAKLsC,EAAYrF,CAAA;EACtBA,CAAA,CAAMY,CAAA,KACVZ,CAAA,CAAMY,CAAA,GAAQmC,CAAA,CAAY/C,CAAA,CAAMI,CAAA;AAAA;ACtDlC,SAAgB2E,EACf/E,CAAA,EACAC,CAAA,EACAG,CAAA;EAAA,IAGME,CAAA,GAAiBsB,CAAA,CAAM3B,CAAA,IAC1B6D,CAAA,CAAU,UAAUwB,CAAA,CAAUrF,CAAA,EAAOG,CAAA,IACrCyB,CAAA,CAAM5B,CAAA,IACN6D,CAAA,CAAU,UAAUyB,CAAA,CAAUtF,CAAA,EAAOG,CAAA,IACrCJ,CAAA,CAAMqE,CAAA,aDvLTrE,CAAA,EACAC,CAAA;IAAA,IAEMG,CAAA,GAAUC,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA;MACxBM,CAAA,GAAoB;QACzBI,CAAA,EAAON,CAAA,OAAkC;QAEzCwE,CAAA,EAAQ3E,CAAA,GAASA,CAAA,CAAO2E,CAAA,GAASX,CAAA;QAEjCO,CAAA,GAAW;QAEXK,CAAA,GAAY;QAEZG,CAAA,EAAW;QAEXjC,CAAA,EAAS9C,CAAA;QAETG,CAAA,EAAOJ,CAAA;QAEP8E,CAAA,EAAQ;QAERlE,CAAA,EAAO;QAEPuD,CAAA,EAAS;QACTqB,CAAA,GAAW;MAAA;MASR9E,CAAA,GAAYJ,CAAA;MACZM,CAAA,GAA2C6E,EAAA;IAC3CrF,CAAA,KACHM,CAAA,GAAS,CAACJ,CAAA,GACVM,CAAA,GAAQ8E,EAAA;IAAA,IAAAzD,CAAA,GAGe0D,KAAA,CAAMC,SAAA,CAAUlF,CAAA,EAAQE,CAAA;MAAzCwB,CAAA,GAAAH,CAAA,CAAA4D,MAAA;MAAQvD,CAAA,GAAAL,CAAA,CAAA6D,KAAA;IAAA,OACfxF,CAAA,CAAMwE,CAAA,GAASxC,CAAA,EACfhC,CAAA,CAAM6D,CAAA,GAAU/B,CAAA,EACTE,CAAA;EAAA,CC6IJ,CAAiBrC,CAAA,EAAOG,CAAA,IACxB0D,CAAA,CAAU,OAAOiC,CAAA,CAAgB9F,CAAA,EAAOG,CAAA;EAAA,QAE7BA,CAAA,GAASA,CAAA,CAAOwE,CAAA,GAASX,CAAA,IACjCnB,CAAA,CAAQkD,IAAA,CAAK1F,CAAA,GACZA,CAAA;AAAA;AAAA,SC9NQ0E,EAAQ1E,CAAA;EAAA,OAClBL,CAAA,CAAQK,CAAA,KAAQN,CAAA,CAAI,IAAIM,CAAA,GAI9B,SAASN,EAAYC,CAAA;IAAA,KACfG,CAAA,CAAYH,CAAA,GAAQ,OAAOA,CAAA;IAAA,IAE5BK,CAAA;MADE2B,CAAA,GAAgChC,CAAA,CAAMgB,CAAA;MAEtCwB,CAAA,GAAW7B,CAAA,CAAYX,CAAA;IAAA,IACzBgC,CAAA,EAAO;MAAA,KAERA,CAAA,CAAMuC,CAAA,KACNvC,CAAA,CAAMvB,CAAA,GAAQ,MAAMoD,CAAA,CAAU,OAAOmC,CAAA,CAAYhE,CAAA,IAElD,OAAOA,CAAA,CAAM7B,CAAA;MAEd6B,CAAA,CAAM4C,CAAA,IAAa,GACnBvE,CAAA,GAAO4E,CAAA,CAAWjF,CAAA,EAAOwC,CAAA,GACzBR,CAAA,CAAM4C,CAAA,IAAa;IAAA,OAEnBvE,CAAA,GAAO4E,CAAA,CAAWjF,CAAA,EAAOwC,CAAA;IAAA,OAG1B/B,CAAA,CAAKJ,CAAA,EAAM,UAACL,CAAA,EAAKG,CAAA;MACZ6B,CAAA,IAASG,CAAA,CAAIH,CAAA,CAAM7B,CAAA,EAAOH,CAAA,MAASG,CAAA,IACvCkC,CAAA,CAAIhC,CAAA,EAAML,CAAA,EAAKD,CAAA,CAAYI,CAAA;IAAA,UAGrBqC,CAAA,GAA4B,IAAII,GAAA,CAAIvC,CAAA,IAAQA,CAAA;EAAA,CA3B5C,CAAYA,CAAA;AAAA;AA8BpB,SAAS4E,EAAWlF,CAAA,EAAYC,CAAA;EAAA,QAEvBA,CAAA;IAAA;MAAA,OAEC,IAAI0C,GAAA,CAAI3C,CAAA;IAAA;MAAA,OAGRK,KAAA,CAAM6F,IAAA,CAAKlG,CAAA;EAAA;EAAA,OAEb+C,CAAA,CAAY/C,CAAA;AAAA;AAAA,SClCJsF,EAAA;EAAA,SA8ENlF,EACRJ,CAAA,EACAC,CAAA;IAAA,IAEIG,CAAA,GAAOwB,CAAA,CAAY5B,CAAA;IAAA,OACnBI,CAAA,GACHA,CAAA,CAAKgD,UAAA,GAAanD,CAAA,GAElB2B,CAAA,CAAY5B,CAAA,IAAQI,CAAA,GAAO;MAC1B+C,YAAA,GAAc;MACdC,UAAA,EAAAnD,CAAA;MACAoC,GAAA,WAAAA,CAAA;QAAA,IACOpC,CAAA,GAAQ,KAAKgB,CAAA;QAAA,wBAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,IACN6B,CAAA,CAAgBrC,CAAA,GAEtBwF,EAAA,CAAYpD,GAAA,CAAIpC,CAAA,EAAOD,CAAA;MAAA;MAE/BuC,GAAA,WAAAA,CAAetC,CAAA;QAAA,IACRG,CAAA,GAAQ,KAAKa,CAAA;QAAA,iBAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,IACN6B,CAAA,CAAgBlC,CAAA,GAE7BqF,EAAA,CAAYlD,GAAA,CAAInC,CAAA,EAAOJ,CAAA,EAAMC,CAAA;MAAA;IAAA,GAIzBG,CAAA;EAAA;EAAA,SAICE,EAAiBN,CAAA;IAAA,KAKpB,IAAIC,CAAA,GAAID,CAAA,CAAOG,MAAA,GAAS,GAAGF,CAAA,IAAK,GAAGA,CAAA,IAAK;MAAA,IACtCG,CAAA,GAAkBJ,CAAA,CAAOC,CAAA,EAAGgB,CAAA;MAAA,KAC7Bb,CAAA,CAAMoE,CAAA,UACFpE,CAAA,CAAMM,CAAA;QAAA;UAER0B,CAAA,CAAgBhC,CAAA,KAAQ0E,CAAA,CAAY1E,CAAA;UAAA;QAAA;UAGpCQ,CAAA,CAAiBR,CAAA,KAAQ0E,CAAA,CAAY1E,CAAA;MAAA;IAAA;EAAA;EAAA,SA6DrCQ,EAAiBZ,CAAA;IAAA,SAClBC,CAAA,GAAiBD,CAAA,CAAjBI,CAAA,EAAOA,CAAA,GAAUJ,CAAA,CAAV8E,CAAA,EAIRxE,CAAA,GAAOyB,EAAA,CAAQ3B,CAAA,GACZM,CAAA,GAAIJ,CAAA,CAAKH,MAAA,GAAS,GAAGO,CAAA,IAAK,GAAGA,CAAA,IAAK;MAAA,IACpCE,CAAA,GAAWN,CAAA,CAAKI,CAAA;MAAA,IAClBE,CAAA,KAAQK,CAAA;QAAA,IACNmB,CAAA,GAAYnC,CAAA,CAAMW,CAAA;QAAA,SAEN,MAAdwB,CAAA,KAA4BH,CAAA,CAAIhC,CAAA,EAAOW,CAAA,WACnC;QAAA,IAKD0B,CAAA,GAAQlC,CAAA,CAAOQ,CAAA;UACfgB,CAAA,GAAoBU,CAAA,IAASA,CAAA,CAAMrB,CAAA;QAAA,IACrCW,CAAA,GAAQA,CAAA,CAAMxB,CAAA,KAAUgC,CAAA,IAAaK,CAAA,CAAGH,CAAA,EAAOF,CAAA,WAC3C;MAAA;IAAA;IAAA,IAOJP,CAAA,KAAgB5B,CAAA,CAAMgB,CAAA;IAAA,OACrBX,CAAA,CAAKH,MAAA,KAAW4B,EAAA,CAAQ9B,CAAA,EAAOE,MAAA,IAAU0B,CAAA,GAAc,IAAI;EAAA;EAAA,SAG1DO,EAAgBpC,CAAA;IAAA,IACjBC,CAAA,GAAUD,CAAA,CAAV8E,CAAA;IAAA,IACH7E,CAAA,CAAOE,MAAA,KAAWH,CAAA,CAAMI,CAAA,CAAMD,MAAA,EAAQ,QAAO;IAAA,IAS3CC,CAAA,GAAac,MAAA,CAAOkE,wBAAA,CACzBnF,CAAA,EACAA,CAAA,CAAOE,MAAA,GAAS;IAAA,IAGbC,CAAA,KAAeA,CAAA,CAAWiC,GAAA,EAAK,QAAO;IAAA,KAErC,IAAI/B,CAAA,GAAI,GAAGA,CAAA,GAAIL,CAAA,CAAOE,MAAA,EAAQG,CAAA,SAC7BL,CAAA,CAAOmB,cAAA,CAAed,CAAA,GAAI,QAAO;IAAA,QAGhC;EAAA;EAAA,SASCgC,EAAgBrC,CAAA;IACpBA,CAAA,CAAMmE,CAAA,IAAUpE,CAAA,CAAI,GAAGmG,IAAA,CAAKC,SAAA,CAAUtD,CAAA,CAAO7C,CAAA;EAAA;EAAA,IAxK5C2B,CAAA,GAAoD;EA2K1DoC,CAAA,CAAW,OAAO;IACjB+B,CAAA,WAAAA,CA5MA/F,CAAA,EACAC,CAAA;MAAA,IAEMK,CAAA,GAAUD,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA;QACxBU,CAAA,aA1BiBV,CAAA,EAAkBC,CAAA;UAAA,IACrCD,CAAA,EAAS;YAAA,SACNM,CAAA,GAAYD,KAAA,CAAMJ,CAAA,CAAKE,MAAA,GACpBO,CAAA,GAAI,GAAGA,CAAA,GAAIT,CAAA,CAAKE,MAAA,EAAQO,CAAA,IAChCQ,MAAA,CAAOmF,cAAA,CAAe/F,CAAA,EAAO,KAAKI,CAAA,EAAGN,CAAA,CAAcM,CAAA,GAAG;YAAA,OAChDJ,CAAA;UAAA;UAAA,IAEDM,CAAA,GAAcqC,EAAA,CAA0BhD,CAAA;UAAA,OACvCW,CAAA,CAAYK,CAAA;UAAA,SACbgB,CAAA,GAAOF,EAAA,CAAQnB,CAAA,GACZwB,CAAA,GAAI,GAAGA,CAAA,GAAIH,CAAA,CAAK9B,MAAA,EAAQiC,CAAA,IAAK;YAAA,IAC/BE,CAAA,GAAWL,CAAA,CAAKG,CAAA;YACtBxB,CAAA,CAAY0B,CAAA,IAAOlC,CAAA,CAClBkC,CAAA,EACAtC,CAAA,MAAaY,CAAA,CAAY0B,CAAA,EAAKc,UAAA;UAAA;UAAA,OAGzBlC,MAAA,CAAOoC,MAAA,CAAOpC,MAAA,CAAOC,cAAA,CAAelB,CAAA,GAAOW,CAAA;QAAA,CASrC,CAAeN,CAAA,EAASN,CAAA;QAEhCY,CAAA,GAAwC;UAC7CF,CAAA,EAAOJ,CAAA,OAAgC;UACvCsE,CAAA,EAAQ3E,CAAA,GAASA,CAAA,CAAO2E,CAAA,GAASX,CAAA;UACjCO,CAAA,GAAW;UACXK,CAAA,GAAY;UACZG,CAAA,EAAW;UACXjC,CAAA,EAAS9C,CAAA;UAETG,CAAA,EAAOJ,CAAA;UAEP8E,CAAA,EAAQpE,CAAA;UACRE,CAAA,EAAO;UACPwD,CAAA,GAAU;UACVoB,CAAA,GAAW;QAAA;MAAA,OAGZtE,MAAA,CAAOmF,cAAA,CAAe3F,CAAA,EAAOO,CAAA,EAAa;QACzCoC,KAAA,EAAOzC,CAAA;QAEPsC,QAAA,GAAU;MAAA,IAEJxC,CAAA;IAAA;IAkLP4D,CAAA,WAAAA,CAvPAtE,CAAA,EACAI,CAAA,EACAQ,CAAA;MAEKA,CAAA,GASJX,CAAA,CAAQG,CAAA,KACPA,CAAA,CAAOa,CAAA,EAA0B2D,CAAA,KAAW5E,CAAA,IAE7CM,CAAA,CAAiBN,CAAA,CAAM8C,CAAA,KAXnB9C,CAAA,CAAMiC,CAAA,aAwHHjC,EAAuBC,CAAA;QAAA,IAC1BA,CAAA,IAA4B,mBAAXA,CAAA;UAAA,IAChBG,CAAA,GAA8BH,CAAA,CAAOgB,CAAA;UAAA,IACtCb,CAAA;YAAA,IACEE,CAAA,GAAmCF,CAAA,CAAnCA,CAAA;cAAOQ,CAAA,GAA4BR,CAAA,CAA5B0E,CAAA;cAAQxC,CAAA,GAAoBlC,CAAA,CAApB4E,CAAA;cAAWvC,CAAA,GAASrC,CAAA,CAATM,CAAA;YAAA,UAC7B+B,CAAA,EAKH/B,CAAA,CAAKE,CAAA,EAAQ,UAAAX,CAAA;cACPA,CAAA,KAAgBgB,CAAA,UAEO,MAAvBX,CAAA,CAAcL,CAAA,KAAuBgC,CAAA,CAAI3B,CAAA,EAAOL,CAAA,IAGzCqC,CAAA,CAAUrC,CAAA,KAErBD,CAAA,CAAuBY,CAAA,CAAOX,CAAA,MAJ9BqC,CAAA,CAAUrC,CAAA,KAAO,GACjB6E,CAAA,CAAY1E,CAAA;YAAA,IAOdM,CAAA,CAAKJ,CAAA,EAAO,UAAAN,CAAA;cAAA,KAES,MAAhBY,CAAA,CAAOZ,CAAA,KAAuBiC,CAAA,CAAIrB,CAAA,EAAQZ,CAAA,MAC7CsC,CAAA,CAAUtC,CAAA,KAAO,GACjB8E,CAAA,CAAY1E,CAAA;YAAA,QAGR,UAAIqC,CAAA,EAA8B;cAAA,IACpCL,CAAA,CAAgBhC,CAAA,MACnB0E,CAAA,CAAY1E,CAAA,GACZkC,CAAA,CAAUnC,MAAA,IAAS,IAGhBS,CAAA,CAAOT,MAAA,GAASG,CAAA,CAAMH,MAAA,OACpB,IAAIyB,CAAA,GAAIhB,CAAA,CAAOT,MAAA,EAAQyB,CAAA,GAAItB,CAAA,CAAMH,MAAA,EAAQyB,CAAA,IAAKU,CAAA,CAAUV,CAAA,KAAK,YAE7D,IAAIC,CAAA,GAAIvB,CAAA,CAAMH,MAAA,EAAQ0B,CAAA,GAAIjB,CAAA,CAAOT,MAAA,EAAQ0B,CAAA,IAAKS,CAAA,CAAUT,CAAA,KAAK;cAAA,SAI7DiB,CAAA,GAAMwD,IAAA,CAAKC,GAAA,CAAI3F,CAAA,CAAOT,MAAA,EAAQG,CAAA,CAAMH,MAAA,GAEjC4C,CAAA,GAAI,GAAGA,CAAA,GAAID,CAAA,EAAKC,CAAA,IAEnBnC,CAAA,CAAOQ,cAAA,CAAe2B,CAAA,MAC1BT,CAAA,CAAUS,CAAA,KAAK,SAEK,MAAjBT,CAAA,CAAUS,CAAA,KAAkB/C,CAAA,CAAuBY,CAAA,CAAOmC,CAAA;YAAA;UAAA;QAAA;MAAA,CAxK9D,CAAuB/C,CAAA,CAAM8C,CAAA,CAAS,KAGvCxC,CAAA,CAAiBN,CAAA,CAAM8C,CAAA;IAAA;IA+OxBmD,CAAA,WAAAA,CAboBjG,CAAA;MAAA,aACbA,CAAA,CAAMU,CAAA,GACVE,CAAA,CAAiBZ,CAAA,IACjBoC,CAAA,CAAgBpC,CAAA;IAAA;EAAA;AAAA;AAAA,SC9OLuF,EAAA;EAAA,SA6PNjF,EAAoBN,CAAA;IAAA,KACvBI,CAAA,CAAYJ,CAAA,GAAM,OAAOA,CAAA;IAAA,IAC1BK,KAAA,CAAMqB,OAAA,CAAQ1B,CAAA,GAAM,OAAOA,CAAA,CAAIe,GAAA,CAAIT,CAAA;IAAA,IACnCsB,CAAA,CAAM5B,CAAA,GACT,OAAO,IAAI2C,GAAA,CACVtC,KAAA,CAAM6F,IAAA,CAAKlG,CAAA,CAAIwG,OAAA,IAAWzF,GAAA,CAAI,UAAAf,CAAA;MAAA,OAAY,CAAAA,CAAA,KAAIM,CAAA,CAAAN,CAAA;IAAA;IAAA,IAE5C6B,CAAA,CAAM7B,CAAA,GAAM,OAAO,IAAI6C,GAAA,CAAIxC,KAAA,CAAM6F,IAAA,CAAKlG,CAAA,EAAKe,GAAA,CAAIT,CAAA;IAAA,IAC7CL,CAAA,GAASiB,MAAA,CAAOoC,MAAA,CAAOpC,MAAA,CAAOC,cAAA,CAAenB,CAAA;IAAA,KAC9C,IAAMU,CAAA,IAAOV,CAAA,EAAKC,CAAA,CAAOS,CAAA,IAAOJ,CAAA,CAAoBN,CAAA,CAAIU,CAAA;IAAA,OACzDuB,CAAA,CAAIjC,CAAA,EAAK2B,CAAA,MAAY1B,CAAA,CAAO0B,CAAA,IAAa3B,CAAA,CAAI2B,CAAA,IAC1C1B,CAAA;EAAA;EAAA,SAGCqC,EAA2BtC,CAAA;IAAA,OAC/BC,CAAA,CAAQD,CAAA,IACJM,CAAA,CAAoBN,CAAA,IACdA,CAAA;EAAA;EAAA,IA5QTyC,CAAA,GAAM;EA+QZuB,CAAA,CAAW,WAAW;IACrByC,CAAA,WAAAA,CAlGyBxG,CAAA,EAAUG,CAAA;MAAA,OACnCA,CAAA,CAAQ4B,OAAA,CAAQ,UAAA5B,CAAA;QAAA,SACRM,CAAA,GAAYN,CAAA,CAAZsG,IAAA,EAAMzE,CAAA,GAAM7B,CAAA,CAANuG,EAAA,EAETrE,CAAA,GAAYrC,CAAA,EACP2B,CAAA,GAAI,GAAGA,CAAA,GAAIlB,CAAA,CAAKP,MAAA,GAAS,GAAGyB,CAAA,IAAK;UAAA,IACnCC,CAAA,GAAajB,CAAA,CAAY0B,CAAA;YAC3BQ,CAAA,GAAIpC,CAAA,CAAKkB,CAAA;UACI,mBAANkB,CAAA,IAA+B,mBAANA,CAAA,KACnCA,CAAA,GAAI,KAAKA,CAAA,SAKRjB,CAAA,UAAkCA,CAAA,IAC5B,gBAANiB,CAAA,IAA2B,kBAANA,CAAA,IAEtB9C,CAAA,CAAI,KACe,qBAATsC,CAAA,IAA6B,gBAANQ,CAAA,IAAmB9C,CAAA,CAAI,KAErC,oBADpBsC,CAAA,GAAOF,CAAA,CAAIE,CAAA,EAAMQ,CAAA,MACa9C,CAAA,CAAI,IAAIU,CAAA,CAAKM,IAAA,CAAK;QAAA;QAAA,IAG3C+B,CAAA,GAAOnC,CAAA,CAAY0B,CAAA;UACnBiB,CAAA,GAAQjD,CAAA,CAAoBF,CAAA,CAAMiD,KAAA;UAClCM,CAAA,GAAMjD,CAAA,CAAKA,CAAA,CAAKP,MAAA,GAAS;QAAA,QACvB8B,CAAA;UAAA,KAzMM;YAAA,QA2MJc,CAAA;cAAA;gBAAA,OAECT,CAAA,CAAKC,GAAA,CAAIoB,CAAA,EAAKJ,CAAA;cAAA;gBAGrBvD,CAAA,CAAI;cAAA;gBAAA,OAMIsC,CAAA,CAAKqB,CAAA,IAAOJ,CAAA;YAAA;UAAA,KAElBd,CAAA;YAAA,QACIM,CAAA;cAAA;gBAAA,OAES,QAARY,CAAA,GACJrB,CAAA,CAAK0D,IAAA,CAAKzC,CAAA,IACVjB,CAAA,CAAKsE,MAAA,CAAOjD,CAAA,EAAY,GAAGJ,CAAA;cAAA;gBAAA,OAEvBjB,CAAA,CAAKC,GAAA,CAAIoB,CAAA,EAAKJ,CAAA;cAAA;gBAAA,OAEdjB,CAAA,CAAKE,GAAA,CAAIe,CAAA;cAAA;gBAAA,OAERjB,CAAA,CAAKqB,CAAA,IAAOJ,CAAA;YAAA;UAAA,KAjOX;YAAA,QAoOHR,CAAA;cAAA;gBAAA,OAECT,CAAA,CAAKsE,MAAA,CAAOjD,CAAA,EAAY;cAAA;gBAAA,OAExBrB,CAAA,CAAKoB,MAAA,CAAOC,CAAA;cAAA;gBAAA,OAEZrB,CAAA,CAAKoB,MAAA,CAAOtD,CAAA,CAAMiD,KAAA;cAAA;gBAAA,cAEXf,CAAA,CAAKqB,CAAA;YAAA;UAAA;YAGrB3D,CAAA,CAAI,IAAIiC,CAAA;QAAA;MAAA,IAIJhC,CAAA;IAAA;IA6BP8E,CAAA,WAAAA,CA7QA/E,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;MAAA,QAEQN,CAAA,CAAMU,CAAA;QAAA;QAAA;QAAA;UAAA,iBAgFdV,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;YAAA,IAEOM,CAAA,GAAgBZ,CAAA,CAAhBI,CAAA;cAAOwB,CAAA,GAAS5B,CAAA,CAATY,CAAA;YACdF,CAAA,CAAKV,CAAA,CAAMgF,CAAA,EAAY,UAAChF,CAAA,EAAKU,CAAA;cAAA,IACtBmB,CAAA,GAAYO,CAAA,CAAIxB,CAAA,EAAOZ,CAAA;gBACvB8C,CAAA,GAAQV,CAAA,CAAIR,CAAA,EAAQ5B,CAAA;gBACpB+C,CAAA,GAAMrC,CAAA,GAAyBuB,CAAA,CAAIrB,CAAA,EAAOZ,CAAA,IAnGlC,YAmGmDyC,CAAA,GAjGpD;cAAA,IAkGTZ,CAAA,KAAciB,CAAA,IApGJ,cAoGaC,CAAA;gBAAA,IACrBQ,CAAA,GAAOtD,CAAA,CAASgF,MAAA,CAAOjF,CAAA;gBAC7BI,CAAA,CAAQ4F,IAAA,CApGK,aAoGAjD,CAAA,GAAgB;kBAAC4D,EAAA,EAAA5D,CAAA;kBAAI2D,IAAA,EAAAnD;gBAAA,IAAQ;kBAACoD,EAAA,EAAA5D,CAAA;kBAAI2D,IAAA,EAAAnD,CAAA;kBAAMF,KAAA,EAAAP;gBAAA,IACrDxC,CAAA,CAAe0F,IAAA,CACdjD,CAAA,KAAON,CAAA,GACJ;kBAACkE,EAAA,EAvGQ;kBAuGID,IAAA,EAAAnD;gBAAA,IAvGJ,aAwGTR,CAAA,GACA;kBAAC4D,EAAA,EAAIlE,CAAA;kBAAKiE,IAAA,EAAAnD,CAAA;kBAAMF,KAAA,EAAOf,CAAA,CAAwBT,CAAA;gBAAA,IAC/C;kBAAC8E,EAAA,EA5GS;kBA4GID,IAAA,EAAAnD,CAAA;kBAAMF,KAAA,EAAOf,CAAA,CAAwBT,CAAA;gBAAA;cAAA;YAAA;UAAA,CA9F/C,CACN7B,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;QAAA;QAAA;UAAA,iBAgBHN,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;YAAA,IAEKI,CAAA,GAAoBV,CAAA,CAApBI,CAAA;cAAOQ,CAAA,GAAaZ,CAAA,CAAbgF,CAAA;cACR/C,CAAA,GAAQjC,CAAA,CAAMY,CAAA;YAAA,IAGdqB,CAAA,CAAM9B,MAAA,GAASO,CAAA,CAAMP,MAAA,EAAQ;cAAA,IAAAiC,CAAA,GAEd,CAACH,CAAA,EAAOvB,CAAA;cAAxBA,CAAA,GAAA0B,CAAA,KAAOH,CAAA,GAAAG,CAAA;cAAA,IAAAR,CAAA,GACoB,CAACtB,CAAA,EAAgBF,CAAA;cAA5CA,CAAA,GAAAwB,CAAA,KAAStB,CAAA,GAAAsB,CAAA;YAAA;YAAA,KAIP,IAAIC,CAAA,GAAI,GAAGA,CAAA,GAAInB,CAAA,CAAMP,MAAA,EAAQ0B,CAAA,QAC7BjB,CAAA,CAAUiB,CAAA,KAAMI,CAAA,CAAMJ,CAAA,MAAOnB,CAAA,CAAMmB,CAAA,GAAI;cAAA,IACpCiB,CAAA,GAAO7C,CAAA,CAASgF,MAAA,CAAO,CAACpD,CAAA;cAC9BzB,CAAA,CAAQ4F,IAAA,CAAK;gBACZW,EAAA,EAtDY;gBAuDZD,IAAA,EAAA5D,CAAA;gBAGAO,KAAA,EAAOf,CAAA,CAAwBL,CAAA,CAAMJ,CAAA;cAAA,IAEtCvB,CAAA,CAAe0F,IAAA,CAAK;gBACnBW,EAAA,EA7DY;gBA8DZD,IAAA,EAAA5D,CAAA;gBACAO,KAAA,EAAOf,CAAA,CAAwB5B,CAAA,CAAMmB,CAAA;cAAA;YAAA;YAAA,KAMnC,IAAIkB,CAAA,GAAIrC,CAAA,CAAMP,MAAA,EAAQ4C,CAAA,GAAId,CAAA,CAAM9B,MAAA,EAAQ4C,CAAA,IAAK;cAAA,IAC3CQ,CAAA,GAAOtD,CAAA,CAASgF,MAAA,CAAO,CAAClC,CAAA;cAC9B3C,CAAA,CAAQ4F,IAAA,CAAK;gBACZW,EAAA,EAAIlE,CAAA;gBACJiE,IAAA,EAAAnD,CAAA;gBAGAF,KAAA,EAAOf,CAAA,CAAwBL,CAAA,CAAMc,CAAA;cAAA;YAAA;YAGnCrC,CAAA,CAAMP,MAAA,GAAS8B,CAAA,CAAM9B,MAAA,IACxBG,CAAA,CAAe0F,IAAA,CAAK;cACnBW,EAAA,EAjFa;cAkFbD,IAAA,EAAMzG,CAAA,CAASgF,MAAA,CAAO,CAAC;cACvB5B,KAAA,EAAO3C,CAAA,CAAMP;YAAA;UAAA,CA7DN,CAAqBH,CAAA,EAAOC,CAAA,EAAUG,CAAA,EAASE,CAAA;QAAA;UAAA,iBA4FxDN,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;YAAA,IAEKI,CAAA,GAAgBV,CAAA,CAAhBI,CAAA;cAAOQ,CAAA,GAASZ,CAAA,CAATY,CAAA;cAERqB,CAAA,GAAI;YACRvB,CAAA,CAAMsB,OAAA,CAAQ,UAAChC,CAAA;cAAA,KACTY,CAAA,CAAOsB,GAAA,CAAIlC,CAAA,GAAQ;gBAAA,IACjBU,CAAA,GAAOT,CAAA,CAASgF,MAAA,CAAO,CAAChD,CAAA;gBAC9B7B,CAAA,CAAQ4F,IAAA,CAAK;kBACZW,EAAA,EA5HW;kBA6HXD,IAAA,EAAAhG,CAAA;kBACA2C,KAAA,EAAArD;gBAAA,IAEDM,CAAA,CAAeuG,OAAA,CAAQ;kBACtBF,EAAA,EAAIlE,CAAA;kBACJiE,IAAA,EAAAhG,CAAA;kBACA2C,KAAA,EAAArD;gBAAA;cAAA;cAGFiC,CAAA;YAAA,IAEDA,CAAA,GAAI,GACJrB,CAAA,CAAOoB,OAAA,CAAQ,UAAChC,CAAA;cAAA,KACVU,CAAA,CAAMwB,GAAA,CAAIlC,CAAA,GAAQ;gBAAA,IAChBY,CAAA,GAAOX,CAAA,CAASgF,MAAA,CAAO,CAAChD,CAAA;gBAC9B7B,CAAA,CAAQ4F,IAAA,CAAK;kBACZW,EAAA,EAAIlE,CAAA;kBACJiE,IAAA,EAAA9F,CAAA;kBACAyC,KAAA,EAAArD;gBAAA,IAEDM,CAAA,CAAeuG,OAAA,CAAQ;kBACtBF,EAAA,EAlJW;kBAmJXD,IAAA,EAAA9F,CAAA;kBACAyC,KAAA,EAAArD;gBAAA;cAAA;cAGFiC,CAAA;YAAA;UAAA,CAjIQ,CACLjC,CAAA,EACDC,CAAA,EACAG,CAAA,EACAE,CAAA;MAAA;IAAA;IAuPHmE,CAAA,WAAAA,CArHAzE,CAAA,EACAC,CAAA,EACAG,CAAA,EACAE,CAAA;MAEAF,CAAA,CAAQ4F,IAAA,CAAK;QACZW,EAAA,EApKc;QAqKdD,IAAA,EAAM;QACNrD,KAAA,EAAOpD,CAAA,KAAgB0E,CAAA,QAAU,IAAY1E;MAAA,IAE9CK,CAAA,CAAe0F,IAAA,CAAK;QACnBW,EAAA,EAzKc;QA0KdD,IAAA,EAAM;QACNrD,KAAA,EAAOrD;MAAA;IAAA;EAAA;AAAA;ACrMV,SAmBgBwF,EAAA;EAAA,SAgBNvF,EAAUD,CAAA,EAAQC,CAAA;IAAA,SAEjBG,EAAA;MAAA,KACHkB,WAAA,GAActB,CAAA;IAAA;IAFpBoC,CAAA,CAAcpC,CAAA,EAAGC,CAAA,GAIjBD,CAAA,CAAEmC,SAAA,IAEC/B,CAAA,CAAG+B,SAAA,GAAYlC,CAAA,CAAEkC,SAAA,EAAY,IAAI/B,CAAA;EAAA;EAAA,SA8J5BE,EAAeN,CAAA;IAClBA,CAAA,CAAMY,CAAA,KACVZ,CAAA,CAAMgF,CAAA,GAAY,IAAIrC,GAAA,IACtB3C,CAAA,CAAMY,CAAA,GAAQ,IAAI+B,GAAA,CAAI3C,CAAA,CAAMI,CAAA;EAAA;EAAA,SA0HrBQ,EAAeZ,CAAA;IAClBA,CAAA,CAAMY,CAAA,KAEVZ,CAAA,CAAMY,CAAA,GAAQ,IAAIiC,GAAA,IAClB7C,CAAA,CAAMI,CAAA,CAAM4B,OAAA,CAAQ,UAAA/B,CAAA;MAAA,IACfG,CAAA,CAAYH,CAAA,GAAQ;QAAA,IACjBK,CAAA,GAAQyE,CAAA,CAAY/E,CAAA,CAAM4E,CAAA,CAAOjB,CAAA,EAAQ1D,CAAA,EAAOD,CAAA;QACtDA,CAAA,CAAM8C,CAAA,CAAQP,GAAA,CAAItC,CAAA,EAAOK,CAAA,GACzBN,CAAA,CAAMY,CAAA,CAAO4B,GAAA,CAAIlC,CAAA;MAAA,OAEjBN,CAAA,CAAMY,CAAA,CAAO4B,GAAA,CAAIvC,CAAA;IAAA;EAAA;EAAA,SAMZgC,EAAgBhC,CAAA;IACpBA,CAAA,CAAMmE,CAAA,IAAUpE,CAAA,CAAI,GAAGmG,IAAA,CAAKC,SAAA,CAAUtD,CAAA,CAAO7C,CAAA;EAAA;EAAA,IAjU9CmC,CAAA,GAAgB,SAAA0E,CAAS9G,CAAA,EAAQC,CAAA;MAAA,QACpCmC,CAAA,GACClB,MAAA,CAAO6F,cAAA,IACN;QAACC,SAAA,EAAW;MAAA,aAAe3G,KAAA,IAC3B,UAASL,CAAA,EAAGC,CAAA;QACXD,CAAA,CAAEgH,SAAA,GAAY/G,CAAA;MAAA,KAEhB,UAASD,CAAA,EAAGC,CAAA;QAAA,KACN,IAAIG,CAAA,IAAKH,CAAA,EAAOA,CAAA,CAAEmB,cAAA,CAAehB,CAAA,MAAIJ,CAAA,CAAEI,CAAA,IAAKH,CAAA,CAAEG,CAAA;MAAA,GAEhCJ,CAAA,EAAGC,CAAA;IAAA;IAcnBqC,CAAA,GAAY;MAAA,SAGRtC,EAAoBA,CAAA,EAAgBC,CAAA;QAAA,YACvCgB,CAAA,IAAe;UACnBP,CAAA;UACAqC,CAAA,EAAS9C,CAAA;UACT2E,CAAA,EAAQ3E,CAAA,GAASA,CAAA,CAAO2E,CAAA,GAASX,CAAA;UACjCO,CAAA,GAAW;UACXK,CAAA,GAAY;UACZjE,CAAA,OAAO;UACPoE,CAAA,OAAW;UACX5E,CAAA,EAAOJ,CAAA;UACP8E,CAAA,EAAQ;UACRU,CAAA,GAAW;UACXpB,CAAA,GAAU;QAAA,GAEJ;MAAA;MAhBRnE,CAAA,CAAUD,CAAA,EAmJR2C,GAAA;MAAA,IAjII/B,CAAA,GAAIZ,CAAA,CAASmC,SAAA;MAAA,OAEnBjB,MAAA,CAAOmF,cAAA,CAAezF,CAAA,EAAG,QAAQ;QAChCyB,GAAA,EAAK,SAAAA,CAAA;UAAA,OACGS,CAAA,CAAO,KAAK7B,CAAA,GAAcgG,IAAA;QAAA;MAAA,IAMnCrG,CAAA,CAAEsB,GAAA,GAAM,UAASlC,CAAA;QAAA,OACT8C,CAAA,CAAO,KAAK7B,CAAA,GAAciB,GAAA,CAAIlC,CAAA;MAAA,GAGtCY,CAAA,CAAE2B,GAAA,GAAM,UAASvC,CAAA,EAAUC,CAAA;QAAA,IACpBG,CAAA,GAAkB,KAAKa,CAAA;QAAA,OAC7BgB,CAAA,CAAgB7B,CAAA,GACX0C,CAAA,CAAO1C,CAAA,EAAO8B,GAAA,CAAIlC,CAAA,KAAQ8C,CAAA,CAAO1C,CAAA,EAAOiC,GAAA,CAAIrC,CAAA,MAASC,CAAA,KACzDK,CAAA,CAAeF,CAAA,GACf0E,CAAA,CAAY1E,CAAA,GACZA,CAAA,CAAM4E,CAAA,CAAWzC,GAAA,CAAIvC,CAAA,GAAK,IAC1BI,CAAA,CAAMQ,CAAA,CAAO2B,GAAA,CAAIvC,CAAA,EAAKC,CAAA,GACtBG,CAAA,CAAM4E,CAAA,CAAWzC,GAAA,CAAIvC,CAAA,GAAK,KAEpB;MAAA,GAGRY,CAAA,CAAE8C,MAAA,GAAS,UAAS1D,CAAA;QAAA,KACd,KAAKkC,GAAA,CAAIlC,CAAA,WACN;QAAA,IAGFC,CAAA,GAAkB,KAAKgB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBhC,CAAA,GAChBK,CAAA,CAAeL,CAAA,GACf6E,CAAA,CAAY7E,CAAA,GACRA,CAAA,CAAMG,CAAA,CAAM8B,GAAA,CAAIlC,CAAA,IACnBC,CAAA,CAAM+E,CAAA,CAAWzC,GAAA,CAAIvC,CAAA,GAAK,KAE1BC,CAAA,CAAM+E,CAAA,CAAWtB,MAAA,CAAO1D,CAAA,GAEzBC,CAAA,CAAMW,CAAA,CAAO8C,MAAA,CAAO1D,CAAA,IACb;MAAA,GAGRY,CAAA,CAAE6C,KAAA,GAAQ;QAAA,IACHzD,CAAA,GAAkB,KAAKiB,CAAA;QAC7BgB,CAAA,CAAgBjC,CAAA,GACZ8C,CAAA,CAAO9C,CAAA,EAAOiH,IAAA,KACjB3G,CAAA,CAAeN,CAAA,GACf8E,CAAA,CAAY9E,CAAA,GACZA,CAAA,CAAMgF,CAAA,GAAY,IAAIrC,GAAA,IACtBjC,CAAA,CAAKV,CAAA,CAAMI,CAAA,EAAO,UAAAH,CAAA;UACjBD,CAAA,CAAMgF,CAAA,CAAWzC,GAAA,CAAItC,CAAA,GAAK;QAAA,IAE3BD,CAAA,CAAMY,CAAA,CAAO6C,KAAA;MAAA,GAIf7C,CAAA,CAAEoB,OAAA,GAAU,UACXhC,CAAA,EACAC,CAAA;QAAA,IAAAG,CAAA;QAGA0C,CAAA,CADwB,KAAK7B,CAAA,GACfe,OAAA,CAAQ,UAAC1B,CAAA,EAAaI,CAAA;UACnCV,CAAA,CAAGqB,IAAA,CAAKpB,CAAA,EAASG,CAAA,CAAKiC,GAAA,CAAI3B,CAAA,GAAMA,CAAA,EAAKN,CAAA;QAAA;MAAA,GAIvCQ,CAAA,CAAEyB,GAAA,GAAM,UAASrC,CAAA;QAAA,IACVC,CAAA,GAAkB,KAAKgB,CAAA;QAC7BgB,CAAA,CAAgBhC,CAAA;QAAA,IACVS,CAAA,GAAQoC,CAAA,CAAO7C,CAAA,EAAOoC,GAAA,CAAIrC,CAAA;QAAA,IAC5BC,CAAA,CAAM4E,CAAA,KAAezE,CAAA,CAAYM,CAAA,UAC7BA,CAAA;QAAA,IAEJA,CAAA,KAAUT,CAAA,CAAMG,CAAA,CAAMiC,GAAA,CAAIrC,CAAA,UACtBU,CAAA;QAAA,IAGFE,CAAA,GAAQmE,CAAA,CAAY9E,CAAA,CAAM2E,CAAA,CAAOjB,CAAA,EAAQjD,CAAA,EAAOT,CAAA;QAAA,OACtDK,CAAA,CAAeL,CAAA,GACfA,CAAA,CAAMW,CAAA,CAAO2B,GAAA,CAAIvC,CAAA,EAAKY,CAAA,GACfA,CAAA;MAAA,GAGRA,CAAA,CAAEkB,IAAA,GAAO;QAAA,OACDgB,CAAA,CAAO,KAAK7B,CAAA,GAAca,IAAA;MAAA,GAGlClB,CAAA,CAAEsG,MAAA,GAAS;QAAA,IAAAlH,CAAA;UAAAC,CAAA;UACJG,CAAA,GAAW,KAAK0B,IAAA;QAAA,QAAA9B,CAAA,OAEpBmH,CAAA,IAAiB;UAAA,OAAMlH,CAAA,CAAKiH,MAAA;QAAA,GAAAlH,CAAA,CAC7BoH,IAAA,GAAM;UAAA,IACCpH,CAAA,GAAII,CAAA,CAASgH,IAAA;UAAA,OAEfpH,CAAA,CAAEqH,IAAA,GAAarH,CAAA,GAEZ;YACNqH,IAAA,GAAM;YACNhE,KAAA,EAHapD,CAAA,CAAKoC,GAAA,CAAIrC,CAAA,CAAEqD,KAAA;UAAA;QAAA,GAAArD,CAAA;MAAA,GAS5BY,CAAA,CAAE4F,OAAA,GAAU;QAAA,IAAAxG,CAAA;UAAAC,CAAA;UACLG,CAAA,GAAW,KAAK0B,IAAA;QAAA,QAAA9B,CAAA,OAEpBmH,CAAA,IAAiB;UAAA,OAAMlH,CAAA,CAAKuG,OAAA;QAAA,GAAAxG,CAAA,CAC7BoH,IAAA,GAAM;UAAA,IACCpH,CAAA,GAAII,CAAA,CAASgH,IAAA;UAAA,IAEfpH,CAAA,CAAEqH,IAAA,EAAM,OAAOrH,CAAA;UAAA,IACbM,CAAA,GAAQL,CAAA,CAAKoC,GAAA,CAAIrC,CAAA,CAAEqD,KAAA;UAAA,OAClB;YACNgE,IAAA,GAAM;YACNhE,KAAA,EAAO,CAACrD,CAAA,CAAEqD,KAAA,EAAO/C,CAAA;UAAA;QAAA,GAAAN,CAAA;MAAA,GAMrBY,CAAA,CAAEuG,CAAA,IAAkB;QAAA,OACZ,KAAKX,OAAA;MAAA,GAGNxG,CAAA;IAAA,CAnJU;IAkKZyC,CAAA,GAAY;MAAA,SAGRzC,EAAoBA,CAAA,EAAgBC,CAAA;QAAA,YACvCgB,CAAA,IAAe;UACnBP,CAAA;UACAqC,CAAA,EAAS9C,CAAA;UACT2E,CAAA,EAAQ3E,CAAA,GAASA,CAAA,CAAO2E,CAAA,GAASX,CAAA;UACjCO,CAAA,GAAW;UACXK,CAAA,GAAY;UACZjE,CAAA,OAAO;UACPR,CAAA,EAAOJ,CAAA;UACP8E,CAAA,EAAQ;UACRhC,CAAA,EAAS,IAAIH,GAAA;UACbyB,CAAA,GAAU;UACVoB,CAAA,GAAW;QAAA,GAEL;MAAA;MAhBRvF,CAAA,CAAUD,CAAA,EA8GR6C,GAAA;MAAA,IA5FIzC,CAAA,GAAIJ,CAAA,CAASmC,SAAA;MAAA,OAEnBjB,MAAA,CAAOmF,cAAA,CAAejG,CAAA,EAAG,QAAQ;QAChCiC,GAAA,EAAK,SAAAA,CAAA;UAAA,OACGS,CAAA,CAAO,KAAK7B,CAAA,GAAcgG,IAAA;QAAA;MAAA,IAKnC7G,CAAA,CAAE8B,GAAA,GAAM,UAASlC,CAAA;QAAA,IACVC,CAAA,GAAkB,KAAKgB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBhC,CAAA,GAEXA,CAAA,CAAMW,CAAA,KAGPX,CAAA,CAAMW,CAAA,CAAMsB,GAAA,CAAIlC,CAAA,QAChBC,CAAA,CAAM6C,CAAA,CAAQZ,GAAA,CAAIlC,CAAA,MAAUC,CAAA,CAAMW,CAAA,CAAMsB,GAAA,CAAIjC,CAAA,CAAM6C,CAAA,CAAQT,GAAA,CAAIrC,CAAA,MAH1DC,CAAA,CAAMG,CAAA,CAAM8B,GAAA,CAAIlC,CAAA;MAAA,GAQzBI,CAAA,CAAEoC,GAAA,GAAM,UAASxC,CAAA;QAAA,IACVC,CAAA,GAAkB,KAAKgB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBhC,CAAA,GACX,KAAKiC,GAAA,CAAIlC,CAAA,MACbY,CAAA,CAAeX,CAAA,GACf6E,CAAA,CAAY7E,CAAA,GACZA,CAAA,CAAMW,CAAA,CAAO4B,GAAA,CAAIxC,CAAA,IAEX;MAAA,GAGRI,CAAA,CAAEsD,MAAA,GAAS,UAAS1D,CAAA;QAAA,KACd,KAAKkC,GAAA,CAAIlC,CAAA,WACN;QAAA,IAGFC,CAAA,GAAkB,KAAKgB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBhC,CAAA,GAChBW,CAAA,CAAeX,CAAA,GACf6E,CAAA,CAAY7E,CAAA,GAEXA,CAAA,CAAMW,CAAA,CAAO8C,MAAA,CAAO1D,CAAA,OACnBC,CAAA,CAAM6C,CAAA,CAAQZ,GAAA,CAAIlC,CAAA,KAChBC,CAAA,CAAMW,CAAA,CAAO8C,MAAA,CAAOzD,CAAA,CAAM6C,CAAA,CAAQT,GAAA,CAAIrC,CAAA;MAAA,GAK3CI,CAAA,CAAEqD,KAAA,GAAQ;QAAA,IACHzD,CAAA,GAAkB,KAAKiB,CAAA;QAC7BgB,CAAA,CAAgBjC,CAAA,GACZ8C,CAAA,CAAO9C,CAAA,EAAOiH,IAAA,KACjBrG,CAAA,CAAeZ,CAAA,GACf8E,CAAA,CAAY9E,CAAA,GACZA,CAAA,CAAMY,CAAA,CAAO6C,KAAA;MAAA,GAIfrD,CAAA,CAAE8G,MAAA,GAAS;QAAA,IACJlH,CAAA,GAAkB,KAAKiB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBjC,CAAA,GAChBY,CAAA,CAAeZ,CAAA,GACRA,CAAA,CAAMY,CAAA,CAAOsG,MAAA;MAAA,GAGrB9G,CAAA,CAAEoG,OAAA,GAAU;QAAA,IACLxG,CAAA,GAAkB,KAAKiB,CAAA;QAAA,OAC7BgB,CAAA,CAAgBjC,CAAA,GAChBY,CAAA,CAAeZ,CAAA,GACRA,CAAA,CAAMY,CAAA,CAAO4F,OAAA;MAAA,GAGrBpG,CAAA,CAAE0B,IAAA,GAAO;QAAA,OACD,KAAKoF,MAAA;MAAA,GAGb9G,CAAA,CAAE+G,CAAA,IAAkB;QAAA,OACZ,KAAKD,MAAA;MAAA,GAGb9G,CAAA,CAAE4B,OAAA,GAAU,UAAiBhC,CAAA,EAASC,CAAA;QAAA,SAC/BG,CAAA,GAAW,KAAK8G,MAAA,IAClB5G,CAAA,GAASF,CAAA,CAASgH,IAAA,KACd9G,CAAA,CAAO+G,IAAA,GACdrH,CAAA,CAAGqB,IAAA,CAAKpB,CAAA,EAASK,CAAA,CAAO+C,KAAA,EAAO/C,CAAA,CAAO+C,KAAA,EAAO,OAC7C/C,CAAA,GAASF,CAAA,CAASgH,IAAA;MAAA,GAIbpH,CAAA;IAAA,CA9GU;EA0IlBgE,CAAA,CAAW,UAAU;IAACsB,CAAA,WAAAA,CAtJetF,CAAA,EAAWC,CAAA;MAAA,OAExC,IAAIqC,CAAA,CAAStC,CAAA,EAAQC,CAAA;IAAA;IAoJIsF,CAAA,WAAAA,CAzBIvF,CAAA,EAAWC,CAAA;MAAA,OAExC,IAAIwC,CAAA,CAASzC,CAAA,EAAQC,CAAA;IAAA;EAAA;AAAA;AAAA,SC/Td8F,EAAA;EACfT,CAAA,IACAE,CAAA,IACAD,CAAA;AAAA;AAAA,SC2FeU,EAAajG,CAAA;EAAA,OACrBA,CAAA;AAAA;AAAA,SAQQyG,EAAiBzG,CAAA;EAAA,OACzBA,CAAA;AAAA;AAAA,IAAAsH,CAAA;ETnFJpD,CAAA;EUpBEqD,CAAA,GACa,sBAAXC,MAAA,IAAiD,mBAAhBA,MAAA,CAAO;EACnC9E,CAAA,GAAwB,sBAARC,GAAA;EAChBC,CAAA,GAAwB,sBAARC,GAAA;EAChB4E,CAAA,GACK,sBAAV9B,KAAA,SACoB,MAApBA,KAAA,CAAMC,SAAA,IACM,sBAAZ8B,OAAA;EAKK/C,CAAA,GAAmB4C,CAAA,GAC7BC,MAAA,CAAOG,GAAA,CAAI,qBAAAL,CAAA,OACR,oBAAkB,GAAAA,CAAA;EAUX3F,CAAA,GAA2B4F,CAAA,GACrCC,MAAA,CAAOG,GAAA,CAAI,qBACV;EAES1G,CAAA,GAA6BsG,CAAA,GACvCC,MAAA,CAAOG,GAAA,CAAI,iBACV;EAGSR,CAAA,GACM,sBAAVK,MAAA,IAAyBA,MAAA,CAAOI,QAAA,IAAc;EbvCjDjH,CAAA,GAAS;IAAA,GACX;IAAA,GACA;IAAA,GACA;IAAA,aACDX,CAAA;MAAA,OAEA,yHACAA,CAAA;IAAA;IAAA,GAGC;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,GACA;IAAA,IACC;IAAA,IACA;IAAA,IACA;IAAA,IACA;IAAA,IACA;IAAA,cACDA,CAAA;MAAA,OACK,+CAA+CA,CAAA;IAAA;IAAA,IAEnD;IAAA,cACDA,CAAA;MAAA,OACK,kCAAkCA,CAAA;IAAA;IAAA,cAEvCA,CAAA;MAAA,4BACwBA,CAAA,uFAAyFA,CAAA;IAAA;IAAA,IAEhH;IAAA,cACDA,CAAA;MAAA,+JAC2JA,CAAA;IAAA;IAAA,cAE3JA,CAAA;MAAA,4CACwCA,CAAA;IAAA;IAAA,cAExCA,CAAA;MAAA,6CACyCA,CAAA;IAAA;IAAA,IAExC;EAAA;ECNCyB,CAAA,GAAmB,KAAAP,MAAA,CAAOiB,SAAA,CAAUb,WAAA;EA4B7BS,EAAA,GACO,sBAAZ2F,OAAA,IAA2BA,OAAA,CAAQG,OAAA,GACvCH,OAAA,CAAQG,OAAA,QACgC,MAAjC3G,MAAA,CAAO4G,qBAAA,GACd,UAAA9H,CAAA;IAAA,OACAkB,MAAA,CAAO6G,mBAAA,CAAoB/H,CAAA,EAAKiF,MAAA,CAC/B/D,MAAA,CAAO4G,qBAAA,CAAsB9H,CAAA;EAAA,IAEHkB,MAAA,CAAO6G,mBAAA;EAEzB9E,EAAA,GACZ/B,MAAA,CAAO8G,yBAAA,IACP,UAAmChI,CAAA;IAAA,IAE5BC,CAAA,GAAW;IAAA,OACjB8B,EAAA,CAAQ/B,CAAA,EAAQgC,OAAA,CAAQ,UAAA5B,CAAA;MACvBH,CAAA,CAAIG,CAAA,IAAOc,MAAA,CAAOkE,wBAAA,CAAyBpF,CAAA,EAAQI,CAAA;IAAA,IAE7CH,CAAA;EAAA;ECnEH8D,EAAA,GA4BF;EGyDS0B,EAAA,GAAwC;IACpDpD,GAAA,WAAAA,CAAIrC,CAAA,EAAOC,CAAA;MAAA,IACNA,CAAA,KAASgB,CAAA,EAAa,OAAOjB,CAAA;MAAA,IAE3BM,CAAA,GAASwC,CAAA,CAAO9C,CAAA;MAAA,KACjBiC,CAAA,CAAI3B,CAAA,EAAQL,CAAA,UAwInB,UAA2BD,CAAA,EAAmBC,CAAA,EAAaG,CAAA;QAAA,IAAAE,CAAA;UACpDI,CAAA,GAAOmE,CAAA,CAAuB5E,CAAA,EAAQG,CAAA;QAAA,OACrCM,CAAA,GACJ,WAAWA,CAAA,GACVA,CAAA,CAAK2C,KAAA,aAAA/C,CAAA,GAGLI,CAAA,CAAK2B,GAAA,gBAAA/B,CAAA,YAALA,CAAA,CAAUe,IAAA,CAAKrB,CAAA,CAAM8E,CAAA,SACtB;MAAA,CA9IM,CAAkB9E,CAAA,EAAOM,CAAA,EAAQL,CAAA;MAAA,IAEnCS,CAAA,GAAQJ,CAAA,CAAOL,CAAA;MAAA,OACjBD,CAAA,CAAM6E,CAAA,KAAezE,CAAA,CAAYM,CAAA,IAC7BA,CAAA,GAIJA,CAAA,KAAUyE,CAAA,CAAKnF,CAAA,CAAMI,CAAA,EAAOH,CAAA,KAC/BoF,CAAA,CAAYrF,CAAA,GACJA,CAAA,CAAMY,CAAA,CAAOX,CAAA,IAAe8E,CAAA,CACnC/E,CAAA,CAAM4E,CAAA,CAAOjB,CAAA,EACbjD,CAAA,EACAV,CAAA,KAGKU,CAAA;IAAA;IAERwB,GAAA,WAAAA,CAAIlC,CAAA,EAAOC,CAAA;MAAA,OACHA,CAAA,IAAQ6C,CAAA,CAAO9C,CAAA;IAAA;IAEvB6H,OAAA,WAAAA,CAAQ7H,CAAA;MAAA,OACA0H,OAAA,CAAQG,OAAA,CAAQ/E,CAAA,CAAO9C,CAAA;IAAA;IAE/BuC,GAAA,WAAAA,CACCvC,CAAA,EACAC,CAAA,EACAG,CAAA;MAAA,IAEME,CAAA,GAAOuE,CAAA,CAAuB/B,CAAA,CAAO9C,CAAA,GAAQC,CAAA;MAAA,IAC/C,QAAAK,CAAA,YAAAA,CAAA,CAAMiC,GAAA,SAGTjC,CAAA,CAAKiC,GAAA,CAAIlB,IAAA,CAAKrB,CAAA,CAAM8E,CAAA,EAAQ1E,CAAA,IACrB;MAAA,KAEHJ,CAAA,CAAMwE,CAAA,EAAW;QAAA,IAGf9D,CAAA,GAAUyE,CAAA,CAAKrC,CAAA,CAAO9C,CAAA,GAAQC,CAAA;UAE9BW,CAAA,GAAiC,QAAAF,CAAA,YAAAA,CAAA,CAAUO,CAAA;QAAA,IAC7CL,CAAA,IAAgBA,CAAA,CAAaR,CAAA,KAAUA,CAAA,SAC1CJ,CAAA,CAAMY,CAAA,CAAOX,CAAA,IAAQG,CAAA,EACrBJ,CAAA,CAAMgF,CAAA,CAAU/E,CAAA,KAAQ,IACjB;QAAA,IAEJwC,CAAA,CAAGrC,CAAA,EAAOM,CAAA,WAAuB,MAAVN,CAAA,IAAuB6B,CAAA,CAAIjC,CAAA,CAAMI,CAAA,EAAOH,CAAA,IAClE,QAAO;QACRoF,CAAA,CAAYrF,CAAA,GACZ8E,CAAA,CAAY9E,CAAA;MAAA;MAAA,OAIXA,CAAA,CAAMY,CAAA,CAAOX,CAAA,MAAUG,CAAA,UAEZ,MAAVA,CAAA,IAAuBH,CAAA,IAAQD,CAAA,CAAMY,CAAA,KAEtCqH,MAAA,CAAOC,KAAA,CAAM9H,CAAA,KAAU6H,MAAA,CAAOC,KAAA,CAAMlI,CAAA,CAAMY,CAAA,CAAOX,CAAA,OAKnDD,CAAA,CAAMY,CAAA,CAAOX,CAAA,IAAQG,CAAA,EACrBJ,CAAA,CAAMgF,CAAA,CAAU/E,CAAA,KAAQ,KAJhB;IAAA;IAOTkI,cAAA,WAAAA,CAAenI,CAAA,EAAOC,CAAA;MAAA,YAEW,MAA5BkF,CAAA,CAAKnF,CAAA,CAAMI,CAAA,EAAOH,CAAA,KAAuBA,CAAA,IAAQD,CAAA,CAAMI,CAAA,IAC1DJ,CAAA,CAAMgF,CAAA,CAAU/E,CAAA,KAAQ,GACxBoF,CAAA,CAAYrF,CAAA,GACZ8E,CAAA,CAAY9E,CAAA,YAGLA,CAAA,CAAMgF,CAAA,CAAU/E,CAAA,GAGpBD,CAAA,CAAMY,CAAA,WAAcZ,CAAA,CAAMY,CAAA,CAAMX,CAAA,IAC7B;IAAA;IAIRmF,wBAAA,WAAAA,CAAyBpF,CAAA,EAAOC,CAAA;MAAA,IACzBG,CAAA,GAAQ0C,CAAA,CAAO9C,CAAA;QACfM,CAAA,GAAOoH,OAAA,CAAQtC,wBAAA,CAAyBhF,CAAA,EAAOH,CAAA;MAAA,OAChDK,CAAA,GACE;QACN4C,QAAA,GAAU;QACVC,YAAA,QAAcnD,CAAA,CAAMU,CAAA,IAA2C,aAATT,CAAA;QACtDmD,UAAA,EAAY9C,CAAA,CAAK8C,UAAA;QACjBC,KAAA,EAAOjD,CAAA,CAAMH,CAAA;MAAA,IALIK,CAAA;IAAA;IAQnB+F,cAAA,WAAAA,CAAA;MACCrG,CAAA,CAAI;IAAA;IAELmB,cAAA,WAAAA,CAAenB,CAAA;MAAA,OACPkB,MAAA,CAAOC,cAAA,CAAenB,CAAA,CAAMI,CAAA;IAAA;IAEpC2G,cAAA,WAAAA,CAAA;MACC/G,CAAA,CAAI;IAAA;EAAA;EAQA0F,EAAA,GAA8C;AACpDhF,CAAA,CAAK+E,EAAA,EAAa,UAACzF,CAAA,EAAKC,CAAA;EAEvByF,EAAA,CAAW1F,CAAA,IAAO;IAAA,OACjBE,SAAA,CAAU,KAAKA,SAAA,CAAU,GAAG,IACrBD,CAAA,CAAGY,KAAA,CAAM,MAAMX,SAAA;EAAA;AAAA,IAGxBwF,EAAA,CAAWyC,cAAA,GAAiB,UAASlI,CAAA,EAAOG,CAAA;EAAA,wBACvCG,OAAA,CAAAC,GAAA,CAAAC,QAAA,IAAWyH,KAAA,CAAME,QAAA,CAAShI,CAAA,MAAeJ,CAAA,CAAI,KAE1C0F,EAAA,CAAWnD,GAAA,CAAKlB,IAAA,CAAK,MAAMpB,CAAA,EAAOG,CAAA,OAAM;AAAA,GAEhDsF,EAAA,CAAWnD,GAAA,GAAM,UAAStC,CAAA,EAAOG,CAAA,EAAME,CAAA;EAAA,wBAClCC,OAAA,CAAAC,GAAA,CAAAC,QAAA,IAAoB,aAATL,CAAA,IAAqB8H,KAAA,CAAME,QAAA,CAAShI,CAAA,MAAeJ,CAAA,CAAI,KAC/DyF,EAAA,CAAYlD,GAAA,CAAKlB,IAAA,CAAK,MAAMpB,CAAA,CAAM,IAAIG,CAAA,EAAME,CAAA,EAAOL,CAAA,CAAM;AAAA;AAAA,ICpMpDoI,EAAA,GAAb;IAAA,SAAA/H,EAKaL,CAAA;MAAA,IAAAK,CAAA;MAAA,KAAA+D,CAAA,GAJWoD,CAAA,OAAAvC,CAAA,IAEA,QAAAoD,OAAA,GA4BH,UAACrI,CAAA,EAAWS,CAAA,EAAcE,CAAA;QAAA,IAEzB,qBAATX,CAAA,IAAyC,qBAAXS,CAAA,EAAuB;UAAA,IACzDuB,CAAA,GAAcvB,CAAA;UACpBA,CAAA,GAAST,CAAA;UAAA,IAEHmC,CAAA,GAAO9B,CAAA;UAAA,OACN,UAENN,CAAA;YAAA,IAAAC,CAAA;YAAA,WAAAD,CAAA,KAAAA,CAAA,GAAOiC,CAAA;YAAA,SAAA7B,CAAA,GAAAF,SAAA,CAAAC,MAAA,EACJG,CAAA,GAAAD,KAAA,CAAAD,CAAA,OAAAA,CAAA,WAAAQ,CAAA,MAAAA,CAAA,GAAAR,CAAA,EAAAQ,CAAA,IAAAN,CAAA,CAAAM,CAAA,QAAAV,SAAA,CAAAU,CAAA;YAAA,OAEIwB,CAAA,CAAKkG,OAAA,CAAQtI,CAAA,EAAM,UAACA,CAAA;cAAA,IAAAI,CAAA;cAAA,QAAAA,CAAA,GAAmBM,CAAA,EAAOW,IAAA,CAAAR,KAAA,CAAAT,CAAA,GAAKH,CAAA,EAAMD,CAAA,EAAAiF,MAAA,CAAU3E,CAAA;YAAA;UAAA;QAAA;QAAA,IAQxEgC,CAAA;QAAA,IAJkB,qBAAX5B,CAAA,IAAuBV,CAAA,CAAI,SAChB,MAAlBY,CAAA,IAAwD,qBAAlBA,CAAA,IACzCZ,CAAA,CAAI,IAKDI,CAAA,CAAYH,CAAA,GAAO;UAAA,IAChBwC,CAAA,GAAQ8B,CAAA,CAAWjE,CAAA;YACnBsB,CAAA,GAAQmD,CAAA,CAAYzE,CAAA,EAAML,CAAA,OAAM;YAClC4B,CAAA,IAAW;UAAA;YAEdS,CAAA,GAAS5B,CAAA,CAAOkB,CAAA,GAChBC,CAAA,IAAW;UAAA;YAGPA,CAAA,GAAUuC,CAAA,CAAY3B,CAAA,IACrB4B,CAAA,CAAW5B,CAAA;UAAA;UAAA,OAEM,sBAAZ8F,OAAA,IAA2BjG,CAAA,YAAkBiG,OAAA,GAChDjG,CAAA,CAAOkG,IAAA,CACb,UAAAxI,CAAA;YAAA,OACCmE,CAAA,CAAkB1B,CAAA,EAAO7B,CAAA,GAClB4D,CAAA,CAAcxE,CAAA,EAAQyC,CAAA;UAAA,GAE9B,UAAAzC,CAAA;YAAA,MACCoE,CAAA,CAAY3B,CAAA,GACNzC,CAAA;UAAA,MAITmE,CAAA,CAAkB1B,CAAA,EAAO7B,CAAA,GAClB4D,CAAA,CAAclC,CAAA,EAAQG,CAAA;QAAA;QACvB,KAAKxC,CAAA,IAAwB,mBAATA,CAAA,EAAmB;UAAA,SAE9B,OADfqC,CAAA,GAAS5B,CAAA,CAAOT,CAAA,OACUqC,CAAA,GAASrC,CAAA,GAC/BqC,CAAA,KAAWqC,CAAA,KAASrC,CAAA,QAAS,IAC7BhC,CAAA,CAAK4E,CAAA,IAAa3B,CAAA,CAAOjB,CAAA,GAAQ,IACjC1B,CAAA,EAAe;YAAA,IACZkC,CAAA,GAAa;cACbC,CAAA,GAAc;YACpBe,CAAA,CAAU,WAAWW,CAAA,CAA4BxE,CAAA,EAAMqC,CAAA,EAAQQ,CAAA,EAAGC,CAAA,GAClEnC,CAAA,CAAckC,CAAA,EAAGC,CAAA;UAAA;UAAA,OAEXT,CAAA;QAAA;QACDtC,CAAA,CAAI,IAAIC,CAAA;MAAA,QAAAwI,kBAAA,GAG0B,UAACzI,CAAA,EAAWC,CAAA;QAAA,IAEjC,qBAATD,CAAA,SACH,UAACC,CAAA;UAAA,SAAAG,CAAA,GAAAF,SAAA,CAAAC,MAAA,EAAeO,CAAA,GAAAL,KAAA,CAAAD,CAAA,OAAAA,CAAA,WAAAQ,CAAA,MAAAA,CAAA,GAAAR,CAAA,EAAAQ,CAAA,IAAAF,CAAA,CAAAE,CAAA,QAAAV,SAAA,CAAAU,CAAA;UAAA,OACtBN,CAAA,CAAKmI,kBAAA,CAAmBxI,CAAA,EAAO,UAACA,CAAA;YAAA,OAAeD,CAAA,CAAAa,KAAA,UAAKZ,CAAA,EAAAgF,MAAA,CAAUvE,CAAA;UAAA;QAAA;QAAA,IAG5DN,CAAA;UAAkBM,CAAA;UAChBE,CAAA,GAASN,CAAA,CAAKgI,OAAA,CAAQtI,CAAA,EAAMC,CAAA,EAAQ,UAACD,CAAA,EAAYC,CAAA;YACtDG,CAAA,GAAUJ,CAAA,EACVU,CAAA,GAAiBT,CAAA;UAAA;QAAA,OAGK,sBAAZsI,OAAA,IAA2B3H,CAAA,YAAkB2H,OAAA,GAChD3H,CAAA,CAAO4H,IAAA,CAAK,UAAAxI,CAAA;UAAA,OAAa,CAACA,CAAA,EAAWI,CAAA,EAAUM,CAAA;QAAA,KAEhD,CAACE,CAAA,EAAQR,CAAA,EAAUM,CAAA;MAAA,GAzGQ,qBAAvB,QAAAT,CAAA,YAAAA,CAAA,CAAQyI,UAAA,KAClB,KAAKC,aAAA,CAAc1I,CAAA,CAAQyI,UAAA,GACM,qBAAvB,QAAAzI,CAAA,YAAAA,CAAA,CAAQ2I,UAAA,KAClB,KAAKC,aAAA,CAAc5I,CAAA,CAAQ2I,UAAA;IAAA;IAAA,IAAAlI,CAAA,GAAAJ,CAAA,CAAA6B,SAAA;IAAA,OAAAzB,CAAA,CAyG7BoI,WAAA,aAAiCxI,CAAA;MAC3BF,CAAA,CAAYE,CAAA,KAAON,CAAA,CAAI,IACxBC,CAAA,CAAQK,CAAA,MAAOA,CAAA,GAAO0E,CAAA,CAAQ1E,CAAA;MAAA,IAC5BI,CAAA,GAAQ6D,CAAA,CAAW;QACnB3D,CAAA,GAAQmE,CAAA,CAAY,MAAMzE,CAAA,OAAM;MAAA,OACtCM,CAAA,CAAMK,CAAA,EAAauE,CAAA,IAAY,GAC/BnB,CAAA,CAAW3D,CAAA,GACJE,CAAA;IAAA,GAAAF,CAAA,CAGRqI,WAAA,aACC9I,CAAA,EACAG,CAAA;MAAA,IAEME,CAAA,GAAoBL,CAAA,IAAUA,CAAA,CAAcgB,CAAA;MAAA,iBAAAV,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAE5CH,CAAA,IAAUA,CAAA,CAAMkF,CAAA,IAAWxF,CAAA,CAAI,IAChCM,CAAA,CAAMuE,CAAA,IAAY7E,CAAA,CAAI;MAAA,IAEZU,CAAA,GAASJ,CAAA,CAAjBsE,CAAA;MAAA,OACPT,CAAA,CAAkBzD,CAAA,EAAON,CAAA,GAClBoE,CAAA,MAAc,GAAW9D,CAAA;IAAA,GAAAA,CAAA,CAQjCmI,aAAA,aAAc7I,CAAA;MAAA,KACRkF,CAAA,GAAclF,CAAA;IAAA,GAAAU,CAAA,CASpBiI,aAAA,aAAc1I,CAAA;MACTA,CAAA,KAAUwH,CAAA,IACbzH,CAAA,CAAI,UAEAqE,CAAA,GAAcpE,CAAA;IAAA,GAAAS,CAAA,CAGpBsI,YAAA,aAAkChJ,CAAA,EAASI,CAAA;MAAA,IAGtCE,CAAA;MAAA,KACCA,CAAA,GAAIF,CAAA,CAAQD,MAAA,GAAS,GAAGG,CAAA,IAAK,GAAGA,CAAA,IAAK;QAAA,IACnCI,CAAA,GAAQN,CAAA,CAAQE,CAAA;QAAA,IACI,MAAtBI,CAAA,CAAMgG,IAAA,CAAKvG,MAAA,IAA6B,cAAbO,CAAA,CAAMiG,EAAA,EAAkB;UACtD3G,CAAA,GAAOU,CAAA,CAAM2C,KAAA;UAAA;QAAA;MAAA;MAMX/C,CAAA,IAAK,MACRF,CAAA,GAAUA,CAAA,CAAQ4C,KAAA,CAAM1C,CAAA,GAAI;MAAA,IAGvBM,CAAA,GAAmBkD,CAAA,CAAU,WAAW2C,CAAA;MAAA,OAC1CxG,CAAA,CAAQD,CAAA,IAEJY,CAAA,CAAiBZ,CAAA,EAAMI,CAAA,IAGxB,KAAKkI,OAAA,CAAQtI,CAAA,EAAM,UAACA,CAAA;QAAA,OAC1BY,CAAA,CAAiBZ,CAAA,EAAOI,CAAA;MAAA;IAAA,GAAAE,CAAA;EAAA,CAxL3B;EMZM2I,EAAA,GAAQ,IAAIZ,EAAA;EAqBLa,EAAA,GAAoBD,EAAA,CAAMX,OAAA;EAO1Ba,EAAA,GAA0CF,EAAA,CAAMR,kBAAA,CAAmBW,IAAA,CAC/EH,EAAA;EAQYI,EAAA,GAAgBJ,EAAA,CAAMJ,aAAA,CAAcO,IAAA,CAAKH,EAAA;EAQzCK,EAAA,GAAgBL,EAAA,CAAMN,aAAA,CAAcS,IAAA,CAAKH,EAAA;EAOzCM,EAAA,GAAeN,EAAA,CAAMD,YAAA,CAAaI,IAAA,CAAKH,EAAA;EAMvCO,EAAA,GAAcP,EAAA,CAAMH,WAAA,CAAYM,IAAA,CAAKH,EAAA;EAUrCQ,EAAA,GAAcR,EAAA,CAAMF,WAAA,CAAYK,IAAA,CAAKH,EAAA;AAAA,eAAAC,EAAA;AAAA,SAAAb,EAAA,IAAAqB,KAAA,EAAAH,EAAA,IAAAP,YAAA,EAAA/C,CAAA,IAAA0D,SAAA,EAAAlD,CAAA,IAAAmD,aAAA,EAAAJ,EAAA,IAAAV,WAAA,EAAA9D,CAAA,IAAA6E,OAAA,EAAA9D,CAAA,IAAA+D,gBAAA,EAAAxE,CAAA,IAAAyE,SAAA,EAAAvE,CAAA,IAAAwE,YAAA,EAAAzE,CAAA,IAAA0E,aAAA,EAAAR,EAAA,IAAAV,WAAA,EAAAxF,CAAA,IAAAK,MAAA,EAAAjC,CAAA,IAAAuI,SAAA,EAAAjK,CAAA,IAAAkK,OAAA,EAAA/J,CAAA,IAAAgK,WAAA,EAAAzF,CAAA,IAAA0F,OAAA,EAAA/J,CAAA,IAAAgK,QAAA,EAAApB,EAAA,IAAAZ,OAAA,EAAAa,EAAA,IAAAV,kBAAA,EAAAY,EAAA,IAAAR,aAAA,EAAAS,EAAA,IAAAX,aAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}