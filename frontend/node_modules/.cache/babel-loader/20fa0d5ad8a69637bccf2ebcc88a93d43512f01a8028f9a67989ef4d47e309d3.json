{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const VideoPreview = () => {\n  _s();\n  const {\n    videoId\n  } = useParams();\n  const navigate = useNavigate();\n  const [videoUrl, setVideoUrl] = useState('');\n  useEffect(() => {\n    // Fetch watermarked video URL\n    fetch(`/api/videos/${videoId}/preview`).then(res => res.json()).then(data => setVideoUrl(data.url));\n  }, [videoId]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto py-8 px-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"aspect-w-16 aspect-h-9 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"video\", {\n        src: videoUrl,\n        controls: true,\n        className: \"rounded-lg shadow-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(`/checkout/${videoId}`),\n        className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\",\n        children: \"Remove Watermark and Download Video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPreview, \"tR2Gm99dLJffvqxnKdSlU5Piy0E=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = VideoPreview;\nvar _c;\n$RefreshReg$(_c, \"VideoPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "jsxDEV", "_jsxDEV", "VideoPreview", "_s", "videoId", "navigate", "videoUrl", "setVideoUrl", "fetch", "then", "res", "json", "data", "url", "className", "children", "src", "controls", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nexport const VideoPreview: React.FC = () => {\n  const { videoId } = useParams();\n  const navigate = useNavigate();\n  const [videoUrl, setVideoUrl] = useState('');\n  \n  useEffect(() => {\n    // Fetch watermarked video URL\n    fetch(`/api/videos/${videoId}/preview`)\n      .then(res => res.json())\n      .then(data => setVideoUrl(data.url));\n  }, [videoId]);\n  \n  return (\n    <div className=\"max-w-4xl mx-auto py-8 px-4\">\n      <div className=\"aspect-w-16 aspect-h-9 mb-6\">\n        <video src={videoUrl} controls className=\"rounded-lg shadow-lg\" />\n      </div>\n      \n      <div className=\"text-center\">\n        <button\n          onClick={() => navigate(`/checkout/${videoId}`)}\n          className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\"\n        >\n          Remove Watermark and Download Video\n        </button>\n      </div>\n    </div>\n  );\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,OAAO,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAQ,CAAC,GAAGN,SAAS,CAAC,CAAC;EAC/B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACAW,KAAK,CAAC,eAAeJ,OAAO,UAAU,CAAC,CACpCK,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAIL,WAAW,CAACK,IAAI,CAACC,GAAG,CAAC,CAAC;EACxC,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;EAEb,oBACEH,OAAA;IAAKa,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAC1Cd,OAAA;MAAKa,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1Cd,OAAA;QAAOe,GAAG,EAAEV,QAAS;QAACW,QAAQ;QAACH,SAAS,EAAC;MAAsB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAENpB,OAAA;MAAKa,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1Bd,OAAA;QACEqB,OAAO,EAAEA,CAAA,KAAMjB,QAAQ,CAAC,aAAaD,OAAO,EAAE,CAAE;QAChDU,SAAS,EAAC,iEAAiE;QAAAC,QAAA,EAC5E;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA5BWD,YAAsB;EAAA,QACbJ,SAAS,EACZC,WAAW;AAAA;AAAAwB,EAAA,GAFjBrB,YAAsB;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}