{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './CreateVideoPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateVideoPage = () => {\n  _s();\n  const [lessonName, setLessonName] = useState('');\n  const [showCustomization, setShowCustomization] = useState(false);\n  const [studentName, setStudentName] = useState('');\n  const [sex, setSex] = useState('');\n  const [grade, setGrade] = useState('');\n  const [showVoices, setShowVoices] = useState(false);\n  const [selectedVoice, setSelectedVoice] = useState('');\n\n  // Voice options based on sex\n  const maleVoices = [{\n    value: 'male_voice_1',\n    label: '<PERSON> (Friendly)'\n  }, {\n    value: 'male_voice_2',\n    label: '<PERSON> (Energetic)'\n  }, {\n    value: 'male_voice_3',\n    label: '<PERSON> (Calm)'\n  }];\n  const femaleVoices = [{\n    value: 'female_voice_1',\n    label: 'Sarah (Warm)'\n  }, {\n    value: 'female_voice_2',\n    label: 'Emma (Clear)'\n  }, {\n    value: 'female_voice_3',\n    label: 'Lisa (Gentle)'\n  }];\n  const handleLessonSubmit = () => {\n    if (lessonName.trim()) {\n      setShowCustomization(true);\n    }\n  };\n  const handleCustomizationComplete = () => {\n    setShowVoices(true);\n    // Set default voice based on sex\n    if (sex === 'male' && !selectedVoice) {\n      setSelectedVoice('male_voice_1');\n    } else if (sex === 'female' && !selectedVoice) {\n      setSelectedVoice('female_voice_1');\n    }\n  };\n  const handleCreateVideo = () => {\n    // Handle video creation logic here\n    console.log({\n      lessonName,\n      studentName,\n      sex,\n      grade,\n      selectedVoice\n    });\n    alert('Video creation started!');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-300 text-gray-800 p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-2xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold mb-8 text-center text-gray-900\",\n        children: \"Create Educational Video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"lesson-name\",\n          className: \"block text-xl font-semibold mb-4 text-gray-800\",\n          children: \"What's the lesson name?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"lesson-name\",\n          type: \"text\",\n          value: lessonName,\n          onChange: e => setLessonName(e.target.value),\n          placeholder: \"Enter lesson name...\",\n          className: \"w-full p-4 text-lg border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), lessonName && !showCustomization && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLessonSubmit,\n          className: \"mt-4 bg-gray-600 text-gray-100 px-6 py-3 rounded hover:bg-gray-700 transition-colors font-semibold\",\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), showCustomization && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold mb-2 text-gray-800\",\n            children: \"That's all you need!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-700\",\n            children: \"But we can customize the video some more if you'd like.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-base text-gray-600 italic\",\n            children: \"All fields below are optional\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"student-name\",\n              className: \"block text-lg font-medium mb-2 text-gray-800\",\n              children: [\"Student Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600 font-normal\",\n                children: \"(optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 32\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"student-name\",\n              type: \"text\",\n              value: studentName,\n              onChange: e => setStudentName(e.target.value),\n              placeholder: \"Enter student's name...\",\n              className: \"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"sex\",\n              className: \"block text-lg font-medium mb-2 text-gray-800\",\n              children: [\"Sex \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600 font-normal\",\n                children: \"(optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"sex\",\n              value: sex,\n              onChange: e => setSex(e.target.value),\n              className: \"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 focus:border-gray-700 focus:outline-none\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select sex...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"male\",\n                children: \"Male\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"female\",\n                children: \"Female\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"grade\",\n              className: \"block text-lg font-medium mb-2 text-gray-800\",\n              children: [\"Grade Level \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-600 font-normal\",\n                children: \"(optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 31\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"grade\",\n              type: \"text\",\n              value: grade,\n              onChange: e => setGrade(e.target.value),\n              placeholder: \"e.g., 5th grade, 10th grade...\",\n              className: \"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), !showVoices && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCustomizationComplete,\n          className: \"mt-6 w-full bg-gray-600 text-gray-100 px-6 py-3 rounded hover:bg-gray-700 transition-colors font-semibold\",\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), showVoices && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold mb-4 text-gray-800\",\n          children: \"Choose a Voice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: (sex === 'male' ? maleVoices : sex === 'female' ? femaleVoices : [...maleVoices, ...femaleVoices]).map(voice => /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-3 p-3 border border-gray-400 rounded hover:border-gray-600 cursor-pointer bg-gray-100 hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"radio\",\n              name: \"voice\",\n              value: voice.value,\n              checked: selectedVoice === voice.value,\n              onChange: e => setSelectedVoice(e.target.value),\n              className: \"w-4 h-4 text-gray-600 focus:ring-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-gray-800\",\n              children: voice.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)]\n          }, voice.value, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCreateVideo,\n          className: \"mt-6 w-full bg-gray-600 text-gray-100 px-8 py-4 rounded hover:bg-gray-700 transition-colors font-bold text-xl\",\n          children: \"Create Video\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center space-x-2 mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 rounded-full bg-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-3 h-3 rounded-full ${showCustomization ? 'bg-gray-600' : 'bg-gray-400'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-3 h-3 rounded-full ${showVoices ? 'bg-gray-600' : 'bg-gray-400'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateVideoPage, \"4tyCCAiN6r1P4NJ+92ShIZ/I7m0=\");\n_c = CreateVideoPage;\nexport default CreateVideoPage;\nvar _c;\n$RefreshReg$(_c, \"CreateVideoPage\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CreateVideoPage", "_s", "lessonName", "setLessonName", "showCustomization", "setShowCustomization", "studentName", "setStudentName", "sex", "setSex", "grade", "setGrade", "showVoices", "setShowVoices", "selected<PERSON><PERSON><PERSON>", "setSelectedVoice", "maleVoices", "value", "label", "femaleVoices", "handleLessonSubmit", "trim", "handleCustomizationComplete", "handleCreateVideo", "console", "log", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "type", "onChange", "e", "target", "placeholder", "onClick", "map", "voice", "name", "checked", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport './CreateVideoPage.css';\n\nconst CreateVideoPage = () => {\n  const [lessonName, setLessonName] = useState('');\n  const [showCustomization, setShowCustomization] = useState(false);\n  const [studentName, setStudentName] = useState('');\n  const [sex, setSex] = useState('');\n  const [grade, setGrade] = useState('');\n  const [showVoices, setShowVoices] = useState(false);\n  const [selectedVoice, setSelectedVoice] = useState('');\n\n  // Voice options based on sex\n  const maleVoices = [\n    { value: 'male_voice_1', label: '<PERSON> (Friendly)' },\n    { value: 'male_voice_2', label: '<PERSON> (Energetic)' },\n    { value: 'male_voice_3', label: '<PERSON> (Calm)' }\n  ];\n\n  const femaleVoices = [\n    { value: 'female_voice_1', label: '<PERSON> (<PERSON>)' },\n    { value: 'female_voice_2', label: 'Emma (Clear)' },\n    { value: 'female_voice_3', label: '<PERSON> (Gentle)' }\n  ];\n\n  const handleLessonSubmit = () => {\n    if (lessonName.trim()) {\n      setShowCustomization(true);\n    }\n  };\n\n  const handleCustomizationComplete = () => {\n    setShowVoices(true);\n    // Set default voice based on sex\n    if (sex === 'male' && !selectedVoice) {\n      setSelectedVoice('male_voice_1');\n    } else if (sex === 'female' && !selectedVoice) {\n      setSelectedVoice('female_voice_1');\n    }\n  };\n\n  const handleCreateVideo = () => {\n    // Handle video creation logic here\n    console.log({\n      lessonName,\n      studentName,\n      sex,\n      grade,\n      selectedVoice\n    });\n    alert('Video creation started!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-300 text-gray-800 p-8\">\n      <div className=\"max-w-2xl mx-auto\">\n        <h1 className=\"text-4xl font-bold mb-8 text-center text-gray-900\">Create Educational Video</h1>\n        \n        {/* Step 1: Lesson Name */}\n        <div className=\"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm\">\n          <label htmlFor=\"lesson-name\" className=\"block text-xl font-semibold mb-4 text-gray-800\">\n            What's the lesson name?\n          </label>\n          <input\n            id=\"lesson-name\"\n            type=\"text\"\n            value={lessonName}\n            onChange={(e) => setLessonName(e.target.value)}\n            placeholder=\"Enter lesson name...\"\n            className=\"w-full p-4 text-lg border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n          />\n          {lessonName && !showCustomization && (\n            <button\n              onClick={handleLessonSubmit}\n              className=\"mt-4 bg-gray-600 text-gray-100 px-6 py-3 rounded hover:bg-gray-700 transition-colors font-semibold\"\n            >\n              Continue\n            </button>\n          )}\n        </div>\n\n        {/* Step 2: Optional Customization */}\n        {showCustomization && (\n          <div className=\"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm form-group\">\n            <div className=\"text-center mb-6\">\n              <h2 className=\"text-2xl font-bold mb-2 text-gray-800\">That's all you need!</h2>\n              <p className=\"text-lg text-gray-700\">But we can customize the video some more if you'd like.</p>\n              <p className=\"text-base text-gray-600 italic\">All fields below are optional</p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"student-name\" className=\"block text-lg font-medium mb-2 text-gray-800\">\n                  Student Name <span className=\"text-gray-600 font-normal\">(optional)</span>\n                </label>\n                <input\n                  id=\"student-name\"\n                  type=\"text\"\n                  value={studentName}\n                  onChange={(e) => setStudentName(e.target.value)}\n                  placeholder=\"Enter student's name...\"\n                  className=\"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"sex\" className=\"block text-lg font-medium mb-2 text-gray-800\">\n                  Sex <span className=\"text-gray-600 font-normal\">(optional)</span>\n                </label>\n                <select\n                  id=\"sex\"\n                  value={sex}\n                  onChange={(e) => setSex(e.target.value)}\n                  className=\"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 focus:border-gray-700 focus:outline-none\"\n                >\n                  <option value=\"\">Select sex...</option>\n                  <option value=\"male\">Male</option>\n                  <option value=\"female\">Female</option>\n                </select>\n              </div>\n\n              <div>\n                <label htmlFor=\"grade\" className=\"block text-lg font-medium mb-2 text-gray-800\">\n                  Grade Level <span className=\"text-gray-600 font-normal\">(optional)</span>\n                </label>\n                <input\n                  id=\"grade\"\n                  type=\"text\"\n                  value={grade}\n                  onChange={(e) => setGrade(e.target.value)}\n                  placeholder=\"e.g., 5th grade, 10th grade...\"\n                  className=\"w-full p-3 border border-gray-500 rounded bg-gray-100 text-gray-800 placeholder-gray-600 focus:border-gray-700 focus:outline-none focus:bg-gray-50\"\n                />\n              </div>\n            </div>\n\n            {!showVoices && (\n              <button\n                onClick={handleCustomizationComplete}\n                className=\"mt-6 w-full bg-gray-600 text-gray-100 px-6 py-3 rounded hover:bg-gray-700 transition-colors font-semibold\"\n              >\n                Continue\n              </button>\n            )}\n          </div>\n        )}\n\n        {/* Step 3: Voice Selection */}\n        {showVoices && (\n          <div className=\"bg-gray-200 rounded-lg p-6 mb-6 border border-gray-400 shadow-sm form-group\">\n            <h3 className=\"text-xl font-bold mb-4 text-gray-800\">Choose a Voice</h3>\n            <div className=\"space-y-3\">\n              {(sex === 'male' ? maleVoices : sex === 'female' ? femaleVoices : [...maleVoices, ...femaleVoices]).map((voice) => (\n                <label key={voice.value} className=\"flex items-center space-x-3 p-3 border border-gray-400 rounded hover:border-gray-600 cursor-pointer bg-gray-100 hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"voice\"\n                    value={voice.value}\n                    checked={selectedVoice === voice.value}\n                    onChange={(e) => setSelectedVoice(e.target.value)}\n                    className=\"w-4 h-4 text-gray-600 focus:ring-gray-500\"\n                  />\n                  <span className=\"text-lg text-gray-800\">{voice.label}</span>\n                </label>\n              ))}\n            </div>\n\n            <button\n              onClick={handleCreateVideo}\n              className=\"mt-6 w-full bg-gray-600 text-gray-100 px-8 py-4 rounded hover:bg-gray-700 transition-colors font-bold text-xl\"\n            >\n              Create Video\n            </button>\n          </div>\n        )}\n\n        {/* Progress indicator */}\n        <div className=\"flex justify-center space-x-2 mt-8\">\n          <div className=\"w-3 h-3 rounded-full bg-gray-600\"></div>\n          <div className={`w-3 h-3 rounded-full ${showCustomization ? 'bg-gray-600' : 'bg-gray-400'}`}></div>\n          <div className={`w-3 h-3 rounded-full ${showVoices ? 'bg-gray-600' : 'bg-gray-400'}`}></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateVideoPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACO,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACW,GAAG,EAAEC,MAAM,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMmB,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACpD;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAqB,CAAC,EACtD;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,CACjD;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEF,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EAClD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAe,CAAC,EAClD;IAAED,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACpD;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlB,UAAU,CAACmB,IAAI,CAAC,CAAC,EAAE;MACrBhB,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMiB,2BAA2B,GAAGA,CAAA,KAAM;IACxCT,aAAa,CAAC,IAAI,CAAC;IACnB;IACA,IAAIL,GAAG,KAAK,MAAM,IAAI,CAACM,aAAa,EAAE;MACpCC,gBAAgB,CAAC,cAAc,CAAC;IAClC,CAAC,MAAM,IAAIP,GAAG,KAAK,QAAQ,IAAI,CAACM,aAAa,EAAE;MAC7CC,gBAAgB,CAAC,gBAAgB,CAAC;IACpC;EACF,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAC,OAAO,CAACC,GAAG,CAAC;MACVvB,UAAU;MACVI,WAAW;MACXE,GAAG;MACHE,KAAK;MACLI;IACF,CAAC,CAAC;IACFY,KAAK,CAAC,yBAAyB,CAAC;EAClC,CAAC;EAED,oBACE3B,OAAA;IAAK4B,SAAS,EAAC,4CAA4C;IAAAC,QAAA,eACzD7B,OAAA;MAAK4B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC7B,OAAA;QAAI4B,SAAS,EAAC,mDAAmD;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG/FjC,OAAA;QAAK4B,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC/E7B,OAAA;UAAOkC,OAAO,EAAC,aAAa;UAACN,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAAC;QAExF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjC,OAAA;UACEmC,EAAE,EAAC,aAAa;UAChBC,IAAI,EAAC,MAAM;UACXlB,KAAK,EAAEf,UAAW;UAClBkC,QAAQ,EAAGC,CAAC,IAAKlC,aAAa,CAACkC,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;UAC/CsB,WAAW,EAAC,sBAAsB;UAClCZ,SAAS,EAAC;QAA4J;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvK,CAAC,EACD9B,UAAU,IAAI,CAACE,iBAAiB,iBAC/BL,OAAA;UACEyC,OAAO,EAAEpB,kBAAmB;UAC5BO,SAAS,EAAC,oGAAoG;UAAAC,QAAA,EAC/G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5B,iBAAiB,iBAChBL,OAAA;QAAK4B,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBAC1F7B,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B7B,OAAA;YAAI4B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EjC,OAAA;YAAG4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAuD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChGjC,OAAA;YAAG4B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENjC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOkC,OAAO,EAAC,cAAc;cAACN,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,eACxE,eAAA7B,OAAA;gBAAM4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACRjC,OAAA;cACEmC,EAAE,EAAC,cAAc;cACjBC,IAAI,EAAC,MAAM;cACXlB,KAAK,EAAEX,WAAY;cACnB8B,QAAQ,EAAGC,CAAC,IAAK9B,cAAc,CAAC8B,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAChDsB,WAAW,EAAC,yBAAyB;cACrCZ,SAAS,EAAC;YAAoJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOkC,OAAO,EAAC,KAAK;cAACN,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,MACxE,eAAA7B,OAAA;gBAAM4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACRjC,OAAA;cACEmC,EAAE,EAAC,KAAK;cACRjB,KAAK,EAAET,GAAI;cACX4B,QAAQ,EAAGC,CAAC,IAAK5B,MAAM,CAAC4B,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cACxCU,SAAS,EAAC,8GAA8G;cAAAC,QAAA,gBAExH7B,OAAA;gBAAQkB,KAAK,EAAC,EAAE;gBAAAW,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCjC,OAAA;gBAAQkB,KAAK,EAAC,MAAM;gBAAAW,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCjC,OAAA;gBAAQkB,KAAK,EAAC,QAAQ;gBAAAW,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjC,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAOkC,OAAO,EAAC,OAAO;cAACN,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAC,cAClE,eAAA7B,OAAA;gBAAM4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACRjC,OAAA;cACEmC,EAAE,EAAC,OAAO;cACVC,IAAI,EAAC,MAAM;cACXlB,KAAK,EAAEP,KAAM;cACb0B,QAAQ,EAAGC,CAAC,IAAK1B,QAAQ,CAAC0B,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAC1CsB,WAAW,EAAC,gCAAgC;cAC5CZ,SAAS,EAAC;YAAoJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL,CAACpB,UAAU,iBACVb,OAAA;UACEyC,OAAO,EAAElB,2BAA4B;UACrCK,SAAS,EAAC,2GAA2G;UAAAC,QAAA,EACtH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGApB,UAAU,iBACTb,OAAA;QAAK4B,SAAS,EAAC,6EAA6E;QAAAC,QAAA,gBAC1F7B,OAAA;UAAI4B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEjC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAACpB,GAAG,KAAK,MAAM,GAAGQ,UAAU,GAAGR,GAAG,KAAK,QAAQ,GAAGW,YAAY,GAAG,CAAC,GAAGH,UAAU,EAAE,GAAGG,YAAY,CAAC,EAAEsB,GAAG,CAAEC,KAAK,iBAC5G3C,OAAA;YAAyB4B,SAAS,EAAC,kIAAkI;YAAAC,QAAA,gBACnK7B,OAAA;cACEoC,IAAI,EAAC,OAAO;cACZQ,IAAI,EAAC,OAAO;cACZ1B,KAAK,EAAEyB,KAAK,CAACzB,KAAM;cACnB2B,OAAO,EAAE9B,aAAa,KAAK4B,KAAK,CAACzB,KAAM;cACvCmB,QAAQ,EAAGC,CAAC,IAAKtB,gBAAgB,CAACsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;cAClDU,SAAS,EAAC;YAA2C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACFjC,OAAA;cAAM4B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEc,KAAK,CAACxB;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATlDU,KAAK,CAACzB,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUhB,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjC,OAAA;UACEyC,OAAO,EAAEjB,iBAAkB;UAC3BI,SAAS,EAAC,+GAA+G;UAAAC,QAAA,EAC1H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAGDjC,OAAA;QAAK4B,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD7B,OAAA;UAAK4B,SAAS,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDjC,OAAA;UAAK4B,SAAS,EAAE,wBAAwBvB,iBAAiB,GAAG,aAAa,GAAG,aAAa;QAAG;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGjC,OAAA;UAAK4B,SAAS,EAAE,wBAAwBf,UAAU,GAAG,aAAa,GAAG,aAAa;QAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAtLID,eAAe;AAAA6C,EAAA,GAAf7C,eAAe;AAwLrB,eAAeA,eAAe;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}