{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap'; // Uncomment this in your local project\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gyroscope = () => {\n  _s();\n  const gyroscopeRef = useRef(null);\n  const outerRingRef = useRef(null);\n  const middleRingRef = useRef(null);\n  const innerRingRef = useRef(null);\n  const coreRef = useRef(null);\n  const particlesRef = useRef(null);\n  const [rotation, setRotation] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const animationTimeline = useRef(null);\n  useEffect(() => {\n    var _particlesRef$current;\n    // GSAP Timeline for continuous animation\n    animationTimeline.current = gsap.timeline({\n      repeat: -1\n    });\n\n    // Outer ring rotation\n    gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Middle ring counter-rotation\n    gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Inner ring complex rotation\n    gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Core pulsing and rotation\n    gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.1,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1\n    });\n\n    // Particle animations\n    const particles = (_particlesRef$current = particlesRef.current) === null || _particlesRef$current === void 0 ? void 0 : _particlesRef$current.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        gsap.to(particle, {\n          duration: 2 + Math.random() * 3,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          opacity: Math.random() * 0.8 + 0.2,\n          ease: \"power2.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 2\n        });\n      });\n    }\n    return () => {\n      // Clean up GSAP animations\n      gsap.killTweensOf([outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current]);\n    };\n  }, []);\n  const handleMouseMove = e => {\n    var _gyroscopeRef$current;\n    if (!isDragging) return;\n    const rect = (_gyroscopeRef$current = gyroscopeRef.current) === null || _gyroscopeRef$current === void 0 ? void 0 : _gyroscopeRef$current.getBoundingClientRect();\n    if (!rect) return;\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n    const newRotation = {\n      x: mouseY / rect.height * 180,\n      y: mouseX / rect.width * 180,\n      z: (mouseX + mouseY) / (rect.width + rect.height) * 90\n    };\n    setRotation(newRotation);\n\n    // GSAP smooth transitions for drag\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: newRotation.x,\n      rotationY: newRotation.y,\n      rotationZ: newRotation.z,\n      ease: \"power2.out\"\n    });\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: newRotation.x * 1.5,\n      rotationY: newRotation.y * 0.7,\n      rotationZ: newRotation.z * -1,\n      ease: \"power2.out\"\n    });\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: newRotation.x * 0.5,\n      rotationY: newRotation.y * 1.8,\n      rotationZ: newRotation.z * 1.5,\n      ease: \"power2.out\"\n    });\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n\n    // GSAP spring back animation\n    gsap.to([outerRingRef.current, middleRingRef.current, innerRingRef.current], {\n      duration: 1,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\"\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: gyroscopeRef,\n      className: \"relative w-96 h-96 cursor-grab active:cursor-grabbing\",\n      style: {\n        perspective: '1000px'\n      },\n      onMouseDown: () => setIsDragging(true),\n      onMouseMove: handleMouseMove,\n      onMouseUp: handleMouseUp,\n      onMouseLeave: handleMouseUp,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        ref: particlesRef,\n        className: \"absolute inset-0 pointer-events-none\",\n        children: [...Array(30)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full\",\n          style: {\n            left: '50%',\n            top: '50%',\n            transform: 'translate(-50%, -50%)',\n            boxShadow: '0 0 10px currentColor'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: outerRingRef,\n        className: \"absolute inset-0 rounded-full border-4 border-blue-500\",\n        style: {\n          boxShadow: `\n              0 0 20px #3b82f6,\n              inset 0 0 20px #3b82f6,\n              0 0 40px #3b82f6aa\n            `\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: middleRingRef,\n          className: \"absolute inset-8 rounded-full border-4 border-purple-500\",\n          style: {\n            boxShadow: `\n                0 0 30px #8b5cf6,\n                inset 0 0 30px #8b5cf6\n              `\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: innerRingRef,\n            className: \"absolute inset-8 rounded-full border-4 border-pink-500\",\n            style: {\n              boxShadow: `\n                  0 0 40px #ec4899,\n                  inset 0 0 40px #ec4899\n                `\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: coreRef,\n              className: \"absolute inset-8 rounded-full bg-gradient-to-br from-white via-yellow-200 to-orange-300\",\n              style: {\n                boxShadow: `\n                    0 0 60px #ffffff,\n                    inset 0 0 20px #f59e0b88\n                  `\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-1/2 left-1/2 w-6 h-6 -mt-3 -ml-3 rounded-full bg-gradient-to-r from-red-500 to-yellow-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center text-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n        children: \"GSAP Quantum Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm opacity-75\",\n        children: \"Enhanced with GreenSock Animation Platform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(Gyroscope, \"LJ4dKPkzoG53vW28iZXCio4ALxo=\");\n_c = Gyroscope;\nexport default Gyroscope;\nvar _c;\n$RefreshReg$(_c, \"Gyroscope\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "gsap", "jsxDEV", "_jsxDEV", "Gyroscope", "_s", "gyroscopeRef", "outerRingRef", "middleRingRef", "innerRingRef", "coreRef", "particlesRef", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "animationTimeline", "_particlesRef$current", "current", "timeline", "repeat", "to", "duration", "rotationY", "ease", "rotationX", "rotationZ", "scale", "yoyo", "particles", "children", "Array", "from", "for<PERSON>ach", "particle", "index", "Math", "random", "opacity", "delay", "killTweensOf", "handleMouseMove", "e", "_gyroscopeRef$current", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "mouseX", "clientX", "mouseY", "clientY", "newRotation", "handleMouseUp", "className", "ref", "style", "perspective", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "map", "_", "i", "transform", "boxShadow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap'; // Uncomment this in your local project\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\nconst Gyroscope = () => {\n  const gyroscopeRef = useRef<HTMLDivElement>(null);\n  const outerRingRef = useRef<HTMLDivElement>(null);\n  const middleRingRef = useRef<HTMLDivElement>(null);\n  const innerRingRef = useRef<HTMLDivElement>(null);\n  const coreRef = useRef<HTMLDivElement>(null);\n  const particlesRef = useRef<HTMLDivElement>(null);\n  \n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const animationTimeline = useRef<any>(null);\n\n  useEffect(() => {\n    // GSAP Timeline for continuous animation\n    animationTimeline.current = gsap.timeline({ repeat: -1 });\n    \n    // Outer ring rotation\n    gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Middle ring counter-rotation\n    gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Inner ring complex rotation\n    gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1\n    });\n\n    // Core pulsing and rotation\n    gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.1,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1\n    });\n\n    // Particle animations\n    const particles = particlesRef.current?.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        gsap.to(particle, {\n          duration: 2 + Math.random() * 3,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          opacity: Math.random() * 0.8 + 0.2,\n          ease: \"power2.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 2\n        });\n      });\n    }\n\n    return () => {\n      // Clean up GSAP animations\n      gsap.killTweensOf([outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current]);\n    };\n  }, []);\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging) return;\n    \n    const rect = gyroscopeRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    const newRotation = {\n      x: (mouseY / rect.height) * 180,\n      y: (mouseX / rect.width) * 180,\n      z: ((mouseX + mouseY) / (rect.width + rect.height)) * 90\n    };\n\n    setRotation(newRotation);\n\n    // GSAP smooth transitions for drag\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: newRotation.x,\n      rotationY: newRotation.y,\n      rotationZ: newRotation.z,\n      ease: \"power2.out\"\n    });\n\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: newRotation.x * 1.5,\n      rotationY: newRotation.y * 0.7,\n      rotationZ: newRotation.z * -1,\n      ease: \"power2.out\"\n    });\n\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: newRotation.x * 0.5,\n      rotationY: newRotation.y * 1.8,\n      rotationZ: newRotation.z * 1.5,\n      ease: \"power2.out\"\n    });\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    \n    // GSAP spring back animation\n    gsap.to([outerRingRef.current, middleRingRef.current, innerRingRef.current], {\n      duration: 1,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\"\n    });\n  };\n\n  return (\n    <div className=\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center overflow-hidden\">\n      <div\n        ref={gyroscopeRef}\n        className=\"relative w-96 h-96 cursor-grab active:cursor-grabbing\"\n        style={{ perspective: '1000px' }}\n        onMouseDown={() => setIsDragging(true)}\n        onMouseMove={handleMouseMove}\n        onMouseUp={handleMouseUp}\n        onMouseLeave={handleMouseUp}\n      >\n        {/* Particle Container */}\n        <div ref={particlesRef} className=\"absolute inset-0 pointer-events-none\">\n          {[...Array(30)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full\"\n              style={{\n                left: '50%',\n                top: '50%',\n                transform: 'translate(-50%, -50%)',\n                boxShadow: '0 0 10px currentColor'\n              }}\n            />\n          ))}\n        </div>\n\n        {/* Outer Ring */}\n        <div \n          ref={outerRingRef}\n          className=\"absolute inset-0 rounded-full border-4 border-blue-500\"\n          style={{\n            boxShadow: `\n              0 0 20px #3b82f6,\n              inset 0 0 20px #3b82f6,\n              0 0 40px #3b82f6aa\n            `\n          }}\n        >\n          {/* Middle Ring */}\n          <div \n            ref={middleRingRef}\n            className=\"absolute inset-8 rounded-full border-4 border-purple-500\"\n            style={{\n              boxShadow: `\n                0 0 30px #8b5cf6,\n                inset 0 0 30px #8b5cf6\n              `\n            }}\n          >\n            {/* Inner Ring */}\n            <div \n              ref={innerRingRef}\n              className=\"absolute inset-8 rounded-full border-4 border-pink-500\"\n              style={{\n                boxShadow: `\n                  0 0 40px #ec4899,\n                  inset 0 0 40px #ec4899\n                `\n              }}\n            >\n              {/* Core */}\n              <div \n                ref={coreRef}\n                className=\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-yellow-200 to-orange-300\"\n                style={{\n                  boxShadow: `\n                    0 0 60px #ffffff,\n                    inset 0 0 20px #f59e0b88\n                  `\n                }}\n              >\n                <div className=\"absolute top-1/2 left-1/2 w-6 h-6 -mt-3 -ml-3 rounded-full bg-gradient-to-r from-red-500 to-yellow-500\" />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center text-white\">\n        <h1 className=\"text-3xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n          GSAP Quantum Gyroscope\n        </h1>\n        <p className=\"text-sm opacity-75\">Enhanced with GreenSock Animation Platform</p>\n      </div>\n    </div>\n  );\n};\n\nexport default Gyroscope;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,MAAM,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAQ7B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,YAAY,GAAGP,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMQ,YAAY,GAAGR,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMS,aAAa,GAAGT,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAMU,YAAY,GAAGV,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMW,OAAO,GAAGX,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMY,YAAY,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EAEjD,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAgB;IAAEc,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,iBAAiB,GAAGpB,MAAM,CAAM,IAAI,CAAC;EAE3CD,SAAS,CAAC,MAAM;IAAA,IAAAsB,qBAAA;IACd;IACAD,iBAAiB,CAACE,OAAO,GAAGpB,IAAI,CAACqB,QAAQ,CAAC;MAAEC,MAAM,EAAE,CAAC;IAAE,CAAC,CAAC;;IAEzD;IACAtB,IAAI,CAACuB,EAAE,CAACjB,YAAY,CAACc,OAAO,EAAE;MAC5BI,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,MAAM;MACZJ,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;;IAEF;IACAtB,IAAI,CAACuB,EAAE,CAAChB,aAAa,CAACa,OAAO,EAAE;MAC7BI,QAAQ,EAAE,EAAE;MACZG,SAAS,EAAE,CAAC,GAAG;MACfD,IAAI,EAAE,MAAM;MACZJ,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;;IAEF;IACAtB,IAAI,CAACuB,EAAE,CAACf,YAAY,CAACY,OAAO,EAAE;MAC5BI,QAAQ,EAAE,EAAE;MACZI,SAAS,EAAE,GAAG;MACdF,IAAI,EAAE,MAAM;MACZJ,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;;IAEF;IACAtB,IAAI,CAACuB,EAAE,CAACd,OAAO,CAACW,OAAO,EAAE;MACvBI,QAAQ,EAAE,CAAC;MACXK,KAAK,EAAE,GAAG;MACVH,IAAI,EAAE,cAAc;MACpBI,IAAI,EAAE,IAAI;MACVR,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;;IAEF;IACA,MAAMS,SAAS,IAAAZ,qBAAA,GAAGT,YAAY,CAACU,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBa,QAAQ;IAChD,IAAID,SAAS,EAAE;MACbE,KAAK,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACjDrC,IAAI,CAACuB,EAAE,CAACa,QAAQ,EAAE;UAChBZ,QAAQ,EAAE,CAAC,GAAGc,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/B1B,CAAC,EAAE,CAACyB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BzB,CAAC,EAAE,CAACwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9B5B,QAAQ,EAAE2B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC7BC,OAAO,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAClCb,IAAI,EAAE,cAAc;UACpBJ,MAAM,EAAE,CAAC,CAAC;UACVQ,IAAI,EAAE,IAAI;UACVW,KAAK,EAAEH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX;MACAvC,IAAI,CAAC0C,YAAY,CAAC,CAACpC,YAAY,CAACc,OAAO,EAAEb,aAAa,CAACa,OAAO,EAAEZ,YAAY,CAACY,OAAO,EAAEX,OAAO,CAACW,OAAO,CAAC,CAAC;IACzG,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuB,eAAe,GAAIC,CAAmB,IAAK;IAAA,IAAAC,qBAAA;IAC/C,IAAI,CAAC7B,UAAU,EAAE;IAEjB,MAAM8B,IAAI,IAAAD,qBAAA,GAAGxC,YAAY,CAACe,OAAO,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBE,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACD,IAAI,EAAE;IAEX,MAAME,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;IAC1C,MAAMC,MAAM,GAAGV,CAAC,CAACW,OAAO,GAAGP,OAAO;IAClC,MAAMQ,MAAM,GAAGZ,CAAC,CAACa,OAAO,GAAGN,OAAO;IAElC,MAAMO,WAAW,GAAG;MAClB7C,CAAC,EAAG2C,MAAM,GAAGV,IAAI,CAACO,MAAM,GAAI,GAAG;MAC/BvC,CAAC,EAAGwC,MAAM,GAAGR,IAAI,CAACI,KAAK,GAAI,GAAG;MAC9BnC,CAAC,EAAG,CAACuC,MAAM,GAAGE,MAAM,KAAKV,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACO,MAAM,CAAC,GAAI;IACxD,CAAC;IAEDzC,WAAW,CAAC8C,WAAW,CAAC;;IAExB;IACA1D,IAAI,CAACuB,EAAE,CAACjB,YAAY,CAACc,OAAO,EAAE;MAC5BI,QAAQ,EAAE,GAAG;MACbG,SAAS,EAAE+B,WAAW,CAAC7C,CAAC;MACxBY,SAAS,EAAEiC,WAAW,CAAC5C,CAAC;MACxBc,SAAS,EAAE8B,WAAW,CAAC3C,CAAC;MACxBW,IAAI,EAAE;IACR,CAAC,CAAC;IAEF1B,IAAI,CAACuB,EAAE,CAAChB,aAAa,CAACa,OAAO,EAAE;MAC7BI,QAAQ,EAAE,GAAG;MACbG,SAAS,EAAE+B,WAAW,CAAC7C,CAAC,GAAG,GAAG;MAC9BY,SAAS,EAAEiC,WAAW,CAAC5C,CAAC,GAAG,GAAG;MAC9Bc,SAAS,EAAE8B,WAAW,CAAC3C,CAAC,GAAG,CAAC,CAAC;MAC7BW,IAAI,EAAE;IACR,CAAC,CAAC;IAEF1B,IAAI,CAACuB,EAAE,CAACf,YAAY,CAACY,OAAO,EAAE;MAC5BI,QAAQ,EAAE,GAAG;MACbG,SAAS,EAAE+B,WAAW,CAAC7C,CAAC,GAAG,GAAG;MAC9BY,SAAS,EAAEiC,WAAW,CAAC5C,CAAC,GAAG,GAAG;MAC9Bc,SAAS,EAAE8B,WAAW,CAAC3C,CAAC,GAAG,GAAG;MAC9BW,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAC1B1C,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACAjB,IAAI,CAACuB,EAAE,CAAC,CAACjB,YAAY,CAACc,OAAO,EAAEb,aAAa,CAACa,OAAO,EAAEZ,YAAY,CAACY,OAAO,CAAC,EAAE;MAC3EI,QAAQ,EAAE,CAAC;MACXG,SAAS,EAAE,CAAC;MACZF,SAAS,EAAE,CAAC;MACZG,SAAS,EAAE,CAAC;MACZF,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,oBACExB,OAAA;IAAK0D,SAAS,EAAC,+HAA+H;IAAA5B,QAAA,gBAC5I9B,OAAA;MACE2D,GAAG,EAAExD,YAAa;MAClBuD,SAAS,EAAC,uDAAuD;MACjEE,KAAK,EAAE;QAAEC,WAAW,EAAE;MAAS,CAAE;MACjCC,WAAW,EAAEA,CAAA,KAAM/C,aAAa,CAAC,IAAI,CAAE;MACvCgD,WAAW,EAAEtB,eAAgB;MAC7BuB,SAAS,EAAEP,aAAc;MACzBQ,YAAY,EAAER,aAAc;MAAA3B,QAAA,gBAG5B9B,OAAA;QAAK2D,GAAG,EAAEnD,YAAa;QAACkD,SAAS,EAAC,sCAAsC;QAAA5B,QAAA,EACrE,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACmC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBpE,OAAA;UAEE0D,SAAS,EAAC,8EAA8E;UACxFE,KAAK,EAAE;YACLb,IAAI,EAAE,KAAK;YACXG,GAAG,EAAE,KAAK;YACVmB,SAAS,EAAE,uBAAuB;YAClCC,SAAS,EAAE;UACb;QAAE,GAPGF,CAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1E,OAAA;QACE2D,GAAG,EAAEvD,YAAa;QAClBsD,SAAS,EAAC,wDAAwD;QAClEE,KAAK,EAAE;UACLU,SAAS,EAAE;AACvB;AACA;AACA;AACA;QACU,CAAE;QAAAxC,QAAA,eAGF9B,OAAA;UACE2D,GAAG,EAAEtD,aAAc;UACnBqD,SAAS,EAAC,0DAA0D;UACpEE,KAAK,EAAE;YACLU,SAAS,EAAE;AACzB;AACA;AACA;UACY,CAAE;UAAAxC,QAAA,eAGF9B,OAAA;YACE2D,GAAG,EAAErD,YAAa;YAClBoD,SAAS,EAAC,wDAAwD;YAClEE,KAAK,EAAE;cACLU,SAAS,EAAE;AAC3B;AACA;AACA;YACc,CAAE;YAAAxC,QAAA,eAGF9B,OAAA;cACE2D,GAAG,EAAEpD,OAAQ;cACbmD,SAAS,EAAC,yFAAyF;cACnGE,KAAK,EAAE;gBACLU,SAAS,EAAE;AAC7B;AACA;AACA;cACgB,CAAE;cAAAxC,QAAA,eAEF9B,OAAA;gBAAK0D,SAAS,EAAC;cAAwG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1E,OAAA;MAAK0D,SAAS,EAAC,8EAA8E;MAAA5B,QAAA,gBAC3F9B,OAAA;QAAI0D,SAAS,EAAC,oGAAoG;QAAA5B,QAAA,EAAC;MAEnH;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL1E,OAAA;QAAG0D,SAAS,EAAC,oBAAoB;QAAA5B,QAAA,EAAC;MAA0C;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CA1NID,SAAS;AAAA0E,EAAA,GAAT1E,SAAS;AA4Nf,eAAeA,SAAS;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}