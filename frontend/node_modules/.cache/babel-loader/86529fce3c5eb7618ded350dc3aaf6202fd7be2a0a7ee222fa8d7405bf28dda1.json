{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateVideoPage = () => {\n  _s();\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n  const formFields = [{\n    label: \"lesson title\",\n    type: \"text\",\n    placeholder: \"enter your video lesson title...\"\n  }, {\n    label: \"student's first name\",\n    type: \"text\",\n    placeholder: \"All Of This Is Optional!!\"\n  }, {\n    label: \"student's age\",\n    type: \"select\",\n    options: [\"6\", \"7\", \"8\", \"9\"]\n  }, {\n    label: \"teacher's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"]\n  }, {\n    label: \"student's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"]\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"westworld-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"particles-container\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 3}s`,\n          animationDuration: `${2 + Math.random() * 2}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"milk-waves\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"100%\",\n        height: \"100%\",\n        viewBox: \"0 0 1000 1000\",\n        preserveAspectRatio: \"none\",\n        children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n          children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"milkGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"100%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#ffffff\",\n              stopOpacity: \"0.8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"50%\",\n              stopColor: \"#f8fafc\",\n              stopOpacity: \"0.6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#f1f5f9\",\n              stopOpacity: \"0.4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\",\n          fill: \"url(#milkGradient)\",\n          className: \"milk-surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `form-container ${formVisible ? 'visible' : 'hidden'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"main-title\",\n            children: \"CREATE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: [formFields.map((field, index) => {\n            var _field$options2;\n            // Special handling for student's first name and age on same line\n            if (index === 1) {\n              var _formFields$2$options;\n              // Render combined container for first name and age\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `field-wrapper combined-fields ${childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n                style: {\n                  transitionDelay: `${index * 200}ms`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"field-label\",\n                    children: field.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    placeholder: field.placeholder,\n                    className: \"field-input\",\n                    value: \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"field-label\",\n                    children: formFields[2].label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"field-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: [\"Select \", formFields[2].label.toLowerCase(), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 25\n                    }, this), (_formFields$2$options = formFields[2].options) === null || _formFields$2$options === void 0 ? void 0 : _formFields$2$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: option,\n                      children: option\n                    }, optIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)]\n              }, `combined-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this);\n            }\n\n            // Skip the age field since it's included in the combined container\n            if (index === 2) {\n              return null;\n            }\n\n            // Special handling for teacher's voice and student's voice on same line\n            if (index === 3) {\n              var _field$options, _formFields$4$options;\n              // Render combined container for teacher and student voices\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `field-wrapper combined-fields ${childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n                style: {\n                  transitionDelay: `${index * 200}ms`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"field-label\",\n                    children: field.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"field-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: [\"Select \", field.label.toLowerCase(), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 25\n                    }, this), (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: option,\n                      children: option\n                    }, optIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"field-label\",\n                    children: formFields[4].label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"field-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: [\"Select \", formFields[4].label.toLowerCase(), \"...\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 25\n                    }, this), (_formFields$4$options = formFields[4].options) === null || _formFields$4$options === void 0 ? void 0 : _formFields$4$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: option,\n                      children: option\n                    }, optIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)]\n              }, `combined-voices-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this);\n            }\n\n            // Skip the student's voice field since it's included in the combined container\n            if (index === 4) {\n              return null;\n            }\n\n            // Regular field rendering\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `field-wrapper ${index === 0 ? 'emerged' : childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n              style: {\n                transitionDelay: `${index * 200}ms`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"field-label\",\n                children: field.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), field.type === 'text' && /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: field.placeholder,\n                className: \"field-input\",\n                value: index === 0 ? lessonTitle : '',\n                onChange: index === 0 ? e => setLessonTitle(e.target.value) : undefined\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), field.type === 'select' && /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"field-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: [\"Select \", field.label.toLowerCase(), \"...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this), (_field$options2 = field.options) === null || _field$options2 === void 0 ? void 0 : _field$options2.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option,\n                  children: option\n                }, optIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this), field.type === 'textarea' && /*#__PURE__*/_jsxDEV(\"textarea\", {\n                placeholder: field.placeholder,\n                rows: 4,\n                className: \"field-textarea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `gyroscope-wrapper ${childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gyroscope-container\",\n              children: /*#__PURE__*/_jsxDEV(Gyroscope, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid-pattern\",\n        children: [...Array(400)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid-cell\"\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ambient-light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateVideoPage, \"yQFMt5143mRLX7AkiooqDviJs0o=\");\n_c = CreateVideoPage;\nexport default CreateVideoPage;\nvar _c;\n$RefreshReg$(_c, \"CreateVideoPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Gyroscope", "jsxDEV", "_jsxDEV", "CreateVideoPage", "_s", "formVisible", "setFormVisible", "current<PERSON><PERSON>", "setCurrentField", "lessonTitle", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childFieldsVisible", "<PERSON><PERSON><PERSON><PERSON>FieldsVisible", "setTimeout", "length", "timer", "setInterval", "prev", "formFields", "clearInterval", "label", "type", "placeholder", "options", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "preserveAspectRatio", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "d", "fill", "field", "index", "_field$options2", "_formFields$2$options", "transitionDelay", "value", "toLowerCase", "option", "optIndex", "_field$options", "_formFields$4$options", "onChange", "e", "target", "undefined", "rows", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport Gyroscope from '../Xtra/Gyroscope';\n\nconst CreateVideoPage = () => {\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n\n  const formFields = [\n    { label: \"lesson title\", type: \"text\", placeholder: \"enter your video lesson title...\" },\n    { label: \"student's first name\", type: \"text\", placeholder: \"All Of This Is Optional!!\" },\n    { label: \"student's age\", type: \"select\", options: [\"6\", \"7\", \"8\", \"9\"] },\n    { label: \"teacher's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] },\n    { label: \"student's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] }\n  ];\n\n  return (\n    <div className=\"westworld-container\">\n      {/* Floating particles for depth */}\n      <div className=\"particles-container\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"floating-particle\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${2 + Math.random() * 2}s`\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Milk-like surface waves */}\n      <div className=\"milk-waves\">\n        <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1000 1000\" preserveAspectRatio=\"none\">\n          <defs>\n            <linearGradient id=\"milkGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#ffffff\" stopOpacity=\"0.8\" />\n              <stop offset=\"50%\" stopColor=\"#f8fafc\" stopOpacity=\"0.6\" />\n              <stop offset=\"100%\" stopColor=\"#f1f5f9\" stopOpacity=\"0.4\" />\n            </linearGradient>\n          </defs>\n          <path\n            d=\"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\"\n            fill=\"url(#milkGradient)\"\n            className=\"milk-surface\"\n          />\n        </svg>\n      </div>\n\n      {/* Main content */}\n      <div className=\"content-wrapper\">\n        <div className={`form-container ${formVisible ? 'visible' : 'hidden'}`}>\n          \n          {/* Title */}\n          <div className=\"title-section\">\n            <h1 className=\"main-title\">CREATE</h1>\n            <div className=\"title-line\"></div>\n          </div>\n\n          {/* Form */}\n          <div className=\"form-fields\">\n            {formFields.map((field, index) => {\n              // Special handling for student's first name and age on same line\n              if (index === 1) {\n                // Render combined container for first name and age\n                return (\n                  <div\n                    key={`combined-${index}`}\n                    className={`field-wrapper combined-fields ${\n                      childFieldsVisible\n                        ? (currentField > index ? 'emerged' : 'emerging')\n                        : 'hidden'\n                    }`}\n                    style={{ transitionDelay: `${index * 200}ms` }}\n                  >\n                    {/* First Name Field */}\n                    <div className=\"inline-field\">\n                      <label className=\"field-label\">\n                        {field.label}\n                      </label>\n                      <input\n                        type=\"text\"\n                        placeholder={field.placeholder}\n                        className=\"field-input\"\n                        value=\"\"\n                      />\n                    </div>\n\n                    {/* Age Field */}\n                    <div className=\"inline-field\">\n                      <label className=\"field-label\">\n                        {formFields[2].label}\n                      </label>\n                      <select className=\"field-select\">\n                        <option value=\"\">Select {formFields[2].label.toLowerCase()}...</option>\n                        {formFields[2].options?.map((option, optIndex) => (\n                          <option key={optIndex} value={option}>\n                            {option}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n                );\n              }\n\n              // Skip the age field since it's included in the combined container\n              if (index === 2) {\n                return null;\n              }\n\n              // Special handling for teacher's voice and student's voice on same line\n              if (index === 3) {\n                // Render combined container for teacher and student voices\n                return (\n                  <div\n                    key={`combined-voices-${index}`}\n                    className={`field-wrapper combined-fields ${\n                      childFieldsVisible\n                        ? (currentField > index ? 'emerged' : 'emerging')\n                        : 'hidden'\n                    }`}\n                    style={{ transitionDelay: `${index * 200}ms` }}\n                  >\n                    {/* Teacher's Voice Field */}\n                    <div className=\"inline-field\">\n                      <label className=\"field-label\">\n                        {field.label}\n                      </label>\n                      <select className=\"field-select\">\n                        <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                        {field.options?.map((option, optIndex) => (\n                          <option key={optIndex} value={option}>\n                            {option}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    {/* Student's Voice Field */}\n                    <div className=\"inline-field\">\n                      <label className=\"field-label\">\n                        {formFields[4].label}\n                      </label>\n                      <select className=\"field-select\">\n                        <option value=\"\">Select {formFields[4].label.toLowerCase()}...</option>\n                        {formFields[4].options?.map((option, optIndex) => (\n                          <option key={optIndex} value={option}>\n                            {option}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n                );\n              }\n\n              // Skip the student's voice field since it's included in the combined container\n              if (index === 4) {\n                return null;\n              }\n\n              // Regular field rendering\n              return (\n                <div\n                  key={index}\n                  className={`field-wrapper ${\n                    index === 0\n                      ? 'emerged'\n                      : childFieldsVisible\n                      ? (currentField > index ? 'emerged' : 'emerging')\n                      : 'hidden'\n                  }`}\n                  style={{ transitionDelay: `${index * 200}ms` }}\n                >\n                  <label className=\"field-label\">\n                    {field.label}\n                  </label>\n\n                  {field.type === 'text' && (\n                    <input\n                      type=\"text\"\n                      placeholder={field.placeholder}\n                      className=\"field-input\"\n                      value={index === 0 ? lessonTitle : ''}\n                      onChange={index === 0 ? (e) => setLessonTitle(e.target.value) : undefined}\n                    />\n                  )}\n\n                  {field.type === 'select' && (\n                    <select className=\"field-select\">\n                      <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                      {field.options?.map((option, optIndex) => (\n                        <option key={optIndex} value={option}>\n                          {option}\n                        </option>\n                      ))}\n                    </select>\n                  )}\n\n                  {field.type === 'textarea' && (\n                    <textarea\n                      placeholder={field.placeholder}\n                      rows={4}\n                      className=\"field-textarea\"\n                    />\n                  )}\n                </div>\n              );\n            })}\n\n            {/* Gyroscope Generate Button */}\n            <div className={`gyroscope-wrapper ${\n              childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'\n            }`}>\n              <div className=\"gyroscope-container\">\n                <Gyroscope />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid overlay */}\n      <div className=\"grid-overlay\">\n        <div className=\"grid-pattern\">\n          {[...Array(400)].map((_, i) => (\n            <div key={i} className=\"grid-cell\"></div>\n          ))}\n        </div>\n      </div>\n\n      {/* Ambient lighting */}\n      <div className=\"ambient-light\"></div>\n    </div>\n  );\n};\n\nexport default CreateVideoPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,uBAAuB;AAC9B,OAAOC,SAAS,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd;IACAc,UAAU,CAAC,MAAM;MACfP,cAAc,CAAC,IAAI,CAAC;MACpBE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EAENT,SAAS,CAAC,MAAM;IACd,IAAIU,WAAW,CAACK,MAAM,IAAI,CAAC,EAAE;MAC3BF,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,MAAM;MACLA,qBAAqB,CAAC,KAAK,CAAC;MAC5BJ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EAEjBV,SAAS,CAAC,MAAM;IACd,IAAIM,WAAW,IAAIM,kBAAkB,EAAE;MACrC,MAAMI,KAAK,GAAGC,WAAW,CAAC,MAAM;QAC9BR,eAAe,CAACS,IAAI,IAAIA,IAAI,GAAGC,UAAU,CAACJ,MAAM,GAAGG,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC;MACrE,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAME,aAAa,CAACJ,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACV,WAAW,EAAEM,kBAAkB,CAAC,CAAC;EAErC,MAAMO,UAAU,GAAG,CACjB;IAAEE,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAAmC,CAAC,EACxF;IAAEF,KAAK,EAAE,sBAAsB;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAA4B,CAAC,EACzF;IAAEF,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAAE,CAAC,EACzE;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB;EAAE,CAAC,EAC3F;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB;EAAE,CAAC,CAC5F;EAED,oBACErB,OAAA;IAAKsB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCvB,OAAA;MAAKsB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EACjC,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB3B,OAAA;QAEEsB,SAAS,EAAC,mBAAmB;QAC7BM,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GAPGJ,CAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBvB,OAAA;QAAKuC,KAAK,EAAC,MAAM;QAACC,MAAM,EAAC,MAAM;QAACC,OAAO,EAAC,eAAe;QAACC,mBAAmB,EAAC,MAAM;QAAAnB,QAAA,gBAChFvB,OAAA;UAAAuB,QAAA,eACEvB,OAAA;YAAgB2C,EAAE,EAAC,cAAc;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAACC,EAAE,EAAC,MAAM;YAAAxB,QAAA,gBACnEvB,OAAA;cAAMgD,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DtC,OAAA;cAAMgD,MAAM,EAAC,KAAK;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtC,OAAA;cAAMgD,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACPtC,OAAA;UACEmD,CAAC,EAAC,wDAAwD;UAC1DC,IAAI,EAAC,oBAAoB;UACzB9B,SAAS,EAAC;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BvB,OAAA;QAAKsB,SAAS,EAAE,kBAAkBnB,WAAW,GAAG,SAAS,GAAG,QAAQ,EAAG;QAAAoB,QAAA,gBAGrEvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvB,OAAA;YAAIsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCtC,OAAA;YAAKsB,SAAS,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGNtC,OAAA;UAAKsB,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBP,UAAU,CAACS,GAAG,CAAC,CAAC4B,KAAK,EAAEC,KAAK,KAAK;YAAA,IAAAC,eAAA;YAChC;YACA,IAAID,KAAK,KAAK,CAAC,EAAE;cAAA,IAAAE,qBAAA;cACf;cACA,oBACExD,OAAA;gBAEEsB,SAAS,EAAE,iCACTb,kBAAkB,GACbJ,YAAY,GAAGiD,KAAK,GAAG,SAAS,GAAG,UAAU,GAC9C,QAAQ,EACX;gBACH1B,KAAK,EAAE;kBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;gBAAK,CAAE;gBAAA/B,QAAA,gBAG/CvB,OAAA;kBAAKsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BvB,OAAA;oBAAOsB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC3B8B,KAAK,CAACnC;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACRtC,OAAA;oBACEmB,IAAI,EAAC,MAAM;oBACXC,WAAW,EAAEiC,KAAK,CAACjC,WAAY;oBAC/BE,SAAS,EAAC,aAAa;oBACvBoC,KAAK,EAAC;kBAAE;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNtC,OAAA;kBAAKsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BvB,OAAA;oBAAOsB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC3BP,UAAU,CAAC,CAAC,CAAC,CAACE;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACRtC,OAAA;oBAAQsB,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC9BvB,OAAA;sBAAQ0D,KAAK,EAAC,EAAE;sBAAAnC,QAAA,GAAC,SAAO,EAACP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACyC,WAAW,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,GAAAkB,qBAAA,GACtExC,UAAU,CAAC,CAAC,CAAC,CAACK,OAAO,cAAAmC,qBAAA,uBAArBA,qBAAA,CAAuB/B,GAAG,CAAC,CAACmC,MAAM,EAAEC,QAAQ,kBAC3C7D,OAAA;sBAAuB0D,KAAK,EAAEE,MAAO;sBAAArC,QAAA,EAClCqC;oBAAM,GADIC,QAAQ;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAlCD,YAAYgB,KAAK,EAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmCrB,CAAC;YAEV;;YAEA;YACA,IAAIgB,KAAK,KAAK,CAAC,EAAE;cACf,OAAO,IAAI;YACb;;YAEA;YACA,IAAIA,KAAK,KAAK,CAAC,EAAE;cAAA,IAAAQ,cAAA,EAAAC,qBAAA;cACf;cACA,oBACE/D,OAAA;gBAEEsB,SAAS,EAAE,iCACTb,kBAAkB,GACbJ,YAAY,GAAGiD,KAAK,GAAG,SAAS,GAAG,UAAU,GAC9C,QAAQ,EACX;gBACH1B,KAAK,EAAE;kBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;gBAAK,CAAE;gBAAA/B,QAAA,gBAG/CvB,OAAA;kBAAKsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BvB,OAAA;oBAAOsB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC3B8B,KAAK,CAACnC;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACRtC,OAAA;oBAAQsB,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC9BvB,OAAA;sBAAQ0D,KAAK,EAAC,EAAE;sBAAAnC,QAAA,GAAC,SAAO,EAAC8B,KAAK,CAACnC,KAAK,CAACyC,WAAW,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,GAAAwB,cAAA,GAC9DT,KAAK,CAAChC,OAAO,cAAAyC,cAAA,uBAAbA,cAAA,CAAerC,GAAG,CAAC,CAACmC,MAAM,EAAEC,QAAQ,kBACnC7D,OAAA;sBAAuB0D,KAAK,EAAEE,MAAO;sBAAArC,QAAA,EAClCqC;oBAAM,GADIC,QAAQ;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGNtC,OAAA;kBAAKsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BvB,OAAA;oBAAOsB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAC3BP,UAAU,CAAC,CAAC,CAAC,CAACE;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACRtC,OAAA;oBAAQsB,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC9BvB,OAAA;sBAAQ0D,KAAK,EAAC,EAAE;sBAAAnC,QAAA,GAAC,SAAO,EAACP,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACyC,WAAW,CAAC,CAAC,EAAC,KAAG;oBAAA;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,GAAAyB,qBAAA,GACtE/C,UAAU,CAAC,CAAC,CAAC,CAACK,OAAO,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBtC,GAAG,CAAC,CAACmC,MAAM,EAAEC,QAAQ,kBAC3C7D,OAAA;sBAAuB0D,KAAK,EAAEE,MAAO;sBAAArC,QAAA,EAClCqC;oBAAM,GADIC,QAAQ;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GApCD,mBAAmBgB,KAAK,EAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqC5B,CAAC;YAEV;;YAEA;YACA,IAAIgB,KAAK,KAAK,CAAC,EAAE;cACf,OAAO,IAAI;YACb;;YAEA;YACA,oBACEtD,OAAA;cAEEsB,SAAS,EAAE,iBACTgC,KAAK,KAAK,CAAC,GACP,SAAS,GACT7C,kBAAkB,GACjBJ,YAAY,GAAGiD,KAAK,GAAG,SAAS,GAAG,UAAU,GAC9C,QAAQ,EACX;cACH1B,KAAK,EAAE;gBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;cAAK,CAAE;cAAA/B,QAAA,gBAE/CvB,OAAA;gBAAOsB,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAC3B8B,KAAK,CAACnC;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EAEPe,KAAK,CAAClC,IAAI,KAAK,MAAM,iBACpBnB,OAAA;gBACEmB,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAEiC,KAAK,CAACjC,WAAY;gBAC/BE,SAAS,EAAC,aAAa;gBACvBoC,KAAK,EAAEJ,KAAK,KAAK,CAAC,GAAG/C,WAAW,GAAG,EAAG;gBACtCyD,QAAQ,EAAEV,KAAK,KAAK,CAAC,GAAIW,CAAC,IAAKzD,cAAc,CAACyD,CAAC,CAACC,MAAM,CAACR,KAAK,CAAC,GAAGS;cAAU;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CACF,EAEAe,KAAK,CAAClC,IAAI,KAAK,QAAQ,iBACtBnB,OAAA;gBAAQsB,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC9BvB,OAAA;kBAAQ0D,KAAK,EAAC,EAAE;kBAAAnC,QAAA,GAAC,SAAO,EAAC8B,KAAK,CAACnC,KAAK,CAACyC,WAAW,CAAC,CAAC,EAAC,KAAG;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,GAAAiB,eAAA,GAC9DF,KAAK,CAAChC,OAAO,cAAAkC,eAAA,uBAAbA,eAAA,CAAe9B,GAAG,CAAC,CAACmC,MAAM,EAAEC,QAAQ,kBACnC7D,OAAA;kBAAuB0D,KAAK,EAAEE,MAAO;kBAAArC,QAAA,EAClCqC;gBAAM,GADIC,QAAQ;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACT,EAEAe,KAAK,CAAClC,IAAI,KAAK,UAAU,iBACxBnB,OAAA;gBACEoB,WAAW,EAAEiC,KAAK,CAACjC,WAAY;gBAC/BgD,IAAI,EAAE,CAAE;gBACR9C,SAAS,EAAC;cAAgB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CACF;YAAA,GAzCIgB,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0CP,CAAC;UAEV,CAAC,CAAC,eAGFtC,OAAA;YAAKsB,SAAS,EAAE,qBACdb,kBAAkB,IAAIJ,YAAY,GAAGW,UAAU,CAACJ,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ,EAChF;YAAAW,QAAA,eACDvB,OAAA;cAAKsB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClCvB,OAAA,CAACF,SAAS;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BvB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B,CAAC,GAAGC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACxB3B,OAAA;UAAasB,SAAS,EAAC;QAAW,GAAxBK,CAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC;IAAe;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAACpC,EAAA,CAxQID,eAAe;AAAAoE,EAAA,GAAfpE,eAAe;AA0QrB,eAAeA,eAAe;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}