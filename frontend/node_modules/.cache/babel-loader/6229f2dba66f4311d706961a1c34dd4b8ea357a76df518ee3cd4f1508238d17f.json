{"ast": null, "code": "import React,{useEffect,useState}from'react';import'./ManualVideoCreationPage.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ManualVideoCreationPage=()=>{const[keywords,setKeywords]=useState('');const[downloadedVideos,setDownloadedVideos]=useState([]);const[selectedVideoIds,setSelectedVideoIds]=useState([]);const[isDownloading,setIsDownloading]=useState(false);const[isProcessing,setIsProcessing]=useState(false);const[statusMessage,setStatusMessage]=useState('');const[error,setError]=useState(null);const[generatedDescription,setGeneratedDescription]=useState('');const[attempts,setAttempts]=useState([]);const[selectedAttempt,setSelectedAttempt]=useState(null);const[loadingAttempts,setLoadingAttempts]=useState(true);const[loadingDetails,setLoadingDetails]=useState(false);const[selectedVideos,setSelectedVideos]=useState([]);const[manualDialogue,setManualDialogue]=useState('');const[useManualDialogue,setUseManualDialogue]=useState(false);// Fetch video creation attempts when the component mounts\nuseEffect(()=>{const fetchAttempts=async()=>{try{setLoadingAttempts(true);setError(null);// Clear previous errors\n// Replace with your actual backend URL for listing attempts\nconst response=await fetch('/api/video-attempts/');if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const data=await response.json();setAttempts(data);setLoadingAttempts(false);}catch(err){// Use any for catch error type for broader compatibility\nconsole.error(\"Error fetching video attempts:\",err);setError(\"Failed to load video attempts: \".concat(err.message));setLoadingAttempts(false);}};fetchAttempts();},[]);// Empty dependency array means this effect runs once on mount\n// Fetch details when an attempt is selected\nconst handleSelectAttempt=async attemptId=>{try{setLoadingDetails(true);setError(null);// Clear previous errors\n// Replace with your actual backend URL for attempt details\nconst response=await fetch(\"/api/video-attempts/\".concat(attemptId,\"/\"));if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const data=await response.json();setSelectedAttempt(data);// Initialize selected videos with all downloaded videos by default,\n// or an empty array depending on desired default behavior\nsetSelectedVideos(data.downloaded_videos);setManualDialogue(data.dialogue);// Load existing dialogue for manual tweaking\nsetUseManualDialogue(false);// Default to not using manual dialogue initially\nsetLoadingDetails(false);}catch(err){// Use any for catch error type for broader compatibility\nconsole.error(\"Error fetching details for attempt \".concat(attemptId,\":\"),err);setError(\"Failed to load details for attempt \".concat(attemptId,\": \").concat(err.message));setLoadingDetails(false);setSelectedAttempt(null);// Clear selected attempt on error\n}};// Handle dialogue text area changes\nconst handleDialogueChange=event=>{setManualDialogue(event.target.value);};// Handle radio button/checkbox for manual dialogue use\nconst handleUseManualDialogueChange=event=>{setUseManualDialogue(event.target.checked);};// Step 1: Initiate Video Download (Requires new backend endpoint)\nconst handleDownloadVideos=async()=>{setIsDownloading(true);setStatusMessage('Downloading videos...');setError(null);setDownloadedVideos([]);// Clear previous downloads\n// TODO: Call your backend endpoint to initiate download\n// Example: const response = await fetch('/api/manual/download-videos', { method: 'POST', body: JSON.stringify({ keywords }) });\n// const data = await response.json(); // Assuming backend returns list of downloaded videos metadata\n// Placeholder for simulating download\nsetTimeout(()=>{const dummyVideos=[{id:'vid1',thumbnailUrl:'placeholder.jpg',localPath:'/path/to/vid1.mp4'},{id:'vid2',thumbnailUrl:'placeholder2.jpg',localPath:'/path/to/vid2.mp4'}// Add more dummy videos or replace with actual backend data\n];setDownloadedVideos(dummyVideos);setStatusMessage('Videos downloaded. Please select 10.');setIsDownloading(false);},2000);// Simulate network request\n};// Handle checkbox changes for video selection - THIS IS THE CORRECT FUNCTION\nconst handleVideoSelect=(videoPath,isSelected)=>{if(isSelected){setSelectedVideos([...selectedVideos,videoPath]);}else{setSelectedVideos(selectedVideos.filter(path=>path!==videoPath));}};// Step 2: Process Selected Videos and Get Analysis (Requires new backend endpoint)\nconst handleProcessSelected=async()=>{if(selectedVideoIds.length!==10){setError('Please select exactly 10 videos.');return;}setIsProcessing(true);setStatusMessage('Processing and analyzing selected videos...');setError(null);setGeneratedDescription('');// Clear previous description\n// Find the full video metadata for selected IDs\nconst videosToProcess=downloadedVideos.filter(video=>selectedVideoIds.includes(video.id));// TODO: Call your backend endpoint to process selected videos and get analysis\n// Example: const response = await fetch('/api/manual/process-analyze', { method: 'POST', body: JSON.stringify({ videos: videosToProcess }) });\n// const data = await response.json(); // Assuming backend returns generated description\n// Placeholder for simulating processing and analysis\nsetTimeout(()=>{setGeneratedDescription('AI-generated visual description based on your selected videos...');// Replace with actual analysis\nsetStatusMessage('Analysis complete. Review description and create final video.');setIsProcessing(false);},3000);// Simulate processing time\n};// Step 3: Create Final Video (Requires new backend endpoint or reuse/modify existing)\nconst handleCreateFinalVideo=async()=>{// This step would use the selected videos and the generated description\n// to proceed with dialogue generation, stitching, etc.\nsetStatusMessage('Creating final video...');// TODO: Call your backend endpoint to create the final video\n// This might be a modified version of your existing generation endpoint\n// Placeholder\nsetTimeout(()=>{setStatusMessage('Final video creation process started.');// Redirect or show link to the final video\n},1000);// Simulate start\n};// Handle the Process button click - implement backend call later\nconst handleProcess=async()=>{// Made async to await fetch call\nif(!selectedAttempt){alert(\"Please select a video attempt first.\");return;}if(selectedVideos.length===0){alert(\"Please select at least one video.\");return;}const dataToSend={attempt_id:selectedAttempt.attempt_details.id,selected_video_paths:selectedVideos,use_manual_dialogue:useManualDialogue,manual_dialogue_content:useManualDialogue?manualDialogue:selectedAttempt.dialogue// Send generated dialogue if not manual\n// We'll also need to send information about which analysis results to keep/regenerate\n// Based on your requirement, we only need visual description from Gemini for selected videos\n// This part of backend logic needs to be implemented.\n};console.log(\"Process button clicked with data:\",dataToSend);// TODO: Implement the backend API call to trigger manual processing\n// Example using fetch:\n/*\n        try {\n            const response = await fetch('/api/process-manual-video/', { // Replace with your actual endpoint\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    // Include CSRF token for Django if needed\n                    // 'X-CSRFToken': getCsrfToken(), // You'll need a function to get the CSRF token\n                },\n                body: JSON.stringify(dataToSend),\n            });\n\n            if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`);\n            }\n\n            const result = await response.json();\n            console.log(\"Manual processing initiated:\", result);\n            alert(\"Manual processing initiated successfully!\");\n            // You might want to update the UI to show processing status\n\n        } catch (err: any) {\n                console.error(\"Error during manual processing:\", err);\n                alert(`Failed to initiate manual processing: ${err.message}`);\n        }\n        */alert(\"Processing logic needs to be implemented on the backend and connected here.\");// Keep the alert for now\n};return/*#__PURE__*/_jsxs(\"div\",{className:\"manual-creation-container\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Manual Video Creation Fallback\"}),loadingAttempts&&/*#__PURE__*/_jsx(\"p\",{children:\"Loading video attempts...\"}),error&&/*#__PURE__*/_jsxs(\"p\",{className:\"error-message\",children:[\"Error: \",error]}),!loadingAttempts&&!error&&/*#__PURE__*/_jsxs(\"div\",{className:\"attempts-list\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Previous Creation Attempts\"}),attempts.length===0?/*#__PURE__*/_jsx(\"p\",{children:\"No previous attempts found.\"}):/*#__PURE__*/_jsx(\"ul\",{children:attempts.map(attempt=>/*#__PURE__*/_jsxs(\"li\",{onClick:()=>handleSelectAttempt(attempt.id),className:(selectedAttempt===null||selectedAttempt===void 0?void 0:selectedAttempt.attempt_details.id)===attempt.id?'selected':'',children:[\"Attempt ID: \",attempt.id,\" - Status: \",attempt.status,\" - Created: \",new Date(attempt.created_at).toLocaleString()]},attempt.id))})]}),loadingDetails&&/*#__PURE__*/_jsx(\"p\",{children:\"Loading attempt details...\"}),selectedAttempt&&/*#__PURE__*/_jsxs(\"div\",{className:\"attempt-details\",children:[/*#__PURE__*/_jsxs(\"h2\",{children:[\"Details for Attempt \",selectedAttempt.attempt_details.id]}),/*#__PURE__*/_jsxs(\"div\",{className:\"downloaded-videos\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[\"Downloaded Videos (\",selectedVideos.length,\" selected)\"]}),\" \",/*#__PURE__*/_jsx(\"div\",{className:\"video-thumbnails\",children:selectedAttempt.downloaded_videos.length===0?/*#__PURE__*/_jsx(\"p\",{children:\"No downloaded videos found for this attempt.\"}):selectedAttempt.downloaded_videos.map(videoPath=>/*#__PURE__*/_jsxs(\"div\",{className:\"video-thumbnail-item \".concat(selectedVideos.includes(videoPath)?'selected-thumbnail':''),children:[\" \",/*#__PURE__*/_jsx(\"img\",{src:\"/media/\".concat(videoPath),alt:\"Video \".concat(videoPath)// More descriptive alt text\n// You might need a proper thumbnail generation logic\n// For now, a generic image or a frame from the video could be used\n// This is a placeholder, replace with actual thumbnail logic if available\n,style:{width:'100px',height:'auto',border:'1px solid #ccc'}// Added border\n,onError:e=>{e.target.src='/path/to/placeholder-image.png';}// Add fallback for broken images\n}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedVideos.includes(videoPath),onChange:e=>handleVideoSelect(videoPath,e.target.checked)}),/*#__PURE__*/_jsx(\"span\",{children:videoPath.split('/').pop()}),\" \"]},videoPath))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"dialogue-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Dialogue\"}),/*#__PURE__*/_jsxs(\"label\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:useManualDialogue,onChange:handleUseManualDialogueChange}),\"Manually edit dialogue\"]}),/*#__PURE__*/_jsx(\"textarea\",{value:useManualDialogue?manualDialogue:selectedAttempt.dialogue,onChange:handleDialogueChange,rows:10,cols:80,disabled:!useManualDialogue// Disable if not using manual dialogue\n,placeholder:\"Enter manual dialogue here or uncheck to use generated dialogue\"// Added placeholder\n}),!useManualDialogue&&/*#__PURE__*/_jsx(\"p\",{children:\"Using automatically generated dialogue.\"}),\" \"]}),/*#__PURE__*/_jsx(\"button\",{onClick:handleProcess,children:\"Process Manually Selected Videos\"})]})]});};export default ManualVideoCreationPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "ManualVideoCreationPage", "keywords", "setKeywords", "downloadedVideos", "setDownloadedVideos", "selectedVideoIds", "setSelectedVideoIds", "isDownloading", "setIsDownloading", "isProcessing", "setIsProcessing", "statusMessage", "setStatusMessage", "error", "setError", "generatedDescription", "setGeneratedDescription", "attempts", "setAttempts", "selectedAttempt", "setSelectedAttempt", "loadingAttempts", "setLoa<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingDetails", "setLoadingDetails", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideos", "manualDialogue", "setManualDialogue", "useManualDialogue", "setUseManualDialogue", "fetchAttempts", "response", "fetch", "ok", "Error", "concat", "status", "data", "json", "err", "console", "message", "handleSelectAttempt", "attemptId", "downloaded_videos", "dialogue", "handleDialogueChange", "event", "target", "value", "handleUseManualDialogueChange", "checked", "handleDownloadVideos", "setTimeout", "dummy<PERSON><PERSON><PERSON>", "id", "thumbnailUrl", "localPath", "handleVideoSelect", "videoPath", "isSelected", "filter", "path", "handleProcessSelected", "length", "videosToProcess", "video", "includes", "handleCreateFinalVideo", "handleProcess", "alert", "dataToSend", "attempt_id", "attempt_details", "selected_video_paths", "use_manual_dialogue", "manual_dialogue_content", "log", "className", "children", "map", "attempt", "onClick", "Date", "created_at", "toLocaleString", "src", "alt", "style", "width", "height", "border", "onError", "e", "type", "onChange", "split", "pop", "rows", "cols", "disabled", "placeholder"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport './ManualVideoCreationPage.css';\n\n\ninterface VideoMetadata {\n    id: string; // Assuming a unique ID for the downloaded video\n    thumbnailUrl: string; // URL for displaying the thumbnail\n    localPath: string; // Path to the downloaded video file (backend needs this)\n    // Add other relevant metadata if needed\n}\ninterface VideoAttempt {\n    id: number; // ID from the backend model is a number\n    status: string;\n    created_at: string;\n    // Add other fields from your Video model that you want to display\n    keywords: string[]; // Example based on model\n    status_message: string; // Example\n}\n\ninterface VideoAttemptDetails {\n    attempt_details: VideoAttempt;\n    downloaded_videos: string[]; // List of relative paths to downloaded videos\n    dialogue: string; // The generated dialogue content\n}\nconst ManualVideoCreationPage: React.FC = () => {\n    const [keywords, setKeywords] = useState<string>('');\n    const [downloadedVideos, setDownloadedVideos] = useState<VideoMetadata[]>([]);\n    const [selectedVideoIds, setSelectedVideoIds] = useState<string[]>([]);\n    const [isDownloading, setIsDownloading] = useState(false);\n    const [isProcessing, setIsProcessing] = useState(false);\n    const [statusMessage, setStatusMessage] = useState('');\n    const [error, setError] = useState<string | null>(null);\n    const [generatedDescription, setGeneratedDescription] = useState<string>('');\n\n    const [attempts, setAttempts] = useState<VideoAttempt[]>([]);\n    const [selectedAttempt, setSelectedAttempt] = useState<VideoAttemptDetails | null>(null);\n    const [loadingAttempts, setLoadingAttempts] = useState<boolean>(true);\n    const [loadingDetails, setLoadingDetails] = useState<boolean>(false);\n    const [selectedVideos, setSelectedVideos] = useState<string[]>([]);\n    const [manualDialogue, setManualDialogue] = useState<string>('');\n    const [useManualDialogue, setUseManualDialogue] = useState<boolean>(false);\n\n    // Fetch video creation attempts when the component mounts\n    useEffect(() => {\n        const fetchAttempts = async () => {\n            try {\n                setLoadingAttempts(true);\n                setError(null); // Clear previous errors\n                // Replace with your actual backend URL for listing attempts\n                const response = await fetch('/api/video-attempts/');\n                    if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n                const data = await response.json();\n                setAttempts(data);\n                setLoadingAttempts(false);\n            } catch (err: any) { // Use any for catch error type for broader compatibility\n                console.error(\"Error fetching video attempts:\", err);\n                    setError(`Failed to load video attempts: ${err.message}`);\n                setLoadingAttempts(false);\n            }\n        };\n\n        fetchAttempts();\n    }, []); // Empty dependency array means this effect runs once on mount\n\n       // Fetch details when an attempt is selected\n    const handleSelectAttempt = async (attemptId: number) => {\n        try {\n            setLoadingDetails(true);\n            setError(null); // Clear previous errors\n            // Replace with your actual backend URL for attempt details\n            const response = await fetch(`/api/video-attempts/${attemptId}/`);\n                if (!response.ok) {\n                    throw new Error(`HTTP error! status: ${response.status}`);\n                }\n            const data = await response.json();\n            setSelectedAttempt(data);\n            // Initialize selected videos with all downloaded videos by default,\n            // or an empty array depending on desired default behavior\n            setSelectedVideos(data.downloaded_videos);\n            setManualDialogue(data.dialogue); // Load existing dialogue for manual tweaking\n            setUseManualDialogue(false); // Default to not using manual dialogue initially\n            setLoadingDetails(false);\n        } catch (err: any) { // Use any for catch error type for broader compatibility\n            console.error(`Error fetching details for attempt ${attemptId}:`, err);\n            setError(`Failed to load details for attempt ${attemptId}: ${err.message}`);\n            setLoadingDetails(false);\n            setSelectedAttempt(null); // Clear selected attempt on error\n        }\n    };\n\n       // Handle dialogue text area changes\n    const handleDialogueChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n        setManualDialogue(event.target.value);\n    };\n\n    // Handle radio button/checkbox for manual dialogue use\n    const handleUseManualDialogueChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n        setUseManualDialogue(event.target.checked);\n    };\n\n\n    // Step 1: Initiate Video Download (Requires new backend endpoint)\n    const handleDownloadVideos = async () => {\n        setIsDownloading(true);\n        setStatusMessage('Downloading videos...');\n        setError(null);\n        setDownloadedVideos([]); // Clear previous downloads\n\n        // TODO: Call your backend endpoint to initiate download\n        // Example: const response = await fetch('/api/manual/download-videos', { method: 'POST', body: JSON.stringify({ keywords }) });\n        // const data = await response.json(); // Assuming backend returns list of downloaded videos metadata\n\n        // Placeholder for simulating download\n        setTimeout(() => {\n             const dummyVideos: VideoMetadata[] = [\n                 { id: 'vid1', thumbnailUrl: 'placeholder.jpg', localPath: '/path/to/vid1.mp4' },\n                 { id: 'vid2', thumbnailUrl: 'placeholder2.jpg', localPath: '/path/to/vid2.mp4' },\n                 // Add more dummy videos or replace with actual backend data\n             ];\n             setDownloadedVideos(dummyVideos);\n             setStatusMessage('Videos downloaded. Please select 10.');\n             setIsDownloading(false);\n         }, 2000); // Simulate network request\n    };\n\n    // Handle checkbox changes for video selection - THIS IS THE CORRECT FUNCTION\n    const handleVideoSelect = (videoPath: string, isSelected: boolean) => {\n        if (isSelected) {\n            setSelectedVideos([...selectedVideos, videoPath]);\n        } else {\n            setSelectedVideos(selectedVideos.filter(path => path !== videoPath));\n        }\n    };\n\n    // Step 2: Process Selected Videos and Get Analysis (Requires new backend endpoint)\n    const handleProcessSelected = async () => {\n         if (selectedVideoIds.length !== 10) {\n             setError('Please select exactly 10 videos.');\n             return;\n         }\n\n         setIsProcessing(true);\n         setStatusMessage('Processing and analyzing selected videos...');\n         setError(null);\n         setGeneratedDescription(''); // Clear previous description\n\n         // Find the full video metadata for selected IDs\n         const videosToProcess = downloadedVideos.filter(video => selectedVideoIds.includes(video.id));\n\n         // TODO: Call your backend endpoint to process selected videos and get analysis\n         // Example: const response = await fetch('/api/manual/process-analyze', { method: 'POST', body: JSON.stringify({ videos: videosToProcess }) });\n         // const data = await response.json(); // Assuming backend returns generated description\n\n         // Placeholder for simulating processing and analysis\n         setTimeout(() => {\n             setGeneratedDescription('AI-generated visual description based on your selected videos...'); // Replace with actual analysis\n             setStatusMessage('Analysis complete. Review description and create final video.');\n             setIsProcessing(false);\n         }, 3000); // Simulate processing time\n    };\n\n    // Step 3: Create Final Video (Requires new backend endpoint or reuse/modify existing)\n    const handleCreateFinalVideo = async () => {\n        // This step would use the selected videos and the generated description\n        // to proceed with dialogue generation, stitching, etc.\n        setStatusMessage('Creating final video...');\n        // TODO: Call your backend endpoint to create the final video\n        // This might be a modified version of your existing generation endpoint\n\n        // Placeholder\n        setTimeout(() => {\n            setStatusMessage('Final video creation process started.');\n            // Redirect or show link to the final video\n        }, 1000); // Simulate start\n    };\n\n        // Handle the Process button click - implement backend call later\n    const handleProcess = async () => { // Made async to await fetch call\n        if (!selectedAttempt) {\n            alert(\"Please select a video attempt first.\");\n            return;\n        }\n        if (selectedVideos.length === 0) {\n                alert(\"Please select at least one video.\");\n                return;\n        }\n\n        const dataToSend = {\n            attempt_id: selectedAttempt.attempt_details.id,\n            selected_video_paths: selectedVideos,\n            use_manual_dialogue: useManualDialogue,\n            manual_dialogue_content: useManualDialogue ? manualDialogue : selectedAttempt.dialogue, // Send generated dialogue if not manual\n            // We'll also need to send information about which analysis results to keep/regenerate\n            // Based on your requirement, we only need visual description from Gemini for selected videos\n            // This part of backend logic needs to be implemented.\n        };\n\n        console.log(\"Process button clicked with data:\", dataToSend);\n\n        // TODO: Implement the backend API call to trigger manual processing\n        // Example using fetch:\n        /*\n        try {\n            const response = await fetch('/api/process-manual-video/', { // Replace with your actual endpoint\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    // Include CSRF token for Django if needed\n                    // 'X-CSRFToken': getCsrfToken(), // You'll need a function to get the CSRF token\n                },\n                body: JSON.stringify(dataToSend),\n            });\n\n            if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`);\n            }\n\n            const result = await response.json();\n            console.log(\"Manual processing initiated:\", result);\n            alert(\"Manual processing initiated successfully!\");\n            // You might want to update the UI to show processing status\n\n        } catch (err: any) {\n                console.error(\"Error during manual processing:\", err);\n                alert(`Failed to initiate manual processing: ${err.message}`);\n        }\n        */\n            alert(\"Processing logic needs to be implemented on the backend and connected here.\"); // Keep the alert for now\n\n    };\n\n    return (\n        <div className=\"manual-creation-container\">\n            <h1>Manual Video Creation Fallback</h1>\n\n            {loadingAttempts && <p>Loading video attempts...</p>}\n            {error && <p className=\"error-message\">Error: {error}</p>}\n\n            {!loadingAttempts && !error && (\n                <div className=\"attempts-list\">\n                    <h2>Previous Creation Attempts</h2>\n                    {attempts.length === 0 ? (\n                        <p>No previous attempts found.</p>\n                    ) : (\n                        <ul>\n                            {attempts.map(attempt => (\n                                <li key={attempt.id} onClick={() => handleSelectAttempt(attempt.id)} className={selectedAttempt?.attempt_details.id === attempt.id ? 'selected' : ''}>\n                                    Attempt ID: {attempt.id} - Status: {attempt.status} - Created: {new Date(attempt.created_at).toLocaleString()}\n                                    {/* Display other relevant attempt info */}\n                                </li>\n                            ))}\n                        </ul>\n                    )}\n                </div>\n            )}\n\n            {loadingDetails && <p>Loading attempt details...</p>}\n\n            {selectedAttempt && (\n                <div className=\"attempt-details\">\n                    <h2>Details for Attempt {selectedAttempt.attempt_details.id}</h2>\n\n                    <div className=\"downloaded-videos\">\n                        <h3>Downloaded Videos ({selectedVideos.length} selected)</h3> {/* Added selected count */}\n                        <div className=\"video-thumbnails\">\n                            {selectedAttempt.downloaded_videos.length === 0 ? (\n                                <p>No downloaded videos found for this attempt.</p>\n                            ) : (\n                                selectedAttempt.downloaded_videos.map(videoPath => (\n                                    <div key={videoPath} className={`video-thumbnail-item ${selectedVideos.includes(videoPath) ? 'selected-thumbnail' : ''}`}> {/* Added class for styling selected */}\n                                        {/* Construct the URL to the video file */}\n                                        {/* Assuming your Django MEDIA_URL is '/media/' */}\n                                        <img\n                                            src={`/media/${videoPath}`}\n                                            alt={`Video ${videoPath}`} // More descriptive alt text\n                                            // You might need a proper thumbnail generation logic\n                                            // For now, a generic image or a frame from the video could be used\n                                            // This is a placeholder, replace with actual thumbnail logic if available\n                                            style={{ width: '100px', height: 'auto', border: '1px solid #ccc' }} // Added border\n                                            onError={(e) => { (e.target as HTMLImageElement).src = '/path/to/placeholder-image.png'; }} // Add fallback for broken images\n                                        />\n                                        <br />\n                                        <input\n                                            type=\"checkbox\"\n                                            checked={selectedVideos.includes(videoPath)}\n                                            onChange={(e) => handleVideoSelect(videoPath, e.target.checked)}\n                                        />\n                                        {/* Display filename or part of the path */}\n                                        <span>{videoPath.split('/').pop()}</span> {/* Display just the filename */}\n                                    </div>\n                                ))\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"dialogue-section\">\n                        <h3>Dialogue</h3>\n                         <label>\n                             <input\n                                 type=\"checkbox\"\n                                 checked={useManualDialogue}\n                                 onChange={handleUseManualDialogueChange}\n                             />\n                             Manually edit dialogue\n                         </label>\n                        <textarea\n                            value={useManualDialogue ? manualDialogue : selectedAttempt.dialogue}\n                            onChange={handleDialogueChange}\n                            rows={10}\n                            cols={80}\n                            disabled={!useManualDialogue} // Disable if not using manual dialogue\n                            placeholder=\"Enter manual dialogue here or uncheck to use generated dialogue\" // Added placeholder\n                        />\n                         {!useManualDialogue && <p>Using automatically generated dialogue.</p>} {/* Indicate which dialogue is used */}\n                    </div>\n\n                    <button onClick={handleProcess}>Process Manually Selected Videos</button>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default ManualVideoCreationPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAuBvC,KAAM,CAAAC,uBAAiC,CAAGA,CAAA,GAAM,CAC5C,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGP,QAAQ,CAAS,EAAE,CAAC,CACpD,KAAM,CAACQ,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGT,QAAQ,CAAkB,EAAE,CAAC,CAC7E,KAAM,CAACU,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGX,QAAQ,CAAW,EAAE,CAAC,CACtE,KAAM,CAACY,aAAa,CAAEC,gBAAgB,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACc,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACgB,aAAa,CAAEC,gBAAgB,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACoB,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrB,QAAQ,CAAS,EAAE,CAAC,CAE5E,KAAM,CAACsB,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAiB,EAAE,CAAC,CAC5D,KAAM,CAACwB,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAA6B,IAAI,CAAC,CACxF,KAAM,CAAC0B,eAAe,CAAEC,kBAAkB,CAAC,CAAG3B,QAAQ,CAAU,IAAI,CAAC,CACrE,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAU,KAAK,CAAC,CACpE,KAAM,CAAC8B,cAAc,CAAEC,iBAAiB,CAAC,CAAG/B,QAAQ,CAAW,EAAE,CAAC,CAClE,KAAM,CAACgC,cAAc,CAAEC,iBAAiB,CAAC,CAAGjC,QAAQ,CAAS,EAAE,CAAC,CAChE,KAAM,CAACkC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnC,QAAQ,CAAU,KAAK,CAAC,CAE1E;AACAD,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAqC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACAT,kBAAkB,CAAC,IAAI,CAAC,CACxBR,QAAQ,CAAC,IAAI,CAAC,CAAE;AAChB;AACA,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sBAAsB,CAAC,CAChD,GAAI,CAACD,QAAQ,CAACE,EAAE,CAAE,CAClB,KAAM,IAAI,CAAAC,KAAK,wBAAAC,MAAA,CAAwBJ,QAAQ,CAACK,MAAM,CAAE,CAAC,CAC7D,CACA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAClCrB,WAAW,CAACoB,IAAI,CAAC,CACjBhB,kBAAkB,CAAC,KAAK,CAAC,CAC7B,CAAE,MAAOkB,GAAQ,CAAE,CAAE;AACjBC,OAAO,CAAC5B,KAAK,CAAC,gCAAgC,CAAE2B,GAAG,CAAC,CAChD1B,QAAQ,mCAAAsB,MAAA,CAAmCI,GAAG,CAACE,OAAO,CAAE,CAAC,CAC7DpB,kBAAkB,CAAC,KAAK,CAAC,CAC7B,CACJ,CAAC,CAEDS,aAAa,CAAC,CAAC,CACnB,CAAC,CAAE,EAAE,CAAC,CAAE;AAEL;AACH,KAAM,CAAAY,mBAAmB,CAAG,KAAO,CAAAC,SAAiB,EAAK,CACrD,GAAI,CACApB,iBAAiB,CAAC,IAAI,CAAC,CACvBV,QAAQ,CAAC,IAAI,CAAC,CAAE;AAChB;AACA,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAC,KAAK,wBAAAG,MAAA,CAAwBQ,SAAS,KAAG,CAAC,CAC7D,GAAI,CAACZ,QAAQ,CAACE,EAAE,CAAE,CACd,KAAM,IAAI,CAAAC,KAAK,wBAAAC,MAAA,CAAwBJ,QAAQ,CAACK,MAAM,CAAE,CAAC,CAC7D,CACJ,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAN,QAAQ,CAACO,IAAI,CAAC,CAAC,CAClCnB,kBAAkB,CAACkB,IAAI,CAAC,CACxB;AACA;AACAZ,iBAAiB,CAACY,IAAI,CAACO,iBAAiB,CAAC,CACzCjB,iBAAiB,CAACU,IAAI,CAACQ,QAAQ,CAAC,CAAE;AAClChB,oBAAoB,CAAC,KAAK,CAAC,CAAE;AAC7BN,iBAAiB,CAAC,KAAK,CAAC,CAC5B,CAAE,MAAOgB,GAAQ,CAAE,CAAE;AACjBC,OAAO,CAAC5B,KAAK,uCAAAuB,MAAA,CAAuCQ,SAAS,MAAKJ,GAAG,CAAC,CACtE1B,QAAQ,uCAAAsB,MAAA,CAAuCQ,SAAS,OAAAR,MAAA,CAAKI,GAAG,CAACE,OAAO,CAAE,CAAC,CAC3ElB,iBAAiB,CAAC,KAAK,CAAC,CACxBJ,kBAAkB,CAAC,IAAI,CAAC,CAAE;AAC9B,CACJ,CAAC,CAEE;AACH,KAAM,CAAA2B,oBAAoB,CAAIC,KAA6C,EAAK,CAC5EpB,iBAAiB,CAACoB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAC,6BAA6B,CAAIH,KAA0C,EAAK,CAClFlB,oBAAoB,CAACkB,KAAK,CAACC,MAAM,CAACG,OAAO,CAAC,CAC9C,CAAC,CAGD;AACA,KAAM,CAAAC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACrC7C,gBAAgB,CAAC,IAAI,CAAC,CACtBI,gBAAgB,CAAC,uBAAuB,CAAC,CACzCE,QAAQ,CAAC,IAAI,CAAC,CACdV,mBAAmB,CAAC,EAAE,CAAC,CAAE;AAEzB;AACA;AACA;AAEA;AACAkD,UAAU,CAAC,IAAM,CACZ,KAAM,CAAAC,WAA4B,CAAG,CACjC,CAAEC,EAAE,CAAE,MAAM,CAAEC,YAAY,CAAE,iBAAiB,CAAEC,SAAS,CAAE,mBAAoB,CAAC,CAC/E,CAAEF,EAAE,CAAE,MAAM,CAAEC,YAAY,CAAE,kBAAkB,CAAEC,SAAS,CAAE,mBAAoB,CAC/E;AAAA,CACH,CACDtD,mBAAmB,CAACmD,WAAW,CAAC,CAChC3C,gBAAgB,CAAC,sCAAsC,CAAC,CACxDJ,gBAAgB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CAAE;AACf,CAAC,CAED;AACA,KAAM,CAAAmD,iBAAiB,CAAGA,CAACC,SAAiB,CAAEC,UAAmB,GAAK,CAClE,GAAIA,UAAU,CAAE,CACZnC,iBAAiB,CAAC,CAAC,GAAGD,cAAc,CAAEmC,SAAS,CAAC,CAAC,CACrD,CAAC,IAAM,CACHlC,iBAAiB,CAACD,cAAc,CAACqC,MAAM,CAACC,IAAI,EAAIA,IAAI,GAAKH,SAAS,CAAC,CAAC,CACxE,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI3D,gBAAgB,CAAC4D,MAAM,GAAK,EAAE,CAAE,CAChCnD,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,OACJ,CAEAJ,eAAe,CAAC,IAAI,CAAC,CACrBE,gBAAgB,CAAC,6CAA6C,CAAC,CAC/DE,QAAQ,CAAC,IAAI,CAAC,CACdE,uBAAuB,CAAC,EAAE,CAAC,CAAE;AAE7B;AACA,KAAM,CAAAkD,eAAe,CAAG/D,gBAAgB,CAAC2D,MAAM,CAACK,KAAK,EAAI9D,gBAAgB,CAAC+D,QAAQ,CAACD,KAAK,CAACX,EAAE,CAAC,CAAC,CAE7F;AACA;AACA;AAEA;AACAF,UAAU,CAAC,IAAM,CACbtC,uBAAuB,CAAC,kEAAkE,CAAC,CAAE;AAC7FJ,gBAAgB,CAAC,+DAA+D,CAAC,CACjFF,eAAe,CAAC,KAAK,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CAAE;AACf,CAAC,CAED;AACA,KAAM,CAAA2D,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACvC;AACA;AACAzD,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C;AACA;AAEA;AACA0C,UAAU,CAAC,IAAM,CACb1C,gBAAgB,CAAC,uCAAuC,CAAC,CACzD;AACJ,CAAC,CAAE,IAAI,CAAC,CAAE;AACd,CAAC,CAEG;AACJ,KAAM,CAAA0D,aAAa,CAAG,KAAAA,CAAA,GAAY,CAAE;AAChC,GAAI,CAACnD,eAAe,CAAE,CAClBoD,KAAK,CAAC,sCAAsC,CAAC,CAC7C,OACJ,CACA,GAAI9C,cAAc,CAACwC,MAAM,GAAK,CAAC,CAAE,CACzBM,KAAK,CAAC,mCAAmC,CAAC,CAC1C,OACR,CAEA,KAAM,CAAAC,UAAU,CAAG,CACfC,UAAU,CAAEtD,eAAe,CAACuD,eAAe,CAAClB,EAAE,CAC9CmB,oBAAoB,CAAElD,cAAc,CACpCmD,mBAAmB,CAAE/C,iBAAiB,CACtCgD,uBAAuB,CAAEhD,iBAAiB,CAAGF,cAAc,CAAGR,eAAe,CAAC2B,QAAU;AACxF;AACA;AACA;AACJ,CAAC,CAEDL,OAAO,CAACqC,GAAG,CAAC,mCAAmC,CAAEN,UAAU,CAAC,CAE5D;AACA;AACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UACYD,KAAK,CAAC,6EAA6E,CAAC,CAAE;AAE9F,CAAC,CAED,mBACIxE,KAAA,QAAKgF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCnF,IAAA,OAAAmF,QAAA,CAAI,gCAA8B,CAAI,CAAC,CAEtC3D,eAAe,eAAIxB,IAAA,MAAAmF,QAAA,CAAG,2BAAyB,CAAG,CAAC,CACnDnE,KAAK,eAAId,KAAA,MAAGgF,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,SAAO,CAACnE,KAAK,EAAI,CAAC,CAExD,CAACQ,eAAe,EAAI,CAACR,KAAK,eACvBd,KAAA,QAAKgF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC1BnF,IAAA,OAAAmF,QAAA,CAAI,4BAA0B,CAAI,CAAC,CAClC/D,QAAQ,CAACgD,MAAM,GAAK,CAAC,cAClBpE,IAAA,MAAAmF,QAAA,CAAG,6BAA2B,CAAG,CAAC,cAElCnF,IAAA,OAAAmF,QAAA,CACK/D,QAAQ,CAACgE,GAAG,CAACC,OAAO,eACjBnF,KAAA,OAAqBoF,OAAO,CAAEA,CAAA,GAAMxC,mBAAmB,CAACuC,OAAO,CAAC1B,EAAE,CAAE,CAACuB,SAAS,CAAE,CAAA5D,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuD,eAAe,CAAClB,EAAE,IAAK0B,OAAO,CAAC1B,EAAE,CAAG,UAAU,CAAG,EAAG,CAAAwB,QAAA,EAAC,cACtI,CAACE,OAAO,CAAC1B,EAAE,CAAC,aAAW,CAAC0B,OAAO,CAAC7C,MAAM,CAAC,cAAY,CAAC,GAAI,CAAA+C,IAAI,CAACF,OAAO,CAACG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,GADxGJ,OAAO,CAAC1B,EAGb,CACP,CAAC,CACF,CACP,EACA,CACR,CAEAjC,cAAc,eAAI1B,IAAA,MAAAmF,QAAA,CAAG,4BAA0B,CAAG,CAAC,CAEnD7D,eAAe,eACZpB,KAAA,QAAKgF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC5BjF,KAAA,OAAAiF,QAAA,EAAI,sBAAoB,CAAC7D,eAAe,CAACuD,eAAe,CAAClB,EAAE,EAAK,CAAC,cAEjEzD,KAAA,QAAKgF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC9BjF,KAAA,OAAAiF,QAAA,EAAI,qBAAmB,CAACvD,cAAc,CAACwC,MAAM,CAAC,YAAU,EAAI,CAAC,IAAC,cAC9DpE,IAAA,QAAKkF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC5B7D,eAAe,CAAC0B,iBAAiB,CAACoB,MAAM,GAAK,CAAC,cAC3CpE,IAAA,MAAAmF,QAAA,CAAG,8CAA4C,CAAG,CAAC,CAEnD7D,eAAe,CAAC0B,iBAAiB,CAACoC,GAAG,CAACrB,SAAS,eAC3C7D,KAAA,QAAqBgF,SAAS,yBAAA3C,MAAA,CAA0BX,cAAc,CAAC2C,QAAQ,CAACR,SAAS,CAAC,CAAG,oBAAoB,CAAG,EAAE,CAAG,CAAAoB,QAAA,EAAC,GAAC,cAGvHnF,IAAA,QACI0F,GAAG,WAAAnD,MAAA,CAAYwB,SAAS,CAAG,CAC3B4B,GAAG,UAAApD,MAAA,CAAWwB,SAAS,CAAI;AAC3B;AACA;AACA;AAAA,CACA6B,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAEC,MAAM,CAAE,MAAM,CAAEC,MAAM,CAAE,gBAAiB,CAAG;AAAA,CACrEC,OAAO,CAAGC,CAAC,EAAK,CAAGA,CAAC,CAAC7C,MAAM,CAAsBsC,GAAG,CAAG,gCAAgC,CAAE,CAAG;AAAA,CAC/F,CAAC,cACF1F,IAAA,QAAK,CAAC,cACNA,IAAA,UACIkG,IAAI,CAAC,UAAU,CACf3C,OAAO,CAAE3B,cAAc,CAAC2C,QAAQ,CAACR,SAAS,CAAE,CAC5CoC,QAAQ,CAAGF,CAAC,EAAKnC,iBAAiB,CAACC,SAAS,CAAEkC,CAAC,CAAC7C,MAAM,CAACG,OAAO,CAAE,CACnE,CAAC,cAEFvD,IAAA,SAAAmF,QAAA,CAAOpB,SAAS,CAACqC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAO,CAAC,IAAC,GAnBpCtC,SAoBL,CACR,CACJ,CACA,CAAC,EACL,CAAC,cAEN7D,KAAA,QAAKgF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC7BnF,IAAA,OAAAmF,QAAA,CAAI,UAAQ,CAAI,CAAC,cAChBjF,KAAA,UAAAiF,QAAA,eACInF,IAAA,UACIkG,IAAI,CAAC,UAAU,CACf3C,OAAO,CAAEvB,iBAAkB,CAC3BmE,QAAQ,CAAE7C,6BAA8B,CAC3C,CAAC,yBAEN,EAAO,CAAC,cACTtD,IAAA,aACIqD,KAAK,CAAErB,iBAAiB,CAAGF,cAAc,CAAGR,eAAe,CAAC2B,QAAS,CACrEkD,QAAQ,CAAEjD,oBAAqB,CAC/BoD,IAAI,CAAE,EAAG,CACTC,IAAI,CAAE,EAAG,CACTC,QAAQ,CAAE,CAACxE,iBAAmB;AAAA,CAC9ByE,WAAW,CAAC,iEAAkE;AAAA,CACjF,CAAC,CACA,CAACzE,iBAAiB,eAAIhC,IAAA,MAAAmF,QAAA,CAAG,yCAAuC,CAAG,CAAC,CAAC,GAAC,EACvE,CAAC,cAENnF,IAAA,WAAQsF,OAAO,CAAEb,aAAc,CAAAU,QAAA,CAAC,kCAAgC,CAAQ,CAAC,EACxE,CACR,EACA,CAAC,CAEd,CAAC,CAED,cAAe,CAAAhF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}