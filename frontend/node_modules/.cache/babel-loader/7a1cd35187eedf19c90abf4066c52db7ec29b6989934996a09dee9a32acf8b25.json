{"ast": null, "code": "/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n  _numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n  _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n  _selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n  _DEG2RAD = Math.PI / 180,\n  _RAD2DEG = 180 / Math.PI,\n  _sin = Math.sin,\n  _cos = Math.cos,\n  _abs = Math.abs,\n  _sqrt = Math.sqrt,\n  _atan2 = Math.atan2,\n  _largeNum = 1e8,\n  _isString = function _isString(value) {\n    return typeof value === \"string\";\n  },\n  _isNumber = function _isNumber(value) {\n    return typeof value === \"number\";\n  },\n  _isUndefined = function _isUndefined(value) {\n    return typeof value === \"undefined\";\n  },\n  _temp = {},\n  _temp2 = {},\n  _roundingNum = 1e5,\n  _wrapProgress = function _wrapProgress(progress) {\n    return Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum || (progress < 0 ? 0 : 1);\n  },\n  //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n  _round = function _round(value) {\n    return Math.round(value * _roundingNum) / _roundingNum || 0;\n  },\n  _roundPrecise = function _roundPrecise(value) {\n    return Math.round(value * 1e10) / 1e10 || 0;\n  },\n  _splitSegment = function _splitSegment(rawPath, segIndex, i, t) {\n    var segment = rawPath[segIndex],\n      shift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n    if ((shift || !t) && shift + i + 2 < segment.length) {\n      rawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n      segment.splice(0, i + shift);\n      return 1;\n    }\n  },\n  _getSampleIndex = function _getSampleIndex(samples, length, progress) {\n    // slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n    var l = samples.length,\n      i = ~~(progress * l);\n    if (samples[i] > length) {\n      while (--i && samples[i] > length) {}\n      i < 0 && (i = 0);\n    } else {\n      while (samples[++i] < length && i < l) {}\n    }\n    return i < l ? i : l - 1;\n  },\n  _reverseRawPath = function _reverseRawPath(rawPath, skipOuter) {\n    var i = rawPath.length;\n    skipOuter || rawPath.reverse();\n    while (i--) {\n      rawPath[i].reversed || reverseSegment(rawPath[i]);\n    }\n  },\n  _copyMetaData = function _copyMetaData(source, copy) {\n    copy.totalLength = source.totalLength;\n    if (source.samples) {\n      //segment\n      copy.samples = source.samples.slice(0);\n      copy.lookup = source.lookup.slice(0);\n      copy.minLength = source.minLength;\n      copy.resolution = source.resolution;\n    } else if (source.totalPoints) {\n      //rawPath\n      copy.totalPoints = source.totalPoints;\n    }\n    return copy;\n  },\n  //pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n  _appendOrMerge = function _appendOrMerge(rawPath, segment) {\n    var index = rawPath.length,\n      prevSeg = rawPath[index - 1] || [],\n      l = prevSeg.length;\n    if (index && segment[0] === prevSeg[l - 2] && segment[1] === prevSeg[l - 1]) {\n      segment = prevSeg.concat(segment.slice(2));\n      index--;\n    }\n    rawPath[index] = segment;\n  },\n  _bestDistance;\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\n\nexport function getRawPath(value) {\n  value = _isString(value) && _selectorExp.test(value) ? document.querySelector(value) || value : value;\n  var e = value.getAttribute ? value : 0,\n    rawPath;\n  if (e && (value = value.getAttribute(\"d\"))) {\n    //implements caching\n    if (!e._gsPath) {\n      e._gsPath = {};\n    }\n    rawPath = e._gsPath[value];\n    return rawPath && !rawPath._dirty ? rawPath : e._gsPath[value] = stringToRawPath(value);\n  }\n  return !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : _isNumber(value[0]) ? [value] : value;\n} //copies a RawPath WITHOUT the length meta data (for speed)\n\nexport function copyRawPath(rawPath) {\n  var a = [],\n    i = 0;\n  for (; i < rawPath.length; i++) {\n    a[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n  }\n  return _copyMetaData(rawPath, a);\n}\nexport function reverseSegment(segment) {\n  var i = 0,\n    y;\n  segment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\n  for (; i < segment.length; i += 2) {\n    y = segment[i];\n    segment[i] = segment[i + 1];\n    segment[i + 1] = y;\n  }\n  segment.reversed = !segment.reversed;\n}\nvar _createPath = function _createPath(e, ignore) {\n    var path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n      attr = [].slice.call(e.attributes),\n      i = attr.length,\n      name;\n    ignore = \",\" + ignore + \",\";\n    while (--i > -1) {\n      name = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\n      if (ignore.indexOf(\",\" + name + \",\") < 0) {\n        path.setAttributeNS(null, name, attr[i].nodeValue);\n      }\n    }\n    return path;\n  },\n  _typeAttrs = {\n    rect: \"rx,ry,x,y,width,height\",\n    circle: \"r,cx,cy\",\n    ellipse: \"rx,ry,cx,cy\",\n    line: \"x1,x2,y1,y2\"\n  },\n  _attrToObj = function _attrToObj(e, attrs) {\n    var props = attrs ? attrs.split(\",\") : [],\n      obj = {},\n      i = props.length;\n    while (--i > -1) {\n      obj[props[i]] = +e.getAttribute(props[i]) || 0;\n    }\n    return obj;\n  }; //converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\n\nexport function convertToPath(element, swap) {\n  var type = element.tagName.toLowerCase(),\n    circ = 0.552284749831,\n    data,\n    x,\n    y,\n    r,\n    ry,\n    path,\n    rcirc,\n    rycirc,\n    points,\n    w,\n    h,\n    x2,\n    x3,\n    x4,\n    x5,\n    x6,\n    y2,\n    y3,\n    y4,\n    y5,\n    y6,\n    attr;\n  if (type === \"path\" || !element.getBBox) {\n    return element;\n  }\n  path = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n  attr = _attrToObj(element, _typeAttrs[type]);\n  if (type === \"rect\") {\n    r = attr.rx;\n    ry = attr.ry || r;\n    x = attr.x;\n    y = attr.y;\n    w = attr.width - r * 2;\n    h = attr.height - ry * 2;\n    if (r || ry) {\n      //if there are rounded corners, render cubic beziers\n      x2 = x + r * (1 - circ);\n      x3 = x + r;\n      x4 = x3 + w;\n      x5 = x4 + r * circ;\n      x6 = x4 + r;\n      y2 = y + ry * (1 - circ);\n      y3 = y + ry;\n      y4 = y3 + h;\n      y5 = y4 + ry * circ;\n      y6 = y4 + ry;\n      data = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n    } else {\n      data = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + -w + \" v\" + -h + \" h\" + w + \"z\";\n    }\n  } else if (type === \"circle\" || type === \"ellipse\") {\n    if (type === \"circle\") {\n      r = ry = attr.r;\n      rycirc = r * circ;\n    } else {\n      r = attr.rx;\n      ry = attr.ry;\n      rycirc = ry * circ;\n    }\n    x = attr.cx;\n    y = attr.cy;\n    rcirc = r * circ;\n    data = \"M\" + (x + r) + \",\" + y + \" C\" + [x + r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n  } else if (type === \"line\") {\n    data = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n  } else if (type === \"polyline\" || type === \"polygon\") {\n    points = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n    x = points.shift();\n    y = points.shift();\n    data = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n    if (type === \"polygon\") {\n      data += \",\" + x + \",\" + y + \"z\";\n    }\n  }\n  path.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n  if (swap && element.parentNode) {\n    element.parentNode.insertBefore(path, element);\n    element.parentNode.removeChild(element);\n  }\n  return path;\n} //returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\n\nexport function getRotationAtProgress(rawPath, progress) {\n  var d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n  return getRotationAtBezierT(d.segment, d.i, d.t);\n}\nfunction getRotationAtBezierT(segment, i, t) {\n  var a = segment[i],\n    b = segment[i + 2],\n    c = segment[i + 4],\n    x;\n  a += (b - a) * t;\n  b += (c - b) * t;\n  a += (b - a) * t;\n  x = b + (c + (segment[i + 6] - c) * t - b) * t - a;\n  a = segment[i + 1];\n  b = segment[i + 3];\n  c = segment[i + 5];\n  a += (b - a) * t;\n  b += (c - b) * t;\n  a += (b - a) * t;\n  return _round(_atan2(b + (c + (segment[i + 7] - c) * t - b) * t - a, x) * _RAD2DEG);\n}\nexport function sliceRawPath(rawPath, start, end) {\n  end = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\n  start = _roundPrecise(start) || 0;\n  var loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n    path = copyRawPath(rawPath);\n  if (start > end) {\n    start = 1 - start;\n    end = 1 - end;\n    _reverseRawPath(path);\n    path.totalLength = 0;\n  }\n  if (start < 0 || end < 0) {\n    var offset = Math.abs(~~Math.min(start, end)) + 1;\n    start += offset;\n    end += offset;\n  }\n  path.totalLength || cacheRawPathMeasurements(path);\n  var wrap = end > 1,\n    s = getProgressData(path, start, _temp, true),\n    e = getProgressData(path, end, _temp2),\n    eSeg = e.segment,\n    sSeg = s.segment,\n    eSegIndex = e.segIndex,\n    sSegIndex = s.segIndex,\n    ei = e.i,\n    si = s.i,\n    sameSegment = sSegIndex === eSegIndex,\n    sameBezier = ei === si && sameSegment,\n    wrapsBehind,\n    sShift,\n    eShift,\n    i,\n    copy,\n    totalSegments,\n    l,\n    j;\n  if (wrap || loops) {\n    wrapsBehind = eSegIndex < sSegIndex || sameSegment && ei < si || sameBezier && e.t < s.t;\n    if (_splitSegment(path, sSegIndex, si, s.t)) {\n      sSegIndex++;\n      if (!wrapsBehind) {\n        eSegIndex++;\n        if (sameBezier) {\n          e.t = (e.t - s.t) / (1 - s.t);\n          ei = 0;\n        } else if (sameSegment) {\n          ei -= si;\n        }\n      }\n    }\n    if (Math.abs(1 - (end - start)) < 1e-5) {\n      eSegIndex = sSegIndex - 1;\n    } else if (!e.t && eSegIndex) {\n      eSegIndex--;\n    } else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n      sSegIndex++;\n    }\n    if (s.t === 1) {\n      sSegIndex = (sSegIndex + 1) % path.length;\n    }\n    copy = [];\n    totalSegments = path.length;\n    l = 1 + totalSegments * loops;\n    j = sSegIndex;\n    l += (totalSegments - sSegIndex + eSegIndex) % totalSegments;\n    for (i = 0; i < l; i++) {\n      _appendOrMerge(copy, path[j++ % totalSegments]);\n    }\n    path = copy;\n  } else {\n    eShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n    if (start !== end) {\n      sShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n      sameSegment && (eShift += sShift);\n      eSeg.splice(ei + eShift + 2);\n      (sShift || si) && sSeg.splice(0, si + sShift);\n      i = path.length;\n      while (i--) {\n        //chop off any extra segments\n        (i < sSegIndex || i > eSegIndex) && path.splice(i, 1);\n      }\n    } else {\n      eSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\n      ei += eShift;\n      s = eSeg[ei];\n      e = eSeg[ei + 1];\n      eSeg.length = eSeg.totalLength = 0;\n      eSeg.totalPoints = path.totalPoints = 8;\n      eSeg.push(s, e, s, e, s, e, s, e);\n    }\n  }\n  path.totalLength = 0;\n  return path;\n} //measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\n\nfunction measureSegment(segment, startIndex, bezierQty) {\n  startIndex = startIndex || 0;\n  if (!segment.samples) {\n    segment.samples = [];\n    segment.lookup = [];\n  }\n  var resolution = ~~segment.resolution || 12,\n    inc = 1 / resolution,\n    endIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n    x1 = segment[startIndex],\n    y1 = segment[startIndex + 1],\n    samplesIndex = startIndex ? startIndex / 6 * resolution : 0,\n    samples = segment.samples,\n    lookup = segment.lookup,\n    min = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n    prevLength = samples[samplesIndex + bezierQty * resolution - 1],\n    length = startIndex ? samples[samplesIndex - 1] : 0,\n    i,\n    j,\n    x4,\n    x3,\n    x2,\n    xd,\n    xd1,\n    y4,\n    y3,\n    y2,\n    yd,\n    yd1,\n    inv,\n    t,\n    lengthIndex,\n    l,\n    segLength;\n  samples.length = lookup.length = 0;\n  for (j = startIndex + 2; j < endIndex; j += 6) {\n    x4 = segment[j + 4] - x1;\n    x3 = segment[j + 2] - x1;\n    x2 = segment[j] - x1;\n    y4 = segment[j + 5] - y1;\n    y3 = segment[j + 3] - y1;\n    y2 = segment[j + 1] - y1;\n    xd = xd1 = yd = yd1 = 0;\n    if (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) {\n      //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n      if (segment.length > 8) {\n        segment.splice(j, 6);\n        j -= 6;\n        endIndex -= 6;\n      }\n    } else {\n      for (i = 1; i <= resolution; i++) {\n        t = inc * i;\n        inv = 1 - t;\n        xd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n        yd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n        l = _sqrt(yd * yd + xd * xd);\n        if (l < min) {\n          min = l;\n        }\n        length += l;\n        samples[samplesIndex++] = length;\n      }\n    }\n    x1 += x4;\n    y1 += y4;\n  }\n  if (prevLength) {\n    prevLength -= length;\n    for (; samplesIndex < samples.length; samplesIndex++) {\n      samples[samplesIndex] += prevLength;\n    }\n  }\n  if (samples.length && min) {\n    segment.totalLength = segLength = samples[samples.length - 1] || 0;\n    segment.minLength = min;\n    if (segLength / min < 9999) {\n      // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n      l = lengthIndex = 0;\n      for (i = 0; i < segLength; i += min) {\n        lookup[l++] = samples[lengthIndex] < i ? ++lengthIndex : lengthIndex;\n      }\n    }\n  } else {\n    segment.totalLength = samples[0] = 0;\n  }\n  return startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n  var pathLength, points, i;\n  for (i = pathLength = points = 0; i < rawPath.length; i++) {\n    rawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\n    points += rawPath[i].length;\n    pathLength += measureSegment(rawPath[i]);\n  }\n  rawPath.totalPoints = points;\n  rawPath.totalLength = pathLength;\n  return rawPath;\n} //divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\n\nexport function subdivideSegment(segment, i, t) {\n  if (t <= 0 || t >= 1) {\n    return 0;\n  }\n  var ax = segment[i],\n    ay = segment[i + 1],\n    cp1x = segment[i + 2],\n    cp1y = segment[i + 3],\n    cp2x = segment[i + 4],\n    cp2y = segment[i + 5],\n    bx = segment[i + 6],\n    by = segment[i + 7],\n    x1a = ax + (cp1x - ax) * t,\n    x2 = cp1x + (cp2x - cp1x) * t,\n    y1a = ay + (cp1y - ay) * t,\n    y2 = cp1y + (cp2y - cp1y) * t,\n    x1 = x1a + (x2 - x1a) * t,\n    y1 = y1a + (y2 - y1a) * t,\n    x2a = cp2x + (bx - cp2x) * t,\n    y2a = cp2y + (by - cp2y) * t;\n  x2 += (x2a - x2) * t;\n  y2 += (y2a - y2) * t;\n  segment.splice(i + 2, 4, _round(x1a),\n  //first control point\n  _round(y1a), _round(x1),\n  //second control point\n  _round(y1), _round(x1 + (x2 - x1) * t),\n  //new fabricated anchor on line\n  _round(y1 + (y2 - y1) * t), _round(x2),\n  //third control point\n  _round(y2), _round(x2a),\n  //fourth control point\n  _round(y2a));\n  segment.samples && segment.samples.splice(i / 6 * segment.resolution | 0, 0, 0, 0, 0, 0, 0, 0);\n  return 6;\n} // returns an object {path, segment, segIndex, i, t}\n\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n  decoratee = decoratee || {};\n  rawPath.totalLength || cacheRawPathMeasurements(rawPath);\n  if (progress < 0 || progress > 1) {\n    progress = _wrapProgress(progress);\n  }\n  var segIndex = 0,\n    segment = rawPath[0],\n    samples,\n    resolution,\n    length,\n    min,\n    max,\n    i,\n    t;\n  if (!progress) {\n    t = i = segIndex = 0;\n    segment = rawPath[0];\n  } else if (progress === 1) {\n    t = 1;\n    segIndex = rawPath.length - 1;\n    segment = rawPath[segIndex];\n    i = segment.length - 8;\n  } else {\n    if (rawPath.length > 1) {\n      //speed optimization: most of the time, there's only one segment so skip the recursion.\n      length = rawPath.totalLength * progress;\n      max = i = 0;\n      while ((max += rawPath[i++].totalLength) < length) {\n        segIndex = i;\n      }\n      segment = rawPath[segIndex];\n      min = max - segment.totalLength;\n      progress = (length - min) / (max - min) || 0;\n    }\n    samples = segment.samples;\n    resolution = segment.resolution; //how many samples per cubic bezier chunk\n\n    length = segment.totalLength * progress;\n    i = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n    min = i ? samples[i - 1] : 0;\n    max = samples[i];\n    if (max < length) {\n      min = max;\n      max = samples[++i];\n    }\n    t = 1 / resolution * ((length - min) / (max - min) + i % resolution);\n    i = ~~(i / resolution) * 6;\n    if (pushToNextIfAtEnd && t === 1) {\n      if (i + 6 < segment.length) {\n        i += 6;\n        t = 0;\n      } else if (segIndex + 1 < rawPath.length) {\n        i = t = 0;\n        segment = rawPath[++segIndex];\n      }\n    }\n  }\n  decoratee.t = t;\n  decoratee.i = i;\n  decoratee.path = rawPath;\n  decoratee.segment = segment;\n  decoratee.segIndex = segIndex;\n  return decoratee;\n}\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n  var segment = rawPath[0],\n    result = point || {},\n    samples,\n    resolution,\n    length,\n    min,\n    max,\n    i,\n    t,\n    a,\n    inv;\n  if (progress < 0 || progress > 1) {\n    progress = _wrapProgress(progress);\n  }\n  segment.lookup || cacheRawPathMeasurements(rawPath);\n  if (rawPath.length > 1) {\n    //speed optimization: most of the time, there's only one segment so skip the recursion.\n    length = rawPath.totalLength * progress;\n    max = i = 0;\n    while ((max += rawPath[i++].totalLength) < length) {\n      segment = rawPath[i];\n    }\n    min = max - segment.totalLength;\n    progress = (length - min) / (max - min) || 0;\n  }\n  samples = segment.samples;\n  resolution = segment.resolution;\n  length = segment.totalLength * progress;\n  i = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n  min = i ? samples[i - 1] : 0;\n  max = samples[i];\n  if (max < length) {\n    min = max;\n    max = samples[++i];\n  }\n  t = 1 / resolution * ((length - min) / (max - min) + i % resolution) || 0;\n  inv = 1 - t;\n  i = ~~(i / resolution) * 6;\n  a = segment[i];\n  result.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n  result.y = _round((t * t * (segment[i + 7] - (a = segment[i + 1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n  if (includeAngle) {\n    result.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n  }\n  return result;\n} //applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\n\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n  var j = rawPath.length,\n    segment,\n    l,\n    i,\n    x,\n    y;\n  while (--j > -1) {\n    segment = rawPath[j];\n    l = segment.length;\n    for (i = 0; i < l; i += 2) {\n      x = segment[i];\n      y = segment[i + 1];\n      segment[i] = x * a + y * c + tx;\n      segment[i + 1] = x * b + y * d + ty;\n    }\n  }\n  rawPath._dirty = 1;\n  return rawPath;\n} // translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\n\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n  if (lastX === x && lastY === y) {\n    return;\n  }\n  rx = _abs(rx);\n  ry = _abs(ry);\n  var angleRad = angle % 360 * _DEG2RAD,\n    cosAngle = _cos(angleRad),\n    sinAngle = _sin(angleRad),\n    PI = Math.PI,\n    TWOPI = PI * 2,\n    dx2 = (lastX - x) / 2,\n    dy2 = (lastY - y) / 2,\n    x1 = cosAngle * dx2 + sinAngle * dy2,\n    y1 = -sinAngle * dx2 + cosAngle * dy2,\n    x1_sq = x1 * x1,\n    y1_sq = y1 * y1,\n    radiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n  if (radiiCheck > 1) {\n    rx = _sqrt(radiiCheck) * rx;\n    ry = _sqrt(radiiCheck) * ry;\n  }\n  var rx_sq = rx * rx,\n    ry_sq = ry * ry,\n    sq = (rx_sq * ry_sq - rx_sq * y1_sq - ry_sq * x1_sq) / (rx_sq * y1_sq + ry_sq * x1_sq);\n  if (sq < 0) {\n    sq = 0;\n  }\n  var coef = (largeArcFlag === sweepFlag ? -1 : 1) * _sqrt(sq),\n    cx1 = coef * (rx * y1 / ry),\n    cy1 = coef * -(ry * x1 / rx),\n    sx2 = (lastX + x) / 2,\n    sy2 = (lastY + y) / 2,\n    cx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n    cy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n    ux = (x1 - cx1) / rx,\n    uy = (y1 - cy1) / ry,\n    vx = (-x1 - cx1) / rx,\n    vy = (-y1 - cy1) / ry,\n    temp = ux * ux + uy * uy,\n    angleStart = (uy < 0 ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n    angleExtent = (ux * vy - uy * vx < 0 ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n  isNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\n  if (!sweepFlag && angleExtent > 0) {\n    angleExtent -= TWOPI;\n  } else if (sweepFlag && angleExtent < 0) {\n    angleExtent += TWOPI;\n  }\n  angleStart %= TWOPI;\n  angleExtent %= TWOPI;\n  var segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n    rawPath = [],\n    angleIncrement = angleExtent / segments,\n    controlLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n    ma = cosAngle * rx,\n    mb = sinAngle * rx,\n    mc = sinAngle * -ry,\n    md = cosAngle * ry,\n    i;\n  for (i = 0; i < segments; i++) {\n    angle = angleStart + i * angleIncrement;\n    x1 = _cos(angle);\n    y1 = _sin(angle);\n    ux = _cos(angle += angleIncrement);\n    uy = _sin(angle);\n    rawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n  } //now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\n  for (i = 0; i < rawPath.length; i += 2) {\n    x1 = rawPath[i];\n    y1 = rawPath[i + 1];\n    rawPath[i] = x1 * ma + y1 * mc + cx;\n    rawPath[i + 1] = x1 * mb + y1 * md + cy;\n  }\n  rawPath[i - 2] = x; //always set the end to exactly where it's supposed to be\n\n  rawPath[i - 1] = y;\n  return rawPath;\n} //Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\n\nexport function stringToRawPath(d) {\n  var a = (d + \"\").replace(_scientific, function (m) {\n      var n = +m;\n      return n < 0.0001 && n > -0.0001 ? 0 : n;\n    }).match(_svgPathExp) || [],\n    //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n    path = [],\n    relativeX = 0,\n    relativeY = 0,\n    twoThirds = 2 / 3,\n    elements = a.length,\n    points = 0,\n    errorMessage = \"ERROR: malformed path: \" + d,\n    i,\n    j,\n    x,\n    y,\n    command,\n    isRelative,\n    segment,\n    startX,\n    startY,\n    difX,\n    difY,\n    beziers,\n    prevCommand,\n    flag1,\n    flag2,\n    line = function line(sx, sy, ex, ey) {\n      difX = (ex - sx) / 3;\n      difY = (ey - sy) / 3;\n      segment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n    };\n  if (!d || !isNaN(a[0]) || isNaN(a[1])) {\n    console.log(errorMessage);\n    return path;\n  }\n  for (i = 0; i < elements; i++) {\n    prevCommand = command;\n    if (isNaN(a[i])) {\n      command = a[i].toUpperCase();\n      isRelative = command !== a[i]; //lower case means relative\n    } else {\n      //commands like \"C\" can be strung together without any new command characters between.\n      i--;\n    }\n    x = +a[i + 1];\n    y = +a[i + 2];\n    if (isRelative) {\n      x += relativeX;\n      y += relativeY;\n    }\n    if (!i) {\n      startX = x;\n      startY = y;\n    } // \"M\" (move)\n\n    if (command === \"M\") {\n      if (segment) {\n        if (segment.length < 8) {\n          //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n          path.length -= 1;\n        } else {\n          points += segment.length;\n        }\n      }\n      relativeX = startX = x;\n      relativeY = startY = y;\n      segment = [x, y];\n      path.push(segment);\n      i += 2;\n      command = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n      // \"C\" (cubic bezier)\n    } else if (command === \"C\") {\n      if (!segment) {\n        segment = [0, 0];\n      }\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      } //note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\n      segment.push(x, y, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, relativeX += a[i + 5] * 1, relativeY += a[i + 6] * 1);\n      i += 6; // \"S\" (continuation of cubic bezier)\n    } else if (command === \"S\") {\n      difX = relativeX;\n      difY = relativeY;\n      if (prevCommand === \"C\" || prevCommand === \"S\") {\n        difX += relativeX - segment[segment.length - 4];\n        difY += relativeY - segment[segment.length - 3];\n      }\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      }\n      segment.push(difX, difY, x, y, relativeX += a[i + 3] * 1, relativeY += a[i + 4] * 1);\n      i += 4; // \"Q\" (quadratic bezier)\n    } else if (command === \"Q\") {\n      difX = relativeX + (x - relativeX) * twoThirds;\n      difY = relativeY + (y - relativeY) * twoThirds;\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      }\n      relativeX += a[i + 3] * 1;\n      relativeY += a[i + 4] * 1;\n      segment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n      i += 4; // \"T\" (continuation of quadratic bezier)\n    } else if (command === \"T\") {\n      difX = relativeX - segment[segment.length - 4];\n      difY = relativeY - segment[segment.length - 3];\n      segment.push(relativeX + difX, relativeY + difY, x + (relativeX + difX * 1.5 - x) * twoThirds, y + (relativeY + difY * 1.5 - y) * twoThirds, relativeX = x, relativeY = y);\n      i += 2; // \"H\" (horizontal line)\n    } else if (command === \"H\") {\n      line(relativeX, relativeY, relativeX = x, relativeY);\n      i += 1; // \"V\" (vertical line)\n    } else if (command === \"V\") {\n      //adjust values because the first (and only one) isn't x in this case, it's y.\n      line(relativeX, relativeY, relativeX, relativeY = x + (isRelative ? relativeY - relativeX : 0));\n      i += 1; // \"L\" (line) or \"Z\" (close)\n    } else if (command === \"L\" || command === \"Z\") {\n      if (command === \"Z\") {\n        x = startX;\n        y = startY;\n        segment.closed = true;\n      }\n      if (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n        line(relativeX, relativeY, x, y);\n        if (command === \"L\") {\n          i += 2;\n        }\n      }\n      relativeX = x;\n      relativeY = y; // \"A\" (arc)\n    } else if (command === \"A\") {\n      flag1 = a[i + 4];\n      flag2 = a[i + 5];\n      difX = a[i + 6];\n      difY = a[i + 7];\n      j = 7;\n      if (flag1.length > 1) {\n        // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n        if (flag1.length < 3) {\n          difY = difX;\n          difX = flag2;\n          j--;\n        } else {\n          difY = flag2;\n          difX = flag1.substr(2);\n          j -= 2;\n        }\n        flag2 = flag1.charAt(1);\n        flag1 = flag1.charAt(0);\n      }\n      beziers = arcToSegment(relativeX, relativeY, +a[i + 1], +a[i + 2], +a[i + 3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX * 1, (isRelative ? relativeY : 0) + difY * 1);\n      i += j;\n      if (beziers) {\n        for (j = 0; j < beziers.length; j++) {\n          segment.push(beziers[j]);\n        }\n      }\n      relativeX = segment[segment.length - 2];\n      relativeY = segment[segment.length - 1];\n    } else {\n      console.log(errorMessage);\n    }\n  }\n  i = segment.length;\n  if (i < 6) {\n    //in case there's odd SVG like a M0,0 command at the very end.\n    path.pop();\n    i = 0;\n  } else if (segment[0] === segment[i - 2] && segment[1] === segment[i - 1]) {\n    segment.closed = true;\n  }\n  path.totalPoints = points + i;\n  return path;\n} //populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\n\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n  var x12 = (x1 + x2) / 2,\n    y12 = (y1 + y2) / 2,\n    x23 = (x2 + x3) / 2,\n    y23 = (y2 + y3) / 2,\n    x34 = (x3 + x4) / 2,\n    y34 = (y3 + y4) / 2,\n    x123 = (x12 + x23) / 2,\n    y123 = (y12 + y23) / 2,\n    x234 = (x23 + x34) / 2,\n    y234 = (y23 + y34) / 2,\n    x1234 = (x123 + x234) / 2,\n    y1234 = (y123 + y234) / 2,\n    dx = x4 - x1,\n    dy = y4 - y1,\n    d2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n    d3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n    length;\n  if (!points) {\n    points = [x1, y1, x4, y4];\n    index = 2;\n  }\n  points.splice(index || points.length - 2, 0, x1234, y1234);\n  if ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n    length = points.length;\n    bezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n    bezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n  }\n  return points;\n}\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\n\nexport function flatPointsToSegment(points, curviness) {\n  if (curviness === void 0) {\n    curviness = 1;\n  }\n  var x = points[0],\n    y = 0,\n    segment = [x, y],\n    i = 2;\n  for (; i < points.length; i += 2) {\n    segment.push(x, y, points[i], y = (points[i] - x) * curviness / 2, x = points[i], -y);\n  }\n  return segment;\n} //points is an array of x/y points, like [x, y, x, y, x, y]\n\nexport function pointsToSegment(points, curviness) {\n  //points = simplifyPoints(points, tolerance);\n  _abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\n  var l = points.length - 2,\n    x = +points[0],\n    y = +points[1],\n    nextX = +points[2],\n    nextY = +points[3],\n    segment = [x, y, x, y],\n    dx2 = nextX - x,\n    dy2 = nextY - y,\n    closed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l + 1] - y) < 0.001,\n    prevX,\n    prevY,\n    i,\n    dx1,\n    dy1,\n    r1,\n    r2,\n    r3,\n    tl,\n    mx1,\n    mx2,\n    mxm,\n    my1,\n    my2,\n    mym;\n  if (closed) {\n    // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n    points.push(nextX, nextY);\n    nextX = x;\n    nextY = y;\n    x = points[l - 2];\n    y = points[l - 1];\n    points.unshift(x, y);\n    l += 4;\n  }\n  curviness = curviness || curviness === 0 ? +curviness : 1;\n  for (i = 2; i < l; i += 2) {\n    prevX = x;\n    prevY = y;\n    x = nextX;\n    y = nextY;\n    nextX = +points[i + 2];\n    nextY = +points[i + 3];\n    if (x === nextX && y === nextY) {\n      continue;\n    }\n    dx1 = dx2;\n    dy1 = dy2;\n    dx2 = nextX - x;\n    dy2 = nextY - y;\n    r1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\n    r2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n    r3 = _sqrt(Math.pow(dx2 / r2 + dx1 / r1, 2) + Math.pow(dy2 / r2 + dy1 / r1, 2));\n    tl = (r1 + r2) * curviness * 0.25 / r3;\n    mx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n    mx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n    mxm = x - (mx1 + ((mx2 - mx1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\n    my1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n    my2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n    mym = y - (my1 + ((my2 - my1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\n    if (x !== prevX || y !== prevY) {\n      segment.push(_round(mx1 + mxm),\n      // first control point\n      _round(my1 + mym), _round(x),\n      // anchor\n      _round(y), _round(mx2 + mxm),\n      // second control point\n      _round(my2 + mym));\n    }\n  }\n  x !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : segment.length -= 2;\n  if (segment.length === 2) {\n    // only one point!\n    segment.push(x, y, x, y, x, y);\n  } else if (closed) {\n    segment.splice(0, 6);\n    segment.length = segment.length - 6;\n  }\n  return segment;\n} //returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\n\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n  var dx = x2 - x1,\n    dy = y2 - y1,\n    t;\n  if (dx || dy) {\n    t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n    if (t > 1) {\n      x1 = x2;\n      y1 = y2;\n    } else if (t > 0) {\n      x1 += dx * t;\n      y1 += dy * t;\n    }\n  }\n  return Math.pow(x - x1, 2) + Math.pow(y - y1, 2);\n}\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n  var maxSqDist = tolerance,\n    firstX = points[first],\n    firstY = points[first + 1],\n    lastX = points[last],\n    lastY = points[last + 1],\n    index,\n    i,\n    d;\n  for (i = first + 2; i < last; i += 2) {\n    d = pointToSegDist(points[i], points[i + 1], firstX, firstY, lastX, lastY);\n    if (d > maxSqDist) {\n      index = i;\n      maxSqDist = d;\n    }\n  }\n  if (maxSqDist > tolerance) {\n    index - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n    simplified.push(points[index], points[index + 1]);\n    last - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n  }\n} //points is an array of x/y values like [x, y, x, y, x, y]\n\nexport function simplifyPoints(points, tolerance) {\n  var prevX = parseFloat(points[0]),\n    prevY = parseFloat(points[1]),\n    temp = [prevX, prevY],\n    l = points.length - 2,\n    i,\n    x,\n    y,\n    dx,\n    dy,\n    result,\n    last;\n  tolerance = Math.pow(tolerance || 1, 2);\n  for (i = 2; i < l; i += 2) {\n    x = parseFloat(points[i]);\n    y = parseFloat(points[i + 1]);\n    dx = prevX - x;\n    dy = prevY - y;\n    if (dx * dx + dy * dy > tolerance) {\n      temp.push(x, y);\n      prevX = x;\n      prevY = y;\n    }\n  }\n  temp.push(parseFloat(points[l]), parseFloat(points[l + 1]));\n  last = temp.length - 2;\n  result = [temp[0], temp[1]];\n  simplifyStep(temp, 0, last, tolerance, result);\n  result.push(temp[last], temp[last + 1]);\n  return result;\n}\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n  var inc = (end - start) / slices,\n    best = 0,\n    t = start,\n    x,\n    y,\n    d,\n    dx,\n    dy,\n    inv;\n  _bestDistance = _largeNum;\n  while (t <= end) {\n    inv = 1 - t;\n    x = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n    y = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n    dx = x - px;\n    dy = y - py;\n    d = dx * dx + dy * dy;\n    if (d < _bestDistance) {\n      _bestDistance = d;\n      best = t;\n    }\n    t += inc;\n  }\n  return iterations > 1 ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\nexport function getClosestData(rawPath, x, y, slices) {\n  //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n  var closest = {\n      j: 0,\n      i: 0,\n      t: 0\n    },\n    bestDistance = _largeNum,\n    i,\n    j,\n    t,\n    segment;\n  for (j = 0; j < rawPath.length; j++) {\n    segment = rawPath[j];\n    for (i = 0; i < segment.length; i += 6) {\n      t = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\n      if (bestDistance > _bestDistance) {\n        bestDistance = _bestDistance;\n        closest.j = j;\n        closest.i = i;\n        closest.t = t;\n      }\n    }\n  }\n  return closest;\n} //subdivide a Segment closest to a specific x,y coordinate\n\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n  var l = segment.length,\n    bestDistance = _largeNum,\n    bestT = 0,\n    bestSegmentIndex = 0,\n    t,\n    i;\n  slices = slices || 20;\n  iterations = iterations || 3;\n  for (i = 0; i < l; i += 6) {\n    t = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\n    if (bestDistance > _bestDistance) {\n      bestDistance = _bestDistance;\n      bestT = t;\n      bestSegmentIndex = i;\n    }\n  }\n  t = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex + 1], segment[bestSegmentIndex + 2], segment[bestSegmentIndex + 3], segment[bestSegmentIndex + 4], segment[bestSegmentIndex + 5], segment[bestSegmentIndex + 6], segment[bestSegmentIndex + 7]);\n  subdivideSegment(segment, bestSegmentIndex, t);\n  return bestSegmentIndex + 6;\n}\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\n\nexport function rawPathToString(rawPath) {\n  if (_isNumber(rawPath[0])) {\n    //in case a segment is passed in instead\n    rawPath = [rawPath];\n  }\n  var result = \"\",\n    l = rawPath.length,\n    sl,\n    s,\n    i,\n    segment;\n  for (s = 0; s < l; s++) {\n    segment = rawPath[s];\n    result += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n    sl = segment.length;\n    for (i = 2; i < sl; i++) {\n      result += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n    }\n    if (segment.closed) {\n      result += \"z\";\n    }\n  }\n  return result;\n}\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/", "map": {"version": 3, "names": ["_svgPathExp", "_numbersExp", "_scientific", "_selectorExp", "_DEG2RAD", "Math", "PI", "_RAD2DEG", "_sin", "sin", "_cos", "cos", "_abs", "abs", "_sqrt", "sqrt", "_atan2", "atan2", "_largeNum", "_isString", "value", "_isNumber", "_isUndefined", "_temp", "_temp2", "_roundingNum", "_wrapProgress", "progress", "round", "_round", "_roundPrecise", "_splitSegment", "rawPath", "segIndex", "i", "t", "segment", "shift", "subdivideSegment", "length", "splice", "slice", "_getSampleIndex", "samples", "l", "_reverseRawPath", "skip<PERSON>uter", "reverse", "reversed", "reverseSegment", "_copyMetaData", "source", "copy", "totalLength", "lookup", "<PERSON><PERSON><PERSON><PERSON>", "resolution", "totalPoints", "_appendOrMerge", "index", "prevSeg", "concat", "_bestDistance", "getRawPath", "test", "document", "querySelector", "e", "getAttribute", "_gsPath", "_dirty", "stringToRawPath", "console", "warn", "copyRawPath", "a", "y", "_createPath", "ignore", "path", "createElementNS", "attr", "call", "attributes", "name", "nodeName", "toLowerCase", "indexOf", "setAttributeNS", "nodeValue", "_typeAttrs", "rect", "circle", "ellipse", "line", "_attrToObj", "attrs", "props", "split", "obj", "convertToPath", "element", "swap", "type", "tagName", "circ", "data", "x", "r", "ry", "rcirc", "rycirc", "points", "w", "h", "x2", "x3", "x4", "x5", "x6", "y2", "y3", "y4", "y5", "y6", "getBBox", "rx", "width", "height", "join", "cx", "cy", "x1", "y1", "match", "setAttribute", "rawPathToString", "_gsRawPath", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "getRotationAtProgress", "d", "getProgressData", "getRotationAtBezierT", "b", "c", "sliceRawPath", "start", "end", "loops", "max", "offset", "min", "cacheRawPathMeasurements", "wrap", "s", "eSeg", "sSeg", "eSegIndex", "sSegIndex", "ei", "si", "sameSegment", "sameBezier", "wraps<PERSON><PERSON><PERSON>", "sShift", "eShift", "totalSegments", "j", "angle", "push", "measureSegment", "startIndex", "bezier<PERSON><PERSON>", "inc", "endIndex", "samplesIndex", "prevLength", "xd", "xd1", "yd", "yd1", "inv", "lengthIndex", "seg<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ax", "ay", "cp1x", "cp1y", "cp2x", "cp2y", "bx", "by", "x1a", "y1a", "x2a", "y2a", "decoratee", "pushToNextIfAtEnd", "getPositionOnPath", "includeAngle", "point", "result", "transformRawPath", "tx", "ty", "arcToSegment", "lastX", "lastY", "largeArcFlag", "sweepFlag", "angleRad", "cosAngle", "sinAngle", "TWOPI", "dx2", "dy2", "x1_sq", "y1_sq", "radiiCheck", "rx_sq", "ry_sq", "sq", "coef", "cx1", "cy1", "sx2", "sy2", "ux", "uy", "vx", "vy", "temp", "angleStart", "acos", "angleExtent", "isNaN", "segments", "ceil", "angleIncrement", "controlLength", "ma", "mb", "mc", "md", "replace", "m", "n", "relativeX", "relativeY", "twoThirds", "elements", "errorMessage", "command", "isRelative", "startX", "startY", "difX", "difY", "beziers", "prevCommand", "flag1", "flag2", "sx", "sy", "ex", "ey", "log", "toUpperCase", "closed", "substr", "char<PERSON>t", "pop", "bezierToPoints", "threshold", "x12", "y12", "x23", "y23", "x34", "y34", "x123", "y123", "x234", "y234", "x1234", "y1234", "dx", "dy", "d2", "d3", "flatPointsToSegment", "curviness", "pointsToSegment", "nextX", "nextY", "prevX", "prevY", "dx1", "dy1", "r1", "r2", "r3", "tl", "mx1", "mx2", "mxm", "my1", "my2", "mym", "unshift", "pow", "pointToSegDist", "simplifyStep", "first", "last", "tolerance", "simplified", "maxSqDist", "firstX", "firstY", "simplifyPoints", "parseFloat", "getClosestProgressOnBezier", "iterations", "px", "py", "slices", "x0", "y0", "best", "getClosestData", "closest", "bestDistance", "subdivideSegmentNear", "bestT", "bestSegmentIndex", "sl"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/gsap/utils/paths.js"], "sourcesContent": ["/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n    _numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n    _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n    _selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n    _DEG2RAD = Math.PI / 180,\n    _RAD2DEG = 180 / Math.PI,\n    _sin = Math.sin,\n    _cos = Math.cos,\n    _abs = Math.abs,\n    _sqrt = Math.sqrt,\n    _atan2 = Math.atan2,\n    _largeNum = 1e8,\n    _isString = function _isString(value) {\n  return typeof value === \"string\";\n},\n    _isNumber = function _isNumber(value) {\n  return typeof value === \"number\";\n},\n    _isUndefined = function _isUndefined(value) {\n  return typeof value === \"undefined\";\n},\n    _temp = {},\n    _temp2 = {},\n    _roundingNum = 1e5,\n    _wrapProgress = function _wrapProgress(progress) {\n  return Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum || (progress < 0 ? 0 : 1);\n},\n    //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n_round = function _round(value) {\n  return Math.round(value * _roundingNum) / _roundingNum || 0;\n},\n    _roundPrecise = function _roundPrecise(value) {\n  return Math.round(value * 1e10) / 1e10 || 0;\n},\n    _splitSegment = function _splitSegment(rawPath, segIndex, i, t) {\n  var segment = rawPath[segIndex],\n      shift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n\n  if ((shift || !t) && shift + i + 2 < segment.length) {\n    rawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n    segment.splice(0, i + shift);\n    return 1;\n  }\n},\n    _getSampleIndex = function _getSampleIndex(samples, length, progress) {\n  // slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n  var l = samples.length,\n      i = ~~(progress * l);\n\n  if (samples[i] > length) {\n    while (--i && samples[i] > length) {}\n\n    i < 0 && (i = 0);\n  } else {\n    while (samples[++i] < length && i < l) {}\n  }\n\n  return i < l ? i : l - 1;\n},\n    _reverseRawPath = function _reverseRawPath(rawPath, skipOuter) {\n  var i = rawPath.length;\n  skipOuter || rawPath.reverse();\n\n  while (i--) {\n    rawPath[i].reversed || reverseSegment(rawPath[i]);\n  }\n},\n    _copyMetaData = function _copyMetaData(source, copy) {\n  copy.totalLength = source.totalLength;\n\n  if (source.samples) {\n    //segment\n    copy.samples = source.samples.slice(0);\n    copy.lookup = source.lookup.slice(0);\n    copy.minLength = source.minLength;\n    copy.resolution = source.resolution;\n  } else if (source.totalPoints) {\n    //rawPath\n    copy.totalPoints = source.totalPoints;\n  }\n\n  return copy;\n},\n    //pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n_appendOrMerge = function _appendOrMerge(rawPath, segment) {\n  var index = rawPath.length,\n      prevSeg = rawPath[index - 1] || [],\n      l = prevSeg.length;\n\n  if (index && segment[0] === prevSeg[l - 2] && segment[1] === prevSeg[l - 1]) {\n    segment = prevSeg.concat(segment.slice(2));\n    index--;\n  }\n\n  rawPath[index] = segment;\n},\n    _bestDistance;\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\n\n\nexport function getRawPath(value) {\n  value = _isString(value) && _selectorExp.test(value) ? document.querySelector(value) || value : value;\n  var e = value.getAttribute ? value : 0,\n      rawPath;\n\n  if (e && (value = value.getAttribute(\"d\"))) {\n    //implements caching\n    if (!e._gsPath) {\n      e._gsPath = {};\n    }\n\n    rawPath = e._gsPath[value];\n    return rawPath && !rawPath._dirty ? rawPath : e._gsPath[value] = stringToRawPath(value);\n  }\n\n  return !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : _isNumber(value[0]) ? [value] : value;\n} //copies a RawPath WITHOUT the length meta data (for speed)\n\nexport function copyRawPath(rawPath) {\n  var a = [],\n      i = 0;\n\n  for (; i < rawPath.length; i++) {\n    a[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n  }\n\n  return _copyMetaData(rawPath, a);\n}\nexport function reverseSegment(segment) {\n  var i = 0,\n      y;\n  segment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\n  for (; i < segment.length; i += 2) {\n    y = segment[i];\n    segment[i] = segment[i + 1];\n    segment[i + 1] = y;\n  }\n\n  segment.reversed = !segment.reversed;\n}\n\nvar _createPath = function _createPath(e, ignore) {\n  var path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n      attr = [].slice.call(e.attributes),\n      i = attr.length,\n      name;\n  ignore = \",\" + ignore + \",\";\n\n  while (--i > -1) {\n    name = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\n    if (ignore.indexOf(\",\" + name + \",\") < 0) {\n      path.setAttributeNS(null, name, attr[i].nodeValue);\n    }\n  }\n\n  return path;\n},\n    _typeAttrs = {\n  rect: \"rx,ry,x,y,width,height\",\n  circle: \"r,cx,cy\",\n  ellipse: \"rx,ry,cx,cy\",\n  line: \"x1,x2,y1,y2\"\n},\n    _attrToObj = function _attrToObj(e, attrs) {\n  var props = attrs ? attrs.split(\",\") : [],\n      obj = {},\n      i = props.length;\n\n  while (--i > -1) {\n    obj[props[i]] = +e.getAttribute(props[i]) || 0;\n  }\n\n  return obj;\n}; //converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\n\n\nexport function convertToPath(element, swap) {\n  var type = element.tagName.toLowerCase(),\n      circ = 0.552284749831,\n      data,\n      x,\n      y,\n      r,\n      ry,\n      path,\n      rcirc,\n      rycirc,\n      points,\n      w,\n      h,\n      x2,\n      x3,\n      x4,\n      x5,\n      x6,\n      y2,\n      y3,\n      y4,\n      y5,\n      y6,\n      attr;\n\n  if (type === \"path\" || !element.getBBox) {\n    return element;\n  }\n\n  path = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n  attr = _attrToObj(element, _typeAttrs[type]);\n\n  if (type === \"rect\") {\n    r = attr.rx;\n    ry = attr.ry || r;\n    x = attr.x;\n    y = attr.y;\n    w = attr.width - r * 2;\n    h = attr.height - ry * 2;\n\n    if (r || ry) {\n      //if there are rounded corners, render cubic beziers\n      x2 = x + r * (1 - circ);\n      x3 = x + r;\n      x4 = x3 + w;\n      x5 = x4 + r * circ;\n      x6 = x4 + r;\n      y2 = y + ry * (1 - circ);\n      y3 = y + ry;\n      y4 = y3 + h;\n      y5 = y4 + ry * circ;\n      y6 = y4 + ry;\n      data = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n    } else {\n      data = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + -w + \" v\" + -h + \" h\" + w + \"z\";\n    }\n  } else if (type === \"circle\" || type === \"ellipse\") {\n    if (type === \"circle\") {\n      r = ry = attr.r;\n      rycirc = r * circ;\n    } else {\n      r = attr.rx;\n      ry = attr.ry;\n      rycirc = ry * circ;\n    }\n\n    x = attr.cx;\n    y = attr.cy;\n    rcirc = r * circ;\n    data = \"M\" + (x + r) + \",\" + y + \" C\" + [x + r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n  } else if (type === \"line\") {\n    data = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n  } else if (type === \"polyline\" || type === \"polygon\") {\n    points = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n    x = points.shift();\n    y = points.shift();\n    data = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n\n    if (type === \"polygon\") {\n      data += \",\" + x + \",\" + y + \"z\";\n    }\n  }\n\n  path.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n\n  if (swap && element.parentNode) {\n    element.parentNode.insertBefore(path, element);\n    element.parentNode.removeChild(element);\n  }\n\n  return path;\n} //returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\n\nexport function getRotationAtProgress(rawPath, progress) {\n  var d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n  return getRotationAtBezierT(d.segment, d.i, d.t);\n}\n\nfunction getRotationAtBezierT(segment, i, t) {\n  var a = segment[i],\n      b = segment[i + 2],\n      c = segment[i + 4],\n      x;\n  a += (b - a) * t;\n  b += (c - b) * t;\n  a += (b - a) * t;\n  x = b + (c + (segment[i + 6] - c) * t - b) * t - a;\n  a = segment[i + 1];\n  b = segment[i + 3];\n  c = segment[i + 5];\n  a += (b - a) * t;\n  b += (c - b) * t;\n  a += (b - a) * t;\n  return _round(_atan2(b + (c + (segment[i + 7] - c) * t - b) * t - a, x) * _RAD2DEG);\n}\n\nexport function sliceRawPath(rawPath, start, end) {\n  end = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\n  start = _roundPrecise(start) || 0;\n  var loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n      path = copyRawPath(rawPath);\n\n  if (start > end) {\n    start = 1 - start;\n    end = 1 - end;\n\n    _reverseRawPath(path);\n\n    path.totalLength = 0;\n  }\n\n  if (start < 0 || end < 0) {\n    var offset = Math.abs(~~Math.min(start, end)) + 1;\n    start += offset;\n    end += offset;\n  }\n\n  path.totalLength || cacheRawPathMeasurements(path);\n  var wrap = end > 1,\n      s = getProgressData(path, start, _temp, true),\n      e = getProgressData(path, end, _temp2),\n      eSeg = e.segment,\n      sSeg = s.segment,\n      eSegIndex = e.segIndex,\n      sSegIndex = s.segIndex,\n      ei = e.i,\n      si = s.i,\n      sameSegment = sSegIndex === eSegIndex,\n      sameBezier = ei === si && sameSegment,\n      wrapsBehind,\n      sShift,\n      eShift,\n      i,\n      copy,\n      totalSegments,\n      l,\n      j;\n\n  if (wrap || loops) {\n    wrapsBehind = eSegIndex < sSegIndex || sameSegment && ei < si || sameBezier && e.t < s.t;\n\n    if (_splitSegment(path, sSegIndex, si, s.t)) {\n      sSegIndex++;\n\n      if (!wrapsBehind) {\n        eSegIndex++;\n\n        if (sameBezier) {\n          e.t = (e.t - s.t) / (1 - s.t);\n          ei = 0;\n        } else if (sameSegment) {\n          ei -= si;\n        }\n      }\n    }\n\n    if (Math.abs(1 - (end - start)) < 1e-5) {\n      eSegIndex = sSegIndex - 1;\n    } else if (!e.t && eSegIndex) {\n      eSegIndex--;\n    } else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n      sSegIndex++;\n    }\n\n    if (s.t === 1) {\n      sSegIndex = (sSegIndex + 1) % path.length;\n    }\n\n    copy = [];\n    totalSegments = path.length;\n    l = 1 + totalSegments * loops;\n    j = sSegIndex;\n    l += (totalSegments - sSegIndex + eSegIndex) % totalSegments;\n\n    for (i = 0; i < l; i++) {\n      _appendOrMerge(copy, path[j++ % totalSegments]);\n    }\n\n    path = copy;\n  } else {\n    eShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n\n    if (start !== end) {\n      sShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n      sameSegment && (eShift += sShift);\n      eSeg.splice(ei + eShift + 2);\n      (sShift || si) && sSeg.splice(0, si + sShift);\n      i = path.length;\n\n      while (i--) {\n        //chop off any extra segments\n        (i < sSegIndex || i > eSegIndex) && path.splice(i, 1);\n      }\n    } else {\n      eSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\n      ei += eShift;\n      s = eSeg[ei];\n      e = eSeg[ei + 1];\n      eSeg.length = eSeg.totalLength = 0;\n      eSeg.totalPoints = path.totalPoints = 8;\n      eSeg.push(s, e, s, e, s, e, s, e);\n    }\n  }\n\n  path.totalLength = 0;\n  return path;\n} //measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\n\nfunction measureSegment(segment, startIndex, bezierQty) {\n  startIndex = startIndex || 0;\n\n  if (!segment.samples) {\n    segment.samples = [];\n    segment.lookup = [];\n  }\n\n  var resolution = ~~segment.resolution || 12,\n      inc = 1 / resolution,\n      endIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n      x1 = segment[startIndex],\n      y1 = segment[startIndex + 1],\n      samplesIndex = startIndex ? startIndex / 6 * resolution : 0,\n      samples = segment.samples,\n      lookup = segment.lookup,\n      min = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n      prevLength = samples[samplesIndex + bezierQty * resolution - 1],\n      length = startIndex ? samples[samplesIndex - 1] : 0,\n      i,\n      j,\n      x4,\n      x3,\n      x2,\n      xd,\n      xd1,\n      y4,\n      y3,\n      y2,\n      yd,\n      yd1,\n      inv,\n      t,\n      lengthIndex,\n      l,\n      segLength;\n  samples.length = lookup.length = 0;\n\n  for (j = startIndex + 2; j < endIndex; j += 6) {\n    x4 = segment[j + 4] - x1;\n    x3 = segment[j + 2] - x1;\n    x2 = segment[j] - x1;\n    y4 = segment[j + 5] - y1;\n    y3 = segment[j + 3] - y1;\n    y2 = segment[j + 1] - y1;\n    xd = xd1 = yd = yd1 = 0;\n\n    if (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) {\n      //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n      if (segment.length > 8) {\n        segment.splice(j, 6);\n        j -= 6;\n        endIndex -= 6;\n      }\n    } else {\n      for (i = 1; i <= resolution; i++) {\n        t = inc * i;\n        inv = 1 - t;\n        xd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n        yd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n        l = _sqrt(yd * yd + xd * xd);\n\n        if (l < min) {\n          min = l;\n        }\n\n        length += l;\n        samples[samplesIndex++] = length;\n      }\n    }\n\n    x1 += x4;\n    y1 += y4;\n  }\n\n  if (prevLength) {\n    prevLength -= length;\n\n    for (; samplesIndex < samples.length; samplesIndex++) {\n      samples[samplesIndex] += prevLength;\n    }\n  }\n\n  if (samples.length && min) {\n    segment.totalLength = segLength = samples[samples.length - 1] || 0;\n    segment.minLength = min;\n\n    if (segLength / min < 9999) {\n      // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n      l = lengthIndex = 0;\n\n      for (i = 0; i < segLength; i += min) {\n        lookup[l++] = samples[lengthIndex] < i ? ++lengthIndex : lengthIndex;\n      }\n    }\n  } else {\n    segment.totalLength = samples[0] = 0;\n  }\n\n  return startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\n\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n  var pathLength, points, i;\n\n  for (i = pathLength = points = 0; i < rawPath.length; i++) {\n    rawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\n    points += rawPath[i].length;\n    pathLength += measureSegment(rawPath[i]);\n  }\n\n  rawPath.totalPoints = points;\n  rawPath.totalLength = pathLength;\n  return rawPath;\n} //divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\n\nexport function subdivideSegment(segment, i, t) {\n  if (t <= 0 || t >= 1) {\n    return 0;\n  }\n\n  var ax = segment[i],\n      ay = segment[i + 1],\n      cp1x = segment[i + 2],\n      cp1y = segment[i + 3],\n      cp2x = segment[i + 4],\n      cp2y = segment[i + 5],\n      bx = segment[i + 6],\n      by = segment[i + 7],\n      x1a = ax + (cp1x - ax) * t,\n      x2 = cp1x + (cp2x - cp1x) * t,\n      y1a = ay + (cp1y - ay) * t,\n      y2 = cp1y + (cp2y - cp1y) * t,\n      x1 = x1a + (x2 - x1a) * t,\n      y1 = y1a + (y2 - y1a) * t,\n      x2a = cp2x + (bx - cp2x) * t,\n      y2a = cp2y + (by - cp2y) * t;\n  x2 += (x2a - x2) * t;\n  y2 += (y2a - y2) * t;\n  segment.splice(i + 2, 4, _round(x1a), //first control point\n  _round(y1a), _round(x1), //second control point\n  _round(y1), _round(x1 + (x2 - x1) * t), //new fabricated anchor on line\n  _round(y1 + (y2 - y1) * t), _round(x2), //third control point\n  _round(y2), _round(x2a), //fourth control point\n  _round(y2a));\n  segment.samples && segment.samples.splice(i / 6 * segment.resolution | 0, 0, 0, 0, 0, 0, 0, 0);\n  return 6;\n} // returns an object {path, segment, segIndex, i, t}\n\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n  decoratee = decoratee || {};\n  rawPath.totalLength || cacheRawPathMeasurements(rawPath);\n\n  if (progress < 0 || progress > 1) {\n    progress = _wrapProgress(progress);\n  }\n\n  var segIndex = 0,\n      segment = rawPath[0],\n      samples,\n      resolution,\n      length,\n      min,\n      max,\n      i,\n      t;\n\n  if (!progress) {\n    t = i = segIndex = 0;\n    segment = rawPath[0];\n  } else if (progress === 1) {\n    t = 1;\n    segIndex = rawPath.length - 1;\n    segment = rawPath[segIndex];\n    i = segment.length - 8;\n  } else {\n    if (rawPath.length > 1) {\n      //speed optimization: most of the time, there's only one segment so skip the recursion.\n      length = rawPath.totalLength * progress;\n      max = i = 0;\n\n      while ((max += rawPath[i++].totalLength) < length) {\n        segIndex = i;\n      }\n\n      segment = rawPath[segIndex];\n      min = max - segment.totalLength;\n      progress = (length - min) / (max - min) || 0;\n    }\n\n    samples = segment.samples;\n    resolution = segment.resolution; //how many samples per cubic bezier chunk\n\n    length = segment.totalLength * progress;\n    i = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n    min = i ? samples[i - 1] : 0;\n    max = samples[i];\n\n    if (max < length) {\n      min = max;\n      max = samples[++i];\n    }\n\n    t = 1 / resolution * ((length - min) / (max - min) + i % resolution);\n    i = ~~(i / resolution) * 6;\n\n    if (pushToNextIfAtEnd && t === 1) {\n      if (i + 6 < segment.length) {\n        i += 6;\n        t = 0;\n      } else if (segIndex + 1 < rawPath.length) {\n        i = t = 0;\n        segment = rawPath[++segIndex];\n      }\n    }\n  }\n\n  decoratee.t = t;\n  decoratee.i = i;\n  decoratee.path = rawPath;\n  decoratee.segment = segment;\n  decoratee.segIndex = segIndex;\n  return decoratee;\n}\n\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n  var segment = rawPath[0],\n      result = point || {},\n      samples,\n      resolution,\n      length,\n      min,\n      max,\n      i,\n      t,\n      a,\n      inv;\n\n  if (progress < 0 || progress > 1) {\n    progress = _wrapProgress(progress);\n  }\n\n  segment.lookup || cacheRawPathMeasurements(rawPath);\n\n  if (rawPath.length > 1) {\n    //speed optimization: most of the time, there's only one segment so skip the recursion.\n    length = rawPath.totalLength * progress;\n    max = i = 0;\n\n    while ((max += rawPath[i++].totalLength) < length) {\n      segment = rawPath[i];\n    }\n\n    min = max - segment.totalLength;\n    progress = (length - min) / (max - min) || 0;\n  }\n\n  samples = segment.samples;\n  resolution = segment.resolution;\n  length = segment.totalLength * progress;\n  i = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n  min = i ? samples[i - 1] : 0;\n  max = samples[i];\n\n  if (max < length) {\n    min = max;\n    max = samples[++i];\n  }\n\n  t = 1 / resolution * ((length - min) / (max - min) + i % resolution) || 0;\n  inv = 1 - t;\n  i = ~~(i / resolution) * 6;\n  a = segment[i];\n  result.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n  result.y = _round((t * t * (segment[i + 7] - (a = segment[i + 1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n\n  if (includeAngle) {\n    result.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n  }\n\n  return result;\n} //applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\n\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n  var j = rawPath.length,\n      segment,\n      l,\n      i,\n      x,\n      y;\n\n  while (--j > -1) {\n    segment = rawPath[j];\n    l = segment.length;\n\n    for (i = 0; i < l; i += 2) {\n      x = segment[i];\n      y = segment[i + 1];\n      segment[i] = x * a + y * c + tx;\n      segment[i + 1] = x * b + y * d + ty;\n    }\n  }\n\n  rawPath._dirty = 1;\n  return rawPath;\n} // translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\n\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n  if (lastX === x && lastY === y) {\n    return;\n  }\n\n  rx = _abs(rx);\n  ry = _abs(ry);\n\n  var angleRad = angle % 360 * _DEG2RAD,\n      cosAngle = _cos(angleRad),\n      sinAngle = _sin(angleRad),\n      PI = Math.PI,\n      TWOPI = PI * 2,\n      dx2 = (lastX - x) / 2,\n      dy2 = (lastY - y) / 2,\n      x1 = cosAngle * dx2 + sinAngle * dy2,\n      y1 = -sinAngle * dx2 + cosAngle * dy2,\n      x1_sq = x1 * x1,\n      y1_sq = y1 * y1,\n      radiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n\n  if (radiiCheck > 1) {\n    rx = _sqrt(radiiCheck) * rx;\n    ry = _sqrt(radiiCheck) * ry;\n  }\n\n  var rx_sq = rx * rx,\n      ry_sq = ry * ry,\n      sq = (rx_sq * ry_sq - rx_sq * y1_sq - ry_sq * x1_sq) / (rx_sq * y1_sq + ry_sq * x1_sq);\n\n  if (sq < 0) {\n    sq = 0;\n  }\n\n  var coef = (largeArcFlag === sweepFlag ? -1 : 1) * _sqrt(sq),\n      cx1 = coef * (rx * y1 / ry),\n      cy1 = coef * -(ry * x1 / rx),\n      sx2 = (lastX + x) / 2,\n      sy2 = (lastY + y) / 2,\n      cx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n      cy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n      ux = (x1 - cx1) / rx,\n      uy = (y1 - cy1) / ry,\n      vx = (-x1 - cx1) / rx,\n      vy = (-y1 - cy1) / ry,\n      temp = ux * ux + uy * uy,\n      angleStart = (uy < 0 ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n      angleExtent = (ux * vy - uy * vx < 0 ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n\n  isNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\n  if (!sweepFlag && angleExtent > 0) {\n    angleExtent -= TWOPI;\n  } else if (sweepFlag && angleExtent < 0) {\n    angleExtent += TWOPI;\n  }\n\n  angleStart %= TWOPI;\n  angleExtent %= TWOPI;\n\n  var segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n      rawPath = [],\n      angleIncrement = angleExtent / segments,\n      controlLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n      ma = cosAngle * rx,\n      mb = sinAngle * rx,\n      mc = sinAngle * -ry,\n      md = cosAngle * ry,\n      i;\n\n  for (i = 0; i < segments; i++) {\n    angle = angleStart + i * angleIncrement;\n    x1 = _cos(angle);\n    y1 = _sin(angle);\n    ux = _cos(angle += angleIncrement);\n    uy = _sin(angle);\n    rawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n  } //now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\n\n  for (i = 0; i < rawPath.length; i += 2) {\n    x1 = rawPath[i];\n    y1 = rawPath[i + 1];\n    rawPath[i] = x1 * ma + y1 * mc + cx;\n    rawPath[i + 1] = x1 * mb + y1 * md + cy;\n  }\n\n  rawPath[i - 2] = x; //always set the end to exactly where it's supposed to be\n\n  rawPath[i - 1] = y;\n  return rawPath;\n} //Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\n\n\nexport function stringToRawPath(d) {\n  var a = (d + \"\").replace(_scientific, function (m) {\n    var n = +m;\n    return n < 0.0001 && n > -0.0001 ? 0 : n;\n  }).match(_svgPathExp) || [],\n      //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n  path = [],\n      relativeX = 0,\n      relativeY = 0,\n      twoThirds = 2 / 3,\n      elements = a.length,\n      points = 0,\n      errorMessage = \"ERROR: malformed path: \" + d,\n      i,\n      j,\n      x,\n      y,\n      command,\n      isRelative,\n      segment,\n      startX,\n      startY,\n      difX,\n      difY,\n      beziers,\n      prevCommand,\n      flag1,\n      flag2,\n      line = function line(sx, sy, ex, ey) {\n    difX = (ex - sx) / 3;\n    difY = (ey - sy) / 3;\n    segment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n  };\n\n  if (!d || !isNaN(a[0]) || isNaN(a[1])) {\n    console.log(errorMessage);\n    return path;\n  }\n\n  for (i = 0; i < elements; i++) {\n    prevCommand = command;\n\n    if (isNaN(a[i])) {\n      command = a[i].toUpperCase();\n      isRelative = command !== a[i]; //lower case means relative\n    } else {\n      //commands like \"C\" can be strung together without any new command characters between.\n      i--;\n    }\n\n    x = +a[i + 1];\n    y = +a[i + 2];\n\n    if (isRelative) {\n      x += relativeX;\n      y += relativeY;\n    }\n\n    if (!i) {\n      startX = x;\n      startY = y;\n    } // \"M\" (move)\n\n\n    if (command === \"M\") {\n      if (segment) {\n        if (segment.length < 8) {\n          //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n          path.length -= 1;\n        } else {\n          points += segment.length;\n        }\n      }\n\n      relativeX = startX = x;\n      relativeY = startY = y;\n      segment = [x, y];\n      path.push(segment);\n      i += 2;\n      command = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n      // \"C\" (cubic bezier)\n    } else if (command === \"C\") {\n      if (!segment) {\n        segment = [0, 0];\n      }\n\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      } //note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\n\n      segment.push(x, y, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, relativeX += a[i + 5] * 1, relativeY += a[i + 6] * 1);\n      i += 6; // \"S\" (continuation of cubic bezier)\n    } else if (command === \"S\") {\n      difX = relativeX;\n      difY = relativeY;\n\n      if (prevCommand === \"C\" || prevCommand === \"S\") {\n        difX += relativeX - segment[segment.length - 4];\n        difY += relativeY - segment[segment.length - 3];\n      }\n\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      }\n\n      segment.push(difX, difY, x, y, relativeX += a[i + 3] * 1, relativeY += a[i + 4] * 1);\n      i += 4; // \"Q\" (quadratic bezier)\n    } else if (command === \"Q\") {\n      difX = relativeX + (x - relativeX) * twoThirds;\n      difY = relativeY + (y - relativeY) * twoThirds;\n\n      if (!isRelative) {\n        relativeX = relativeY = 0;\n      }\n\n      relativeX += a[i + 3] * 1;\n      relativeY += a[i + 4] * 1;\n      segment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n      i += 4; // \"T\" (continuation of quadratic bezier)\n    } else if (command === \"T\") {\n      difX = relativeX - segment[segment.length - 4];\n      difY = relativeY - segment[segment.length - 3];\n      segment.push(relativeX + difX, relativeY + difY, x + (relativeX + difX * 1.5 - x) * twoThirds, y + (relativeY + difY * 1.5 - y) * twoThirds, relativeX = x, relativeY = y);\n      i += 2; // \"H\" (horizontal line)\n    } else if (command === \"H\") {\n      line(relativeX, relativeY, relativeX = x, relativeY);\n      i += 1; // \"V\" (vertical line)\n    } else if (command === \"V\") {\n      //adjust values because the first (and only one) isn't x in this case, it's y.\n      line(relativeX, relativeY, relativeX, relativeY = x + (isRelative ? relativeY - relativeX : 0));\n      i += 1; // \"L\" (line) or \"Z\" (close)\n    } else if (command === \"L\" || command === \"Z\") {\n      if (command === \"Z\") {\n        x = startX;\n        y = startY;\n        segment.closed = true;\n      }\n\n      if (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n        line(relativeX, relativeY, x, y);\n\n        if (command === \"L\") {\n          i += 2;\n        }\n      }\n\n      relativeX = x;\n      relativeY = y; // \"A\" (arc)\n    } else if (command === \"A\") {\n      flag1 = a[i + 4];\n      flag2 = a[i + 5];\n      difX = a[i + 6];\n      difY = a[i + 7];\n      j = 7;\n\n      if (flag1.length > 1) {\n        // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n        if (flag1.length < 3) {\n          difY = difX;\n          difX = flag2;\n          j--;\n        } else {\n          difY = flag2;\n          difX = flag1.substr(2);\n          j -= 2;\n        }\n\n        flag2 = flag1.charAt(1);\n        flag1 = flag1.charAt(0);\n      }\n\n      beziers = arcToSegment(relativeX, relativeY, +a[i + 1], +a[i + 2], +a[i + 3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX * 1, (isRelative ? relativeY : 0) + difY * 1);\n      i += j;\n\n      if (beziers) {\n        for (j = 0; j < beziers.length; j++) {\n          segment.push(beziers[j]);\n        }\n      }\n\n      relativeX = segment[segment.length - 2];\n      relativeY = segment[segment.length - 1];\n    } else {\n      console.log(errorMessage);\n    }\n  }\n\n  i = segment.length;\n\n  if (i < 6) {\n    //in case there's odd SVG like a M0,0 command at the very end.\n    path.pop();\n    i = 0;\n  } else if (segment[0] === segment[i - 2] && segment[1] === segment[i - 1]) {\n    segment.closed = true;\n  }\n\n  path.totalPoints = points + i;\n  return path;\n} //populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\n\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n  var x12 = (x1 + x2) / 2,\n      y12 = (y1 + y2) / 2,\n      x23 = (x2 + x3) / 2,\n      y23 = (y2 + y3) / 2,\n      x34 = (x3 + x4) / 2,\n      y34 = (y3 + y4) / 2,\n      x123 = (x12 + x23) / 2,\n      y123 = (y12 + y23) / 2,\n      x234 = (x23 + x34) / 2,\n      y234 = (y23 + y34) / 2,\n      x1234 = (x123 + x234) / 2,\n      y1234 = (y123 + y234) / 2,\n      dx = x4 - x1,\n      dy = y4 - y1,\n      d2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n      d3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n      length;\n\n  if (!points) {\n    points = [x1, y1, x4, y4];\n    index = 2;\n  }\n\n  points.splice(index || points.length - 2, 0, x1234, y1234);\n\n  if ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n    length = points.length;\n    bezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n    bezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n  }\n\n  return points;\n}\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\n\nexport function flatPointsToSegment(points, curviness) {\n  if (curviness === void 0) {\n    curviness = 1;\n  }\n\n  var x = points[0],\n      y = 0,\n      segment = [x, y],\n      i = 2;\n\n  for (; i < points.length; i += 2) {\n    segment.push(x, y, points[i], y = (points[i] - x) * curviness / 2, x = points[i], -y);\n  }\n\n  return segment;\n} //points is an array of x/y points, like [x, y, x, y, x, y]\n\nexport function pointsToSegment(points, curviness) {\n  //points = simplifyPoints(points, tolerance);\n  _abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\n  var l = points.length - 2,\n      x = +points[0],\n      y = +points[1],\n      nextX = +points[2],\n      nextY = +points[3],\n      segment = [x, y, x, y],\n      dx2 = nextX - x,\n      dy2 = nextY - y,\n      closed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l + 1] - y) < 0.001,\n      prevX,\n      prevY,\n      i,\n      dx1,\n      dy1,\n      r1,\n      r2,\n      r3,\n      tl,\n      mx1,\n      mx2,\n      mxm,\n      my1,\n      my2,\n      mym;\n\n  if (closed) {\n    // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n    points.push(nextX, nextY);\n    nextX = x;\n    nextY = y;\n    x = points[l - 2];\n    y = points[l - 1];\n    points.unshift(x, y);\n    l += 4;\n  }\n\n  curviness = curviness || curviness === 0 ? +curviness : 1;\n\n  for (i = 2; i < l; i += 2) {\n    prevX = x;\n    prevY = y;\n    x = nextX;\n    y = nextY;\n    nextX = +points[i + 2];\n    nextY = +points[i + 3];\n\n    if (x === nextX && y === nextY) {\n      continue;\n    }\n\n    dx1 = dx2;\n    dy1 = dy2;\n    dx2 = nextX - x;\n    dy2 = nextY - y;\n    r1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\n    r2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n    r3 = _sqrt(Math.pow(dx2 / r2 + dx1 / r1, 2) + Math.pow(dy2 / r2 + dy1 / r1, 2));\n    tl = (r1 + r2) * curviness * 0.25 / r3;\n    mx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n    mx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n    mxm = x - (mx1 + ((mx2 - mx1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\n    my1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n    my2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n    mym = y - (my1 + ((my2 - my1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\n\n    if (x !== prevX || y !== prevY) {\n      segment.push(_round(mx1 + mxm), // first control point\n      _round(my1 + mym), _round(x), // anchor\n      _round(y), _round(mx2 + mxm), // second control point\n      _round(my2 + mym));\n    }\n  }\n\n  x !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : segment.length -= 2;\n\n  if (segment.length === 2) {\n    // only one point!\n    segment.push(x, y, x, y, x, y);\n  } else if (closed) {\n    segment.splice(0, 6);\n    segment.length = segment.length - 6;\n  }\n\n  return segment;\n} //returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\n\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n  var dx = x2 - x1,\n      dy = y2 - y1,\n      t;\n\n  if (dx || dy) {\n    t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n\n    if (t > 1) {\n      x1 = x2;\n      y1 = y2;\n    } else if (t > 0) {\n      x1 += dx * t;\n      y1 += dy * t;\n    }\n  }\n\n  return Math.pow(x - x1, 2) + Math.pow(y - y1, 2);\n}\n\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n  var maxSqDist = tolerance,\n      firstX = points[first],\n      firstY = points[first + 1],\n      lastX = points[last],\n      lastY = points[last + 1],\n      index,\n      i,\n      d;\n\n  for (i = first + 2; i < last; i += 2) {\n    d = pointToSegDist(points[i], points[i + 1], firstX, firstY, lastX, lastY);\n\n    if (d > maxSqDist) {\n      index = i;\n      maxSqDist = d;\n    }\n  }\n\n  if (maxSqDist > tolerance) {\n    index - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n    simplified.push(points[index], points[index + 1]);\n    last - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n  }\n} //points is an array of x/y values like [x, y, x, y, x, y]\n\n\nexport function simplifyPoints(points, tolerance) {\n  var prevX = parseFloat(points[0]),\n      prevY = parseFloat(points[1]),\n      temp = [prevX, prevY],\n      l = points.length - 2,\n      i,\n      x,\n      y,\n      dx,\n      dy,\n      result,\n      last;\n  tolerance = Math.pow(tolerance || 1, 2);\n\n  for (i = 2; i < l; i += 2) {\n    x = parseFloat(points[i]);\n    y = parseFloat(points[i + 1]);\n    dx = prevX - x;\n    dy = prevY - y;\n\n    if (dx * dx + dy * dy > tolerance) {\n      temp.push(x, y);\n      prevX = x;\n      prevY = y;\n    }\n  }\n\n  temp.push(parseFloat(points[l]), parseFloat(points[l + 1]));\n  last = temp.length - 2;\n  result = [temp[0], temp[1]];\n  simplifyStep(temp, 0, last, tolerance, result);\n  result.push(temp[last], temp[last + 1]);\n  return result;\n}\n\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n  var inc = (end - start) / slices,\n      best = 0,\n      t = start,\n      x,\n      y,\n      d,\n      dx,\n      dy,\n      inv;\n  _bestDistance = _largeNum;\n\n  while (t <= end) {\n    inv = 1 - t;\n    x = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n    y = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n    dx = x - px;\n    dy = y - py;\n    d = dx * dx + dy * dy;\n\n    if (d < _bestDistance) {\n      _bestDistance = d;\n      best = t;\n    }\n\n    t += inc;\n  }\n\n  return iterations > 1 ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\n\nexport function getClosestData(rawPath, x, y, slices) {\n  //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n  var closest = {\n    j: 0,\n    i: 0,\n    t: 0\n  },\n      bestDistance = _largeNum,\n      i,\n      j,\n      t,\n      segment;\n\n  for (j = 0; j < rawPath.length; j++) {\n    segment = rawPath[j];\n\n    for (i = 0; i < segment.length; i += 6) {\n      t = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\n\n      if (bestDistance > _bestDistance) {\n        bestDistance = _bestDistance;\n        closest.j = j;\n        closest.i = i;\n        closest.t = t;\n      }\n    }\n  }\n\n  return closest;\n} //subdivide a Segment closest to a specific x,y coordinate\n\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n  var l = segment.length,\n      bestDistance = _largeNum,\n      bestT = 0,\n      bestSegmentIndex = 0,\n      t,\n      i;\n  slices = slices || 20;\n  iterations = iterations || 3;\n\n  for (i = 0; i < l; i += 6) {\n    t = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\n\n    if (bestDistance > _bestDistance) {\n      bestDistance = _bestDistance;\n      bestT = t;\n      bestSegmentIndex = i;\n    }\n  }\n\n  t = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex + 1], segment[bestSegmentIndex + 2], segment[bestSegmentIndex + 3], segment[bestSegmentIndex + 4], segment[bestSegmentIndex + 5], segment[bestSegmentIndex + 6], segment[bestSegmentIndex + 7]);\n  subdivideSegment(segment, bestSegmentIndex, t);\n  return bestSegmentIndex + 6;\n}\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\n\nexport function rawPathToString(rawPath) {\n  if (_isNumber(rawPath[0])) {\n    //in case a segment is passed in instead\n    rawPath = [rawPath];\n  }\n\n  var result = \"\",\n      l = rawPath.length,\n      sl,\n      s,\n      i,\n      segment;\n\n  for (s = 0; s < l; s++) {\n    segment = rawPath[s];\n    result += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n    sl = segment.length;\n\n    for (i = 2; i < sl; i++) {\n      result += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n    }\n\n    if (segment.closed) {\n      result += \"z\";\n    }\n  }\n\n  return result;\n}\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIA,WAAW,GAAG,kDAAkD;EAChEC,WAAW,GAAG,yCAAyC;EACvDC,WAAW,GAAG,+BAA+B;EAC7CC,YAAY,GAAG,2BAA2B;EAC1CC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;EACxBC,QAAQ,GAAG,GAAG,GAAGF,IAAI,CAACC,EAAE;EACxBE,IAAI,GAAGH,IAAI,CAACI,GAAG;EACfC,IAAI,GAAGL,IAAI,CAACM,GAAG;EACfC,IAAI,GAAGP,IAAI,CAACQ,GAAG;EACfC,KAAK,GAAGT,IAAI,CAACU,IAAI;EACjBC,MAAM,GAAGX,IAAI,CAACY,KAAK;EACnBC,SAAS,GAAG,GAAG;EACfC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC,CAAC;EACGC,SAAS,GAAG,SAASA,SAASA,CAACD,KAAK,EAAE;IACxC,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC,CAAC;EACGE,YAAY,GAAG,SAASA,YAAYA,CAACF,KAAK,EAAE;IAC9C,OAAO,OAAOA,KAAK,KAAK,WAAW;EACrC,CAAC;EACGG,KAAK,GAAG,CAAC,CAAC;EACVC,MAAM,GAAG,CAAC,CAAC;EACXC,YAAY,GAAG,GAAG;EAClBC,aAAa,GAAG,SAASA,aAAaA,CAACC,QAAQ,EAAE;IACnD,OAAOtB,IAAI,CAACuB,KAAK,CAAC,CAACD,QAAQ,GAAGT,SAAS,IAAI,CAAC,GAAGO,YAAY,CAAC,GAAGA,YAAY,KAAKE,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvG,CAAC;EACG;EACJE,MAAM,GAAG,SAASA,MAAMA,CAACT,KAAK,EAAE;IAC9B,OAAOf,IAAI,CAACuB,KAAK,CAACR,KAAK,GAAGK,YAAY,CAAC,GAAGA,YAAY,IAAI,CAAC;EAC7D,CAAC;EACGK,aAAa,GAAG,SAASA,aAAaA,CAACV,KAAK,EAAE;IAChD,OAAOf,IAAI,CAACuB,KAAK,CAACR,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;EAC7C,CAAC;EACGW,aAAa,GAAG,SAASA,aAAaA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAClE,IAAIC,OAAO,GAAGJ,OAAO,CAACC,QAAQ,CAAC;MAC3BI,KAAK,GAAGF,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGG,gBAAgB,CAACF,OAAO,EAAEF,CAAC,EAAEC,CAAC,CAAC;IAEzD,IAAI,CAACE,KAAK,IAAI,CAACF,CAAC,KAAKE,KAAK,GAAGH,CAAC,GAAG,CAAC,GAAGE,OAAO,CAACG,MAAM,EAAE;MACnDP,OAAO,CAACQ,MAAM,CAACP,QAAQ,EAAE,CAAC,EAAEG,OAAO,CAACK,KAAK,CAAC,CAAC,EAAEP,CAAC,GAAGG,KAAK,GAAG,CAAC,CAAC,CAAC;MAC5DD,OAAO,CAACI,MAAM,CAAC,CAAC,EAAEN,CAAC,GAAGG,KAAK,CAAC;MAC5B,OAAO,CAAC;IACV;EACF,CAAC;EACGK,eAAe,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAEJ,MAAM,EAAEZ,QAAQ,EAAE;IACxE;IACA,IAAIiB,CAAC,GAAGD,OAAO,CAACJ,MAAM;MAClBL,CAAC,GAAG,CAAC,EAAEP,QAAQ,GAAGiB,CAAC,CAAC;IAExB,IAAID,OAAO,CAACT,CAAC,CAAC,GAAGK,MAAM,EAAE;MACvB,OAAO,EAAEL,CAAC,IAAIS,OAAO,CAACT,CAAC,CAAC,GAAGK,MAAM,EAAE,CAAC;MAEpCL,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,OAAOS,OAAO,CAAC,EAAET,CAAC,CAAC,GAAGK,MAAM,IAAIL,CAAC,GAAGU,CAAC,EAAE,CAAC;IAC1C;IAEA,OAAOV,CAAC,GAAGU,CAAC,GAAGV,CAAC,GAAGU,CAAC,GAAG,CAAC;EAC1B,CAAC;EACGC,eAAe,GAAG,SAASA,eAAeA,CAACb,OAAO,EAAEc,SAAS,EAAE;IACjE,IAAIZ,CAAC,GAAGF,OAAO,CAACO,MAAM;IACtBO,SAAS,IAAId,OAAO,CAACe,OAAO,CAAC,CAAC;IAE9B,OAAOb,CAAC,EAAE,EAAE;MACVF,OAAO,CAACE,CAAC,CAAC,CAACc,QAAQ,IAAIC,cAAc,CAACjB,OAAO,CAACE,CAAC,CAAC,CAAC;IACnD;EACF,CAAC;EACGgB,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACvDA,IAAI,CAACC,WAAW,GAAGF,MAAM,CAACE,WAAW;IAErC,IAAIF,MAAM,CAACR,OAAO,EAAE;MAClB;MACAS,IAAI,CAACT,OAAO,GAAGQ,MAAM,CAACR,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC;MACtCW,IAAI,CAACE,MAAM,GAAGH,MAAM,CAACG,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC;MACpCW,IAAI,CAACG,SAAS,GAAGJ,MAAM,CAACI,SAAS;MACjCH,IAAI,CAACI,UAAU,GAAGL,MAAM,CAACK,UAAU;IACrC,CAAC,MAAM,IAAIL,MAAM,CAACM,WAAW,EAAE;MAC7B;MACAL,IAAI,CAACK,WAAW,GAAGN,MAAM,CAACM,WAAW;IACvC;IAEA,OAAOL,IAAI;EACb,CAAC;EACG;EACJM,cAAc,GAAG,SAASA,cAAcA,CAAC1B,OAAO,EAAEI,OAAO,EAAE;IACzD,IAAIuB,KAAK,GAAG3B,OAAO,CAACO,MAAM;MACtBqB,OAAO,GAAG5B,OAAO,CAAC2B,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE;MAClCf,CAAC,GAAGgB,OAAO,CAACrB,MAAM;IAEtB,IAAIoB,KAAK,IAAIvB,OAAO,CAAC,CAAC,CAAC,KAAKwB,OAAO,CAAChB,CAAC,GAAG,CAAC,CAAC,IAAIR,OAAO,CAAC,CAAC,CAAC,KAAKwB,OAAO,CAAChB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC3ER,OAAO,GAAGwB,OAAO,CAACC,MAAM,CAACzB,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1CkB,KAAK,EAAE;IACT;IAEA3B,OAAO,CAAC2B,KAAK,CAAC,GAAGvB,OAAO;EAC1B,CAAC;EACG0B,aAAa;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASC,UAAUA,CAAC3C,KAAK,EAAE;EAChCA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,IAAIjB,YAAY,CAAC6D,IAAI,CAAC5C,KAAK,CAAC,GAAG6C,QAAQ,CAACC,aAAa,CAAC9C,KAAK,CAAC,IAAIA,KAAK,GAAGA,KAAK;EACrG,IAAI+C,CAAC,GAAG/C,KAAK,CAACgD,YAAY,GAAGhD,KAAK,GAAG,CAAC;IAClCY,OAAO;EAEX,IAAImC,CAAC,KAAK/C,KAAK,GAAGA,KAAK,CAACgD,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1C;IACA,IAAI,CAACD,CAAC,CAACE,OAAO,EAAE;MACdF,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC;IAChB;IAEArC,OAAO,GAAGmC,CAAC,CAACE,OAAO,CAACjD,KAAK,CAAC;IAC1B,OAAOY,OAAO,IAAI,CAACA,OAAO,CAACsC,MAAM,GAAGtC,OAAO,GAAGmC,CAAC,CAACE,OAAO,CAACjD,KAAK,CAAC,GAAGmD,eAAe,CAACnD,KAAK,CAAC;EACzF;EAEA,OAAO,CAACA,KAAK,GAAGoD,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC,GAAGtD,SAAS,CAACC,KAAK,CAAC,GAAGmD,eAAe,CAACnD,KAAK,CAAC,GAAGC,SAAS,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,CAAC,GAAGA,KAAK;AAC3K,CAAC,CAAC;;AAEF,OAAO,SAASsD,WAAWA,CAAC1C,OAAO,EAAE;EACnC,IAAI2C,CAAC,GAAG,EAAE;IACNzC,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAGF,OAAO,CAACO,MAAM,EAAEL,CAAC,EAAE,EAAE;IAC9ByC,CAAC,CAACzC,CAAC,CAAC,GAAGgB,aAAa,CAAClB,OAAO,CAACE,CAAC,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC;EACvD;EAEA,OAAOS,aAAa,CAAClB,OAAO,EAAE2C,CAAC,CAAC;AAClC;AACA,OAAO,SAAS1B,cAAcA,CAACb,OAAO,EAAE;EACtC,IAAIF,CAAC,GAAG,CAAC;IACL0C,CAAC;EACLxC,OAAO,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEnB,OAAOb,CAAC,GAAGE,OAAO,CAACG,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;IACjC0C,CAAC,GAAGxC,OAAO,CAACF,CAAC,CAAC;IACdE,OAAO,CAACF,CAAC,CAAC,GAAGE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IAC3BE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAG0C,CAAC;EACpB;EAEAxC,OAAO,CAACY,QAAQ,GAAG,CAACZ,OAAO,CAACY,QAAQ;AACtC;AAEA,IAAI6B,WAAW,GAAG,SAASA,WAAWA,CAACV,CAAC,EAAEW,MAAM,EAAE;IAChD,IAAIC,IAAI,GAAGd,QAAQ,CAACe,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;MACrEC,IAAI,GAAG,EAAE,CAACxC,KAAK,CAACyC,IAAI,CAACf,CAAC,CAACgB,UAAU,CAAC;MAClCjD,CAAC,GAAG+C,IAAI,CAAC1C,MAAM;MACf6C,IAAI;IACRN,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,GAAG;IAE3B,OAAO,EAAE5C,CAAC,GAAG,CAAC,CAAC,EAAE;MACfkD,IAAI,GAAGH,IAAI,CAAC/C,CAAC,CAAC,CAACmD,QAAQ,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC;;MAEvC,IAAIR,MAAM,CAACS,OAAO,CAAC,GAAG,GAAGH,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;QACxCL,IAAI,CAACS,cAAc,CAAC,IAAI,EAAEJ,IAAI,EAAEH,IAAI,CAAC/C,CAAC,CAAC,CAACuD,SAAS,CAAC;MACpD;IACF;IAEA,OAAOV,IAAI;EACb,CAAC;EACGW,UAAU,GAAG;IACfC,IAAI,EAAE,wBAAwB;IAC9BC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,aAAa;IACtBC,IAAI,EAAE;EACR,CAAC;EACGC,UAAU,GAAG,SAASA,UAAUA,CAAC5B,CAAC,EAAE6B,KAAK,EAAE;IAC7C,IAAIC,KAAK,GAAGD,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;MACrCC,GAAG,GAAG,CAAC,CAAC;MACRjE,CAAC,GAAG+D,KAAK,CAAC1D,MAAM;IAEpB,OAAO,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE;MACfiE,GAAG,CAACF,KAAK,CAAC/D,CAAC,CAAC,CAAC,GAAG,CAACiC,CAAC,CAACC,YAAY,CAAC6B,KAAK,CAAC/D,CAAC,CAAC,CAAC,IAAI,CAAC;IAChD;IAEA,OAAOiE,GAAG;EACZ,CAAC,CAAC,CAAC;;AAGH,OAAO,SAASC,aAAaA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAGF,OAAO,CAACG,OAAO,CAAClB,WAAW,CAAC,CAAC;IACpCmB,IAAI,GAAG,cAAc;IACrBC,IAAI;IACJC,CAAC;IACD/B,CAAC;IACDgC,CAAC;IACDC,EAAE;IACF9B,IAAI;IACJ+B,KAAK;IACLC,MAAM;IACNC,MAAM;IACNC,CAAC;IACDC,CAAC;IACDC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACF3C,IAAI;EAER,IAAIsB,IAAI,KAAK,MAAM,IAAI,CAACF,OAAO,CAACwB,OAAO,EAAE;IACvC,OAAOxB,OAAO;EAChB;EAEAtB,IAAI,GAAGF,WAAW,CAACwB,OAAO,EAAE,mDAAmD,CAAC;EAChFpB,IAAI,GAAGc,UAAU,CAACM,OAAO,EAAEX,UAAU,CAACa,IAAI,CAAC,CAAC;EAE5C,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnBK,CAAC,GAAG3B,IAAI,CAAC6C,EAAE;IACXjB,EAAE,GAAG5B,IAAI,CAAC4B,EAAE,IAAID,CAAC;IACjBD,CAAC,GAAG1B,IAAI,CAAC0B,CAAC;IACV/B,CAAC,GAAGK,IAAI,CAACL,CAAC;IACVqC,CAAC,GAAGhC,IAAI,CAAC8C,KAAK,GAAGnB,CAAC,GAAG,CAAC;IACtBM,CAAC,GAAGjC,IAAI,CAAC+C,MAAM,GAAGnB,EAAE,GAAG,CAAC;IAExB,IAAID,CAAC,IAAIC,EAAE,EAAE;MACX;MACAM,EAAE,GAAGR,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGH,IAAI,CAAC;MACvBW,EAAE,GAAGT,CAAC,GAAGC,CAAC;MACVS,EAAE,GAAGD,EAAE,GAAGH,CAAC;MACXK,EAAE,GAAGD,EAAE,GAAGT,CAAC,GAAGH,IAAI;MAClBc,EAAE,GAAGF,EAAE,GAAGT,CAAC;MACXY,EAAE,GAAG5C,CAAC,GAAGiC,EAAE,IAAI,CAAC,GAAGJ,IAAI,CAAC;MACxBgB,EAAE,GAAG7C,CAAC,GAAGiC,EAAE;MACXa,EAAE,GAAGD,EAAE,GAAGP,CAAC;MACXS,EAAE,GAAGD,EAAE,GAAGb,EAAE,GAAGJ,IAAI;MACnBmB,EAAE,GAAGF,EAAE,GAAGb,EAAE;MACZH,IAAI,GAAG,GAAG,GAAGa,EAAE,GAAG,GAAG,GAAGE,EAAE,GAAG,IAAI,GAAGC,EAAE,GAAG,IAAI,GAAG,CAACH,EAAE,EAAEI,EAAE,EAAEL,EAAE,EAAEM,EAAE,EAAEP,EAAE,EAAEO,EAAE,EAAEP,EAAE,GAAG,CAACA,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAEQ,EAAE,EAAER,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAEQ,EAAE,EAAER,EAAE,EAAEQ,EAAE,EAAET,EAAE,EAAES,EAAE,EAAEjB,CAAC,EAAEgB,EAAE,EAAEhB,CAAC,EAAEe,EAAE,EAAEf,CAAC,EAAEe,EAAE,GAAG,CAACA,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAEd,CAAC,EAAEc,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAEd,CAAC,EAAEc,EAAE,EAAEd,CAAC,EAAEa,EAAE,EAAEL,EAAE,EAAEvC,CAAC,EAAEwC,EAAE,EAAExC,CAAC,EAAEwC,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAExC,CAAC,EAAEyC,EAAE,GAAG,CAACA,EAAE,GAAGD,EAAE,IAAI,CAAC,EAAExC,CAAC,EAAEyC,EAAE,EAAEzC,CAAC,EAAE0C,EAAE,EAAE1C,CAAC,EAAE2C,EAAE,EAAEC,EAAE,EAAED,EAAE,EAAEE,EAAE,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;IAC9T,CAAC,MAAM;MACLvB,IAAI,GAAG,GAAG,IAAIC,CAAC,GAAGM,CAAC,CAAC,GAAG,GAAG,GAAGrC,CAAC,GAAG,IAAI,GAAGsC,CAAC,GAAG,IAAI,GAAG,CAACD,CAAC,GAAG,IAAI,GAAG,CAACC,CAAC,GAAG,IAAI,GAAGD,CAAC,GAAG,GAAG;IACpF;EACF,CAAC,MAAM,IAAIV,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,SAAS,EAAE;IAClD,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBK,CAAC,GAAGC,EAAE,GAAG5B,IAAI,CAAC2B,CAAC;MACfG,MAAM,GAAGH,CAAC,GAAGH,IAAI;IACnB,CAAC,MAAM;MACLG,CAAC,GAAG3B,IAAI,CAAC6C,EAAE;MACXjB,EAAE,GAAG5B,IAAI,CAAC4B,EAAE;MACZE,MAAM,GAAGF,EAAE,GAAGJ,IAAI;IACpB;IAEAE,CAAC,GAAG1B,IAAI,CAACiD,EAAE;IACXtD,CAAC,GAAGK,IAAI,CAACkD,EAAE;IACXrB,KAAK,GAAGF,CAAC,GAAGH,IAAI;IAChBC,IAAI,GAAG,GAAG,IAAIC,CAAC,GAAGC,CAAC,CAAC,GAAG,GAAG,GAAGhC,CAAC,GAAG,IAAI,GAAG,CAAC+B,CAAC,GAAGC,CAAC,EAAEhC,CAAC,GAAGmC,MAAM,EAAEJ,CAAC,GAAGG,KAAK,EAAElC,CAAC,GAAGiC,EAAE,EAAEF,CAAC,EAAE/B,CAAC,GAAGiC,EAAE,EAAEF,CAAC,GAAGG,KAAK,EAAElC,CAAC,GAAGiC,EAAE,EAAEF,CAAC,GAAGC,CAAC,EAAEhC,CAAC,GAAGmC,MAAM,EAAEJ,CAAC,GAAGC,CAAC,EAAEhC,CAAC,EAAE+B,CAAC,GAAGC,CAAC,EAAEhC,CAAC,GAAGmC,MAAM,EAAEJ,CAAC,GAAGG,KAAK,EAAElC,CAAC,GAAGiC,EAAE,EAAEF,CAAC,EAAE/B,CAAC,GAAGiC,EAAE,EAAEF,CAAC,GAAGG,KAAK,EAAElC,CAAC,GAAGiC,EAAE,EAAEF,CAAC,GAAGC,CAAC,EAAEhC,CAAC,GAAGmC,MAAM,EAAEJ,CAAC,GAAGC,CAAC,EAAEhC,CAAC,CAAC,CAACqD,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC5P,CAAC,MAAM,IAAI1B,IAAI,KAAK,MAAM,EAAE;IAC1BG,IAAI,GAAG,GAAG,GAAGzB,IAAI,CAACmD,EAAE,GAAG,GAAG,GAAGnD,IAAI,CAACoD,EAAE,GAAG,IAAI,GAAGpD,IAAI,CAACkC,EAAE,GAAG,GAAG,GAAGlC,IAAI,CAACuC,EAAE,CAAC,CAAC;EACzE,CAAC,MAAM,IAAIjB,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,SAAS,EAAE;IACpDS,MAAM,GAAG,CAACX,OAAO,CAACjC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAEkE,KAAK,CAACrI,WAAW,CAAC,IAAI,EAAE;IACvE0G,CAAC,GAAGK,MAAM,CAAC3E,KAAK,CAAC,CAAC;IAClBuC,CAAC,GAAGoC,MAAM,CAAC3E,KAAK,CAAC,CAAC;IAClBqE,IAAI,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAG/B,CAAC,GAAG,IAAI,GAAGoC,MAAM,CAACiB,IAAI,CAAC,GAAG,CAAC;IAElD,IAAI1B,IAAI,KAAK,SAAS,EAAE;MACtBG,IAAI,IAAI,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAG/B,CAAC,GAAG,GAAG;IACjC;EACF;EAEAG,IAAI,CAACwD,YAAY,CAAC,GAAG,EAAEC,eAAe,CAACzD,IAAI,CAAC0D,UAAU,GAAGlE,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC;EAEhF,IAAIJ,IAAI,IAAID,OAAO,CAACqC,UAAU,EAAE;IAC9BrC,OAAO,CAACqC,UAAU,CAACC,YAAY,CAAC5D,IAAI,EAAEsB,OAAO,CAAC;IAC9CA,OAAO,CAACqC,UAAU,CAACE,WAAW,CAACvC,OAAO,CAAC;EACzC;EAEA,OAAOtB,IAAI;AACb,CAAC,CAAC;;AAEF,OAAO,SAAS8D,qBAAqBA,CAAC7G,OAAO,EAAEL,QAAQ,EAAE;EACvD,IAAImH,CAAC,GAAGC,eAAe,CAAC/G,OAAO,EAAEL,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGA,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAAC;EACvF,OAAOqH,oBAAoB,CAACF,CAAC,CAAC1G,OAAO,EAAE0G,CAAC,CAAC5G,CAAC,EAAE4G,CAAC,CAAC3G,CAAC,CAAC;AAClD;AAEA,SAAS6G,oBAAoBA,CAAC5G,OAAO,EAAEF,CAAC,EAAEC,CAAC,EAAE;EAC3C,IAAIwC,CAAC,GAAGvC,OAAO,CAACF,CAAC,CAAC;IACd+G,CAAC,GAAG7G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IAClBgH,CAAC,GAAG9G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IAClByE,CAAC;EACLhC,CAAC,IAAI,CAACsE,CAAC,GAAGtE,CAAC,IAAIxC,CAAC;EAChB8G,CAAC,IAAI,CAACC,CAAC,GAAGD,CAAC,IAAI9G,CAAC;EAChBwC,CAAC,IAAI,CAACsE,CAAC,GAAGtE,CAAC,IAAIxC,CAAC;EAChBwE,CAAC,GAAGsC,CAAC,GAAG,CAACC,CAAC,GAAG,CAAC9G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGgH,CAAC,IAAI/G,CAAC,GAAG8G,CAAC,IAAI9G,CAAC,GAAGwC,CAAC;EAClDA,CAAC,GAAGvC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;EAClB+G,CAAC,GAAG7G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;EAClBgH,CAAC,GAAG9G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;EAClByC,CAAC,IAAI,CAACsE,CAAC,GAAGtE,CAAC,IAAIxC,CAAC;EAChB8G,CAAC,IAAI,CAACC,CAAC,GAAGD,CAAC,IAAI9G,CAAC;EAChBwC,CAAC,IAAI,CAACsE,CAAC,GAAGtE,CAAC,IAAIxC,CAAC;EAChB,OAAON,MAAM,CAACb,MAAM,CAACiI,CAAC,GAAG,CAACC,CAAC,GAAG,CAAC9G,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGgH,CAAC,IAAI/G,CAAC,GAAG8G,CAAC,IAAI9G,CAAC,GAAGwC,CAAC,EAAEgC,CAAC,CAAC,GAAGpG,QAAQ,CAAC;AACrF;AAEA,OAAO,SAAS4I,YAAYA,CAACnH,OAAO,EAAEoH,KAAK,EAAEC,GAAG,EAAE;EAChDA,GAAG,GAAG/H,YAAY,CAAC+H,GAAG,CAAC,GAAG,CAAC,GAAGvH,aAAa,CAACuH,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEvDD,KAAK,GAAGtH,aAAa,CAACsH,KAAK,CAAC,IAAI,CAAC;EACjC,IAAIE,KAAK,GAAGjJ,IAAI,CAACkJ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE3I,IAAI,CAACyI,GAAG,GAAGD,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IACjDrE,IAAI,GAAGL,WAAW,CAAC1C,OAAO,CAAC;EAE/B,IAAIoH,KAAK,GAAGC,GAAG,EAAE;IACfD,KAAK,GAAG,CAAC,GAAGA,KAAK;IACjBC,GAAG,GAAG,CAAC,GAAGA,GAAG;IAEbxG,eAAe,CAACkC,IAAI,CAAC;IAErBA,IAAI,CAAC1B,WAAW,GAAG,CAAC;EACtB;EAEA,IAAI+F,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAG,CAAC,EAAE;IACxB,IAAIG,MAAM,GAAGnJ,IAAI,CAACQ,GAAG,CAAC,CAAC,CAACR,IAAI,CAACoJ,GAAG,CAACL,KAAK,EAAEC,GAAG,CAAC,CAAC,GAAG,CAAC;IACjDD,KAAK,IAAII,MAAM;IACfH,GAAG,IAAIG,MAAM;EACf;EAEAzE,IAAI,CAAC1B,WAAW,IAAIqG,wBAAwB,CAAC3E,IAAI,CAAC;EAClD,IAAI4E,IAAI,GAAGN,GAAG,GAAG,CAAC;IACdO,CAAC,GAAGb,eAAe,CAAChE,IAAI,EAAEqE,KAAK,EAAE7H,KAAK,EAAE,IAAI,CAAC;IAC7C4C,CAAC,GAAG4E,eAAe,CAAChE,IAAI,EAAEsE,GAAG,EAAE7H,MAAM,CAAC;IACtCqI,IAAI,GAAG1F,CAAC,CAAC/B,OAAO;IAChB0H,IAAI,GAAGF,CAAC,CAACxH,OAAO;IAChB2H,SAAS,GAAG5F,CAAC,CAAClC,QAAQ;IACtB+H,SAAS,GAAGJ,CAAC,CAAC3H,QAAQ;IACtBgI,EAAE,GAAG9F,CAAC,CAACjC,CAAC;IACRgI,EAAE,GAAGN,CAAC,CAAC1H,CAAC;IACRiI,WAAW,GAAGH,SAAS,KAAKD,SAAS;IACrCK,UAAU,GAAGH,EAAE,KAAKC,EAAE,IAAIC,WAAW;IACrCE,WAAW;IACXC,MAAM;IACNC,MAAM;IACNrI,CAAC;IACDkB,IAAI;IACJoH,aAAa;IACb5H,CAAC;IACD6H,CAAC;EAEL,IAAId,IAAI,IAAIL,KAAK,EAAE;IACjBe,WAAW,GAAGN,SAAS,GAAGC,SAAS,IAAIG,WAAW,IAAIF,EAAE,GAAGC,EAAE,IAAIE,UAAU,IAAIjG,CAAC,CAAChC,CAAC,GAAGyH,CAAC,CAACzH,CAAC;IAExF,IAAIJ,aAAa,CAACgD,IAAI,EAAEiF,SAAS,EAAEE,EAAE,EAAEN,CAAC,CAACzH,CAAC,CAAC,EAAE;MAC3C6H,SAAS,EAAE;MAEX,IAAI,CAACK,WAAW,EAAE;QAChBN,SAAS,EAAE;QAEX,IAAIK,UAAU,EAAE;UACdjG,CAAC,CAAChC,CAAC,GAAG,CAACgC,CAAC,CAAChC,CAAC,GAAGyH,CAAC,CAACzH,CAAC,KAAK,CAAC,GAAGyH,CAAC,CAACzH,CAAC,CAAC;UAC7B8H,EAAE,GAAG,CAAC;QACR,CAAC,MAAM,IAAIE,WAAW,EAAE;UACtBF,EAAE,IAAIC,EAAE;QACV;MACF;IACF;IAEA,IAAI7J,IAAI,CAACQ,GAAG,CAAC,CAAC,IAAIwI,GAAG,GAAGD,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE;MACtCW,SAAS,GAAGC,SAAS,GAAG,CAAC;IAC3B,CAAC,MAAM,IAAI,CAAC7F,CAAC,CAAChC,CAAC,IAAI4H,SAAS,EAAE;MAC5BA,SAAS,EAAE;IACb,CAAC,MAAM,IAAIhI,aAAa,CAACgD,IAAI,EAAEgF,SAAS,EAAEE,EAAE,EAAE9F,CAAC,CAAChC,CAAC,CAAC,IAAIkI,WAAW,EAAE;MACjEL,SAAS,EAAE;IACb;IAEA,IAAIJ,CAAC,CAACzH,CAAC,KAAK,CAAC,EAAE;MACb6H,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAIjF,IAAI,CAACxC,MAAM;IAC3C;IAEAa,IAAI,GAAG,EAAE;IACToH,aAAa,GAAGzF,IAAI,CAACxC,MAAM;IAC3BK,CAAC,GAAG,CAAC,GAAG4H,aAAa,GAAGlB,KAAK;IAC7BmB,CAAC,GAAGT,SAAS;IACbpH,CAAC,IAAI,CAAC4H,aAAa,GAAGR,SAAS,GAAGD,SAAS,IAAIS,aAAa;IAE5D,KAAKtI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;MACtBwB,cAAc,CAACN,IAAI,EAAE2B,IAAI,CAAC0F,CAAC,EAAE,GAAGD,aAAa,CAAC,CAAC;IACjD;IAEAzF,IAAI,GAAG3B,IAAI;EACb,CAAC,MAAM;IACLmH,MAAM,GAAGpG,CAAC,CAAChC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGG,gBAAgB,CAACuH,IAAI,EAAEI,EAAE,EAAE9F,CAAC,CAAChC,CAAC,CAAC;IAExD,IAAIiH,KAAK,KAAKC,GAAG,EAAE;MACjBiB,MAAM,GAAGhI,gBAAgB,CAACwH,IAAI,EAAEI,EAAE,EAAEE,UAAU,GAAGR,CAAC,CAACzH,CAAC,GAAGgC,CAAC,CAAChC,CAAC,GAAGyH,CAAC,CAACzH,CAAC,CAAC;MACjEgI,WAAW,KAAKI,MAAM,IAAID,MAAM,CAAC;MACjCT,IAAI,CAACrH,MAAM,CAACyH,EAAE,GAAGM,MAAM,GAAG,CAAC,CAAC;MAC5B,CAACD,MAAM,IAAIJ,EAAE,KAAKJ,IAAI,CAACtH,MAAM,CAAC,CAAC,EAAE0H,EAAE,GAAGI,MAAM,CAAC;MAC7CpI,CAAC,GAAG6C,IAAI,CAACxC,MAAM;MAEf,OAAOL,CAAC,EAAE,EAAE;QACV;QACA,CAACA,CAAC,GAAG8H,SAAS,IAAI9H,CAAC,GAAG6H,SAAS,KAAKhF,IAAI,CAACvC,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;MACvD;IACF,CAAC,MAAM;MACL2H,IAAI,CAACa,KAAK,GAAG1B,oBAAoB,CAACa,IAAI,EAAEI,EAAE,GAAGM,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEzDN,EAAE,IAAIM,MAAM;MACZX,CAAC,GAAGC,IAAI,CAACI,EAAE,CAAC;MACZ9F,CAAC,GAAG0F,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC;MAChBJ,IAAI,CAACtH,MAAM,GAAGsH,IAAI,CAACxG,WAAW,GAAG,CAAC;MAClCwG,IAAI,CAACpG,WAAW,GAAGsB,IAAI,CAACtB,WAAW,GAAG,CAAC;MACvCoG,IAAI,CAACc,IAAI,CAACf,CAAC,EAAEzF,CAAC,EAAEyF,CAAC,EAAEzF,CAAC,EAAEyF,CAAC,EAAEzF,CAAC,EAAEyF,CAAC,EAAEzF,CAAC,CAAC;IACnC;EACF;EAEAY,IAAI,CAAC1B,WAAW,GAAG,CAAC;EACpB,OAAO0B,IAAI;AACb,CAAC,CAAC;;AAEF,SAAS6F,cAAcA,CAACxI,OAAO,EAAEyI,UAAU,EAAEC,SAAS,EAAE;EACtDD,UAAU,GAAGA,UAAU,IAAI,CAAC;EAE5B,IAAI,CAACzI,OAAO,CAACO,OAAO,EAAE;IACpBP,OAAO,CAACO,OAAO,GAAG,EAAE;IACpBP,OAAO,CAACkB,MAAM,GAAG,EAAE;EACrB;EAEA,IAAIE,UAAU,GAAG,CAAC,CAACpB,OAAO,CAACoB,UAAU,IAAI,EAAE;IACvCuH,GAAG,GAAG,CAAC,GAAGvH,UAAU;IACpBwH,QAAQ,GAAGF,SAAS,GAAGD,UAAU,GAAGC,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG1I,OAAO,CAACG,MAAM;IACtE6F,EAAE,GAAGhG,OAAO,CAACyI,UAAU,CAAC;IACxBxC,EAAE,GAAGjG,OAAO,CAACyI,UAAU,GAAG,CAAC,CAAC;IAC5BI,YAAY,GAAGJ,UAAU,GAAGA,UAAU,GAAG,CAAC,GAAGrH,UAAU,GAAG,CAAC;IAC3Db,OAAO,GAAGP,OAAO,CAACO,OAAO;IACzBW,MAAM,GAAGlB,OAAO,CAACkB,MAAM;IACvBmG,GAAG,GAAG,CAACoB,UAAU,GAAGzI,OAAO,CAACmB,SAAS,GAAGrC,SAAS,KAAKA,SAAS;IAC/DgK,UAAU,GAAGvI,OAAO,CAACsI,YAAY,GAAGH,SAAS,GAAGtH,UAAU,GAAG,CAAC,CAAC;IAC/DjB,MAAM,GAAGsI,UAAU,GAAGlI,OAAO,CAACsI,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IACnD/I,CAAC;IACDuI,CAAC;IACDpD,EAAE;IACFD,EAAE;IACFD,EAAE;IACFgE,EAAE;IACFC,GAAG;IACH1D,EAAE;IACFD,EAAE;IACFD,EAAE;IACF6D,EAAE;IACFC,GAAG;IACHC,GAAG;IACHpJ,CAAC;IACDqJ,WAAW;IACX5I,CAAC;IACD6I,SAAS;EACb9I,OAAO,CAACJ,MAAM,GAAGe,MAAM,CAACf,MAAM,GAAG,CAAC;EAElC,KAAKkI,CAAC,GAAGI,UAAU,GAAG,CAAC,EAAEJ,CAAC,GAAGO,QAAQ,EAAEP,CAAC,IAAI,CAAC,EAAE;IAC7CpD,EAAE,GAAGjF,OAAO,CAACqI,CAAC,GAAG,CAAC,CAAC,GAAGrC,EAAE;IACxBhB,EAAE,GAAGhF,OAAO,CAACqI,CAAC,GAAG,CAAC,CAAC,GAAGrC,EAAE;IACxBjB,EAAE,GAAG/E,OAAO,CAACqI,CAAC,CAAC,GAAGrC,EAAE;IACpBV,EAAE,GAAGtF,OAAO,CAACqI,CAAC,GAAG,CAAC,CAAC,GAAGpC,EAAE;IACxBZ,EAAE,GAAGrF,OAAO,CAACqI,CAAC,GAAG,CAAC,CAAC,GAAGpC,EAAE;IACxBb,EAAE,GAAGpF,OAAO,CAACqI,CAAC,GAAG,CAAC,CAAC,GAAGpC,EAAE;IACxB8C,EAAE,GAAGC,GAAG,GAAGC,EAAE,GAAGC,GAAG,GAAG,CAAC;IAEvB,IAAI1K,IAAI,CAACyG,EAAE,CAAC,GAAG,GAAG,IAAIzG,IAAI,CAAC8G,EAAE,CAAC,GAAG,GAAG,IAAI9G,IAAI,CAACuG,EAAE,CAAC,GAAGvG,IAAI,CAAC4G,EAAE,CAAC,GAAG,GAAG,EAAE;MACjE;MACA,IAAIpF,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;QACtBH,OAAO,CAACI,MAAM,CAACiI,CAAC,EAAE,CAAC,CAAC;QACpBA,CAAC,IAAI,CAAC;QACNO,QAAQ,IAAI,CAAC;MACf;IACF,CAAC,MAAM;MACL,KAAK9I,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIsB,UAAU,EAAEtB,CAAC,EAAE,EAAE;QAChCC,CAAC,GAAG4I,GAAG,GAAG7I,CAAC;QACXqJ,GAAG,GAAG,CAAC,GAAGpJ,CAAC;QACXgJ,EAAE,GAAGC,GAAG,IAAIA,GAAG,GAAG,CAACjJ,CAAC,GAAGA,CAAC,GAAGkF,EAAE,GAAG,CAAC,GAAGkE,GAAG,IAAIpJ,CAAC,GAAGiF,EAAE,GAAGmE,GAAG,GAAGpE,EAAE,CAAC,IAAIhF,CAAC,CAAC;QACnEkJ,EAAE,GAAGC,GAAG,IAAIA,GAAG,GAAG,CAACnJ,CAAC,GAAGA,CAAC,GAAGuF,EAAE,GAAG,CAAC,GAAG6D,GAAG,IAAIpJ,CAAC,GAAGsF,EAAE,GAAG8D,GAAG,GAAG/D,EAAE,CAAC,IAAIrF,CAAC,CAAC;QACnES,CAAC,GAAG9B,KAAK,CAACuK,EAAE,GAAGA,EAAE,GAAGF,EAAE,GAAGA,EAAE,CAAC;QAE5B,IAAIvI,CAAC,GAAG6G,GAAG,EAAE;UACXA,GAAG,GAAG7G,CAAC;QACT;QAEAL,MAAM,IAAIK,CAAC;QACXD,OAAO,CAACsI,YAAY,EAAE,CAAC,GAAG1I,MAAM;MAClC;IACF;IAEA6F,EAAE,IAAIf,EAAE;IACRgB,EAAE,IAAIX,EAAE;EACV;EAEA,IAAIwD,UAAU,EAAE;IACdA,UAAU,IAAI3I,MAAM;IAEpB,OAAO0I,YAAY,GAAGtI,OAAO,CAACJ,MAAM,EAAE0I,YAAY,EAAE,EAAE;MACpDtI,OAAO,CAACsI,YAAY,CAAC,IAAIC,UAAU;IACrC;EACF;EAEA,IAAIvI,OAAO,CAACJ,MAAM,IAAIkH,GAAG,EAAE;IACzBrH,OAAO,CAACiB,WAAW,GAAGoI,SAAS,GAAG9I,OAAO,CAACA,OAAO,CAACJ,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;IAClEH,OAAO,CAACmB,SAAS,GAAGkG,GAAG;IAEvB,IAAIgC,SAAS,GAAGhC,GAAG,GAAG,IAAI,EAAE;MAC1B;MACA7G,CAAC,GAAG4I,WAAW,GAAG,CAAC;MAEnB,KAAKtJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,SAAS,EAAEvJ,CAAC,IAAIuH,GAAG,EAAE;QACnCnG,MAAM,CAACV,CAAC,EAAE,CAAC,GAAGD,OAAO,CAAC6I,WAAW,CAAC,GAAGtJ,CAAC,GAAG,EAAEsJ,WAAW,GAAGA,WAAW;MACtE;IACF;EACF,CAAC,MAAM;IACLpJ,OAAO,CAACiB,WAAW,GAAGV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACtC;EAEA,OAAOkI,UAAU,GAAGtI,MAAM,GAAGI,OAAO,CAACkI,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGtI,MAAM;AACnE;AAEA,OAAO,SAASmH,wBAAwBA,CAAC1H,OAAO,EAAEwB,UAAU,EAAE;EAC5D,IAAIkI,UAAU,EAAE1E,MAAM,EAAE9E,CAAC;EAEzB,KAAKA,CAAC,GAAGwJ,UAAU,GAAG1E,MAAM,GAAG,CAAC,EAAE9E,CAAC,GAAGF,OAAO,CAACO,MAAM,EAAEL,CAAC,EAAE,EAAE;IACzDF,OAAO,CAACE,CAAC,CAAC,CAACsB,UAAU,GAAG,CAAC,CAACA,UAAU,IAAI,EAAE,CAAC,CAAC;;IAE5CwD,MAAM,IAAIhF,OAAO,CAACE,CAAC,CAAC,CAACK,MAAM;IAC3BmJ,UAAU,IAAId,cAAc,CAAC5I,OAAO,CAACE,CAAC,CAAC,CAAC;EAC1C;EAEAF,OAAO,CAACyB,WAAW,GAAGuD,MAAM;EAC5BhF,OAAO,CAACqB,WAAW,GAAGqI,UAAU;EAChC,OAAO1J,OAAO;AAChB,CAAC,CAAC;;AAEF,OAAO,SAASM,gBAAgBA,CAACF,OAAO,EAAEF,CAAC,EAAEC,CAAC,EAAE;EAC9C,IAAIA,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAE;IACpB,OAAO,CAAC;EACV;EAEA,IAAIwJ,EAAE,GAAGvJ,OAAO,CAACF,CAAC,CAAC;IACf0J,EAAE,GAAGxJ,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACnB2J,IAAI,GAAGzJ,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACrB4J,IAAI,GAAG1J,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACrB6J,IAAI,GAAG3J,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACrB8J,IAAI,GAAG5J,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACrB+J,EAAE,GAAG7J,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACnBgK,EAAE,GAAG9J,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;IACnBiK,GAAG,GAAGR,EAAE,GAAG,CAACE,IAAI,GAAGF,EAAE,IAAIxJ,CAAC;IAC1BgF,EAAE,GAAG0E,IAAI,GAAG,CAACE,IAAI,GAAGF,IAAI,IAAI1J,CAAC;IAC7BiK,GAAG,GAAGR,EAAE,GAAG,CAACE,IAAI,GAAGF,EAAE,IAAIzJ,CAAC;IAC1BqF,EAAE,GAAGsE,IAAI,GAAG,CAACE,IAAI,GAAGF,IAAI,IAAI3J,CAAC;IAC7BiG,EAAE,GAAG+D,GAAG,GAAG,CAAChF,EAAE,GAAGgF,GAAG,IAAIhK,CAAC;IACzBkG,EAAE,GAAG+D,GAAG,GAAG,CAAC5E,EAAE,GAAG4E,GAAG,IAAIjK,CAAC;IACzBkK,GAAG,GAAGN,IAAI,GAAG,CAACE,EAAE,GAAGF,IAAI,IAAI5J,CAAC;IAC5BmK,GAAG,GAAGN,IAAI,GAAG,CAACE,EAAE,GAAGF,IAAI,IAAI7J,CAAC;EAChCgF,EAAE,IAAI,CAACkF,GAAG,GAAGlF,EAAE,IAAIhF,CAAC;EACpBqF,EAAE,IAAI,CAAC8E,GAAG,GAAG9E,EAAE,IAAIrF,CAAC;EACpBC,OAAO,CAACI,MAAM,CAACN,CAAC,GAAG,CAAC,EAAE,CAAC,EAAEL,MAAM,CAACsK,GAAG,CAAC;EAAE;EACtCtK,MAAM,CAACuK,GAAG,CAAC,EAAEvK,MAAM,CAACuG,EAAE,CAAC;EAAE;EACzBvG,MAAM,CAACwG,EAAE,CAAC,EAAExG,MAAM,CAACuG,EAAE,GAAG,CAACjB,EAAE,GAAGiB,EAAE,IAAIjG,CAAC,CAAC;EAAE;EACxCN,MAAM,CAACwG,EAAE,GAAG,CAACb,EAAE,GAAGa,EAAE,IAAIlG,CAAC,CAAC,EAAEN,MAAM,CAACsF,EAAE,CAAC;EAAE;EACxCtF,MAAM,CAAC2F,EAAE,CAAC,EAAE3F,MAAM,CAACwK,GAAG,CAAC;EAAE;EACzBxK,MAAM,CAACyK,GAAG,CAAC,CAAC;EACZlK,OAAO,CAACO,OAAO,IAAIP,OAAO,CAACO,OAAO,CAACH,MAAM,CAACN,CAAC,GAAG,CAAC,GAAGE,OAAO,CAACoB,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9F,OAAO,CAAC;AACV,CAAC,CAAC;;AAEF,SAASuF,eAAeA,CAAC/G,OAAO,EAAEL,QAAQ,EAAE4K,SAAS,EAAEC,iBAAiB,EAAE;EACxED,SAAS,GAAGA,SAAS,IAAI,CAAC,CAAC;EAC3BvK,OAAO,CAACqB,WAAW,IAAIqG,wBAAwB,CAAC1H,OAAO,CAAC;EAExD,IAAIL,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAChCA,QAAQ,GAAGD,aAAa,CAACC,QAAQ,CAAC;EACpC;EAEA,IAAIM,QAAQ,GAAG,CAAC;IACZG,OAAO,GAAGJ,OAAO,CAAC,CAAC,CAAC;IACpBW,OAAO;IACPa,UAAU;IACVjB,MAAM;IACNkH,GAAG;IACHF,GAAG;IACHrH,CAAC;IACDC,CAAC;EAEL,IAAI,CAACR,QAAQ,EAAE;IACbQ,CAAC,GAAGD,CAAC,GAAGD,QAAQ,GAAG,CAAC;IACpBG,OAAO,GAAGJ,OAAO,CAAC,CAAC,CAAC;EACtB,CAAC,MAAM,IAAIL,QAAQ,KAAK,CAAC,EAAE;IACzBQ,CAAC,GAAG,CAAC;IACLF,QAAQ,GAAGD,OAAO,CAACO,MAAM,GAAG,CAAC;IAC7BH,OAAO,GAAGJ,OAAO,CAACC,QAAQ,CAAC;IAC3BC,CAAC,GAAGE,OAAO,CAACG,MAAM,GAAG,CAAC;EACxB,CAAC,MAAM;IACL,IAAIP,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MACtB;MACAA,MAAM,GAAGP,OAAO,CAACqB,WAAW,GAAG1B,QAAQ;MACvC4H,GAAG,GAAGrH,CAAC,GAAG,CAAC;MAEX,OAAO,CAACqH,GAAG,IAAIvH,OAAO,CAACE,CAAC,EAAE,CAAC,CAACmB,WAAW,IAAId,MAAM,EAAE;QACjDN,QAAQ,GAAGC,CAAC;MACd;MAEAE,OAAO,GAAGJ,OAAO,CAACC,QAAQ,CAAC;MAC3BwH,GAAG,GAAGF,GAAG,GAAGnH,OAAO,CAACiB,WAAW;MAC/B1B,QAAQ,GAAG,CAACY,MAAM,GAAGkH,GAAG,KAAKF,GAAG,GAAGE,GAAG,CAAC,IAAI,CAAC;IAC9C;IAEA9G,OAAO,GAAGP,OAAO,CAACO,OAAO;IACzBa,UAAU,GAAGpB,OAAO,CAACoB,UAAU,CAAC,CAAC;;IAEjCjB,MAAM,GAAGH,OAAO,CAACiB,WAAW,GAAG1B,QAAQ;IACvCO,CAAC,GAAGE,OAAO,CAACkB,MAAM,CAACf,MAAM,GAAGH,OAAO,CAACkB,MAAM,CAAC,CAAC,EAAEf,MAAM,GAAGH,OAAO,CAACmB,SAAS,CAAC,CAAC,IAAI,CAAC,GAAGb,eAAe,CAACC,OAAO,EAAEJ,MAAM,EAAEZ,QAAQ,CAAC;IAC5H8H,GAAG,GAAGvH,CAAC,GAAGS,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC5BqH,GAAG,GAAG5G,OAAO,CAACT,CAAC,CAAC;IAEhB,IAAIqH,GAAG,GAAGhH,MAAM,EAAE;MAChBkH,GAAG,GAAGF,GAAG;MACTA,GAAG,GAAG5G,OAAO,CAAC,EAAET,CAAC,CAAC;IACpB;IAEAC,CAAC,GAAG,CAAC,GAAGqB,UAAU,IAAI,CAACjB,MAAM,GAAGkH,GAAG,KAAKF,GAAG,GAAGE,GAAG,CAAC,GAAGvH,CAAC,GAAGsB,UAAU,CAAC;IACpEtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,UAAU,CAAC,GAAG,CAAC;IAE1B,IAAIgJ,iBAAiB,IAAIrK,CAAC,KAAK,CAAC,EAAE;MAChC,IAAID,CAAC,GAAG,CAAC,GAAGE,OAAO,CAACG,MAAM,EAAE;QAC1BL,CAAC,IAAI,CAAC;QACNC,CAAC,GAAG,CAAC;MACP,CAAC,MAAM,IAAIF,QAAQ,GAAG,CAAC,GAAGD,OAAO,CAACO,MAAM,EAAE;QACxCL,CAAC,GAAGC,CAAC,GAAG,CAAC;QACTC,OAAO,GAAGJ,OAAO,CAAC,EAAEC,QAAQ,CAAC;MAC/B;IACF;EACF;EAEAsK,SAAS,CAACpK,CAAC,GAAGA,CAAC;EACfoK,SAAS,CAACrK,CAAC,GAAGA,CAAC;EACfqK,SAAS,CAACxH,IAAI,GAAG/C,OAAO;EACxBuK,SAAS,CAACnK,OAAO,GAAGA,OAAO;EAC3BmK,SAAS,CAACtK,QAAQ,GAAGA,QAAQ;EAC7B,OAAOsK,SAAS;AAClB;AAEA,OAAO,SAASE,iBAAiBA,CAACzK,OAAO,EAAEL,QAAQ,EAAE+K,YAAY,EAAEC,KAAK,EAAE;EACxE,IAAIvK,OAAO,GAAGJ,OAAO,CAAC,CAAC,CAAC;IACpB4K,MAAM,GAAGD,KAAK,IAAI,CAAC,CAAC;IACpBhK,OAAO;IACPa,UAAU;IACVjB,MAAM;IACNkH,GAAG;IACHF,GAAG;IACHrH,CAAC;IACDC,CAAC;IACDwC,CAAC;IACD4G,GAAG;EAEP,IAAI5J,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAChCA,QAAQ,GAAGD,aAAa,CAACC,QAAQ,CAAC;EACpC;EAEAS,OAAO,CAACkB,MAAM,IAAIoG,wBAAwB,CAAC1H,OAAO,CAAC;EAEnD,IAAIA,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;IACtB;IACAA,MAAM,GAAGP,OAAO,CAACqB,WAAW,GAAG1B,QAAQ;IACvC4H,GAAG,GAAGrH,CAAC,GAAG,CAAC;IAEX,OAAO,CAACqH,GAAG,IAAIvH,OAAO,CAACE,CAAC,EAAE,CAAC,CAACmB,WAAW,IAAId,MAAM,EAAE;MACjDH,OAAO,GAAGJ,OAAO,CAACE,CAAC,CAAC;IACtB;IAEAuH,GAAG,GAAGF,GAAG,GAAGnH,OAAO,CAACiB,WAAW;IAC/B1B,QAAQ,GAAG,CAACY,MAAM,GAAGkH,GAAG,KAAKF,GAAG,GAAGE,GAAG,CAAC,IAAI,CAAC;EAC9C;EAEA9G,OAAO,GAAGP,OAAO,CAACO,OAAO;EACzBa,UAAU,GAAGpB,OAAO,CAACoB,UAAU;EAC/BjB,MAAM,GAAGH,OAAO,CAACiB,WAAW,GAAG1B,QAAQ;EACvCO,CAAC,GAAGE,OAAO,CAACkB,MAAM,CAACf,MAAM,GAAGH,OAAO,CAACkB,MAAM,CAAC3B,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAEY,MAAM,GAAGH,OAAO,CAACmB,SAAS,CAAC,GAAGnB,OAAO,CAACkB,MAAM,CAACf,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGG,eAAe,CAACC,OAAO,EAAEJ,MAAM,EAAEZ,QAAQ,CAAC;EACvK8H,GAAG,GAAGvH,CAAC,GAAGS,OAAO,CAACT,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC5BqH,GAAG,GAAG5G,OAAO,CAACT,CAAC,CAAC;EAEhB,IAAIqH,GAAG,GAAGhH,MAAM,EAAE;IAChBkH,GAAG,GAAGF,GAAG;IACTA,GAAG,GAAG5G,OAAO,CAAC,EAAET,CAAC,CAAC;EACpB;EAEAC,CAAC,GAAG,CAAC,GAAGqB,UAAU,IAAI,CAACjB,MAAM,GAAGkH,GAAG,KAAKF,GAAG,GAAGE,GAAG,CAAC,GAAGvH,CAAC,GAAGsB,UAAU,CAAC,IAAI,CAAC;EACzE+H,GAAG,GAAG,CAAC,GAAGpJ,CAAC;EACXD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,UAAU,CAAC,GAAG,CAAC;EAC1BmB,CAAC,GAAGvC,OAAO,CAACF,CAAC,CAAC;EACd0K,MAAM,CAACjG,CAAC,GAAG9E,MAAM,CAAC,CAACM,CAAC,GAAGA,CAAC,IAAIC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyC,CAAC,CAAC,GAAG,CAAC,GAAG4G,GAAG,IAAIpJ,CAAC,IAAIC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyC,CAAC,CAAC,GAAG4G,GAAG,IAAInJ,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyC,CAAC,CAAC,CAAC,IAAIxC,CAAC,GAAGwC,CAAC,CAAC;EAC7HiI,MAAM,CAAChI,CAAC,GAAG/C,MAAM,CAAC,CAACM,CAAC,GAAGA,CAAC,IAAIC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,IAAIyC,CAAC,GAAGvC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGqJ,GAAG,IAAIpJ,CAAC,IAAIC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyC,CAAC,CAAC,GAAG4G,GAAG,IAAInJ,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyC,CAAC,CAAC,CAAC,IAAIxC,CAAC,GAAGwC,CAAC,CAAC;EAEhJ,IAAI+H,YAAY,EAAE;IAChBE,MAAM,CAAClC,KAAK,GAAGtI,OAAO,CAACiB,WAAW,GAAG2F,oBAAoB,CAAC5G,OAAO,EAAEF,CAAC,EAAEC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC,GAAGC,OAAO,CAACsI,KAAK,IAAI,CAAC;EAC9H;EAEA,OAAOkC,MAAM;AACf,CAAC,CAAC;;AAEF,OAAO,SAASC,gBAAgBA,CAAC7K,OAAO,EAAE2C,CAAC,EAAEsE,CAAC,EAAEC,CAAC,EAAEJ,CAAC,EAAEgE,EAAE,EAAEC,EAAE,EAAE;EAC5D,IAAItC,CAAC,GAAGzI,OAAO,CAACO,MAAM;IAClBH,OAAO;IACPQ,CAAC;IACDV,CAAC;IACDyE,CAAC;IACD/B,CAAC;EAEL,OAAO,EAAE6F,CAAC,GAAG,CAAC,CAAC,EAAE;IACfrI,OAAO,GAAGJ,OAAO,CAACyI,CAAC,CAAC;IACpB7H,CAAC,GAAGR,OAAO,CAACG,MAAM;IAElB,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE;MACzByE,CAAC,GAAGvE,OAAO,CAACF,CAAC,CAAC;MACd0C,CAAC,GAAGxC,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC;MAClBE,OAAO,CAACF,CAAC,CAAC,GAAGyE,CAAC,GAAGhC,CAAC,GAAGC,CAAC,GAAGsE,CAAC,GAAG4D,EAAE;MAC/B1K,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGyE,CAAC,GAAGsC,CAAC,GAAGrE,CAAC,GAAGkE,CAAC,GAAGiE,EAAE;IACrC;EACF;EAEA/K,OAAO,CAACsC,MAAM,GAAG,CAAC;EAClB,OAAOtC,OAAO;AAChB,CAAC,CAAC;;AAEF,SAASgL,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAEpF,EAAE,EAAEjB,EAAE,EAAE6D,KAAK,EAAEyC,YAAY,EAAEC,SAAS,EAAEzG,CAAC,EAAE/B,CAAC,EAAE;EAChF,IAAIqI,KAAK,KAAKtG,CAAC,IAAIuG,KAAK,KAAKtI,CAAC,EAAE;IAC9B;EACF;EAEAkD,EAAE,GAAGlH,IAAI,CAACkH,EAAE,CAAC;EACbjB,EAAE,GAAGjG,IAAI,CAACiG,EAAE,CAAC;EAEb,IAAIwG,QAAQ,GAAG3C,KAAK,GAAG,GAAG,GAAGtK,QAAQ;IACjCkN,QAAQ,GAAG5M,IAAI,CAAC2M,QAAQ,CAAC;IACzBE,QAAQ,GAAG/M,IAAI,CAAC6M,QAAQ,CAAC;IACzB/M,EAAE,GAAGD,IAAI,CAACC,EAAE;IACZkN,KAAK,GAAGlN,EAAE,GAAG,CAAC;IACdmN,GAAG,GAAG,CAACR,KAAK,GAAGtG,CAAC,IAAI,CAAC;IACrB+G,GAAG,GAAG,CAACR,KAAK,GAAGtI,CAAC,IAAI,CAAC;IACrBwD,EAAE,GAAGkF,QAAQ,GAAGG,GAAG,GAAGF,QAAQ,GAAGG,GAAG;IACpCrF,EAAE,GAAG,CAACkF,QAAQ,GAAGE,GAAG,GAAGH,QAAQ,GAAGI,GAAG;IACrCC,KAAK,GAAGvF,EAAE,GAAGA,EAAE;IACfwF,KAAK,GAAGvF,EAAE,GAAGA,EAAE;IACfwF,UAAU,GAAGF,KAAK,IAAI7F,EAAE,GAAGA,EAAE,CAAC,GAAG8F,KAAK,IAAI/G,EAAE,GAAGA,EAAE,CAAC;EAEtD,IAAIgH,UAAU,GAAG,CAAC,EAAE;IAClB/F,EAAE,GAAGhH,KAAK,CAAC+M,UAAU,CAAC,GAAG/F,EAAE;IAC3BjB,EAAE,GAAG/F,KAAK,CAAC+M,UAAU,CAAC,GAAGhH,EAAE;EAC7B;EAEA,IAAIiH,KAAK,GAAGhG,EAAE,GAAGA,EAAE;IACfiG,KAAK,GAAGlH,EAAE,GAAGA,EAAE;IACfmH,EAAE,GAAG,CAACF,KAAK,GAAGC,KAAK,GAAGD,KAAK,GAAGF,KAAK,GAAGG,KAAK,GAAGJ,KAAK,KAAKG,KAAK,GAAGF,KAAK,GAAGG,KAAK,GAAGJ,KAAK,CAAC;EAE1F,IAAIK,EAAE,GAAG,CAAC,EAAE;IACVA,EAAE,GAAG,CAAC;EACR;EAEA,IAAIC,IAAI,GAAG,CAACd,YAAY,KAAKC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAItM,KAAK,CAACkN,EAAE,CAAC;IACxDE,GAAG,GAAGD,IAAI,IAAInG,EAAE,GAAGO,EAAE,GAAGxB,EAAE,CAAC;IAC3BsH,GAAG,GAAGF,IAAI,GAAG,EAAEpH,EAAE,GAAGuB,EAAE,GAAGN,EAAE,CAAC;IAC5BsG,GAAG,GAAG,CAACnB,KAAK,GAAGtG,CAAC,IAAI,CAAC;IACrB0H,GAAG,GAAG,CAACnB,KAAK,GAAGtI,CAAC,IAAI,CAAC;IACrBsD,EAAE,GAAGkG,GAAG,IAAId,QAAQ,GAAGY,GAAG,GAAGX,QAAQ,GAAGY,GAAG,CAAC;IAC5ChG,EAAE,GAAGkG,GAAG,IAAId,QAAQ,GAAGW,GAAG,GAAGZ,QAAQ,GAAGa,GAAG,CAAC;IAC5CG,EAAE,GAAG,CAAClG,EAAE,GAAG8F,GAAG,IAAIpG,EAAE;IACpByG,EAAE,GAAG,CAAClG,EAAE,GAAG8F,GAAG,IAAItH,EAAE;IACpB2H,EAAE,GAAG,CAAC,CAACpG,EAAE,GAAG8F,GAAG,IAAIpG,EAAE;IACrB2G,EAAE,GAAG,CAAC,CAACpG,EAAE,GAAG8F,GAAG,IAAItH,EAAE;IACrB6H,IAAI,GAAGJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IACxBI,UAAU,GAAG,CAACJ,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIlO,IAAI,CAACuO,IAAI,CAACN,EAAE,GAAGxN,KAAK,CAAC4N,IAAI,CAAC,CAAC;IAC5DG,WAAW,GAAG,CAACP,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAInO,IAAI,CAACuO,IAAI,CAAC,CAACN,EAAE,GAAGE,EAAE,GAAGD,EAAE,GAAGE,EAAE,IAAI3N,KAAK,CAAC4N,IAAI,IAAIF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;EAEvHK,KAAK,CAACD,WAAW,CAAC,KAAKA,WAAW,GAAGvO,EAAE,CAAC,CAAC,CAAC;;EAE1C,IAAI,CAAC8M,SAAS,IAAIyB,WAAW,GAAG,CAAC,EAAE;IACjCA,WAAW,IAAIrB,KAAK;EACtB,CAAC,MAAM,IAAIJ,SAAS,IAAIyB,WAAW,GAAG,CAAC,EAAE;IACvCA,WAAW,IAAIrB,KAAK;EACtB;EAEAmB,UAAU,IAAInB,KAAK;EACnBqB,WAAW,IAAIrB,KAAK;EAEpB,IAAIuB,QAAQ,GAAG1O,IAAI,CAAC2O,IAAI,CAACpO,IAAI,CAACiO,WAAW,CAAC,IAAIrB,KAAK,GAAG,CAAC,CAAC,CAAC;IACrDxL,OAAO,GAAG,EAAE;IACZiN,cAAc,GAAGJ,WAAW,GAAGE,QAAQ;IACvCG,aAAa,GAAG,CAAC,GAAG,CAAC,GAAG1O,IAAI,CAACyO,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGvO,IAAI,CAACuO,cAAc,GAAG,CAAC,CAAC,CAAC;IACjFE,EAAE,GAAG7B,QAAQ,GAAGxF,EAAE;IAClBsH,EAAE,GAAG7B,QAAQ,GAAGzF,EAAE;IAClBuH,EAAE,GAAG9B,QAAQ,GAAG,CAAC1G,EAAE;IACnByI,EAAE,GAAGhC,QAAQ,GAAGzG,EAAE;IAClB3E,CAAC;EAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6M,QAAQ,EAAE7M,CAAC,EAAE,EAAE;IAC7BwI,KAAK,GAAGiE,UAAU,GAAGzM,CAAC,GAAG+M,cAAc;IACvC7G,EAAE,GAAG1H,IAAI,CAACgK,KAAK,CAAC;IAChBrC,EAAE,GAAG7H,IAAI,CAACkK,KAAK,CAAC;IAChB4D,EAAE,GAAG5N,IAAI,CAACgK,KAAK,IAAIuE,cAAc,CAAC;IAClCV,EAAE,GAAG/N,IAAI,CAACkK,KAAK,CAAC;IAChB1I,OAAO,CAAC2I,IAAI,CAACvC,EAAE,GAAG8G,aAAa,GAAG7G,EAAE,EAAEA,EAAE,GAAG6G,aAAa,GAAG9G,EAAE,EAAEkG,EAAE,GAAGY,aAAa,GAAGX,EAAE,EAAEA,EAAE,GAAGW,aAAa,GAAGZ,EAAE,EAAEA,EAAE,EAAEC,EAAE,CAAC;EAC1H,CAAC,CAAC;;EAGF,KAAKrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACO,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;IACtCkG,EAAE,GAAGpG,OAAO,CAACE,CAAC,CAAC;IACfmG,EAAE,GAAGrG,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC;IACnBF,OAAO,CAACE,CAAC,CAAC,GAAGkG,EAAE,GAAG+G,EAAE,GAAG9G,EAAE,GAAGgH,EAAE,GAAGnH,EAAE;IACnClG,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGkG,EAAE,GAAGgH,EAAE,GAAG/G,EAAE,GAAGiH,EAAE,GAAGnH,EAAE;EACzC;EAEAnG,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGyE,CAAC,CAAC,CAAC;;EAEpB3E,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG0C,CAAC;EAClB,OAAO5C,OAAO;AAChB,CAAC,CAAC;;AAGF,OAAO,SAASuC,eAAeA,CAACuE,CAAC,EAAE;EACjC,IAAInE,CAAC,GAAG,CAACmE,CAAC,GAAG,EAAE,EAAEyG,OAAO,CAACrP,WAAW,EAAE,UAAUsP,CAAC,EAAE;MACjD,IAAIC,CAAC,GAAG,CAACD,CAAC;MACV,OAAOC,CAAC,GAAG,MAAM,IAAIA,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAGA,CAAC;IAC1C,CAAC,CAAC,CAACnH,KAAK,CAACtI,WAAW,CAAC,IAAI,EAAE;IACvB;IACJ+E,IAAI,GAAG,EAAE;IACL2K,SAAS,GAAG,CAAC;IACbC,SAAS,GAAG,CAAC;IACbC,SAAS,GAAG,CAAC,GAAG,CAAC;IACjBC,QAAQ,GAAGlL,CAAC,CAACpC,MAAM;IACnByE,MAAM,GAAG,CAAC;IACV8I,YAAY,GAAG,yBAAyB,GAAGhH,CAAC;IAC5C5G,CAAC;IACDuI,CAAC;IACD9D,CAAC;IACD/B,CAAC;IACDmL,OAAO;IACPC,UAAU;IACV5N,OAAO;IACP6N,MAAM;IACNC,MAAM;IACNC,IAAI;IACJC,IAAI;IACJC,OAAO;IACPC,WAAW;IACXC,KAAK;IACLC,KAAK;IACL1K,IAAI,GAAG,SAASA,IAAIA,CAAC2K,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACvCT,IAAI,GAAG,CAACQ,EAAE,GAAGF,EAAE,IAAI,CAAC;MACpBL,IAAI,GAAG,CAACQ,EAAE,GAAGF,EAAE,IAAI,CAAC;MACpBtO,OAAO,CAACuI,IAAI,CAAC8F,EAAE,GAAGN,IAAI,EAAEO,EAAE,GAAGN,IAAI,EAAEO,EAAE,GAAGR,IAAI,EAAES,EAAE,GAAGR,IAAI,EAAEO,EAAE,EAAEC,EAAE,CAAC;IAClE,CAAC;EAED,IAAI,CAAC9H,CAAC,IAAI,CAACgG,KAAK,CAACnK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAImK,KAAK,CAACnK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrCH,OAAO,CAACqM,GAAG,CAACf,YAAY,CAAC;IACzB,OAAO/K,IAAI;EACb;EAEA,KAAK7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2N,QAAQ,EAAE3N,CAAC,EAAE,EAAE;IAC7BoO,WAAW,GAAGP,OAAO;IAErB,IAAIjB,KAAK,CAACnK,CAAC,CAACzC,CAAC,CAAC,CAAC,EAAE;MACf6N,OAAO,GAAGpL,CAAC,CAACzC,CAAC,CAAC,CAAC4O,WAAW,CAAC,CAAC;MAC5Bd,UAAU,GAAGD,OAAO,KAAKpL,CAAC,CAACzC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACL;MACAA,CAAC,EAAE;IACL;IAEAyE,CAAC,GAAG,CAAChC,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;IACb0C,CAAC,GAAG,CAACD,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;IAEb,IAAI8N,UAAU,EAAE;MACdrJ,CAAC,IAAI+I,SAAS;MACd9K,CAAC,IAAI+K,SAAS;IAChB;IAEA,IAAI,CAACzN,CAAC,EAAE;MACN+N,MAAM,GAAGtJ,CAAC;MACVuJ,MAAM,GAAGtL,CAAC;IACZ,CAAC,CAAC;;IAGF,IAAImL,OAAO,KAAK,GAAG,EAAE;MACnB,IAAI3N,OAAO,EAAE;QACX,IAAIA,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;UACtB;UACAwC,IAAI,CAACxC,MAAM,IAAI,CAAC;QAClB,CAAC,MAAM;UACLyE,MAAM,IAAI5E,OAAO,CAACG,MAAM;QAC1B;MACF;MAEAmN,SAAS,GAAGO,MAAM,GAAGtJ,CAAC;MACtBgJ,SAAS,GAAGO,MAAM,GAAGtL,CAAC;MACtBxC,OAAO,GAAG,CAACuE,CAAC,EAAE/B,CAAC,CAAC;MAChBG,IAAI,CAAC4F,IAAI,CAACvI,OAAO,CAAC;MAClBF,CAAC,IAAI,CAAC;MACN6N,OAAO,GAAG,GAAG,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAIA,OAAO,KAAK,GAAG,EAAE;MAC1B,IAAI,CAAC3N,OAAO,EAAE;QACZA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB;MAEA,IAAI,CAAC4N,UAAU,EAAE;QACfN,SAAS,GAAGC,SAAS,GAAG,CAAC;MAC3B,CAAC,CAAC;;MAGFvN,OAAO,CAACuI,IAAI,CAAChE,CAAC,EAAE/B,CAAC,EAAE8K,SAAS,GAAG/K,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEyN,SAAS,GAAGhL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEwN,SAAS,IAAI/K,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEyN,SAAS,IAAIhL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5HA,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,EAAE;MAC1BI,IAAI,GAAGT,SAAS;MAChBU,IAAI,GAAGT,SAAS;MAEhB,IAAIW,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;QAC9CH,IAAI,IAAIT,SAAS,GAAGtN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;QAC/C6N,IAAI,IAAIT,SAAS,GAAGvN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;MACjD;MAEA,IAAI,CAACyN,UAAU,EAAE;QACfN,SAAS,GAAGC,SAAS,GAAG,CAAC;MAC3B;MAEAvN,OAAO,CAACuI,IAAI,CAACwF,IAAI,EAAEC,IAAI,EAAEzJ,CAAC,EAAE/B,CAAC,EAAE8K,SAAS,IAAI/K,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEyN,SAAS,IAAIhL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACpFA,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,EAAE;MAC1BI,IAAI,GAAGT,SAAS,GAAG,CAAC/I,CAAC,GAAG+I,SAAS,IAAIE,SAAS;MAC9CQ,IAAI,GAAGT,SAAS,GAAG,CAAC/K,CAAC,GAAG+K,SAAS,IAAIC,SAAS;MAE9C,IAAI,CAACI,UAAU,EAAE;QACfN,SAAS,GAAGC,SAAS,GAAG,CAAC;MAC3B;MAEAD,SAAS,IAAI/K,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACzByN,SAAS,IAAIhL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACzBE,OAAO,CAACuI,IAAI,CAACwF,IAAI,EAAEC,IAAI,EAAEV,SAAS,GAAG,CAAC/I,CAAC,GAAG+I,SAAS,IAAIE,SAAS,EAAED,SAAS,GAAG,CAAC/K,CAAC,GAAG+K,SAAS,IAAIC,SAAS,EAAEF,SAAS,EAAEC,SAAS,CAAC;MAChIzN,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,EAAE;MAC1BI,IAAI,GAAGT,SAAS,GAAGtN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;MAC9C6N,IAAI,GAAGT,SAAS,GAAGvN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;MAC9CH,OAAO,CAACuI,IAAI,CAAC+E,SAAS,GAAGS,IAAI,EAAER,SAAS,GAAGS,IAAI,EAAEzJ,CAAC,GAAG,CAAC+I,SAAS,GAAGS,IAAI,GAAG,GAAG,GAAGxJ,CAAC,IAAIiJ,SAAS,EAAEhL,CAAC,GAAG,CAAC+K,SAAS,GAAGS,IAAI,GAAG,GAAG,GAAGxL,CAAC,IAAIgL,SAAS,EAAEF,SAAS,GAAG/I,CAAC,EAAEgJ,SAAS,GAAG/K,CAAC,CAAC;MAC1K1C,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,EAAE;MAC1BjK,IAAI,CAAC4J,SAAS,EAAEC,SAAS,EAAED,SAAS,GAAG/I,CAAC,EAAEgJ,SAAS,CAAC;MACpDzN,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,EAAE;MAC1B;MACAjK,IAAI,CAAC4J,SAAS,EAAEC,SAAS,EAAED,SAAS,EAAEC,SAAS,GAAGhJ,CAAC,IAAIqJ,UAAU,GAAGL,SAAS,GAAGD,SAAS,GAAG,CAAC,CAAC,CAAC;MAC/FxN,CAAC,IAAI,CAAC,CAAC,CAAC;IACV,CAAC,MAAM,IAAI6N,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG,EAAE;MAC7C,IAAIA,OAAO,KAAK,GAAG,EAAE;QACnBpJ,CAAC,GAAGsJ,MAAM;QACVrL,CAAC,GAAGsL,MAAM;QACV9N,OAAO,CAAC2O,MAAM,GAAG,IAAI;MACvB;MAEA,IAAIhB,OAAO,KAAK,GAAG,IAAInP,IAAI,CAAC8O,SAAS,GAAG/I,CAAC,CAAC,GAAG,GAAG,IAAI/F,IAAI,CAAC+O,SAAS,GAAG/K,CAAC,CAAC,GAAG,GAAG,EAAE;QAC7EkB,IAAI,CAAC4J,SAAS,EAAEC,SAAS,EAAEhJ,CAAC,EAAE/B,CAAC,CAAC;QAEhC,IAAImL,OAAO,KAAK,GAAG,EAAE;UACnB7N,CAAC,IAAI,CAAC;QACR;MACF;MAEAwN,SAAS,GAAG/I,CAAC;MACbgJ,SAAS,GAAG/K,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,IAAImL,OAAO,KAAK,GAAG,EAAE;MAC1BQ,KAAK,GAAG5L,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;MAChBsO,KAAK,GAAG7L,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;MAChBiO,IAAI,GAAGxL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;MACfkO,IAAI,GAAGzL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC;MACfuI,CAAC,GAAG,CAAC;MAEL,IAAI8F,KAAK,CAAChO,MAAM,GAAG,CAAC,EAAE;QACpB;QACA,IAAIgO,KAAK,CAAChO,MAAM,GAAG,CAAC,EAAE;UACpB6N,IAAI,GAAGD,IAAI;UACXA,IAAI,GAAGK,KAAK;UACZ/F,CAAC,EAAE;QACL,CAAC,MAAM;UACL2F,IAAI,GAAGI,KAAK;UACZL,IAAI,GAAGI,KAAK,CAACS,MAAM,CAAC,CAAC,CAAC;UACtBvG,CAAC,IAAI,CAAC;QACR;QAEA+F,KAAK,GAAGD,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC;QACvBV,KAAK,GAAGA,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC;MACzB;MAEAZ,OAAO,GAAGrD,YAAY,CAAC0C,SAAS,EAAEC,SAAS,EAAE,CAAChL,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACyC,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACyC,CAAC,CAACzC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACqO,KAAK,EAAE,CAACC,KAAK,EAAE,CAACR,UAAU,GAAGN,SAAS,GAAG,CAAC,IAAIS,IAAI,GAAG,CAAC,EAAE,CAACH,UAAU,GAAGL,SAAS,GAAG,CAAC,IAAIS,IAAI,GAAG,CAAC,CAAC;MAC/KlO,CAAC,IAAIuI,CAAC;MAEN,IAAI4F,OAAO,EAAE;QACX,KAAK5F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,OAAO,CAAC9N,MAAM,EAAEkI,CAAC,EAAE,EAAE;UACnCrI,OAAO,CAACuI,IAAI,CAAC0F,OAAO,CAAC5F,CAAC,CAAC,CAAC;QAC1B;MACF;MAEAiF,SAAS,GAAGtN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;MACvCoN,SAAS,GAAGvN,OAAO,CAACA,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;IACzC,CAAC,MAAM;MACLiC,OAAO,CAACqM,GAAG,CAACf,YAAY,CAAC;IAC3B;EACF;EAEA5N,CAAC,GAAGE,OAAO,CAACG,MAAM;EAElB,IAAIL,CAAC,GAAG,CAAC,EAAE;IACT;IACA6C,IAAI,CAACmM,GAAG,CAAC,CAAC;IACVhP,CAAC,GAAG,CAAC;EACP,CAAC,MAAM,IAAIE,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,IAAIE,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAE;IACzEE,OAAO,CAAC2O,MAAM,GAAG,IAAI;EACvB;EAEAhM,IAAI,CAACtB,WAAW,GAAGuD,MAAM,GAAG9E,CAAC;EAC7B,OAAO6C,IAAI;AACb,CAAC,CAAC;;AAEF,OAAO,SAASoM,cAAcA,CAAC/I,EAAE,EAAEC,EAAE,EAAElB,EAAE,EAAEK,EAAE,EAAEJ,EAAE,EAAEK,EAAE,EAAEJ,EAAE,EAAEK,EAAE,EAAE0J,SAAS,EAAEpK,MAAM,EAAErD,KAAK,EAAE;EACvF,IAAI0N,GAAG,GAAG,CAACjJ,EAAE,GAAGjB,EAAE,IAAI,CAAC;IACnBmK,GAAG,GAAG,CAACjJ,EAAE,GAAGb,EAAE,IAAI,CAAC;IACnB+J,GAAG,GAAG,CAACpK,EAAE,GAAGC,EAAE,IAAI,CAAC;IACnBoK,GAAG,GAAG,CAAChK,EAAE,GAAGC,EAAE,IAAI,CAAC;IACnBgK,GAAG,GAAG,CAACrK,EAAE,GAAGC,EAAE,IAAI,CAAC;IACnBqK,GAAG,GAAG,CAACjK,EAAE,GAAGC,EAAE,IAAI,CAAC;IACnBiK,IAAI,GAAG,CAACN,GAAG,GAAGE,GAAG,IAAI,CAAC;IACtBK,IAAI,GAAG,CAACN,GAAG,GAAGE,GAAG,IAAI,CAAC;IACtBK,IAAI,GAAG,CAACN,GAAG,GAAGE,GAAG,IAAI,CAAC;IACtBK,IAAI,GAAG,CAACN,GAAG,GAAGE,GAAG,IAAI,CAAC;IACtBK,KAAK,GAAG,CAACJ,IAAI,GAAGE,IAAI,IAAI,CAAC;IACzBG,KAAK,GAAG,CAACJ,IAAI,GAAGE,IAAI,IAAI,CAAC;IACzBG,EAAE,GAAG5K,EAAE,GAAGe,EAAE;IACZ8J,EAAE,GAAGxK,EAAE,GAAGW,EAAE;IACZ8J,EAAE,GAAGvR,IAAI,CAAC,CAACuG,EAAE,GAAGE,EAAE,IAAI6K,EAAE,GAAG,CAAC1K,EAAE,GAAGE,EAAE,IAAIuK,EAAE,CAAC;IAC1CG,EAAE,GAAGxR,IAAI,CAAC,CAACwG,EAAE,GAAGC,EAAE,IAAI6K,EAAE,GAAG,CAACzK,EAAE,GAAGC,EAAE,IAAIuK,EAAE,CAAC;IAC1C1P,MAAM;EAEV,IAAI,CAACyE,MAAM,EAAE;IACXA,MAAM,GAAG,CAACoB,EAAE,EAAEC,EAAE,EAAEhB,EAAE,EAAEK,EAAE,CAAC;IACzB/D,KAAK,GAAG,CAAC;EACX;EAEAqD,MAAM,CAACxE,MAAM,CAACmB,KAAK,IAAIqD,MAAM,CAACzE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAEwP,KAAK,EAAEC,KAAK,CAAC;EAE1D,IAAI,CAACG,EAAE,GAAGC,EAAE,KAAKD,EAAE,GAAGC,EAAE,CAAC,GAAGhB,SAAS,IAAIa,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,EAAE;IAC3D3P,MAAM,GAAGyE,MAAM,CAACzE,MAAM;IACtB4O,cAAc,CAAC/I,EAAE,EAAEC,EAAE,EAAEgJ,GAAG,EAAEC,GAAG,EAAEK,IAAI,EAAEC,IAAI,EAAEG,KAAK,EAAEC,KAAK,EAAEZ,SAAS,EAAEpK,MAAM,EAAErD,KAAK,CAAC;IACpFwN,cAAc,CAACY,KAAK,EAAEC,KAAK,EAAEH,IAAI,EAAEC,IAAI,EAAEL,GAAG,EAAEC,GAAG,EAAErK,EAAE,EAAEK,EAAE,EAAE0J,SAAS,EAAEpK,MAAM,EAAErD,KAAK,GAAG,CAAC,IAAIqD,MAAM,CAACzE,MAAM,GAAGA,MAAM,CAAC,CAAC;EACrH;EAEA,OAAOyE,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASqL,mBAAmBA,CAACrL,MAAM,EAAEsL,SAAS,EAAE;EACrD,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC;EACf;EAEA,IAAI3L,CAAC,GAAGK,MAAM,CAAC,CAAC,CAAC;IACbpC,CAAC,GAAG,CAAC;IACLxC,OAAO,GAAG,CAACuE,CAAC,EAAE/B,CAAC,CAAC;IAChB1C,CAAC,GAAG,CAAC;EAET,OAAOA,CAAC,GAAG8E,MAAM,CAACzE,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;IAChCE,OAAO,CAACuI,IAAI,CAAChE,CAAC,EAAE/B,CAAC,EAAEoC,MAAM,CAAC9E,CAAC,CAAC,EAAE0C,CAAC,GAAG,CAACoC,MAAM,CAAC9E,CAAC,CAAC,GAAGyE,CAAC,IAAI2L,SAAS,GAAG,CAAC,EAAE3L,CAAC,GAAGK,MAAM,CAAC9E,CAAC,CAAC,EAAE,CAAC0C,CAAC,CAAC;EACvF;EAEA,OAAOxC,OAAO;AAChB,CAAC,CAAC;;AAEF,OAAO,SAASmQ,eAAeA,CAACvL,MAAM,EAAEsL,SAAS,EAAE;EACjD;EACA1R,IAAI,CAACoG,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAIpG,IAAI,CAACoG,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAKA,MAAM,GAAGA,MAAM,CAACvE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExG,IAAIG,CAAC,GAAGoE,MAAM,CAACzE,MAAM,GAAG,CAAC;IACrBoE,CAAC,GAAG,CAACK,MAAM,CAAC,CAAC,CAAC;IACdpC,CAAC,GAAG,CAACoC,MAAM,CAAC,CAAC,CAAC;IACdwL,KAAK,GAAG,CAACxL,MAAM,CAAC,CAAC,CAAC;IAClByL,KAAK,GAAG,CAACzL,MAAM,CAAC,CAAC,CAAC;IAClB5E,OAAO,GAAG,CAACuE,CAAC,EAAE/B,CAAC,EAAE+B,CAAC,EAAE/B,CAAC,CAAC;IACtB6I,GAAG,GAAG+E,KAAK,GAAG7L,CAAC;IACf+G,GAAG,GAAG+E,KAAK,GAAG7N,CAAC;IACfmM,MAAM,GAAG1Q,IAAI,CAACQ,GAAG,CAACmG,MAAM,CAACpE,CAAC,CAAC,GAAG+D,CAAC,CAAC,GAAG,KAAK,IAAItG,IAAI,CAACQ,GAAG,CAACmG,MAAM,CAACpE,CAAC,GAAG,CAAC,CAAC,GAAGgC,CAAC,CAAC,GAAG,KAAK;IAC/E8N,KAAK;IACLC,KAAK;IACLzQ,CAAC;IACD0Q,GAAG;IACHC,GAAG;IACHC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,GAAG;IACHC,GAAG;IACHC,GAAG;IACHC,GAAG;IACHC,GAAG;IACHC,GAAG;EAEP,IAAIxC,MAAM,EAAE;IACV;IACA/J,MAAM,CAAC2D,IAAI,CAAC6H,KAAK,EAAEC,KAAK,CAAC;IACzBD,KAAK,GAAG7L,CAAC;IACT8L,KAAK,GAAG7N,CAAC;IACT+B,CAAC,GAAGK,MAAM,CAACpE,CAAC,GAAG,CAAC,CAAC;IACjBgC,CAAC,GAAGoC,MAAM,CAACpE,CAAC,GAAG,CAAC,CAAC;IACjBoE,MAAM,CAACwM,OAAO,CAAC7M,CAAC,EAAE/B,CAAC,CAAC;IACpBhC,CAAC,IAAI,CAAC;EACR;EAEA0P,SAAS,GAAGA,SAAS,IAAIA,SAAS,KAAK,CAAC,GAAG,CAACA,SAAS,GAAG,CAAC;EAEzD,KAAKpQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE;IACzBwQ,KAAK,GAAG/L,CAAC;IACTgM,KAAK,GAAG/N,CAAC;IACT+B,CAAC,GAAG6L,KAAK;IACT5N,CAAC,GAAG6N,KAAK;IACTD,KAAK,GAAG,CAACxL,MAAM,CAAC9E,CAAC,GAAG,CAAC,CAAC;IACtBuQ,KAAK,GAAG,CAACzL,MAAM,CAAC9E,CAAC,GAAG,CAAC,CAAC;IAEtB,IAAIyE,CAAC,KAAK6L,KAAK,IAAI5N,CAAC,KAAK6N,KAAK,EAAE;MAC9B;IACF;IAEAG,GAAG,GAAGnF,GAAG;IACToF,GAAG,GAAGnF,GAAG;IACTD,GAAG,GAAG+E,KAAK,GAAG7L,CAAC;IACf+G,GAAG,GAAG+E,KAAK,GAAG7N,CAAC;IACfkO,EAAE,GAAGhS,KAAK,CAAC8R,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC;;IAEnCE,EAAE,GAAGjS,KAAK,CAAC2M,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;IACjCsF,EAAE,GAAGlS,KAAK,CAACT,IAAI,CAACoT,GAAG,CAAChG,GAAG,GAAGsF,EAAE,GAAGH,GAAG,GAAGE,EAAE,EAAE,CAAC,CAAC,GAAGzS,IAAI,CAACoT,GAAG,CAAC/F,GAAG,GAAGqF,EAAE,GAAGF,GAAG,GAAGC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/EG,EAAE,GAAG,CAACH,EAAE,GAAGC,EAAE,IAAIT,SAAS,GAAG,IAAI,GAAGU,EAAE;IACtCE,GAAG,GAAGvM,CAAC,GAAG,CAACA,CAAC,GAAG+L,KAAK,KAAKI,EAAE,GAAGG,EAAE,GAAGH,EAAE,GAAG,CAAC,CAAC;IAC1CK,GAAG,GAAGxM,CAAC,GAAG,CAAC6L,KAAK,GAAG7L,CAAC,KAAKoM,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAG,CAAC,CAAC;IAC1CK,GAAG,GAAGzM,CAAC,IAAIuM,GAAG,IAAI,CAACC,GAAG,GAAGD,GAAG,KAAKJ,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACrEM,GAAG,GAAGzO,CAAC,GAAG,CAACA,CAAC,GAAG+N,KAAK,KAAKG,EAAE,GAAGG,EAAE,GAAGH,EAAE,GAAG,CAAC,CAAC;IAC1CQ,GAAG,GAAG1O,CAAC,GAAG,CAAC6N,KAAK,GAAG7N,CAAC,KAAKmO,EAAE,GAAGE,EAAE,GAAGF,EAAE,GAAG,CAAC,CAAC;IAC1CQ,GAAG,GAAG3O,CAAC,IAAIyO,GAAG,IAAI,CAACC,GAAG,GAAGD,GAAG,KAAKP,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAErE,IAAIpM,CAAC,KAAK+L,KAAK,IAAI9N,CAAC,KAAK+N,KAAK,EAAE;MAC9BvQ,OAAO,CAACuI,IAAI,CAAC9I,MAAM,CAACqR,GAAG,GAAGE,GAAG,CAAC;MAAE;MAChCvR,MAAM,CAACwR,GAAG,GAAGE,GAAG,CAAC,EAAE1R,MAAM,CAAC8E,CAAC,CAAC;MAAE;MAC9B9E,MAAM,CAAC+C,CAAC,CAAC,EAAE/C,MAAM,CAACsR,GAAG,GAAGC,GAAG,CAAC;MAAE;MAC9BvR,MAAM,CAACyR,GAAG,GAAGC,GAAG,CAAC,CAAC;IACpB;EACF;EAEA5M,CAAC,KAAK6L,KAAK,IAAI5N,CAAC,KAAK6N,KAAK,IAAIrQ,OAAO,CAACG,MAAM,GAAG,CAAC,GAAGH,OAAO,CAACuI,IAAI,CAAC9I,MAAM,CAAC2Q,KAAK,CAAC,EAAE3Q,MAAM,CAAC4Q,KAAK,CAAC,EAAE5Q,MAAM,CAAC2Q,KAAK,CAAC,EAAE3Q,MAAM,CAAC4Q,KAAK,CAAC,CAAC,GAAGrQ,OAAO,CAACG,MAAM,IAAI,CAAC;EAEjJ,IAAIH,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;IACxB;IACAH,OAAO,CAACuI,IAAI,CAAChE,CAAC,EAAE/B,CAAC,EAAE+B,CAAC,EAAE/B,CAAC,EAAE+B,CAAC,EAAE/B,CAAC,CAAC;EAChC,CAAC,MAAM,IAAImM,MAAM,EAAE;IACjB3O,OAAO,CAACI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACpBJ,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACG,MAAM,GAAG,CAAC;EACrC;EAEA,OAAOH,OAAO;AAChB,CAAC,CAAC;;AAEF,SAASsR,cAAcA,CAAC/M,CAAC,EAAE/B,CAAC,EAAEwD,EAAE,EAAEC,EAAE,EAAElB,EAAE,EAAEK,EAAE,EAAE;EAC5C,IAAIyK,EAAE,GAAG9K,EAAE,GAAGiB,EAAE;IACZ8J,EAAE,GAAG1K,EAAE,GAAGa,EAAE;IACZlG,CAAC;EAEL,IAAI8P,EAAE,IAAIC,EAAE,EAAE;IACZ/P,CAAC,GAAG,CAAC,CAACwE,CAAC,GAAGyB,EAAE,IAAI6J,EAAE,GAAG,CAACrN,CAAC,GAAGyD,EAAE,IAAI6J,EAAE,KAAKD,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IAEzD,IAAI/P,CAAC,GAAG,CAAC,EAAE;MACTiG,EAAE,GAAGjB,EAAE;MACPkB,EAAE,GAAGb,EAAE;IACT,CAAC,MAAM,IAAIrF,CAAC,GAAG,CAAC,EAAE;MAChBiG,EAAE,IAAI6J,EAAE,GAAG9P,CAAC;MACZkG,EAAE,IAAI6J,EAAE,GAAG/P,CAAC;IACd;EACF;EAEA,OAAO9B,IAAI,CAACoT,GAAG,CAAC9M,CAAC,GAAGyB,EAAE,EAAE,CAAC,CAAC,GAAG/H,IAAI,CAACoT,GAAG,CAAC7O,CAAC,GAAGyD,EAAE,EAAE,CAAC,CAAC;AAClD;AAEA,SAASsL,YAAYA,CAAC3M,MAAM,EAAE4M,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAChE,IAAIC,SAAS,GAAGF,SAAS;IACrBG,MAAM,GAAGjN,MAAM,CAAC4M,KAAK,CAAC;IACtBM,MAAM,GAAGlN,MAAM,CAAC4M,KAAK,GAAG,CAAC,CAAC;IAC1B3G,KAAK,GAAGjG,MAAM,CAAC6M,IAAI,CAAC;IACpB3G,KAAK,GAAGlG,MAAM,CAAC6M,IAAI,GAAG,CAAC,CAAC;IACxBlQ,KAAK;IACLzB,CAAC;IACD4G,CAAC;EAEL,KAAK5G,CAAC,GAAG0R,KAAK,GAAG,CAAC,EAAE1R,CAAC,GAAG2R,IAAI,EAAE3R,CAAC,IAAI,CAAC,EAAE;IACpC4G,CAAC,GAAG4K,cAAc,CAAC1M,MAAM,CAAC9E,CAAC,CAAC,EAAE8E,MAAM,CAAC9E,CAAC,GAAG,CAAC,CAAC,EAAE+R,MAAM,EAAEC,MAAM,EAAEjH,KAAK,EAAEC,KAAK,CAAC;IAE1E,IAAIpE,CAAC,GAAGkL,SAAS,EAAE;MACjBrQ,KAAK,GAAGzB,CAAC;MACT8R,SAAS,GAAGlL,CAAC;IACf;EACF;EAEA,IAAIkL,SAAS,GAAGF,SAAS,EAAE;IACzBnQ,KAAK,GAAGiQ,KAAK,GAAG,CAAC,IAAID,YAAY,CAAC3M,MAAM,EAAE4M,KAAK,EAAEjQ,KAAK,EAAEmQ,SAAS,EAAEC,UAAU,CAAC;IAC9EA,UAAU,CAACpJ,IAAI,CAAC3D,MAAM,CAACrD,KAAK,CAAC,EAAEqD,MAAM,CAACrD,KAAK,GAAG,CAAC,CAAC,CAAC;IACjDkQ,IAAI,GAAGlQ,KAAK,GAAG,CAAC,IAAIgQ,YAAY,CAAC3M,MAAM,EAAErD,KAAK,EAAEkQ,IAAI,EAAEC,SAAS,EAAEC,UAAU,CAAC;EAC9E;AACF,CAAC,CAAC;;AAGF,OAAO,SAASI,cAAcA,CAACnN,MAAM,EAAE8M,SAAS,EAAE;EAChD,IAAIpB,KAAK,GAAG0B,UAAU,CAACpN,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B2L,KAAK,GAAGyB,UAAU,CAACpN,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B0H,IAAI,GAAG,CAACgE,KAAK,EAAEC,KAAK,CAAC;IACrB/P,CAAC,GAAGoE,MAAM,CAACzE,MAAM,GAAG,CAAC;IACrBL,CAAC;IACDyE,CAAC;IACD/B,CAAC;IACDqN,EAAE;IACFC,EAAE;IACFtF,MAAM;IACNiH,IAAI;EACRC,SAAS,GAAGzT,IAAI,CAACoT,GAAG,CAACK,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC;EAEvC,KAAK5R,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE;IACzByE,CAAC,GAAGyN,UAAU,CAACpN,MAAM,CAAC9E,CAAC,CAAC,CAAC;IACzB0C,CAAC,GAAGwP,UAAU,CAACpN,MAAM,CAAC9E,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7B+P,EAAE,GAAGS,KAAK,GAAG/L,CAAC;IACduL,EAAE,GAAGS,KAAK,GAAG/N,CAAC;IAEd,IAAIqN,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAG4B,SAAS,EAAE;MACjCpF,IAAI,CAAC/D,IAAI,CAAChE,CAAC,EAAE/B,CAAC,CAAC;MACf8N,KAAK,GAAG/L,CAAC;MACTgM,KAAK,GAAG/N,CAAC;IACX;EACF;EAEA8J,IAAI,CAAC/D,IAAI,CAACyJ,UAAU,CAACpN,MAAM,CAACpE,CAAC,CAAC,CAAC,EAAEwR,UAAU,CAACpN,MAAM,CAACpE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3DiR,IAAI,GAAGnF,IAAI,CAACnM,MAAM,GAAG,CAAC;EACtBqK,MAAM,GAAG,CAAC8B,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3BiF,YAAY,CAACjF,IAAI,EAAE,CAAC,EAAEmF,IAAI,EAAEC,SAAS,EAAElH,MAAM,CAAC;EAC9CA,MAAM,CAACjC,IAAI,CAAC+D,IAAI,CAACmF,IAAI,CAAC,EAAEnF,IAAI,CAACmF,IAAI,GAAG,CAAC,CAAC,CAAC;EACvC,OAAOjH,MAAM;AACf;AAEA,SAASyH,0BAA0BA,CAACC,UAAU,EAAEC,EAAE,EAAEC,EAAE,EAAEpL,KAAK,EAAEC,GAAG,EAAEoL,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEvM,EAAE,EAAEC,EAAE,EAAElB,EAAE,EAAEK,EAAE,EAAEJ,EAAE,EAAEK,EAAE,EAAE;EAC1G,IAAIsD,GAAG,GAAG,CAAC1B,GAAG,GAAGD,KAAK,IAAIqL,MAAM;IAC5BG,IAAI,GAAG,CAAC;IACRzS,CAAC,GAAGiH,KAAK;IACTzC,CAAC;IACD/B,CAAC;IACDkE,CAAC;IACDmJ,EAAE;IACFC,EAAE;IACF3G,GAAG;EACPzH,aAAa,GAAG5C,SAAS;EAEzB,OAAOiB,CAAC,IAAIkH,GAAG,EAAE;IACfkC,GAAG,GAAG,CAAC,GAAGpJ,CAAC;IACXwE,CAAC,GAAG4E,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAGmJ,EAAE,GAAG,CAAC,GAAGnJ,GAAG,GAAGA,GAAG,GAAGpJ,CAAC,GAAGiG,EAAE,GAAG,CAAC,GAAGmD,GAAG,GAAGpJ,CAAC,GAAGA,CAAC,GAAGgF,EAAE,GAAGhF,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGiF,EAAE;IACzFxC,CAAC,GAAG2G,GAAG,GAAGA,GAAG,GAAGA,GAAG,GAAGoJ,EAAE,GAAG,CAAC,GAAGpJ,GAAG,GAAGA,GAAG,GAAGpJ,CAAC,GAAGkG,EAAE,GAAG,CAAC,GAAGkD,GAAG,GAAGpJ,CAAC,GAAGA,CAAC,GAAGqF,EAAE,GAAGrF,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGsF,EAAE;IACzFwK,EAAE,GAAGtL,CAAC,GAAG4N,EAAE;IACXrC,EAAE,GAAGtN,CAAC,GAAG4P,EAAE;IACX1L,CAAC,GAAGmJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAErB,IAAIpJ,CAAC,GAAGhF,aAAa,EAAE;MACrBA,aAAa,GAAGgF,CAAC;MACjB8L,IAAI,GAAGzS,CAAC;IACV;IAEAA,CAAC,IAAI4I,GAAG;EACV;EAEA,OAAOuJ,UAAU,GAAG,CAAC,GAAGD,0BAA0B,CAACC,UAAU,GAAG,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEnU,IAAI,CAACkJ,GAAG,CAACqL,IAAI,GAAG7J,GAAG,EAAE,CAAC,CAAC,EAAE1K,IAAI,CAACoJ,GAAG,CAACmL,IAAI,GAAG7J,GAAG,EAAE,CAAC,CAAC,EAAE0J,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEvM,EAAE,EAAEC,EAAE,EAAElB,EAAE,EAAEK,EAAE,EAAEJ,EAAE,EAAEK,EAAE,CAAC,GAAGmN,IAAI;AAC7K;AAEA,OAAO,SAASC,cAAcA,CAAC7S,OAAO,EAAE2E,CAAC,EAAE/B,CAAC,EAAE6P,MAAM,EAAE;EACpD;EACA,IAAIK,OAAO,GAAG;MACZrK,CAAC,EAAE,CAAC;MACJvI,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACG4S,YAAY,GAAG7T,SAAS;IACxBgB,CAAC;IACDuI,CAAC;IACDtI,CAAC;IACDC,OAAO;EAEX,KAAKqI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzI,OAAO,CAACO,MAAM,EAAEkI,CAAC,EAAE,EAAE;IACnCrI,OAAO,GAAGJ,OAAO,CAACyI,CAAC,CAAC;IAEpB,KAAKvI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,OAAO,CAACG,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;MACtCC,CAAC,GAAGkS,0BAA0B,CAAC,CAAC,EAAE1N,CAAC,EAAE/B,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6P,MAAM,IAAI,EAAE,EAAErS,OAAO,CAACF,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAEvL,IAAI6S,YAAY,GAAGjR,aAAa,EAAE;QAChCiR,YAAY,GAAGjR,aAAa;QAC5BgR,OAAO,CAACrK,CAAC,GAAGA,CAAC;QACbqK,OAAO,CAAC5S,CAAC,GAAGA,CAAC;QACb4S,OAAO,CAAC3S,CAAC,GAAGA,CAAC;MACf;IACF;EACF;EAEA,OAAO2S,OAAO;AAChB,CAAC,CAAC;;AAEF,OAAO,SAASE,oBAAoBA,CAACrO,CAAC,EAAE/B,CAAC,EAAExC,OAAO,EAAEqS,MAAM,EAAEH,UAAU,EAAE;EACtE,IAAI1R,CAAC,GAAGR,OAAO,CAACG,MAAM;IAClBwS,YAAY,GAAG7T,SAAS;IACxB+T,KAAK,GAAG,CAAC;IACTC,gBAAgB,GAAG,CAAC;IACpB/S,CAAC;IACDD,CAAC;EACLuS,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrBH,UAAU,GAAGA,UAAU,IAAI,CAAC;EAE5B,KAAKpS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE;IACzBC,CAAC,GAAGkS,0BAA0B,CAAC,CAAC,EAAE1N,CAAC,EAAE/B,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6P,MAAM,EAAErS,OAAO,CAACF,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEE,OAAO,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;IAEjL,IAAI6S,YAAY,GAAGjR,aAAa,EAAE;MAChCiR,YAAY,GAAGjR,aAAa;MAC5BmR,KAAK,GAAG9S,CAAC;MACT+S,gBAAgB,GAAGhT,CAAC;IACtB;EACF;EAEAC,CAAC,GAAGkS,0BAA0B,CAACC,UAAU,EAAE3N,CAAC,EAAE/B,CAAC,EAAEqQ,KAAK,GAAG,IAAI,EAAEA,KAAK,GAAG,IAAI,EAAER,MAAM,EAAErS,OAAO,CAAC8S,gBAAgB,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,EAAE9S,OAAO,CAAC8S,gBAAgB,GAAG,CAAC,CAAC,CAAC;EACxU5S,gBAAgB,CAACF,OAAO,EAAE8S,gBAAgB,EAAE/S,CAAC,CAAC;EAC9C,OAAO+S,gBAAgB,GAAG,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAAS1M,eAAeA,CAACxG,OAAO,EAAE;EACvC,IAAIX,SAAS,CAACW,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB;IACAA,OAAO,GAAG,CAACA,OAAO,CAAC;EACrB;EAEA,IAAI4K,MAAM,GAAG,EAAE;IACXhK,CAAC,GAAGZ,OAAO,CAACO,MAAM;IAClB4S,EAAE;IACFvL,CAAC;IACD1H,CAAC;IACDE,OAAO;EAEX,KAAKwH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhH,CAAC,EAAEgH,CAAC,EAAE,EAAE;IACtBxH,OAAO,GAAGJ,OAAO,CAAC4H,CAAC,CAAC;IACpBgD,MAAM,IAAI,GAAG,GAAG/K,MAAM,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,MAAM,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IACpE+S,EAAE,GAAG/S,OAAO,CAACG,MAAM;IAEnB,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiT,EAAE,EAAEjT,CAAC,EAAE,EAAE;MACvB0K,MAAM,IAAI/K,MAAM,CAACO,OAAO,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGL,MAAM,CAACO,OAAO,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGL,MAAM,CAACO,OAAO,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGL,MAAM,CAACO,OAAO,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGL,MAAM,CAACO,OAAO,CAACF,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGL,MAAM,CAACO,OAAO,CAACF,CAAC,CAAC,CAAC,GAAG,GAAG;IACrL;IAEA,IAAIE,OAAO,CAAC2O,MAAM,EAAE;MAClBnE,MAAM,IAAI,GAAG;IACf;EACF;EAEA,OAAOA,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}