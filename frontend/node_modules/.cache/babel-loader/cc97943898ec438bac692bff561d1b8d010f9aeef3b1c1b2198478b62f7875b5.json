{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={lessons:{},currentLesson:null,isLoading:false,error:null};export const lessonSlice=createSlice({name:'lessons',initialState,reducers:{clearLessons:state=>{state.lessons={};},setLesson:(state,action)=>{state.lessons[action.payload.id]=action.payload;},setCurrentLesson:(state,action)=>{state.currentLesson=action.payload;},markLessonComplete:(state,action)=>{if(state.lessons[action.payload]){state.lessons[action.payload].completed=true;}},setLoading:(state,action)=>{state.isLoading=action.payload;},setError:(state,action)=>{state.error=action.payload;}}});export const lessonActions=lessonSlice.actions;", "map": {"version": 3, "names": ["createSlice", "initialState", "lessons", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "error", "lessonSlice", "name", "reducers", "clear<PERSON><PERSON><PERSON>", "state", "<PERSON><PERSON><PERSON><PERSON>", "action", "payload", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markLessonComplete", "completed", "setLoading", "setError", "lessonActions", "actions"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { Lesson, LessonState } from '../types/lesson';\n\nconst initialState: LessonState = {\n  lessons: {},\n  currentLesson: null,\n  isLoading: false,\n  error: null\n};\n\nexport const lessonSlice = createSlice({\n  name: 'lessons',\n  initialState,\n  reducers: {\n    clearLessons: (state) => {\n      state.lessons = {};\n    },\n    setLesson: (state, action: PayloadAction<Lesson>) => {\n      state.lessons[action.payload.id] = action.payload;\n    },\n    setCurrentLesson: (state, action: PayloadAction<string>) => {\n      state.currentLesson = action.payload;\n    },\n    markLessonComplete: (state, action: PayloadAction<string>) => {\n      if (state.lessons[action.payload]) {\n        state.lessons[action.payload].completed = true;\n      }\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    }\n  }\n});\n\nexport const lessonActions = lessonSlice.actions; "], "mappings": "AAAA,OAASA,WAAW,KAAuB,kBAAkB,CAG7D,KAAM,CAAAC,YAAyB,CAAG,CAChCC,OAAO,CAAE,CAAC,CAAC,CACXC,aAAa,CAAE,IAAI,CACnBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IACT,CAAC,CAED,MAAO,MAAM,CAAAC,WAAW,CAAGN,WAAW,CAAC,CACrCO,IAAI,CAAE,SAAS,CACfN,YAAY,CACZO,QAAQ,CAAE,CACRC,YAAY,CAAGC,KAAK,EAAK,CACvBA,KAAK,CAACR,OAAO,CAAG,CAAC,CAAC,CACpB,CAAC,CACDS,SAAS,CAAEA,CAACD,KAAK,CAAEE,MAA6B,GAAK,CACnDF,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,CAAGF,MAAM,CAACC,OAAO,CACnD,CAAC,CACDE,gBAAgB,CAAEA,CAACL,KAAK,CAAEE,MAA6B,GAAK,CAC1DF,KAAK,CAACP,aAAa,CAAGS,MAAM,CAACC,OAAO,CACtC,CAAC,CACDG,kBAAkB,CAAEA,CAACN,KAAK,CAAEE,MAA6B,GAAK,CAC5D,GAAIF,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAAC,CAAE,CACjCH,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAAC,CAACI,SAAS,CAAG,IAAI,CAChD,CACF,CAAC,CACDC,UAAU,CAAEA,CAACR,KAAK,CAAEE,MAA8B,GAAK,CACrDF,KAAK,CAACN,SAAS,CAAGQ,MAAM,CAACC,OAAO,CAClC,CAAC,CACDM,QAAQ,CAAEA,CAACT,KAAK,CAAEE,MAAoC,GAAK,CACzDF,KAAK,CAACL,KAAK,CAAGO,MAAM,CAACC,OAAO,CAC9B,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAAO,aAAa,CAAGd,WAAW,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}