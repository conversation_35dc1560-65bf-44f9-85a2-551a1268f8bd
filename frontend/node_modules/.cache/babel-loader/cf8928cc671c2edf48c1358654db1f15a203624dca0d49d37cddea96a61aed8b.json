{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx\",\n  _s = $RefreshSig$();\n// HyperOrangeTransition.tsx - Enhanced with text writing\nimport React, { useRef, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { gsap } from 'gsap';\nimport { MotionPathPlugin } from 'gsap/MotionPathPlugin';\nimport './HyperOrangeTransition.css';\n\n// Register the plugin\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ngsap.registerPlugin(MotionPathPlugin);\nconst HyperOrangeTransition = ({\n  isActive,\n  to,\n  onComplete,\n  buttonRect\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const ballRef = useRef(null);\n  const lineRef = useRef(null);\n  const starsRef = useRef([]);\n  const textContainerRef = useRef(null);\n  const animation = useRef(null);\n  const originalOverflow = useRef(document.body.style.overflow);\n  const dropTrail = (originalLine, opacity) => {\n    if (!originalLine) return;\n    const trail = originalLine.cloneNode(true);\n    trail.classList.add('line-clone', 'line-trail');\n    trail.style.opacity = opacity.toString();\n    trail.style.position = 'fixed';\n    trail.style.left = originalLine.style.left;\n    trail.style.transform = originalLine.style.transform;\n    document.body.appendChild(trail);\n    gsap.to(trail, {\n      opacity: 0,\n      duration: 0.3,\n      delay: 0.2,\n      onComplete: () => {\n        if (trail.parentNode) {\n          trail.parentNode.removeChild(trail);\n        }\n      }\n    });\n  };\n  const createTextWritingEffect = () => {\n    const textContainer = document.createElement('div');\n    textContainer.className = 'firefly-text-container';\n    textContainer.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 1003;\n      pointer-events: none;\n      font-family: 'Arial', sans-serif;\n      font-size: 3rem;\n      font-weight: bold;\n      color: #ffffff;\n      text-shadow: 0 0 20px rgba(255, 255, 255, 0.8),\n                   0 0 40px rgba(255, 200, 100, 0.6),\n                   0 0 60px rgba(255, 150, 50, 0.4);\n      letter-spacing: 0.1em;\n      white-space: nowrap;\n    `;\n    const text = 'Browse Premade Videos';\n    const words = text.split(' ');\n    words.forEach((word, wordIndex) => {\n      const wordSpan = document.createElement('span');\n      wordSpan.style.display = 'inline-block';\n      wordSpan.style.marginRight = '0.3em';\n      for (let i = 0; i < word.length; i++) {\n        const letterSpan = document.createElement('span');\n        letterSpan.textContent = word[i];\n        letterSpan.style.cssText = `\n          display: inline-block;\n          opacity: 0;\n          transform: translateY(20px);\n        `;\n        wordSpan.appendChild(letterSpan);\n      }\n      textContainer.appendChild(wordSpan);\n    });\n    document.body.appendChild(textContainer);\n    textContainerRef.current = textContainer;\n    return textContainer;\n  };\n  const animateTextWriting = textContainer => {\n    const letters = textContainer.querySelectorAll('span span');\n    const textTimeline = gsap.timeline();\n\n    // Create writing path for the firefly\n    const letterPositions = [];\n    letters.forEach((letter, index) => {\n      const rect = letter.getBoundingClientRect();\n      letterPositions.push({\n        x: rect.left + rect.width / 2,\n        y: rect.top + rect.height / 2\n      });\n    });\n\n    // Animate firefly writing each letter\n    textTimeline.to(ballRef.current, {\n      motionPath: {\n        path: letterPositions,\n        curviness: 0.8,\n        autoRotate: false\n      },\n      duration: 2.5,\n      ease: 'power1.inOut',\n      onUpdate: function () {\n        // Show letters as firefly passes over them\n        const progress = this.progress();\n        const currentLetterIndex = Math.floor(progress * letters.length);\n        for (let i = 0; i <= currentLetterIndex && i < letters.length; i++) {\n          gsap.to(letters[i], {\n            opacity: 1,\n            y: 0,\n            duration: 0.3,\n            ease: 'back.out(1.7)'\n          });\n        }\n      }\n    }, 0)\n    // Add glow effect to completed text\n    .to(textContainer, {\n      textShadow: `\n          0 0 30px rgba(255, 255, 255, 1),\n          0 0 50px rgba(255, 200, 100, 0.8),\n          0 0 70px rgba(255, 150, 50, 0.6),\n          0 0 90px rgba(255, 100, 0, 0.4)\n        `,\n      duration: 1,\n      ease: 'power2.out'\n    }, 1.5);\n    return textTimeline;\n  };\n  useEffect(() => {\n    if (isActive && buttonRect) {\n      originalOverflow.current = document.body.style.overflow;\n      document.body.style.overflow = 'hidden';\n      const startTransition = () => {\n        const backgroundImage = document.getElementById('background-image');\n        const contentSections = document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');\n        const mainContent = document.querySelector('main, .hero-section, .features-section');\n        gsap.to(contentSections, {\n          opacity: 0,\n          duration: 0.5,\n          ease: 'power2.out',\n          stagger: 0.05\n        });\n        if (mainContent) {\n          mainContent.style.transition = 'opacity 0.3s ease';\n          setTimeout(() => {\n            mainContent.style.opacity = '0.3';\n          }, 500);\n        }\n\n        // Create stars\n        const createStars = () => {\n          const stars = [];\n          const numStars = 12; // More stars for text writing effect\n\n          for (let i = 0; i < numStars; i++) {\n            const star = document.createElement('div');\n            star.className = 'transition-star';\n            star.style.cssText = `\n              position: fixed;\n              width: 3px;\n              height: 3px;\n              background: #ffffff;\n              border-radius: 50%;\n              opacity: 0 !important;\n              visibility: hidden;\n              z-index: 1002;\n              box-shadow: \n                0 0 4px 1px rgba(255, 255, 255, 0.9),\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\n            `;\n\n            // Position stars around the text area\n            const textAreaX = window.innerWidth / 2;\n            const textAreaY = 100;\n            const spreadX = 400;\n            const spreadY = 100;\n            const x = textAreaX + (Math.random() - 0.5) * spreadX;\n            const y = textAreaY + (Math.random() - 0.5) * spreadY;\n            star.style.left = x + 'px';\n            star.style.top = y + 'px';\n            star.style.transform = 'translate(-50%, -50%)';\n            document.body.appendChild(star);\n            stars.push(star);\n            starsRef.current.push(star);\n            gsap.set(star, {\n              opacity: 0,\n              visibility: 'hidden',\n              scale: 0\n            });\n          }\n          return stars;\n        };\n        const stars = createStars();\n        const startX = buttonRect ? buttonRect.left + buttonRect.width / 2 : window.innerWidth / 2;\n        const startY = buttonRect ? buttonRect.top + buttonRect.height / 2 : window.innerHeight / 2;\n        const textStartX = window.innerWidth / 2 - 200; // Start of text area\n        const textStartY = 100; // Text vertical position\n\n        gsap.set(ballRef.current, {\n          x: startX,\n          y: startY,\n          xPercent: -50,\n          yPercent: -50,\n          position: 'fixed',\n          opacity: 1,\n          scale: 1\n        });\n\n        // Create text container\n        const textContainer = createTextWritingEffect();\n\n        // Create firefly path to text writing area\n        const createInitialPath = () => {\n          const pathWidth = window.innerWidth * 0.3;\n          const loopCenterX = startX + (textStartX - startX) * 0.4;\n          const loopCenterY = startY - window.innerHeight * 0.35;\n          const loopRadius = Math.min(pathWidth * 0.3, 80);\n          return [{\n            x: startX,\n            y: startY\n          }, {\n            x: startX + 100,\n            y: startY - 150\n          }, {\n            x: loopCenterX - loopRadius,\n            y: loopCenterY\n          }, {\n            x: loopCenterX,\n            y: loopCenterY - loopRadius\n          }, {\n            x: loopCenterX + loopRadius,\n            y: loopCenterY\n          }, {\n            x: loopCenterX,\n            y: loopCenterY + loopRadius\n          }, {\n            x: loopCenterX - loopRadius * 0.5,\n            y: loopCenterY\n          }, {\n            x: textStartX - 50,\n            y: textStartY + 20\n          }, {\n            x: textStartX,\n            y: textStartY\n          }];\n        };\n        const initialPath = createInitialPath();\n        gsap.set(lineRef.current, {\n          scaleY: 0,\n          transformOrigin: 'top center',\n          position: 'fixed',\n          left: window.innerWidth / 2,\n          top: 0,\n          transform: 'translateX(-50%) translateY(0%)',\n          zIndex: 10000,\n          opacity: 1\n        });\n\n        // Main animation timeline\n        animation.current = gsap.timeline({\n          onComplete: () => {\n            // Clean up any existing elements\n            const clones = document.querySelectorAll('.line-clone');\n            clones.forEach(clone => {\n              var _clone$parentNode;\n              return (_clone$parentNode = clone.parentNode) === null || _clone$parentNode === void 0 ? void 0 : _clone$parentNode.removeChild(clone);\n            });\n\n            // Keep stars and text visible during page load\n            // Navigate to the samples page\n            navigate(to);\n            onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n\n            // Clean up after a delay to allow the new page to load\n            setTimeout(() => {\n              starsRef.current.forEach(star => {\n                if (star.parentNode) {\n                  star.parentNode.removeChild(star);\n                }\n              });\n              starsRef.current = [];\n              if (textContainerRef.current && textContainerRef.current.parentNode) {\n                textContainerRef.current.parentNode.removeChild(textContainerRef.current);\n              }\n              document.body.style.overflow = originalOverflow.current;\n            }, 2000); // Keep elements visible for 2 seconds after navigation\n          }\n        });\n        animation.current\n        // Background movement\n        .to(backgroundImage, {\n          keyframes: {\n            \"0%\": {\n              transform: \"translateY(-65%) scale(1.0)\"\n            },\n            \"50%\": {\n              transform: \"translateY(-37.5%) scale(0.68)\"\n            },\n            \"100%\": {\n              transform: \"translateY(-10%) scale(0.68)\"\n            }\n          },\n          duration: 6,\n          // Extended duration for text writing\n          ease: 'power1.inOut',\n          transformOrigin: 'center center'\n        }, 0)\n        // Initial firefly path to text area\n        .to(ballRef.current, {\n          motionPath: {\n            path: initialPath,\n            curviness: 1.5,\n            autoRotate: false\n          },\n          duration: 2.5,\n          ease: 'sine.inOut',\n          scale: 0.9\n        }, 0.3)\n        // Stars appear early\n        .to(stars, {\n          opacity: 1,\n          visibility: 'visible',\n          scale: 1,\n          duration: 0.4,\n          ease: 'back.out(1.7)',\n          stagger: 0.05\n        }, 1.5)\n        // Text writing animation\n        .add(animateTextWriting(textContainer), 3)\n        // Line appears after text is written\n        .to(lineRef.current, {\n          scaleY: 1,\n          duration: 1.2,\n          ease: 'power2.out'\n        }, 5.5)\n        // Line split effect\n        .add(() => {\n          if (!lineRef.current) return;\n          const rightLine = lineRef.current.cloneNode(true);\n          rightLine.classList.add('line-clone');\n          rightLine.style.position = 'absolute';\n          rightLine.style.left = '50%';\n          rightLine.style.transform = 'translateX(-50%) translateY(0%)';\n          document.body.appendChild(rightLine);\n          gsap.to(lineRef.current, {\n            x: -window.innerWidth / 2,\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n            onUpdate: function () {\n              const progress = this.ratio;\n              if (progress > 0.25 && !this._dropped25 && lineRef.current) {\n                this._dropped25 = true;\n                dropTrail(lineRef.current, 0.75);\n              }\n              if (progress > 0.5 && !this._dropped50 && lineRef.current) {\n                this._dropped50 = true;\n                dropTrail(lineRef.current, 0.5);\n              }\n              if (progress > 0.75 && !this._dropped75 && lineRef.current) {\n                this._dropped75 = true;\n                dropTrail(lineRef.current, 0.25);\n              }\n            }\n          });\n          gsap.to(rightLine, {\n            x: window.innerWidth / 2,\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n            onComplete: () => {\n              if (rightLine.parentNode) {\n                rightLine.parentNode.removeChild(rightLine);\n              }\n            }\n          });\n        }, 6.7)\n        // Flash effect\n        .to([ballRef.current, lineRef.current], {\n          opacity: 0.8,\n          duration: 0.1,\n          repeat: 4,\n          yoyo: true,\n          ease: 'power1.inOut'\n        }, 7)\n        // Final fade out of ball and line (but keep text and stars)\n        .to([ballRef.current, lineRef.current], {\n          opacity: 0,\n          duration: 0.6,\n          ease: 'power2.inOut'\n        }, 7.5);\n        return () => {\n          if (animation.current) animation.current.kill();\n          if (mainContent) {\n            mainContent.style.opacity = '';\n            mainContent.style.transition = '';\n          }\n          if (backgroundImage) {\n            backgroundImage.style.transform = 'translateY(-62%)';\n          }\n        };\n      };\n      const cleanup = startTransition();\n      return cleanup;\n    }\n  }, [isActive, buttonRect, navigate, onComplete, to]);\n  if (!isActive) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transition-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transition-ball\",\n      ref: ballRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transition-line\",\n      ref: lineRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 435,\n    columnNumber: 5\n  }, this);\n};\n_s(HyperOrangeTransition, \"ZkCrq4bSaf3db8Di7qsMZsunvRI=\", false, function () {\n  return [useNavigate];\n});\n_c = HyperOrangeTransition;\nexport default HyperOrangeTransition;\nvar _c;\n$RefreshReg$(_c, \"HyperOrangeTransition\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useNavigate", "gsap", "MotionPathPlugin", "jsxDEV", "_jsxDEV", "registerPlugin", "HyperOrangeTransition", "isActive", "to", "onComplete", "buttonRect", "_s", "navigate", "ballRef", "lineRef", "starsRef", "textContainerRef", "animation", "originalOverflow", "document", "body", "style", "overflow", "dropTrail", "originalLine", "opacity", "trail", "cloneNode", "classList", "add", "toString", "position", "left", "transform", "append<PERSON><PERSON><PERSON>", "duration", "delay", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createTextWritingEffect", "textContainer", "createElement", "className", "cssText", "text", "words", "split", "for<PERSON>ach", "word", "wordIndex", "wordSpan", "display", "marginRight", "i", "length", "letterSpan", "textContent", "current", "animateTextWriting", "letters", "querySelectorAll", "textTimeline", "timeline", "letterPositions", "letter", "index", "rect", "getBoundingClientRect", "push", "x", "width", "y", "top", "height", "motionPath", "path", "curviness", "autoRotate", "ease", "onUpdate", "progress", "currentLetterIndex", "Math", "floor", "textShadow", "startTransition", "backgroundImage", "getElementById", "contentSections", "mainContent", "querySelector", "stagger", "transition", "setTimeout", "createStars", "stars", "numStars", "star", "textAreaX", "window", "innerWidth", "textAreaY", "spreadX", "spreadY", "random", "set", "visibility", "scale", "startX", "startY", "innerHeight", "textStartX", "textStartY", "xPercent", "yPercent", "createInitialPath", "pathWidth", "loopCenterX", "loopCenterY", "loopRadius", "min", "initialPath", "scaleY", "transform<PERSON><PERSON>in", "zIndex", "clones", "clone", "_clone$parentNode", "keyframes", "rightLine", "ratio", "_dropped25", "_dropped50", "_dropped75", "repeat", "yoyo", "kill", "cleanup", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx"], "sourcesContent": ["// HyperOrangeTransition.tsx - Enhanced with text writing\nimport React, { useRef, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { gsap } from 'gsap';\nimport { MotionPathPlugin } from 'gsap/MotionPathPlugin';\nimport './HyperOrangeTransition.css';\n\n// Register the plugin\ngsap.registerPlugin(MotionPathPlugin);\n\ninterface HyperOrangeTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete?: () => void;\n  buttonRect?: DOMRect | null;\n}\n\nconst HyperOrangeTransition: React.FC<HyperOrangeTransitionProps> = ({\n  isActive,\n  to,\n  onComplete,\n  buttonRect,\n}) => {\n  const navigate = useNavigate();\n  const ballRef = useRef<HTMLDivElement>(null);\n  const lineRef = useRef<HTMLDivElement>(null);\n  const starsRef = useRef<HTMLDivElement[]>([]);\n  const textContainerRef = useRef<HTMLDivElement | null>(null);\n  const animation = useRef<gsap.core.Timeline | null>(null);\n  const originalOverflow = useRef<string>(document.body.style.overflow);\n\n  const dropTrail = (originalLine: HTMLElement, opacity: number) => {\n    if (!originalLine) return;\n    \n    const trail = originalLine.cloneNode(true) as HTMLElement;\n    trail.classList.add('line-clone', 'line-trail');\n    trail.style.opacity = opacity.toString();\n    trail.style.position = 'fixed';\n    trail.style.left = originalLine.style.left;\n    trail.style.transform = originalLine.style.transform;\n    document.body.appendChild(trail);\n    \n    gsap.to(trail, {\n      opacity: 0,\n      duration: 0.3,\n      delay: 0.2,\n      onComplete: () => {\n        if (trail.parentNode) {\n          trail.parentNode.removeChild(trail);\n        }\n      }\n    });\n  };\n\n  const createTextWritingEffect = () => {\n    const textContainer = document.createElement('div');\n    textContainer.className = 'firefly-text-container';\n    textContainer.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      z-index: 1003;\n      pointer-events: none;\n      font-family: 'Arial', sans-serif;\n      font-size: 3rem;\n      font-weight: bold;\n      color: #ffffff;\n      text-shadow: 0 0 20px rgba(255, 255, 255, 0.8),\n                   0 0 40px rgba(255, 200, 100, 0.6),\n                   0 0 60px rgba(255, 150, 50, 0.4);\n      letter-spacing: 0.1em;\n      white-space: nowrap;\n    `;\n    \n    const text = 'Browse Premade Videos';\n    const words = text.split(' ');\n    \n    words.forEach((word, wordIndex) => {\n      const wordSpan = document.createElement('span');\n      wordSpan.style.display = 'inline-block';\n      wordSpan.style.marginRight = '0.3em';\n      \n      for (let i = 0; i < word.length; i++) {\n        const letterSpan = document.createElement('span');\n        letterSpan.textContent = word[i];\n        letterSpan.style.cssText = `\n          display: inline-block;\n          opacity: 0;\n          transform: translateY(20px);\n        `;\n        wordSpan.appendChild(letterSpan);\n      }\n      \n      textContainer.appendChild(wordSpan);\n    });\n    \n    document.body.appendChild(textContainer);\n    textContainerRef.current = textContainer;\n    \n    return textContainer;\n  };\n\n  const animateTextWriting = (textContainer: HTMLElement) => {\n    const letters = textContainer.querySelectorAll('span span');\n    const textTimeline = gsap.timeline();\n    \n    // Create writing path for the firefly\n    const letterPositions: {x: number, y: number}[] = [];\n    \n    letters.forEach((letter, index) => {\n      const rect = (letter as HTMLElement).getBoundingClientRect();\n      letterPositions.push({\n        x: rect.left + rect.width / 2,\n        y: rect.top + rect.height / 2\n      });\n    });\n    \n    // Animate firefly writing each letter\n    textTimeline\n      .to(ballRef.current, {\n        motionPath: {\n          path: letterPositions,\n          curviness: 0.8,\n          autoRotate: false\n        },\n        duration: 2.5,\n        ease: 'power1.inOut',\n        onUpdate: function() {\n          // Show letters as firefly passes over them\n          const progress = this.progress();\n          const currentLetterIndex = Math.floor(progress * letters.length);\n          \n          for (let i = 0; i <= currentLetterIndex && i < letters.length; i++) {\n            gsap.to(letters[i], {\n              opacity: 1,\n              y: 0,\n              duration: 0.3,\n              ease: 'back.out(1.7)'\n            });\n          }\n        }\n      }, 0)\n      // Add glow effect to completed text\n      .to(textContainer, {\n        textShadow: `\n          0 0 30px rgba(255, 255, 255, 1),\n          0 0 50px rgba(255, 200, 100, 0.8),\n          0 0 70px rgba(255, 150, 50, 0.6),\n          0 0 90px rgba(255, 100, 0, 0.4)\n        `,\n        duration: 1,\n        ease: 'power2.out'\n      }, 1.5);\n    \n    return textTimeline;\n  };\n\n  useEffect(() => {\n    if (isActive && buttonRect) {\n      originalOverflow.current = document.body.style.overflow;\n      document.body.style.overflow = 'hidden';\n\n      const startTransition = () => {\n        const backgroundImage = document.getElementById('background-image');\n        const contentSections = document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');\n        const mainContent = document.querySelector('main, .hero-section, .features-section');\n\n        gsap.to(contentSections, {\n          opacity: 0,\n          duration: 0.5,\n          ease: 'power2.out',\n          stagger: 0.05\n        });\n\n        if (mainContent) {\n          (mainContent as HTMLElement).style.transition = 'opacity 0.3s ease';\n          setTimeout(() => {\n            (mainContent as HTMLElement).style.opacity = '0.3';\n          }, 500);\n        }\n\n        // Create stars\n        const createStars = () => {\n          const stars = [];\n          const numStars = 12; // More stars for text writing effect\n          \n          for (let i = 0; i < numStars; i++) {\n            const star = document.createElement('div');\n            star.className = 'transition-star';\n            star.style.cssText = `\n              position: fixed;\n              width: 3px;\n              height: 3px;\n              background: #ffffff;\n              border-radius: 50%;\n              opacity: 0 !important;\n              visibility: hidden;\n              z-index: 1002;\n              box-shadow: \n                0 0 4px 1px rgba(255, 255, 255, 0.9),\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\n            `;\n            \n            // Position stars around the text area\n            const textAreaX = window.innerWidth / 2;\n            const textAreaY = 100;\n            const spreadX = 400;\n            const spreadY = 100;\n            \n            const x = textAreaX + (Math.random() - 0.5) * spreadX;\n            const y = textAreaY + (Math.random() - 0.5) * spreadY;\n            \n            star.style.left = x + 'px';\n            star.style.top = y + 'px';\n            star.style.transform = 'translate(-50%, -50%)';\n            \n            document.body.appendChild(star);\n            stars.push(star);\n            starsRef.current.push(star);\n            \n            gsap.set(star, {\n              opacity: 0,\n              visibility: 'hidden',\n              scale: 0\n            });\n          }\n          \n          return stars;\n        };\n\n        const stars = createStars();\n\n        const startX = buttonRect ? buttonRect.left + (buttonRect.width / 2) : window.innerWidth / 2;\n        const startY = buttonRect ? buttonRect.top + (buttonRect.height / 2) : window.innerHeight / 2;\n        const textStartX = window.innerWidth / 2 - 200; // Start of text area\n        const textStartY = 100; // Text vertical position\n\n        gsap.set(ballRef.current, {\n          x: startX,\n          y: startY,\n          xPercent: -50,\n          yPercent: -50,\n          position: 'fixed',\n          opacity: 1,\n          scale: 1\n        });\n\n        // Create text container\n        const textContainer = createTextWritingEffect();\n\n        // Create firefly path to text writing area\n        const createInitialPath = () => {\n          const pathWidth = window.innerWidth * 0.3;\n          const loopCenterX = startX + (textStartX - startX) * 0.4;\n          const loopCenterY = startY - window.innerHeight * 0.35;\n          const loopRadius = Math.min(pathWidth * 0.3, 80);\n          \n          return [\n            { x: startX, y: startY },\n            { x: startX + 100, y: startY - 150 },\n            { x: loopCenterX - loopRadius, y: loopCenterY },\n            { x: loopCenterX, y: loopCenterY - loopRadius },\n            { x: loopCenterX + loopRadius, y: loopCenterY },\n            { x: loopCenterX, y: loopCenterY + loopRadius },\n            { x: loopCenterX - loopRadius * 0.5, y: loopCenterY },\n            { x: textStartX - 50, y: textStartY + 20 },\n            { x: textStartX, y: textStartY }\n          ];\n        };\n\n        const initialPath = createInitialPath();\n\n        gsap.set(lineRef.current, {\n          scaleY: 0,\n          transformOrigin: 'top center',\n          position: 'fixed',\n          left: window.innerWidth / 2,\n          top: 0,\n          transform: 'translateX(-50%) translateY(0%)',\n          zIndex: 10000,\n          opacity: 1\n        });\n\n        // Main animation timeline\n        animation.current = gsap.timeline({\n          onComplete: () => {\n            // Clean up any existing elements\n            const clones = document.querySelectorAll('.line-clone');\n            clones.forEach(clone => clone.parentNode?.removeChild(clone));\n            \n            // Keep stars and text visible during page load\n            // Navigate to the samples page\n            navigate(to);\n            onComplete?.();\n            \n            // Clean up after a delay to allow the new page to load\n            setTimeout(() => {\n              starsRef.current.forEach(star => {\n                if (star.parentNode) {\n                  star.parentNode.removeChild(star);\n                }\n              });\n              starsRef.current = [];\n              \n              if (textContainerRef.current && textContainerRef.current.parentNode) {\n                textContainerRef.current.parentNode.removeChild(textContainerRef.current);\n              }\n              \n              document.body.style.overflow = originalOverflow.current;\n            }, 2000); // Keep elements visible for 2 seconds after navigation\n          }\n        });\n\n        animation.current\n          // Background movement\n          .to(backgroundImage, {\n            keyframes: {\n              \"0%\": { transform: \"translateY(-65%) scale(1.0)\" },\n              \"50%\": { transform: \"translateY(-37.5%) scale(0.68)\" },\n              \"100%\": { transform: \"translateY(-10%) scale(0.68)\" }\n            },\n            duration: 6, // Extended duration for text writing\n            ease: 'power1.inOut',\n            transformOrigin: 'center center',\n          }, 0)\n          // Initial firefly path to text area\n          .to(ballRef.current, {\n            motionPath: {\n              path: initialPath,\n              curviness: 1.5,\n              autoRotate: false\n            },\n            duration: 2.5,\n            ease: 'sine.inOut',\n            scale: 0.9\n          }, 0.3)\n          // Stars appear early\n          .to(stars, {\n            opacity: 1,\n            visibility: 'visible',\n            scale: 1,\n            duration: 0.4,\n            ease: 'back.out(1.7)',\n            stagger: 0.05\n          }, 1.5)\n          // Text writing animation\n          .add(animateTextWriting(textContainer), 3)\n          // Line appears after text is written\n          .to(lineRef.current, {\n            scaleY: 1,\n            duration: 1.2,\n            ease: 'power2.out',\n          }, 5.5)\n          // Line split effect\n          .add(() => {\n            if (!lineRef.current) return;\n            \n            const rightLine = lineRef.current.cloneNode(true) as HTMLElement;\n            rightLine.classList.add('line-clone');\n            rightLine.style.position = 'absolute';\n            rightLine.style.left = '50%';\n            rightLine.style.transform = 'translateX(-50%) translateY(0%)';\n            document.body.appendChild(rightLine);\n\n            gsap.to(lineRef.current, {\n              x: -window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onUpdate: function() {\n                const progress = this.ratio; \n                if (progress > 0.25 && !this._dropped25 && lineRef.current) {\n                  this._dropped25 = true;\n                  dropTrail(lineRef.current, 0.75);\n                }\n                if (progress > 0.5 && !this._dropped50 && lineRef.current) {\n                  this._dropped50 = true;\n                  dropTrail(lineRef.current, 0.5);\n                }\n                if (progress > 0.75 && !this._dropped75 && lineRef.current) {\n                  this._dropped75 = true;\n                  dropTrail(lineRef.current, 0.25);\n                }\n              }\n            });\n\n            gsap.to(rightLine, {\n              x: window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onComplete: () => {\n                if (rightLine.parentNode) {\n                  rightLine.parentNode.removeChild(rightLine);\n                }\n              }\n            });\n          }, 6.7)\n          // Flash effect\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0.8,\n            duration: 0.1,\n            repeat: 4,\n            yoyo: true,\n            ease: 'power1.inOut',\n          }, 7)\n          // Final fade out of ball and line (but keep text and stars)\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n          }, 7.5);\n\n        return () => {\n          if (animation.current) animation.current.kill();\n          if (mainContent) {\n            (mainContent as HTMLElement).style.opacity = '';\n            (mainContent as HTMLElement).style.transition = '';\n          }\n          if (backgroundImage) {\n            backgroundImage.style.transform = 'translateY(-62%)';\n          }\n        };\n      };\n\n      const cleanup = startTransition();\n      return cleanup;\n    }\n  }, [isActive, buttonRect, navigate, onComplete, to]);\n\n  if (!isActive) return null;\n\n  return (\n    <div className=\"transition-container\">\n      <div className=\"transition-ball\" ref={ballRef} />\n      <div className=\"transition-line\" ref={lineRef} />\n    </div>\n  );\n};\n\nexport default HyperOrangeTransition;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAH,IAAI,CAACI,cAAc,CAACH,gBAAgB,CAAC;AASrC,MAAMI,qBAA2D,GAAGA,CAAC;EACnEC,QAAQ;EACRC,EAAE;EACFC,UAAU;EACVC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,OAAO,GAAGf,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMgB,OAAO,GAAGhB,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMiB,QAAQ,GAAGjB,MAAM,CAAmB,EAAE,CAAC;EAC7C,MAAMkB,gBAAgB,GAAGlB,MAAM,CAAwB,IAAI,CAAC;EAC5D,MAAMmB,SAAS,GAAGnB,MAAM,CAA4B,IAAI,CAAC;EACzD,MAAMoB,gBAAgB,GAAGpB,MAAM,CAASqB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC;EAErE,MAAMC,SAAS,GAAGA,CAACC,YAAyB,EAAEC,OAAe,KAAK;IAChE,IAAI,CAACD,YAAY,EAAE;IAEnB,MAAME,KAAK,GAAGF,YAAY,CAACG,SAAS,CAAC,IAAI,CAAgB;IACzDD,KAAK,CAACE,SAAS,CAACC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC;IAC/CH,KAAK,CAACL,KAAK,CAACI,OAAO,GAAGA,OAAO,CAACK,QAAQ,CAAC,CAAC;IACxCJ,KAAK,CAACL,KAAK,CAACU,QAAQ,GAAG,OAAO;IAC9BL,KAAK,CAACL,KAAK,CAACW,IAAI,GAAGR,YAAY,CAACH,KAAK,CAACW,IAAI;IAC1CN,KAAK,CAACL,KAAK,CAACY,SAAS,GAAGT,YAAY,CAACH,KAAK,CAACY,SAAS;IACpDd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACR,KAAK,CAAC;IAEhCzB,IAAI,CAACO,EAAE,CAACkB,KAAK,EAAE;MACbD,OAAO,EAAE,CAAC;MACVU,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE,GAAG;MACV3B,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAIiB,KAAK,CAACW,UAAU,EAAE;UACpBX,KAAK,CAACW,UAAU,CAACC,WAAW,CAACZ,KAAK,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,aAAa,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,KAAK,CAAC;IACnDD,aAAa,CAACE,SAAS,GAAG,wBAAwB;IAClDF,aAAa,CAACnB,KAAK,CAACsB,OAAO,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAED,MAAMC,IAAI,GAAG,uBAAuB;IACpC,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAE7BD,KAAK,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;MACjC,MAAMC,QAAQ,GAAG/B,QAAQ,CAACsB,aAAa,CAAC,MAAM,CAAC;MAC/CS,QAAQ,CAAC7B,KAAK,CAAC8B,OAAO,GAAG,cAAc;MACvCD,QAAQ,CAAC7B,KAAK,CAAC+B,WAAW,GAAG,OAAO;MAEpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAME,UAAU,GAAGpC,QAAQ,CAACsB,aAAa,CAAC,MAAM,CAAC;QACjDc,UAAU,CAACC,WAAW,GAAGR,IAAI,CAACK,CAAC,CAAC;QAChCE,UAAU,CAAClC,KAAK,CAACsB,OAAO,GAAG;AACnC;AACA;AACA;AACA,SAAS;QACDO,QAAQ,CAAChB,WAAW,CAACqB,UAAU,CAAC;MAClC;MAEAf,aAAa,CAACN,WAAW,CAACgB,QAAQ,CAAC;IACrC,CAAC,CAAC;IAEF/B,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACM,aAAa,CAAC;IACxCxB,gBAAgB,CAACyC,OAAO,GAAGjB,aAAa;IAExC,OAAOA,aAAa;EACtB,CAAC;EAED,MAAMkB,kBAAkB,GAAIlB,aAA0B,IAAK;IACzD,MAAMmB,OAAO,GAAGnB,aAAa,CAACoB,gBAAgB,CAAC,WAAW,CAAC;IAC3D,MAAMC,YAAY,GAAG5D,IAAI,CAAC6D,QAAQ,CAAC,CAAC;;IAEpC;IACA,MAAMC,eAAyC,GAAG,EAAE;IAEpDJ,OAAO,CAACZ,OAAO,CAAC,CAACiB,MAAM,EAAEC,KAAK,KAAK;MACjC,MAAMC,IAAI,GAAIF,MAAM,CAAiBG,qBAAqB,CAAC,CAAC;MAC5DJ,eAAe,CAACK,IAAI,CAAC;QACnBC,CAAC,EAAEH,IAAI,CAAClC,IAAI,GAAGkC,IAAI,CAACI,KAAK,GAAG,CAAC;QAC7BC,CAAC,EAAEL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAZ,YAAY,CACTrD,EAAE,CAACK,OAAO,CAAC4C,OAAO,EAAE;MACnBiB,UAAU,EAAE;QACVC,IAAI,EAAEZ,eAAe;QACrBa,SAAS,EAAE,GAAG;QACdC,UAAU,EAAE;MACd,CAAC;MACD1C,QAAQ,EAAE,GAAG;MACb2C,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,SAAAA,CAAA,EAAW;QACnB;QACA,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;QAChC,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAGrB,OAAO,CAACL,MAAM,CAAC;QAEhE,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI4B,kBAAkB,IAAI5B,CAAC,GAAGM,OAAO,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;UAClEpD,IAAI,CAACO,EAAE,CAACmD,OAAO,CAACN,CAAC,CAAC,EAAE;YAClB5B,OAAO,EAAE,CAAC;YACV8C,CAAC,EAAE,CAAC;YACJpC,QAAQ,EAAE,GAAG;YACb2C,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF;IACF,CAAC,EAAE,CAAC;IACJ;IAAA,CACCtE,EAAE,CAACgC,aAAa,EAAE;MACjB4C,UAAU,EAAE;AACpB;AACA;AACA;AACA;AACA,SAAS;MACDjD,QAAQ,EAAE,CAAC;MACX2C,IAAI,EAAE;IACR,CAAC,EAAE,GAAG,CAAC;IAET,OAAOjB,YAAY;EACrB,CAAC;EAED9D,SAAS,CAAC,MAAM;IACd,IAAIQ,QAAQ,IAAIG,UAAU,EAAE;MAC1BQ,gBAAgB,CAACuC,OAAO,GAAGtC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ;MACvDH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;MAEvC,MAAM+D,eAAe,GAAGA,CAAA,KAAM;QAC5B,MAAMC,eAAe,GAAGnE,QAAQ,CAACoE,cAAc,CAAC,kBAAkB,CAAC;QACnE,MAAMC,eAAe,GAAGrE,QAAQ,CAACyC,gBAAgB,CAAC,sEAAsE,CAAC;QACzH,MAAM6B,WAAW,GAAGtE,QAAQ,CAACuE,aAAa,CAAC,wCAAwC,CAAC;QAEpFzF,IAAI,CAACO,EAAE,CAACgF,eAAe,EAAE;UACvB/D,OAAO,EAAE,CAAC;UACVU,QAAQ,EAAE,GAAG;UACb2C,IAAI,EAAE,YAAY;UAClBa,OAAO,EAAE;QACX,CAAC,CAAC;QAEF,IAAIF,WAAW,EAAE;UACdA,WAAW,CAAiBpE,KAAK,CAACuE,UAAU,GAAG,mBAAmB;UACnEC,UAAU,CAAC,MAAM;YACdJ,WAAW,CAAiBpE,KAAK,CAACI,OAAO,GAAG,KAAK;UACpD,CAAC,EAAE,GAAG,CAAC;QACT;;QAEA;QACA,MAAMqE,WAAW,GAAGA,CAAA,KAAM;UACxB,MAAMC,KAAK,GAAG,EAAE;UAChB,MAAMC,QAAQ,GAAG,EAAE,CAAC,CAAC;;UAErB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,QAAQ,EAAE3C,CAAC,EAAE,EAAE;YACjC,MAAM4C,IAAI,GAAG9E,QAAQ,CAACsB,aAAa,CAAC,KAAK,CAAC;YAC1CwD,IAAI,CAACvD,SAAS,GAAG,iBAAiB;YAClCuD,IAAI,CAAC5E,KAAK,CAACsB,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;YAED;YACA,MAAMuD,SAAS,GAAGC,MAAM,CAACC,UAAU,GAAG,CAAC;YACvC,MAAMC,SAAS,GAAG,GAAG;YACrB,MAAMC,OAAO,GAAG,GAAG;YACnB,MAAMC,OAAO,GAAG,GAAG;YAEnB,MAAMlC,CAAC,GAAG6B,SAAS,GAAG,CAAChB,IAAI,CAACsB,MAAM,CAAC,CAAC,GAAG,GAAG,IAAIF,OAAO;YACrD,MAAM/B,CAAC,GAAG8B,SAAS,GAAG,CAACnB,IAAI,CAACsB,MAAM,CAAC,CAAC,GAAG,GAAG,IAAID,OAAO;YAErDN,IAAI,CAAC5E,KAAK,CAACW,IAAI,GAAGqC,CAAC,GAAG,IAAI;YAC1B4B,IAAI,CAAC5E,KAAK,CAACmD,GAAG,GAAGD,CAAC,GAAG,IAAI;YACzB0B,IAAI,CAAC5E,KAAK,CAACY,SAAS,GAAG,uBAAuB;YAE9Cd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAAC+D,IAAI,CAAC;YAC/BF,KAAK,CAAC3B,IAAI,CAAC6B,IAAI,CAAC;YAChBlF,QAAQ,CAAC0C,OAAO,CAACW,IAAI,CAAC6B,IAAI,CAAC;YAE3BhG,IAAI,CAACwG,GAAG,CAACR,IAAI,EAAE;cACbxE,OAAO,EAAE,CAAC;cACViF,UAAU,EAAE,QAAQ;cACpBC,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;UAEA,OAAOZ,KAAK;QACd,CAAC;QAED,MAAMA,KAAK,GAAGD,WAAW,CAAC,CAAC;QAE3B,MAAMc,MAAM,GAAGlG,UAAU,GAAGA,UAAU,CAACsB,IAAI,GAAItB,UAAU,CAAC4D,KAAK,GAAG,CAAE,GAAG6B,MAAM,CAACC,UAAU,GAAG,CAAC;QAC5F,MAAMS,MAAM,GAAGnG,UAAU,GAAGA,UAAU,CAAC8D,GAAG,GAAI9D,UAAU,CAAC+D,MAAM,GAAG,CAAE,GAAG0B,MAAM,CAACW,WAAW,GAAG,CAAC;QAC7F,MAAMC,UAAU,GAAGZ,MAAM,CAACC,UAAU,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAChD,MAAMY,UAAU,GAAG,GAAG,CAAC,CAAC;;QAExB/G,IAAI,CAACwG,GAAG,CAAC5F,OAAO,CAAC4C,OAAO,EAAE;UACxBY,CAAC,EAAEuC,MAAM;UACTrC,CAAC,EAAEsC,MAAM;UACTI,QAAQ,EAAE,CAAC,EAAE;UACbC,QAAQ,EAAE,CAAC,EAAE;UACbnF,QAAQ,EAAE,OAAO;UACjBN,OAAO,EAAE,CAAC;UACVkF,KAAK,EAAE;QACT,CAAC,CAAC;;QAEF;QACA,MAAMnE,aAAa,GAAGD,uBAAuB,CAAC,CAAC;;QAE/C;QACA,MAAM4E,iBAAiB,GAAGA,CAAA,KAAM;UAC9B,MAAMC,SAAS,GAAGjB,MAAM,CAACC,UAAU,GAAG,GAAG;UACzC,MAAMiB,WAAW,GAAGT,MAAM,GAAG,CAACG,UAAU,GAAGH,MAAM,IAAI,GAAG;UACxD,MAAMU,WAAW,GAAGT,MAAM,GAAGV,MAAM,CAACW,WAAW,GAAG,IAAI;UACtD,MAAMS,UAAU,GAAGrC,IAAI,CAACsC,GAAG,CAACJ,SAAS,GAAG,GAAG,EAAE,EAAE,CAAC;UAEhD,OAAO,CACL;YAAE/C,CAAC,EAAEuC,MAAM;YAAErC,CAAC,EAAEsC;UAAO,CAAC,EACxB;YAAExC,CAAC,EAAEuC,MAAM,GAAG,GAAG;YAAErC,CAAC,EAAEsC,MAAM,GAAG;UAAI,CAAC,EACpC;YAAExC,CAAC,EAAEgD,WAAW,GAAGE,UAAU;YAAEhD,CAAC,EAAE+C;UAAY,CAAC,EAC/C;YAAEjD,CAAC,EAAEgD,WAAW;YAAE9C,CAAC,EAAE+C,WAAW,GAAGC;UAAW,CAAC,EAC/C;YAAElD,CAAC,EAAEgD,WAAW,GAAGE,UAAU;YAAEhD,CAAC,EAAE+C;UAAY,CAAC,EAC/C;YAAEjD,CAAC,EAAEgD,WAAW;YAAE9C,CAAC,EAAE+C,WAAW,GAAGC;UAAW,CAAC,EAC/C;YAAElD,CAAC,EAAEgD,WAAW,GAAGE,UAAU,GAAG,GAAG;YAAEhD,CAAC,EAAE+C;UAAY,CAAC,EACrD;YAAEjD,CAAC,EAAE0C,UAAU,GAAG,EAAE;YAAExC,CAAC,EAAEyC,UAAU,GAAG;UAAG,CAAC,EAC1C;YAAE3C,CAAC,EAAE0C,UAAU;YAAExC,CAAC,EAAEyC;UAAW,CAAC,CACjC;QACH,CAAC;QAED,MAAMS,WAAW,GAAGN,iBAAiB,CAAC,CAAC;QAEvClH,IAAI,CAACwG,GAAG,CAAC3F,OAAO,CAAC2C,OAAO,EAAE;UACxBiE,MAAM,EAAE,CAAC;UACTC,eAAe,EAAE,YAAY;UAC7B5F,QAAQ,EAAE,OAAO;UACjBC,IAAI,EAAEmE,MAAM,CAACC,UAAU,GAAG,CAAC;UAC3B5B,GAAG,EAAE,CAAC;UACNvC,SAAS,EAAE,iCAAiC;UAC5C2F,MAAM,EAAE,KAAK;UACbnG,OAAO,EAAE;QACX,CAAC,CAAC;;QAEF;QACAR,SAAS,CAACwC,OAAO,GAAGxD,IAAI,CAAC6D,QAAQ,CAAC;UAChCrD,UAAU,EAAEA,CAAA,KAAM;YAChB;YACA,MAAMoH,MAAM,GAAG1G,QAAQ,CAACyC,gBAAgB,CAAC,aAAa,CAAC;YACvDiE,MAAM,CAAC9E,OAAO,CAAC+E,KAAK;cAAA,IAAAC,iBAAA;cAAA,QAAAA,iBAAA,GAAID,KAAK,CAACzF,UAAU,cAAA0F,iBAAA,uBAAhBA,iBAAA,CAAkBzF,WAAW,CAACwF,KAAK,CAAC;YAAA,EAAC;;YAE7D;YACA;YACAlH,QAAQ,CAACJ,EAAE,CAAC;YACZC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,CAAC;;YAEd;YACAoF,UAAU,CAAC,MAAM;cACf9E,QAAQ,CAAC0C,OAAO,CAACV,OAAO,CAACkD,IAAI,IAAI;gBAC/B,IAAIA,IAAI,CAAC5D,UAAU,EAAE;kBACnB4D,IAAI,CAAC5D,UAAU,CAACC,WAAW,CAAC2D,IAAI,CAAC;gBACnC;cACF,CAAC,CAAC;cACFlF,QAAQ,CAAC0C,OAAO,GAAG,EAAE;cAErB,IAAIzC,gBAAgB,CAACyC,OAAO,IAAIzC,gBAAgB,CAACyC,OAAO,CAACpB,UAAU,EAAE;gBACnErB,gBAAgB,CAACyC,OAAO,CAACpB,UAAU,CAACC,WAAW,CAACtB,gBAAgB,CAACyC,OAAO,CAAC;cAC3E;cAEAtC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAGJ,gBAAgB,CAACuC,OAAO;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACZ;QACF,CAAC,CAAC;QAEFxC,SAAS,CAACwC;QACR;QAAA,CACCjD,EAAE,CAAC8E,eAAe,EAAE;UACnB0C,SAAS,EAAE;YACT,IAAI,EAAE;cAAE/F,SAAS,EAAE;YAA8B,CAAC;YAClD,KAAK,EAAE;cAAEA,SAAS,EAAE;YAAiC,CAAC;YACtD,MAAM,EAAE;cAAEA,SAAS,EAAE;YAA+B;UACtD,CAAC;UACDE,QAAQ,EAAE,CAAC;UAAE;UACb2C,IAAI,EAAE,cAAc;UACpB6C,eAAe,EAAE;QACnB,CAAC,EAAE,CAAC;QACJ;QAAA,CACCnH,EAAE,CAACK,OAAO,CAAC4C,OAAO,EAAE;UACnBiB,UAAU,EAAE;YACVC,IAAI,EAAE8C,WAAW;YACjB7C,SAAS,EAAE,GAAG;YACdC,UAAU,EAAE;UACd,CAAC;UACD1C,QAAQ,EAAE,GAAG;UACb2C,IAAI,EAAE,YAAY;UAClB6B,KAAK,EAAE;QACT,CAAC,EAAE,GAAG;QACN;QAAA,CACCnG,EAAE,CAACuF,KAAK,EAAE;UACTtE,OAAO,EAAE,CAAC;UACViF,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,CAAC;UACRxE,QAAQ,EAAE,GAAG;UACb2C,IAAI,EAAE,eAAe;UACrBa,OAAO,EAAE;QACX,CAAC,EAAE,GAAG;QACN;QAAA,CACC9D,GAAG,CAAC6B,kBAAkB,CAAClB,aAAa,CAAC,EAAE,CAAC;QACzC;QAAA,CACChC,EAAE,CAACM,OAAO,CAAC2C,OAAO,EAAE;UACnBiE,MAAM,EAAE,CAAC;UACTvF,QAAQ,EAAE,GAAG;UACb2C,IAAI,EAAE;QACR,CAAC,EAAE,GAAG;QACN;QAAA,CACCjD,GAAG,CAAC,MAAM;UACT,IAAI,CAACf,OAAO,CAAC2C,OAAO,EAAE;UAEtB,MAAMwE,SAAS,GAAGnH,OAAO,CAAC2C,OAAO,CAAC9B,SAAS,CAAC,IAAI,CAAgB;UAChEsG,SAAS,CAACrG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;UACrCoG,SAAS,CAAC5G,KAAK,CAACU,QAAQ,GAAG,UAAU;UACrCkG,SAAS,CAAC5G,KAAK,CAACW,IAAI,GAAG,KAAK;UAC5BiG,SAAS,CAAC5G,KAAK,CAACY,SAAS,GAAG,iCAAiC;UAC7Dd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAAC+F,SAAS,CAAC;UAEpChI,IAAI,CAACO,EAAE,CAACM,OAAO,CAAC2C,OAAO,EAAE;YACvBY,CAAC,EAAE,CAAC8B,MAAM,CAACC,UAAU,GAAG,CAAC;YACzB3E,OAAO,EAAE,CAAC;YACVU,QAAQ,EAAE,GAAG;YACb2C,IAAI,EAAE,cAAc;YACpBC,QAAQ,EAAE,SAAAA,CAAA,EAAW;cACnB,MAAMC,QAAQ,GAAG,IAAI,CAACkD,KAAK;cAC3B,IAAIlD,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACmD,UAAU,IAAIrH,OAAO,CAAC2C,OAAO,EAAE;gBAC1D,IAAI,CAAC0E,UAAU,GAAG,IAAI;gBACtB5G,SAAS,CAACT,OAAO,CAAC2C,OAAO,EAAE,IAAI,CAAC;cAClC;cACA,IAAIuB,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI,CAACoD,UAAU,IAAItH,OAAO,CAAC2C,OAAO,EAAE;gBACzD,IAAI,CAAC2E,UAAU,GAAG,IAAI;gBACtB7G,SAAS,CAACT,OAAO,CAAC2C,OAAO,EAAE,GAAG,CAAC;cACjC;cACA,IAAIuB,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACqD,UAAU,IAAIvH,OAAO,CAAC2C,OAAO,EAAE;gBAC1D,IAAI,CAAC4E,UAAU,GAAG,IAAI;gBACtB9G,SAAS,CAACT,OAAO,CAAC2C,OAAO,EAAE,IAAI,CAAC;cAClC;YACF;UACF,CAAC,CAAC;UAEFxD,IAAI,CAACO,EAAE,CAACyH,SAAS,EAAE;YACjB5D,CAAC,EAAE8B,MAAM,CAACC,UAAU,GAAG,CAAC;YACxB3E,OAAO,EAAE,CAAC;YACVU,QAAQ,EAAE,GAAG;YACb2C,IAAI,EAAE,cAAc;YACpBrE,UAAU,EAAEA,CAAA,KAAM;cAChB,IAAIwH,SAAS,CAAC5F,UAAU,EAAE;gBACxB4F,SAAS,CAAC5F,UAAU,CAACC,WAAW,CAAC2F,SAAS,CAAC;cAC7C;YACF;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG;QACN;QAAA,CACCzH,EAAE,CAAC,CAACK,OAAO,CAAC4C,OAAO,EAAE3C,OAAO,CAAC2C,OAAO,CAAC,EAAE;UACtChC,OAAO,EAAE,GAAG;UACZU,QAAQ,EAAE,GAAG;UACbmG,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,IAAI;UACVzD,IAAI,EAAE;QACR,CAAC,EAAE,CAAC;QACJ;QAAA,CACCtE,EAAE,CAAC,CAACK,OAAO,CAAC4C,OAAO,EAAE3C,OAAO,CAAC2C,OAAO,CAAC,EAAE;UACtChC,OAAO,EAAE,CAAC;UACVU,QAAQ,EAAE,GAAG;UACb2C,IAAI,EAAE;QACR,CAAC,EAAE,GAAG,CAAC;QAET,OAAO,MAAM;UACX,IAAI7D,SAAS,CAACwC,OAAO,EAAExC,SAAS,CAACwC,OAAO,CAAC+E,IAAI,CAAC,CAAC;UAC/C,IAAI/C,WAAW,EAAE;YACdA,WAAW,CAAiBpE,KAAK,CAACI,OAAO,GAAG,EAAE;YAC9CgE,WAAW,CAAiBpE,KAAK,CAACuE,UAAU,GAAG,EAAE;UACpD;UACA,IAAIN,eAAe,EAAE;YACnBA,eAAe,CAACjE,KAAK,CAACY,SAAS,GAAG,kBAAkB;UACtD;QACF,CAAC;MACH,CAAC;MAED,MAAMwG,OAAO,GAAGpD,eAAe,CAAC,CAAC;MACjC,OAAOoD,OAAO;IAChB;EACF,CAAC,EAAE,CAAClI,QAAQ,EAAEG,UAAU,EAAEE,QAAQ,EAAEH,UAAU,EAAED,EAAE,CAAC,CAAC;EAEpD,IAAI,CAACD,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEH,OAAA;IAAKsC,SAAS,EAAC,sBAAsB;IAAAgG,QAAA,gBACnCtI,OAAA;MAAKsC,SAAS,EAAC,iBAAiB;MAACiG,GAAG,EAAE9H;IAAQ;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjD3I,OAAA;MAAKsC,SAAS,EAAC,iBAAiB;MAACiG,GAAG,EAAE7H;IAAQ;MAAA8H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC;AAEV,CAAC;AAACpI,EAAA,CAtaIL,qBAA2D;EAAA,QAM9CN,WAAW;AAAA;AAAAgJ,EAAA,GANxB1I,qBAA2D;AAwajE,eAAeA,qBAAqB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}