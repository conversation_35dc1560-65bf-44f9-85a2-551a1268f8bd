{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GSAPGyroscope = () => {\n  _s();\n  const gyroscopeRef = useRef(null);\n  const outerRingRef = useRef(null);\n  const middleRingRef = useRef(null);\n  const innerRingRef = useRef(null);\n  const coreRef = useRef(null);\n  const particleContainerRef = useRef(null);\n  const axesRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const animationsRef = useRef([]);\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n\n  // Initialize GSAP animations\n  useEffect(() => {\n    var _particleContainerRef;\n    // Clear any existing animations\n    animationsRef.current.forEach(tween => tween.kill());\n    animationsRef.current = [];\n\n    // Initialize all elements with proper 3D setup\n    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];\n    gsap.set(elements, {\n      transformOrigin: \"center center\",\n      transformStyle: \"preserve-3d\",\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0\n    });\n\n    // Continuous base rotations - store references for cleanup\n    const outerAnim = gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n    const middleAnim = gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n    const innerAnim = gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Core pulsing animation\n    const coreAnim = gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.15,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Axes rotation\n    const axesAnim = gsap.to(axesRef.current, {\n      duration: 30,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Store animation references\n    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim, axesAnim];\n\n    // Initialize particles with GSAP\n    const particles = (_particleContainerRef = particleContainerRef.current) === null || _particleContainerRef === void 0 ? void 0 : _particleContainerRef.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        const element = particle;\n\n        // Set initial state\n        gsap.set(element, {\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          scale: Math.random() * 0.5 + 0.5,\n          opacity: Math.random() * 0.6 + 0.4\n        });\n\n        // Floating animation\n        const particleAnim = gsap.to(element, {\n          duration: 4 + Math.random() * 6,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          scale: Math.random() * 0.8 + 0.6,\n          ease: \"sine.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 3\n        });\n        animationsRef.current.push(particleAnim);\n      });\n    }\n\n    // Cleanup function\n    return () => {\n      animationsRef.current.forEach(tween => tween.kill());\n      animationsRef.current = [];\n    };\n  }, []);\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !gyroscopeRef.current) return;\n    const rect = gyroscopeRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Calculate rotation based on mouse position\n    const rotationX = mouseY / rect.height * 180;\n    const rotationY = mouseX / rect.width * 180;\n    const rotationZ = (mouseX + mouseY) / (rect.width + rect.height) * 90;\n\n    // Apply interactive rotations with GSAP - these will temporarily override base animations\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: rotationX,\n      rotationY: rotationY,\n      rotationZ: rotationZ,\n      ease: \"power2.out\",\n      overwrite: \"auto\" // This allows temporary override of base animation\n    });\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: rotationX * 1.5,\n      rotationY: rotationY * 0.7,\n      rotationZ: rotationZ * -1,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: rotationX * 0.5,\n      rotationY: rotationY * 1.8,\n      rotationZ: rotationZ * 1.5,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n\n    // Spring back to base rotations with elastic ease\n    gsap.to(outerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        // Restart base rotation\n        const newAnim = gsap.to(outerRingRef.current, {\n          duration: 20,\n          rotationY: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[0] = newAnim;\n      }\n    });\n    gsap.to(middleRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(middleRingRef.current, {\n          duration: 15,\n          rotationX: -360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[1] = newAnim;\n      }\n    });\n    gsap.to(innerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(innerRingRef.current, {\n          duration: 10,\n          rotationZ: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[2] = newAnim;\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center overflow-hidden relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute rounded-full blur-3xl animate-pulse\",\n        style: {\n          width: `${150 + i * 50}px`,\n          height: `${150 + i * 50}px`,\n          left: `${15 + i * 12}%`,\n          top: `${10 + i * 15}%`,\n          background: `radial-gradient(circle, ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'][i]}44, transparent)`,\n          animationDelay: `${i * 0.8}s`,\n          animationDuration: `${4 + i * 0.5}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-5xl font-bold mb-8 text-center bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse\",\n        children: \"GSAP Quantum Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: gyroscopeRef,\n        className: \"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing\",\n        style: {\n          perspective: '1200px',\n          transformStyle: 'preserve-3d'\n        },\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: particleContainerRef,\n          className: \"absolute inset-0 pointer-events-none\",\n          children: [...Array(25)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute w-3 h-3 rounded-full\",\n            style: {\n              left: '50%',\n              top: '50%',\n              background: `radial-gradient(circle, ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i % 5]}, transparent)`,\n              boxShadow: `0 0 10px ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i % 5]}`,\n              transformOrigin: 'center center',\n              transformStyle: 'preserve-3d'\n            }\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: outerRingRef,\n          className: \"absolute inset-0 rounded-full border-4 border-blue-500\",\n          style: {\n            transformStyle: 'preserve-3d',\n            transformOrigin: 'center center',\n            boxShadow: `\n                0 0 30px #3b82f6,\n                inset 0 0 30px #3b82f6aa,\n                0 0 60px #3b82f6aa\n              `\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: middleRingRef,\n            className: \"absolute inset-8 rounded-full border-4 border-purple-500\",\n            style: {\n              transformStyle: 'preserve-3d',\n              transformOrigin: 'center center',\n              boxShadow: `\n                  0 0 40px #8b5cf6,\n                  inset 0 0 40px #8b5cf6aa\n                `\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: innerRingRef,\n              className: \"absolute inset-8 rounded-full border-4 border-pink-500\",\n              style: {\n                transformStyle: 'preserve-3d',\n                transformOrigin: 'center center',\n                boxShadow: `\n                    0 0 50px #ec4899,\n                    inset 0 0 50px #ec4899aa\n                  `\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: coreRef,\n                className: \"absolute inset-8 rounded-full bg-gradient-to-br from-white via-yellow-200 to-orange-300\",\n                style: {\n                  transformStyle: 'preserve-3d',\n                  transformOrigin: 'center center',\n                  boxShadow: `\n                      0 0 60px #ffffff,\n                      inset 0 0 20px #f59e0b88\n                    `\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/2 left-1/2 w-6 h-6 -mt-3 -ml-3 rounded-full bg-gradient-to-r from-red-500 to-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: axesRef,\n          className: \"absolute -inset-16 pointer-events-none opacity-60\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-0 right-0 h-0.5\",\n            style: {\n              background: 'linear-gradient(90deg, transparent, #ef4444, transparent)',\n              boxShadow: '0 0 10px #ef4444'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 bottom-0 left-1/2 w-0.5\",\n            style: {\n              background: 'linear-gradient(180deg, transparent, #22c55e, transparent)',\n              boxShadow: '0 0 10px #22c55e'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-1/2 w-32 h-0.5 -ml-16\",\n            style: {\n              background: 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\n              boxShadow: '0 0 10px #3b82f6'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 text-sm text-gray-300 space-y-2 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-400\",\n          children: \"\\u2728 Drag to manipulate quantum fields with GSAP precision\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-purple-400 text-xs\",\n          children: \"Enhanced with GreenSock Animation Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-pink-400 text-xs\",\n          children: \"Smooth 60fps 3D transforms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 5\n  }, this);\n};\n_s(GSAPGyroscope, \"xcu3xQzCDeslOZsGnDHaPT1YvJI=\");\n_c = GSAPGyroscope;\nexport default GSAPGyroscope;\nvar _c;\n$RefreshReg$(_c, \"GSAPGyroscope\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "gsap", "jsxDEV", "_jsxDEV", "GSAPGyroscope", "_s", "gyroscopeRef", "outerRingRef", "middleRingRef", "innerRingRef", "coreRef", "particleContainerRef", "axesRef", "isDragging", "setIsDragging", "animationsRef", "lastMousePos", "x", "y", "_particleContainerRef", "current", "for<PERSON>ach", "tween", "kill", "elements", "set", "transform<PERSON><PERSON>in", "transformStyle", "rotationX", "rotationY", "rotationZ", "outerAnim", "to", "duration", "ease", "repeat", "middleAnim", "innerAnim", "coreAnim", "scale", "yoyo", "axesAnim", "particles", "children", "Array", "from", "particle", "index", "element", "Math", "random", "opacity", "particleAnim", "rotation", "delay", "push", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "mouseX", "mouseY", "overwrite", "handleMouseUp", "onComplete", "newAnim", "className", "map", "_", "i", "style", "background", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "perspective", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "boxShadow", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\nconst GSAPGyroscope = () => {\n  const gyroscopeRef = useRef<HTMLDivElement>(null);\n  const outerRingRef = useRef<HTMLDivElement>(null);\n  const middleRingRef = useRef<HTMLDivElement>(null);\n  const innerRingRef = useRef<HTMLDivElement>(null);\n  const coreRef = useRef<HTMLDivElement>(null);\n  const particleContainerRef = useRef<HTMLDivElement>(null);\n  const axesRef = useRef<HTMLDivElement>(null);\n  \n  const [isDragging, setIsDragging] = useState(false);\n  const animationsRef = useRef<gsap.core.Tween[]>([]);\n  const lastMousePos = useRef({ x: 0, y: 0 });\n\n  // Initialize GSAP animations\n  useEffect(() => {\n    // Clear any existing animations\n    animationsRef.current.forEach(tween => tween.kill());\n    animationsRef.current = [];\n\n    // Initialize all elements with proper 3D setup\n    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];\n    \n    gsap.set(elements, {\n      transformOrigin: \"center center\",\n      transformStyle: \"preserve-3d\",\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0\n    });\n\n    // Continuous base rotations - store references for cleanup\n    const outerAnim = gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    const middleAnim = gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    const innerAnim = gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Core pulsing animation\n    const coreAnim = gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.15,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Axes rotation\n    const axesAnim = gsap.to(axesRef.current, {\n      duration: 30,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Store animation references\n    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim, axesAnim];\n\n    // Initialize particles with GSAP\n    const particles = particleContainerRef.current?.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        const element = particle as HTMLElement;\n        \n        // Set initial state\n        gsap.set(element, {\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          scale: Math.random() * 0.5 + 0.5,\n          opacity: Math.random() * 0.6 + 0.4\n        });\n        \n        // Floating animation\n        const particleAnim = gsap.to(element, {\n          duration: 4 + Math.random() * 6,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          scale: Math.random() * 0.8 + 0.6,\n          ease: \"sine.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 3\n        });\n        \n        animationsRef.current.push(particleAnim);\n      });\n    }\n\n    // Cleanup function\n    return () => {\n      animationsRef.current.forEach(tween => tween.kill());\n      animationsRef.current = [];\n    };\n  }, []);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging || !gyroscopeRef.current) return;\n    \n    const rect = gyroscopeRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Calculate rotation based on mouse position\n    const rotationX = (mouseY / rect.height) * 180;\n    const rotationY = (mouseX / rect.width) * 180;\n    const rotationZ = ((mouseX + mouseY) / (rect.width + rect.height)) * 90;\n\n    // Apply interactive rotations with GSAP - these will temporarily override base animations\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: rotationX,\n      rotationY: rotationY,\n      rotationZ: rotationZ,\n      ease: \"power2.out\",\n      overwrite: \"auto\" // This allows temporary override of base animation\n    });\n\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: rotationX * 1.5,\n      rotationY: rotationY * 0.7,\n      rotationZ: rotationZ * -1,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: rotationX * 0.5,\n      rotationY: rotationY * 1.8,\n      rotationZ: rotationZ * 1.5,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    \n    // Spring back to base rotations with elastic ease\n    gsap.to(outerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        // Restart base rotation\n        const newAnim = gsap.to(outerRingRef.current, {\n          duration: 20,\n          rotationY: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[0] = newAnim;\n      }\n    });\n\n    gsap.to(middleRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(middleRingRef.current, {\n          duration: 15,\n          rotationX: -360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[1] = newAnim;\n      }\n    });\n\n    gsap.to(innerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(innerRingRef.current, {\n          duration: 10,\n          rotationZ: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[2] = newAnim;\n      }\n    });\n  };\n\n  return (\n    <div className=\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center overflow-hidden relative\">\n      {/* Ambient Effects */}\n      <div className=\"absolute inset-0 opacity-20\">\n        {[...Array(6)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute rounded-full blur-3xl animate-pulse\"\n            style={{\n              width: `${150 + i * 50}px`,\n              height: `${150 + i * 50}px`,\n              left: `${15 + i * 12}%`,\n              top: `${10 + i * 15}%`,\n              background: `radial-gradient(circle, ${ \n                ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'][i] \n              }44, transparent)`,\n              animationDelay: `${i * 0.8}s`,\n              animationDuration: `${4 + i * 0.5}s`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10\">\n        <h1 className=\"text-5xl font-bold mb-8 text-center bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse\">\n          GSAP Quantum Gyroscope\n        </h1>\n\n        <div\n          ref={gyroscopeRef}\n          className=\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing\"\n          style={{ \n            perspective: '1200px',\n            transformStyle: 'preserve-3d'\n          }}\n          onMouseDown={handleMouseDown}\n          onMouseMove={handleMouseMove}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseUp}\n        >\n          {/* Enhanced Particle System */}\n          <div ref={particleContainerRef} className=\"absolute inset-0 pointer-events-none\">\n            {[...Array(25)].map((_, i) => (\n              <div\n                key={i}\n                className=\"absolute w-3 h-3 rounded-full\"\n                style={{\n                  left: '50%',\n                  top: '50%',\n                  background: `radial-gradient(circle, ${\n                    ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i % 5]\n                  }, transparent)`,\n                  boxShadow: `0 0 10px ${\n                    ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i % 5]\n                  }`,\n                  transformOrigin: 'center center',\n                  transformStyle: 'preserve-3d'\n                }}\n              />\n            ))}\n          </div>\n\n          {/* Outer Ring */}\n          <div \n            ref={outerRingRef}\n            className=\"absolute inset-0 rounded-full border-4 border-blue-500\"\n            style={{\n              transformStyle: 'preserve-3d',\n              transformOrigin: 'center center',\n              boxShadow: `\n                0 0 30px #3b82f6,\n                inset 0 0 30px #3b82f6aa,\n                0 0 60px #3b82f6aa\n              `\n            }}\n          >\n            {/* Middle Ring */}\n            <div \n              ref={middleRingRef}\n              className=\"absolute inset-8 rounded-full border-4 border-purple-500\"\n              style={{\n                transformStyle: 'preserve-3d',\n                transformOrigin: 'center center',\n                boxShadow: `\n                  0 0 40px #8b5cf6,\n                  inset 0 0 40px #8b5cf6aa\n                `\n              }}\n            >\n              {/* Inner Ring */}\n              <div \n                ref={innerRingRef}\n                className=\"absolute inset-8 rounded-full border-4 border-pink-500\"\n                style={{\n                  transformStyle: 'preserve-3d',\n                  transformOrigin: 'center center',\n                  boxShadow: `\n                    0 0 50px #ec4899,\n                    inset 0 0 50px #ec4899aa\n                  `\n                }}\n              >\n                {/* Core */}\n                <div \n                  ref={coreRef}\n                  className=\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-yellow-200 to-orange-300\"\n                  style={{\n                    transformStyle: 'preserve-3d',\n                    transformOrigin: 'center center',\n                    boxShadow: `\n                      0 0 60px #ffffff,\n                      inset 0 0 20px #f59e0b88\n                    `\n                  }}\n                >\n                  <div className=\"absolute top-1/2 left-1/2 w-6 h-6 -mt-3 -ml-3 rounded-full bg-gradient-to-r from-red-500 to-yellow-500\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Rotation Axes */}\n          <div ref={axesRef} className=\"absolute -inset-16 pointer-events-none opacity-60\">\n            <div \n              className=\"absolute top-1/2 left-0 right-0 h-0.5\" \n              style={{ \n                background: 'linear-gradient(90deg, transparent, #ef4444, transparent)',\n                boxShadow: '0 0 10px #ef4444'\n              }} \n            />\n            <div \n              className=\"absolute top-0 bottom-0 left-1/2 w-0.5\" \n              style={{ \n                background: 'linear-gradient(180deg, transparent, #22c55e, transparent)',\n                boxShadow: '0 0 10px #22c55e'\n              }} \n            />\n            <div \n              className=\"absolute top-1/2 left-1/2 w-32 h-0.5 -ml-16\" \n              style={{ \n                background: 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\n                boxShadow: '0 0 10px #3b82f6'\n              }} \n            />\n          </div>\n        </div>\n        \n        <div className=\"mt-6 text-sm text-gray-300 space-y-2 text-center\">\n          <p className=\"text-blue-400\">✨ Drag to manipulate quantum fields with GSAP precision</p>\n          <p className=\"text-purple-400 text-xs\">Enhanced with GreenSock Animation Platform</p>\n          <p className=\"text-pink-400 text-xs\">Smooth 60fps 3D transforms</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GSAPGyroscope;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,YAAY,GAAGP,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMQ,YAAY,GAAGR,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMS,aAAa,GAAGT,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAMU,YAAY,GAAGV,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMW,OAAO,GAAGX,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMY,oBAAoB,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EACzD,MAAMa,OAAO,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAE5C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMe,aAAa,GAAGhB,MAAM,CAAoB,EAAE,CAAC;EACnD,MAAMiB,YAAY,GAAGjB,MAAM,CAAC;IAAEkB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;;EAE3C;EACApB,SAAS,CAAC,MAAM;IAAA,IAAAqB,qBAAA;IACd;IACAJ,aAAa,CAACK,OAAO,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;IACpDR,aAAa,CAACK,OAAO,GAAG,EAAE;;IAE1B;IACA,MAAMI,QAAQ,GAAG,CAACjB,YAAY,CAACa,OAAO,EAAEZ,aAAa,CAACY,OAAO,EAAEX,YAAY,CAACW,OAAO,EAAEV,OAAO,CAACU,OAAO,CAAC;IAErGnB,IAAI,CAACwB,GAAG,CAACD,QAAQ,EAAE;MACjBE,eAAe,EAAE,eAAe;MAChCC,cAAc,EAAE,aAAa;MAC7BC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS,GAAG9B,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC9Ca,QAAQ,EAAE,EAAE;MACZJ,SAAS,EAAE,GAAG;MACdK,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,MAAMU,UAAU,GAAGnC,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAChDa,QAAQ,EAAE,EAAE;MACZL,SAAS,EAAE,CAAC,GAAG;MACfM,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,MAAMW,SAAS,GAAGpC,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC9Ca,QAAQ,EAAE,EAAE;MACZH,SAAS,EAAE,GAAG;MACdI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMY,QAAQ,GAAGrC,IAAI,CAAC+B,EAAE,CAACtB,OAAO,CAACU,OAAO,EAAE;MACxCa,QAAQ,EAAE,CAAC;MACXM,KAAK,EAAE,IAAI;MACXL,IAAI,EAAE,cAAc;MACpBM,IAAI,EAAE,IAAI;MACVL,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMe,QAAQ,GAAGxC,IAAI,CAAC+B,EAAE,CAACpB,OAAO,CAACQ,OAAO,EAAE;MACxCa,QAAQ,EAAE,EAAE;MACZH,SAAS,EAAE,GAAG;MACdI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACAX,aAAa,CAACK,OAAO,GAAG,CAACW,SAAS,EAAEK,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEG,QAAQ,CAAC;;IAE9E;IACA,MAAMC,SAAS,IAAAvB,qBAAA,GAAGR,oBAAoB,CAACS,OAAO,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BwB,QAAQ;IACxD,IAAID,SAAS,EAAE;MACbE,KAAK,CAACC,IAAI,CAACH,SAAS,CAAC,CAACrB,OAAO,CAAC,CAACyB,QAAQ,EAAEC,KAAK,KAAK;QACjD,MAAMC,OAAO,GAAGF,QAAuB;;QAEvC;QACA7C,IAAI,CAACwB,GAAG,CAACuB,OAAO,EAAE;UAChB/B,CAAC,EAAE,CAACgC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BhC,CAAC,EAAE,CAAC+B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BX,KAAK,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCC,OAAO,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;;QAEF;QACA,MAAME,YAAY,GAAGnD,IAAI,CAAC+B,EAAE,CAACgB,OAAO,EAAE;UACpCf,QAAQ,EAAE,CAAC,GAAGgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BjC,CAAC,EAAE,CAACgC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BhC,CAAC,EAAE,CAAC+B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BG,QAAQ,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC7BX,KAAK,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChChB,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,CAAC,CAAC;UACVK,IAAI,EAAE,IAAI;UACVc,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB,CAAC,CAAC;QAEFnC,aAAa,CAACK,OAAO,CAACmC,IAAI,CAACH,YAAY,CAAC;MAC1C,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO,MAAM;MACXrC,aAAa,CAACK,OAAO,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACpDR,aAAa,CAACK,OAAO,GAAG,EAAE;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoC,eAAe,GAAIC,CAAmB,IAAK;IAC/C3C,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAACI,OAAO,GAAG;MAAEH,CAAC,EAAEwC,CAAC,CAACC,OAAO;MAAExC,CAAC,EAAEuC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAmB,IAAK;IAC/C,IAAI,CAAC5C,UAAU,IAAI,CAACP,YAAY,CAACc,OAAO,EAAE;IAE1C,MAAMyC,IAAI,GAAGvD,YAAY,CAACc,OAAO,CAAC0C,qBAAqB,CAAC,CAAC;IACzD,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;IAC1C,MAAMC,MAAM,GAAGZ,CAAC,CAACC,OAAO,GAAGK,OAAO;IAClC,MAAMO,MAAM,GAAGb,CAAC,CAACE,OAAO,GAAGO,OAAO;;IAElC;IACA,MAAMtC,SAAS,GAAI0C,MAAM,GAAGT,IAAI,CAACO,MAAM,GAAI,GAAG;IAC9C,MAAMvC,SAAS,GAAIwC,MAAM,GAAGR,IAAI,CAACI,KAAK,GAAI,GAAG;IAC7C,MAAMnC,SAAS,GAAI,CAACuC,MAAM,GAAGC,MAAM,KAAKT,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACO,MAAM,CAAC,GAAI,EAAE;;IAEvE;IACAnE,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE,MAAM,CAAC;IACpB,CAAC,CAAC;IAEFtE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAC7Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,CAAC,CAAC;MACzBI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE;IACb,CAAC,CAAC;IAEFtE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE;IACb,CAAC,CAAC;IAEFvD,YAAY,CAACI,OAAO,GAAG;MAAEH,CAAC,EAAEwC,CAAC,CAACC,OAAO;MAAExC,CAAC,EAAEuC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B1D,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACAb,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB;QACA,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;UAC5Ca,QAAQ,EAAE,EAAE;UACZJ,SAAS,EAAE,GAAG;UACdK,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;IAEFzE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAC7Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;UAC7Ca,QAAQ,EAAE,EAAE;UACZL,SAAS,EAAE,CAAC,GAAG;UACfM,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;IAEFzE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;UAC5Ca,QAAQ,EAAE,EAAE;UACZH,SAAS,EAAE,GAAG;UACdI,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACEvE,OAAA;IAAKwE,SAAS,EAAC,wIAAwI;IAAAhC,QAAA,gBAErJxC,OAAA;MAAKwE,SAAS,EAAC,6BAA6B;MAAAhC,QAAA,EACzC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB3E,OAAA;QAEEwE,SAAS,EAAC,8CAA8C;QACxDI,KAAK,EAAE;UACLd,KAAK,EAAE,GAAG,GAAG,GAAGa,CAAC,GAAG,EAAE,IAAI;UAC1BV,MAAM,EAAE,GAAG,GAAG,GAAGU,CAAC,GAAG,EAAE,IAAI;UAC3Bd,IAAI,EAAE,GAAG,EAAE,GAAGc,CAAC,GAAG,EAAE,GAAG;UACvBX,GAAG,EAAE,GAAG,EAAE,GAAGW,CAAC,GAAG,EAAE,GAAG;UACtBE,UAAU,EAAE,2BACV,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACF,CAAC,CAAC,kBACrD;UAClBG,cAAc,EAAE,GAAGH,CAAC,GAAG,GAAG,GAAG;UAC7BI,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,CAAC,GAAG,GAAG;QACnC;MAAE,GAZGA,CAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnF,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAhC,QAAA,gBAC5BxC,OAAA;QAAIwE,SAAS,EAAC,2IAA2I;QAAAhC,QAAA,EAAC;MAE1J;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QACEoF,GAAG,EAAEjF,YAAa;QAClBqE,SAAS,EAAC,+DAA+D;QACzEI,KAAK,EAAE;UACLS,WAAW,EAAE,QAAQ;UACrB7D,cAAc,EAAE;QAClB,CAAE;QACF8D,WAAW,EAAEjC,eAAgB;QAC7BkC,WAAW,EAAE9B,eAAgB;QAC7B+B,SAAS,EAAEnB,aAAc;QACzBoB,YAAY,EAAEpB,aAAc;QAAA7B,QAAA,gBAG5BxC,OAAA;UAAKoF,GAAG,EAAE5E,oBAAqB;UAACgE,SAAS,EAAC,sCAAsC;UAAAhC,QAAA,EAC7E,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACgC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB3E,OAAA;YAEEwE,SAAS,EAAC,+BAA+B;YACzCI,KAAK,EAAE;cACLf,IAAI,EAAE,KAAK;cACXG,GAAG,EAAE,KAAK;cACVa,UAAU,EAAE,2BACV,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACF,CAAC,GAAG,CAAC,CAAC,gBAChD;cAChBe,SAAS,EAAE,YACT,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACf,CAAC,GAAG,CAAC,CAAC,EAC9D;cACFpD,eAAe,EAAE,eAAe;cAChCC,cAAc,EAAE;YAClB;UAAE,GAbGmD,CAAC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcP,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnF,OAAA;UACEoF,GAAG,EAAEhF,YAAa;UAClBoE,SAAS,EAAC,wDAAwD;UAClEI,KAAK,EAAE;YACLpD,cAAc,EAAE,aAAa;YAC7BD,eAAe,EAAE,eAAe;YAChCmE,SAAS,EAAE;AACzB;AACA;AACA;AACA;UACY,CAAE;UAAAlD,QAAA,eAGFxC,OAAA;YACEoF,GAAG,EAAE/E,aAAc;YACnBmE,SAAS,EAAC,0DAA0D;YACpEI,KAAK,EAAE;cACLpD,cAAc,EAAE,aAAa;cAC7BD,eAAe,EAAE,eAAe;cAChCmE,SAAS,EAAE;AAC3B;AACA;AACA;YACc,CAAE;YAAAlD,QAAA,eAGFxC,OAAA;cACEoF,GAAG,EAAE9E,YAAa;cAClBkE,SAAS,EAAC,wDAAwD;cAClEI,KAAK,EAAE;gBACLpD,cAAc,EAAE,aAAa;gBAC7BD,eAAe,EAAE,eAAe;gBAChCmE,SAAS,EAAE;AAC7B;AACA;AACA;cACgB,CAAE;cAAAlD,QAAA,eAGFxC,OAAA;gBACEoF,GAAG,EAAE7E,OAAQ;gBACbiE,SAAS,EAAC,yFAAyF;gBACnGI,KAAK,EAAE;kBACLpD,cAAc,EAAE,aAAa;kBAC7BD,eAAe,EAAE,eAAe;kBAChCmE,SAAS,EAAE;AAC/B;AACA;AACA;gBACkB,CAAE;gBAAAlD,QAAA,eAEFxC,OAAA;kBAAKwE,SAAS,EAAC;gBAAwG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnF,OAAA;UAAKoF,GAAG,EAAE3E,OAAQ;UAAC+D,SAAS,EAAC,mDAAmD;UAAAhC,QAAA,gBAC9ExC,OAAA;YACEwE,SAAS,EAAC,uCAAuC;YACjDI,KAAK,EAAE;cACLC,UAAU,EAAE,2DAA2D;cACvEa,SAAS,EAAE;YACb;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnF,OAAA;YACEwE,SAAS,EAAC,wCAAwC;YAClDI,KAAK,EAAE;cACLC,UAAU,EAAE,4DAA4D;cACxEa,SAAS,EAAE;YACb;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnF,OAAA;YACEwE,SAAS,EAAC,6CAA6C;YACvDI,KAAK,EAAE;cACLC,UAAU,EAAE,2DAA2D;cACvEa,SAAS,EAAE;YACb;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnF,OAAA;QAAKwE,SAAS,EAAC,kDAAkD;QAAAhC,QAAA,gBAC/DxC,OAAA;UAAGwE,SAAS,EAAC,eAAe;UAAAhC,QAAA,EAAC;QAAuD;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFnF,OAAA;UAAGwE,SAAS,EAAC,yBAAyB;UAAAhC,QAAA,EAAC;QAA0C;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFnF,OAAA;UAAGwE,SAAS,EAAC,uBAAuB;UAAAhC,QAAA,EAAC;QAA0B;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjF,EAAA,CA5XID,aAAa;AAAA0F,EAAA,GAAb1F,aAAa;AA8XnB,eAAeA,aAAa;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}