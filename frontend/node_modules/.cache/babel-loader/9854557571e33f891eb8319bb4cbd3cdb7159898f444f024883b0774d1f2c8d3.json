{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gyroscope = () => {\n  _s();\n  const [rotation, setRotation] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n  const animationRef = useRef();\n\n  // Auto-rotation with momentum\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: prev.x + momentum.x + 0.2,\n          y: prev.y + momentum.y + 0.3,\n          z: prev.z + momentum.z + 0.1\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.98,\n          y: prev.y * 0.98,\n          z: prev.z * 0.98\n        }));\n      }\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.5;\n    setRotation(prev => ({\n      x: prev.x + deltaY * rotationSpeed,\n      y: prev.y + deltaX * rotationSpeed,\n      z: prev.z + (deltaX + deltaY) * 0.1\n    }));\n    setMomentum({\n      x: deltaY * 0.1,\n      y: deltaX * 0.1,\n      z: (deltaX + deltaY) * 0.05\n    });\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseUp = () => setIsDragging(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-screen bg-white flex items-center justify-center p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold mb-4 text-black\",\n        children: \"3D Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\",\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full border-4 border-black shadow-lg\",\n          style: {\n            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n            transition: isDragging ? 'none' : 'transform 0.1s ease-out',\n            boxShadow: '0 0 30px rgba(0,0,0,0.3)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-8 rounded-full border-4 border-gray-600\",\n            style: {\n              transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n              boxShadow: '0 0 20px rgba(0,0,0,0.2)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-8 rounded-full border-4 border-gray-400\",\n              style: {\n                transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                boxShadow: '0 0 15px rgba(0,0,0,0.1)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-8 rounded-full bg-gradient-to-b from-black to-gray-400\",\n                style: {\n                  transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                  boxShadow: 'inset 0 0 20px rgba(0,0,0,0.3)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/2 left-1/2 w-4 h-4 -mt-2 -ml-2 rounded-full bg-white shadow-md\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), [...Array(8)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute w-2 h-2 bg-yellow-400 rounded-full opacity-60\",\n          style: {\n            left: `${20 + Math.random() * 60}%`,\n            top: `${20 + Math.random() * 60}%`,\n            animation: `float ${2 + Math.random() * 3}s ease-in-out infinite`,\n            animationDelay: `${Math.random() * 2}s`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -inset-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-0 right-0 h-0.5 bg-red-500 opacity-30\",\n            style: {\n              transform: `rotateZ(${rotation.x}deg)`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 bottom-0 left-1/2 w-0.5 bg-green-500 opacity-30\",\n            style: {\n              transform: `rotateZ(${rotation.y}deg)`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-1/2 w-24 h-0.5 -ml-12 bg-blue-500 opacity-30\",\n            style: {\n              transform: `rotateZ(${rotation.z}deg)`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 text-sm text-gray-600\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Click and drag to interact with the gyroscope\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Rotation: X: \", Math.round(rotation.x), \"\\xB0 Y: \", Math.round(rotation.y), \"\\xB0 Z: \", Math.round(rotation.z), \"\\xB0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(Gyroscope, \"3U1YGKIof7c/7aZU2wk84FEABVc=\");\n_c = Gyroscope;\nexport default Gyroscope;\nvar _c;\n$RefreshReg$(_c, \"Gyroscope\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Gyroscope", "_s", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "momentum", "setMomentum", "lastMousePos", "animationRef", "animate", "prev", "current", "requestAnimationFrame", "cancelAnimationFrame", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "deltaX", "deltaY", "rotationSpeed", "handleMouseUp", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "style", "transform", "transition", "boxShadow", "Array", "map", "_", "i", "left", "Math", "random", "top", "animation", "animationDelay", "round", "jsx", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\nconst Gyroscope = () => {\n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const lastMousePos = useRef({ x: 0, y: 0 });\n  const animationRef = useRef<number>();\n\n  // Auto-rotation with momentum\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: prev.x + momentum.x + 0.2,\n          y: prev.y + momentum.y + 0.3,\n          z: prev.z + momentum.z + 0.1\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.98,\n          y: prev.y * 0.98,\n          z: prev.z * 0.98\n        }));\n      }\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.5;\n    setRotation(prev => ({\n      x: prev.x + deltaY * rotationSpeed,\n      y: prev.y + deltaX * rotationSpeed,\n      z: prev.z + (deltaX + deltaY) * 0.1\n    }));\n    setMomentum({\n      x: deltaY * 0.1,\n      y: deltaX * 0.1,\n      z: (deltaX + deltaY) * 0.05\n    });\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseUp = () => setIsDragging(false);\n\n  return (\n    <div className=\"w-full h-screen bg-white flex items-center justify-center p-4\">\n      <div className=\"text-center\">\n        <h1 className=\"text-2xl font-bold mb-4 text-black\">3D Gyroscope</h1>\n        <div \n          className=\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\"\n          onMouseDown={handleMouseDown}\n          onMouseMove={handleMouseMove}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseUp}\n        >\n          {/* Outer Ring */}\n          <div \n            className=\"absolute inset-0 rounded-full border-4 border-black shadow-lg\"\n            style={{\n              transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n              transition: isDragging ? 'none' : 'transform 0.1s ease-out',\n              boxShadow: '0 0 30px rgba(0,0,0,0.3)'\n            }}\n          >\n            {/* Middle Ring */}\n            <div \n              className=\"absolute inset-8 rounded-full border-4 border-gray-600\"\n              style={{\n                transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n                boxShadow: '0 0 20px rgba(0,0,0,0.2)'\n              }}\n            >\n              {/* Inner Ring */}\n              <div \n                className=\"absolute inset-8 rounded-full border-4 border-gray-400\"\n                style={{\n                  transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                  boxShadow: '0 0 15px rgba(0,0,0,0.1)'\n                }}\n              >\n                {/* Core */}\n                <div \n                  className=\"absolute inset-8 rounded-full bg-gradient-to-b from-black to-gray-400\"\n                  style={{\n                    transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                    boxShadow: 'inset 0 0 20px rgba(0,0,0,0.3)'\n                  }}\n                >\n                  {/* Center Dot */}\n                  <div className=\"absolute top-1/2 left-1/2 w-4 h-4 -mt-2 -ml-2 rounded-full bg-white shadow-md\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Particle Effects */}\n          {[...Array(8)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute w-2 h-2 bg-yellow-400 rounded-full opacity-60\"\n              style={{\n                left: `${20 + Math.random() * 60}%`,\n                top: `${20 + Math.random() * 60}%`,\n                animation: `float ${2 + Math.random() * 3}s ease-in-out infinite`,\n                animationDelay: `${Math.random() * 2}s`\n              }}\n            />\n          ))}\n\n          {/* Rotation Axes Indicators */}\n          <div className=\"absolute -inset-12\">\n            <div \n              className=\"absolute top-1/2 left-0 right-0 h-0.5 bg-red-500 opacity-30\" \n              style={{ transform: `rotateZ(${rotation.x}deg)` }} \n            />\n            <div \n              className=\"absolute top-0 bottom-0 left-1/2 w-0.5 bg-green-500 opacity-30\" \n              style={{ transform: `rotateZ(${rotation.y}deg)` }} \n            />\n            <div \n              className=\"absolute top-1/2 left-1/2 w-24 h-0.5 -ml-12 bg-blue-500 opacity-30\" \n              style={{ transform: `rotateZ(${rotation.z}deg)` }} \n            />\n          </div>\n        </div>\n        \n        <div className=\"mt-4 text-sm text-gray-600\">\n          <p>Click and drag to interact with the gyroscope</p>\n          <p>Rotation: X: {Math.round(rotation.x)}° Y: {Math.round(rotation.y)}° Z: {Math.round(rotation.z)}°</p>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Gyroscope;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAMK,YAAY,GAAGd,MAAM,CAAC;IAAEO,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC3C,MAAMO,YAAY,GAAGf,MAAM,CAAS,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMiB,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACN,UAAU,EAAE;QACfJ,WAAW,CAACW,IAAI,KAAK;UACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAGK,QAAQ,CAACL,CAAC,GAAG,GAAG;UAC5BC,CAAC,EAAES,IAAI,CAACT,CAAC,GAAGI,QAAQ,CAACJ,CAAC,GAAG,GAAG;UAC5BC,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAGG,QAAQ,CAACH,CAAC,GAAG;QAC3B,CAAC,CAAC,CAAC;QACHI,WAAW,CAACI,IAAI,KAAK;UACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAES,IAAI,CAACT,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAG;QACd,CAAC,CAAC,CAAC;MACL;MACAM,YAAY,CAACG,OAAO,GAAGC,qBAAqB,CAACH,OAAO,CAAC;IACvD,CAAC;IACDD,YAAY,CAACG,OAAO,GAAGC,qBAAqB,CAACH,OAAO,CAAC;IACrD,OAAO,MAAM;MACX,IAAID,YAAY,CAACG,OAAO,EAAEE,oBAAoB,CAACL,YAAY,CAACG,OAAO,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAACR,UAAU,EAAEE,QAAQ,CAAC,CAAC;EAE1B,MAAMS,eAAe,GAAIC,CAAmB,IAAK;IAC/CX,aAAa,CAAC,IAAI,CAAC;IACnBG,YAAY,CAACI,OAAO,GAAG;MAAEX,CAAC,EAAEe,CAAC,CAACC,OAAO;MAAEf,CAAC,EAAEc,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAmB,IAAK;IAC/C,IAAI,CAACZ,UAAU,EAAE;IACjB,MAAMgB,MAAM,GAAGJ,CAAC,CAACC,OAAO,GAAGT,YAAY,CAACI,OAAO,CAACX,CAAC;IACjD,MAAMoB,MAAM,GAAGL,CAAC,CAACE,OAAO,GAAGV,YAAY,CAACI,OAAO,CAACV,CAAC;IACjD,MAAMoB,aAAa,GAAG,GAAG;IACzBtB,WAAW,CAACW,IAAI,KAAK;MACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAGoB,MAAM,GAAGC,aAAa;MAClCpB,CAAC,EAAES,IAAI,CAACT,CAAC,GAAGkB,MAAM,GAAGE,aAAa;MAClCnB,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAG,CAACiB,MAAM,GAAGC,MAAM,IAAI;IAClC,CAAC,CAAC,CAAC;IACHd,WAAW,CAAC;MACVN,CAAC,EAAEoB,MAAM,GAAG,GAAG;MACfnB,CAAC,EAAEkB,MAAM,GAAG,GAAG;MACfjB,CAAC,EAAE,CAACiB,MAAM,GAAGC,MAAM,IAAI;IACzB,CAAC,CAAC;IACFb,YAAY,CAACI,OAAO,GAAG;MAAEX,CAAC,EAAEe,CAAC,CAACC,OAAO;MAAEf,CAAC,EAAEc,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAC;EAEhD,oBACET,OAAA;IAAK4B,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAC5E7B,OAAA;MAAK4B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B7B,OAAA;QAAI4B,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpEjC,OAAA;QACE4B,SAAS,EAAC,2EAA2E;QACrFM,WAAW,EAAEf,eAAgB;QAC7BgB,WAAW,EAAEZ,eAAgB;QAC7Ba,SAAS,EAAET,aAAc;QACzBU,YAAY,EAAEV,aAAc;QAAAE,QAAA,gBAG5B7B,OAAA;UACE4B,SAAS,EAAC,+DAA+D;UACzEU,KAAK,EAAE;YACLC,SAAS,EAAE,WAAWpC,QAAQ,CAACE,CAAC,gBAAgBF,QAAQ,CAACG,CAAC,gBAAgBH,QAAQ,CAACI,CAAC,MAAM;YAC1FiC,UAAU,EAAEhC,UAAU,GAAG,MAAM,GAAG,yBAAyB;YAC3DiC,SAAS,EAAE;UACb,CAAE;UAAAZ,QAAA,eAGF7B,OAAA;YACE4B,SAAS,EAAC,wDAAwD;YAClEU,KAAK,EAAE;cACLC,SAAS,EAAE,WAAWpC,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,CAAC,MAAM;cAC3GkC,SAAS,EAAE;YACb,CAAE;YAAAZ,QAAA,eAGF7B,OAAA;cACE4B,SAAS,EAAC,wDAAwD;cAClEU,KAAK,EAAE;gBACLC,SAAS,EAAE,WAAWpC,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,GAAG,MAAM;gBAC5GkC,SAAS,EAAE;cACb,CAAE;cAAAZ,QAAA,eAGF7B,OAAA;gBACE4B,SAAS,EAAC,uEAAuE;gBACjFU,KAAK,EAAE;kBACLC,SAAS,EAAE,WAAWpC,QAAQ,CAACE,CAAC,GAAG,CAAC,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,CAAC,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,MAAM;kBAC5GkC,SAAS,EAAE;gBACb,CAAE;gBAAAZ,QAAA,eAGF7B,OAAA;kBAAK4B,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC,GAAGS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB7C,OAAA;UAEE4B,SAAS,EAAC,wDAAwD;UAClEU,KAAK,EAAE;YACLQ,IAAI,EAAE,GAAG,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG;YACnCC,GAAG,EAAE,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG;YAClCE,SAAS,EAAE,SAAS,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,wBAAwB;YACjEG,cAAc,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACtC;QAAE,GAPGH,CAAC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACF,CAAC,eAGFjC,OAAA;UAAK4B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC7B,OAAA;YACE4B,SAAS,EAAC,6DAA6D;YACvEU,KAAK,EAAE;cAAEC,SAAS,EAAE,WAAWpC,QAAQ,CAACE,CAAC;YAAO;UAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFjC,OAAA;YACE4B,SAAS,EAAC,gEAAgE;YAC1EU,KAAK,EAAE;cAAEC,SAAS,EAAE,WAAWpC,QAAQ,CAACG,CAAC;YAAO;UAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFjC,OAAA;YACE4B,SAAS,EAAC,oEAAoE;YAC9EU,KAAK,EAAE;cAAEC,SAAS,EAAE,WAAWpC,QAAQ,CAACI,CAAC;YAAO;UAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzC7B,OAAA;UAAA6B,QAAA,EAAG;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACpDjC,OAAA;UAAA6B,QAAA,GAAG,eAAa,EAACkB,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAACE,CAAC,CAAC,EAAC,UAAK,EAAC0C,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAACG,CAAC,CAAC,EAAC,UAAK,EAACyC,IAAI,CAACK,KAAK,CAACjD,QAAQ,CAACI,CAAC,CAAC,EAAC,MAAC;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjC,OAAA;MAAOqD,GAAG;MAAAxB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvJID,SAAS;AAAAqD,EAAA,GAATrD,SAAS;AAyJf,eAAeA,SAAS;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}