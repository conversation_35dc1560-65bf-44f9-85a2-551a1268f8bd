{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CheckoutPage = () => {\n  _s();\n  const {\n    videoId\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [\"Checkout Page for video \", videoId]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 10\n  }, this);\n};\n_s(CheckoutPage, \"FkmO0mWU/4wWDwRH5XyM5T2kQqE=\", false, function () {\n  return [useParams];\n});\n_c = CheckoutPage;\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "CheckoutPage", "_s", "videoId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\n\nexport const CheckoutPage: React.FC = () => {\n  const { videoId } = useParams();\n  return <div>Checkout Page for video {videoId}</div>;\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,OAAO,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAQ,CAAC,GAAGL,SAAS,CAAC,CAAC;EAC/B,oBAAOE,OAAA;IAAAI,QAAA,GAAK,0BAAwB,EAACD,OAAO;EAAA;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AACrD,CAAC;AAACN,EAAA,CAHWD,YAAsB;EAAA,QACbH,SAAS;AAAA;AAAAW,EAAA,GADlBR,YAAsB;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}