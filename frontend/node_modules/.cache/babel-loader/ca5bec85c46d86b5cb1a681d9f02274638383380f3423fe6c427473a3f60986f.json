{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx\",\n  _s = $RefreshSig$();\n// HyperOrangeTransition.tsx\nimport React, { useRef, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { gsap } from 'gsap';\nimport { MotionPathPlugin } from 'gsap/MotionPathPlugin';\nimport './HyperOrangeTransition.css';\n\n// Register the plugin\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ngsap.registerPlugin(MotionPathPlugin);\nconst HyperOrangeTransition = ({\n  isActive,\n  to,\n  onComplete,\n  buttonRect\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const ballRef = useRef(null);\n  const lineRef = useRef(null);\n  const starsRef = useRef([]);\n  const animation = useRef(null);\n  const originalOverflow = useRef(document.body.style.overflow);\n  const dropTrail = (originalLine, opacity) => {\n    if (!originalLine) return;\n    const trail = originalLine.cloneNode(true);\n    trail.classList.add('line-clone', 'line-trail');\n    trail.style.opacity = opacity.toString();\n    trail.style.position = 'fixed';\n    trail.style.left = originalLine.style.left;\n    trail.style.transform = originalLine.style.transform;\n    document.body.appendChild(trail);\n    gsap.to(trail, {\n      opacity: 0,\n      duration: 0.3,\n      delay: 0.2,\n      onComplete: () => {\n        if (trail.parentNode) {\n          trail.parentNode.removeChild(trail);\n        }\n      }\n    });\n  };\n  useEffect(() => {\n    if (isActive && buttonRect) {\n      originalOverflow.current = document.body.style.overflow;\n      document.body.style.overflow = 'hidden';\n      const startTransition = () => {\n        const backgroundImage = document.getElementById('background-image');\n        const contentSections = document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');\n        const mainContent = document.querySelector('main, .hero-section, .features-section');\n        gsap.to(contentSections, {\n          opacity: 0,\n          duration: 0.5,\n          ease: 'power2.out',\n          stagger: 0.05\n        });\n        if (mainContent) {\n          mainContent.style.transition = 'opacity 0.3s ease';\n          setTimeout(() => {\n            mainContent.style.opacity = '0.3';\n          }, 500);\n        }\n\n        // Create stars\n        const createStars = () => {\n          const stars = [];\n          const numStars = 8; // More stars since they're smaller\n\n          for (let i = 0; i < numStars; i++) {\n            const star = document.createElement('div');\n            star.className = 'transition-star';\n            star.style.cssText = `\n              position: fixed;\n              width: 3px;\n              height: 3px;\n              background: #ffffff;\n              border-radius: 50%;\n              opacity: 0 !important;\n              visibility: hidden;\n              z-index: 1002;\n              box-shadow: \n                0 0 4px 1px rgba(255, 255, 255, 0.9),\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\n            `;\n\n            // Completely random positioning across the screen\n            const x = Math.random() * window.innerWidth;\n            const y = Math.random() * window.innerHeight * 0.6; // Top 60% of screen\n\n            star.style.left = x + 'px';\n            star.style.top = y + 'px';\n            star.style.transform = 'translate(-50%, -50%)';\n            document.body.appendChild(star);\n            stars.push(star);\n            starsRef.current.push(star);\n\n            // Set GSAP initial state to ensure they're completely hidden\n            gsap.set(star, {\n              opacity: 0,\n              visibility: 'hidden',\n              scale: 0\n            });\n          }\n          return stars;\n        };\n        const stars = createStars();\n        const startX = buttonRect ? buttonRect.left + buttonRect.width / 2 : window.innerWidth / 2;\n        const startY = buttonRect ? buttonRect.top + buttonRect.height / 2 : window.innerHeight / 2;\n        const targetX = window.innerWidth / 2;\n        gsap.set(ballRef.current, {\n          x: startX,\n          y: startY,\n          xPercent: -50,\n          yPercent: -50,\n          position: 'fixed',\n          opacity: 1,\n          scale: 1\n        });\n        console.log('Ball starting position:', startX, startY);\n        console.log('Ball target position:', targetX, 15);\n        console.log('Ball element:', ballRef.current);\n\n        // Create firefly path with a graceful loop\n        const createFireflyPath = () => {\n          const pathWidth = window.innerWidth * 0.3; // 30% of screen width for the loop\n          const loopCenterX = startX + (targetX - startX) * 0.4; // Loop positioned 40% of the way\n          const loopCenterY = startY - window.innerHeight * 0.35;\n          const loopRadius = Math.min(pathWidth * 0.3, 120); // Reasonable loop size\n\n          return [{\n            x: startX,\n            y: startY\n          },\n          // Start at button\n          {\n            x: startX + 100,\n            y: startY - 150\n          },\n          // Rise up and slightly right\n          {\n            x: loopCenterX - loopRadius,\n            y: loopCenterY\n          },\n          // Approach loop from left\n          {\n            x: loopCenterX,\n            y: loopCenterY - loopRadius\n          },\n          // Top of loop\n          {\n            x: loopCenterX + loopRadius,\n            y: loopCenterY\n          },\n          // Right side of loop\n          {\n            x: loopCenterX,\n            y: loopCenterY + loopRadius\n          },\n          // Bottom of loop\n          {\n            x: loopCenterX - loopRadius * 0.5,\n            y: loopCenterY\n          },\n          // Exit loop\n          {\n            x: targetX - 100,\n            y: 15 + 100\n          },\n          // Approach final position\n          {\n            x: targetX,\n            y: 15\n          } // Final position at top center\n          ];\n        };\n        const fireflyPath = createFireflyPath();\n        console.log('Firefly path points:', fireflyPath);\n        gsap.set(lineRef.current, {\n          scaleY: 0,\n          transformOrigin: 'top center',\n          position: 'fixed',\n          left: window.innerWidth / 2,\n          top: 0,\n          transform: 'translateX(-50%) translateY(0%)',\n          // Start at absolute top\n          zIndex: 10000,\n          opacity: 1\n        });\n\n        // Create carousels container\n        // const createCarousels = () => {\n        //   const container = document.createElement('div');\n        //   container.className = 'carousels-container';\n        //   container.style.cssText = `\n        //     position: fixed;\n        //     top: 0;\n        //     left: 0;\n        //     width: 100%;\n        //     height: 100%;\n        //     display: flex;\n        //     justify-content: space-between;\n        //     padding: 20px;\n        //     opacity: 0;\n        //     z-index: 1000;\n        //   `;\n\n        //   // Create 8 carousels (one for each grade)\n        //   for (let i = 1; i <= 8; i++) {\n        //     const carousel = document.createElement('div');\n        //     carousel.className = `carousel grade-${i}`;\n        //     carousel.style.cssText = `\n        //       width: calc(100% / 8 - 10px);\n        //       height: 100%;\n        //       background: rgba(0, 0, 0, 0.2);\n        //       border-radius: 8px;\n        //       overflow: hidden;\n        //       position: relative;\n        //       opacity: 0;\n        //       transform: translateY(20px);\n        //     `;\n\n        //     // Add grade label at the top of each carousel\n        //     const label = document.createElement('div');\n        //     label.className = 'carousel-label';\n        //     label.textContent = `Grade ${i}`;\n        //     label.style.cssText = `\n        //       position: absolute;\n        //       top: 10px;\n        //       left: 0;\n        //       right: 0;\n        //       text-align: center;\n        //       color: white;\n        //       font-weight: bold;\n        //       text-shadow: 0 1px 3px rgba(0,0,0,0.5);\n        //       z-index: 2;\n        //       opacity: 0;\n        //     `;\n\n        //     // Add a placeholder for the video carousel content\n        //     const content = document.createElement('div');\n        //     content.className = 'carousel-content';\n        //     content.style.cssText = `\n        //       width: 100%;\n        //       height: 100%;\n        //       display: flex;\n        //       flex-direction: column;\n        //       align-items: center;\n        //       justify-content: center;\n        //       padding-top: 40px;\n        //       color: white;\n        //       font-size: 14px;\n        //     `;\n\n        //     // Add a placeholder for the video thumbnail\n        //     const thumbnail = document.createElement('div');\n        //     thumbnail.className = 'video-thumbnail';\n        //     thumbnail.style.cssText = `\n        //       width: 80%;\n        //       aspect-ratio: 16/9;\n        //       background: rgba(255,255,255,0.1);\n        //       margin: 10px 0;\n        //       border-radius: 4px;\n        //       display: flex;\n        //       align-items: center;\n        //       justify-content: center;\n        //       font-size: 12px;\n        //       color: rgba(255,255,255,0.7);\n        //     `;\n        //     thumbnail.textContent = 'Video Preview';\n\n        //     content.appendChild(thumbnail);\n        //     carousel.appendChild(label);\n        //     carousel.appendChild(content);\n        //     container.appendChild(carousel);\n        //   }\n\n        //   document.body.appendChild(container);\n        //   return container;\n        // };\n\n        // const carouselsContainer = createCarousels();\n        // const carousels = Array.from(carouselsContainer.querySelectorAll('.carousel'));\n\n        // Clean up on completion\n        animation.current = gsap.timeline({\n          onComplete: () => {\n            // Clean up any existing elements\n            const clones = document.querySelectorAll('.line-clone');\n            clones.forEach(clone => {\n              var _clone$parentNode;\n              return (_clone$parentNode = clone.parentNode) === null || _clone$parentNode === void 0 ? void 0 : _clone$parentNode.removeChild(clone);\n            });\n\n            // Clean up stars\n            starsRef.current.forEach(star => {\n              if (star.parentNode) {\n                star.parentNode.removeChild(star);\n              }\n            });\n            starsRef.current = [];\n\n            // Fade in carousels\n            //     gsap.to(carouselsContainer, {\n            //       opacity: 1,\n            //       duration: 0.5,\n            //       onComplete: () => {\n            //         // Animate in each carousel with a slight delay\n            //         carousels.forEach((carousel, index) => {\n            //           const label = carousel.querySelector('.carousel-label');\n\n            //           gsap.to(carousel, {\n            //             opacity: 1,\n            //             y: 0,\n            //             duration: 0.5,\n            //             delay: index * 0.05,\n            //             ease: 'power2.out',\n            //             onComplete: () => {\n            //               // Animate in the label after the carousel appears\n            //               if (label) {\n            //                 gsap.to(label, {\n            //                   opacity: 1,\n            //                   y: 0,\n            //                   duration: 0.3,\n            //                   ease: 'power2.out'\n            //                 });\n            //               }\n            //             }\n            //           });\n            //         });\n\n            //         // Navigate after the carousels have appeared\n            navigate(to);\n            //         onComplete?.();\n            //       }\n            //     });\n          }\n        });\n        animation.current\n        // Background movement - split into two parts: movement + scaling, then just movement\n        .to(backgroundImage, {\n          // First half: move from -65% to -37.5% AND scale from 1.0 to 0.7\n          keyframes: {\n            \"0%\": {\n              transform: \"translateY(-65%) scale(1.0)\"\n            },\n            \"50%\": {\n              transform: \"translateY(-37.5%) scale(0.68)\"\n            },\n            \"100%\": {\n              transform: \"translateY(-10%) scale(0.68)\"\n            } // Second half: just movement, no more scaling\n          },\n          duration: 3.5,\n          ease: 'power1.inOut',\n          transformOrigin: 'center center'\n        }, 0)\n        // Ball animation with elegant firefly path including loop\n        .to(ballRef.current, {\n          motionPath: {\n            path: fireflyPath,\n            curviness: 1.5,\n            // Smooth curves but not too dramatic\n            autoRotate: false\n          },\n          duration: 2.8,\n          // Slightly longer to accommodate the loop\n          ease: 'sine.inOut',\n          scale: 0.9,\n          onStart: function () {\n            console.log('Ball animation started with firefly loop path');\n          }\n        }, 0.3)\n        // Line appears when ball reaches near top\n        .to(lineRef.current, {\n          scaleY: 1,\n          duration: 1.2,\n          ease: 'power2.out'\n        }, 2.9) // Start line animation when ball is near top\n        // Line split - starts immediately after line finishes drawing (2.9 + 1.2 = 4.1)\n        .add(() => {\n          if (!lineRef.current) return;\n          const rightLine = lineRef.current.cloneNode(true);\n          rightLine.classList.add('line-clone');\n          rightLine.style.position = 'absolute';\n          rightLine.style.left = '50%';\n          rightLine.style.transform = 'translateX(-50%) translateY(0%)';\n          document.body.appendChild(rightLine);\n          gsap.to(lineRef.current, {\n            x: -window.innerWidth / 2,\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n            onUpdate: function () {\n              const progress = this.ratio;\n              if (progress > 0.25 && !this._dropped25 && lineRef.current) {\n                this._dropped25 = true;\n                dropTrail(lineRef.current, 0.75);\n              }\n              if (progress > 0.5 && !this._dropped50 && lineRef.current) {\n                this._dropped50 = true;\n                dropTrail(lineRef.current, 0.5);\n              }\n              if (progress > 0.75 && !this._dropped75 && lineRef.current) {\n                this._dropped75 = true;\n                dropTrail(lineRef.current, 0.25);\n              }\n            }\n          });\n          gsap.to(rightLine, {\n            x: window.innerWidth / 2,\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n            onUpdate: function () {\n              const progress = this.ratio;\n              if (progress > 0.25 && !this._dropped25) {\n                this._dropped25 = true;\n                dropTrail(rightLine, 0.75);\n              }\n              if (progress > 0.5 && !this._dropped50) {\n                this._dropped50 = true;\n                dropTrail(rightLine, 0.5);\n              }\n              if (progress > 0.75 && !this._dropped75) {\n                this._dropped75 = true;\n                dropTrail(rightLine, 0.25);\n              }\n            },\n            onComplete: () => {\n              if (rightLine.parentNode) {\n                rightLine.parentNode.removeChild(rightLine);\n              }\n            }\n          });\n        }, 4.1) // Exactly when line drawing finishes\n        // Stars appear towards the end of firefly animation\n        .to(stars, {\n          opacity: 1,\n          visibility: 'visible',\n          scale: 1,\n          duration: 0.4,\n          ease: 'back.out(1.7)',\n          stagger: 0.08,\n          onStart: function () {\n            console.log('Stars animation started');\n          },\n          onComplete: function () {\n            console.log('Stars animation completed');\n            // Add the CSS animation back after GSAP animation completes\n            stars.forEach(star => {\n              star.style.animation = 'starTwinkle 2s ease-in-out infinite alternate';\n            });\n          }\n        }, 2.6) // Near the end of firefly animation (firefly ends around 2.8-3.1)\n        // Flash effect - happens during line split\n        .to([ballRef.current, lineRef.current], {\n          opacity: 0.8,\n          duration: 0.1,\n          repeat: 6,\n          yoyo: true,\n          ease: 'power1.inOut'\n        }, 4.2)\n        // Final fade out\n        .to([ballRef.current, lineRef.current], {\n          opacity: 0,\n          duration: 0.6,\n          ease: 'power2.inOut'\n        }, 4.8);\n        return () => {\n          if (animation.current) animation.current.kill();\n          if (mainContent) {\n            mainContent.style.opacity = '';\n            mainContent.style.transition = '';\n          }\n          if (backgroundImage) {\n            backgroundImage.style.transform = 'translateY(-62%)'; // Reset to original\n          }\n          // Clean up stars\n          starsRef.current.forEach(star => {\n            if (star.parentNode) {\n              star.parentNode.removeChild(star);\n            }\n          });\n          starsRef.current = [];\n          document.body.style.overflow = originalOverflow.current;\n        };\n      };\n      const cleanup = startTransition();\n      return cleanup;\n    }\n  }, [isActive, buttonRect, navigate, onComplete, to]);\n  if (!isActive) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transition-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transition-ball\",\n      ref: ballRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"transition-line\",\n      ref: lineRef\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n};\n_s(HyperOrangeTransition, \"bqAl1l3jFirCHTo8i0UqPaYgS2g=\", false, function () {\n  return [useNavigate];\n});\n_c = HyperOrangeTransition;\nexport default HyperOrangeTransition;\nvar _c;\n$RefreshReg$(_c, \"HyperOrangeTransition\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useNavigate", "gsap", "MotionPathPlugin", "jsxDEV", "_jsxDEV", "registerPlugin", "HyperOrangeTransition", "isActive", "to", "onComplete", "buttonRect", "_s", "navigate", "ballRef", "lineRef", "starsRef", "animation", "originalOverflow", "document", "body", "style", "overflow", "dropTrail", "originalLine", "opacity", "trail", "cloneNode", "classList", "add", "toString", "position", "left", "transform", "append<PERSON><PERSON><PERSON>", "duration", "delay", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "current", "startTransition", "backgroundImage", "getElementById", "contentSections", "querySelectorAll", "mainContent", "querySelector", "ease", "stagger", "transition", "setTimeout", "createStars", "stars", "numStars", "i", "star", "createElement", "className", "cssText", "x", "Math", "random", "window", "innerWidth", "y", "innerHeight", "top", "push", "set", "visibility", "scale", "startX", "width", "startY", "height", "targetX", "xPercent", "yPercent", "console", "log", "createFireflyPath", "pathWidth", "loopCenterX", "loopCenterY", "loopRadius", "min", "fireflyPath", "scaleY", "transform<PERSON><PERSON>in", "zIndex", "timeline", "clones", "for<PERSON>ach", "clone", "_clone$parentNode", "keyframes", "motionPath", "path", "curviness", "autoRotate", "onStart", "rightLine", "onUpdate", "progress", "ratio", "_dropped25", "_dropped50", "_dropped75", "repeat", "yoyo", "kill", "cleanup", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx"], "sourcesContent": ["// HyperOrangeTransition.tsx\nimport React, { useRef, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { gsap } from 'gsap';\nimport { MotionPathPlugin } from 'gsap/MotionPathPlugin';\nimport './HyperOrangeTransition.css';\n\n// Register the plugin\ngsap.registerPlugin(MotionPathPlugin);\n\ninterface HyperOrangeTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete?: () => void;\n  buttonRect?: DOMRect | null;\n}\n\nconst HyperOrangeTransition: React.FC<HyperOrangeTransitionProps> = ({\n  isActive,\n  to,\n  onComplete,\n  buttonRect,\n}) => {\n  const navigate = useNavigate();\n  const ballRef = useRef<HTMLDivElement>(null);\n  const lineRef = useRef<HTMLDivElement>(null);\n  const starsRef = useRef<HTMLDivElement[]>([]);\n  const animation = useRef<gsap.core.Timeline | null>(null);\n  const originalOverflow = useRef<string>(document.body.style.overflow);\n\n  const dropTrail = (originalLine: HTMLElement, opacity: number) => {\n    if (!originalLine) return;\n    \n    const trail = originalLine.cloneNode(true) as HTMLElement;\n    trail.classList.add('line-clone', 'line-trail');\n    trail.style.opacity = opacity.toString();\n    trail.style.position = 'fixed';\n    trail.style.left = originalLine.style.left;\n    trail.style.transform = originalLine.style.transform;\n    document.body.appendChild(trail);\n    \n    gsap.to(trail, {\n      opacity: 0,\n      duration: 0.3,\n      delay: 0.2,\n      onComplete: () => {\n        if (trail.parentNode) {\n          trail.parentNode.removeChild(trail);\n        }\n      }\n    });\n  };\n\n  useEffect(() => {\n    if (isActive && buttonRect) {\n      originalOverflow.current = document.body.style.overflow;\n      document.body.style.overflow = 'hidden';\n\n      const startTransition = () => {\n        const backgroundImage = document.getElementById('background-image');\n        const contentSections = document.querySelectorAll('header, .compliance-banner, .hero-section, .features-section, footer');\n        const mainContent = document.querySelector('main, .hero-section, .features-section');\n\n        gsap.to(contentSections, {\n          opacity: 0,\n          duration: 0.5,\n          ease: 'power2.out',\n          stagger: 0.05\n        });\n\n        if (mainContent) {\n          (mainContent as HTMLElement).style.transition = 'opacity 0.3s ease';\n          setTimeout(() => {\n            (mainContent as HTMLElement).style.opacity = '0.3';\n          }, 500);\n        }\n\n        // Create stars\n        const createStars = () => {\n          const stars = [];\n          const numStars = 8; // More stars since they're smaller\n          \n          for (let i = 0; i < numStars; i++) {\n            const star = document.createElement('div');\n            star.className = 'transition-star';\n            star.style.cssText = `\n              position: fixed;\n              width: 3px;\n              height: 3px;\n              background: #ffffff;\n              border-radius: 50%;\n              opacity: 0 !important;\n              visibility: hidden;\n              z-index: 1002;\n              box-shadow: \n                0 0 4px 1px rgba(255, 255, 255, 0.9),\n                0 0 8px 2px rgba(255, 255, 255, 0.5);\n            `;\n            \n            // Completely random positioning across the screen\n            const x = Math.random() * window.innerWidth;\n            const y = Math.random() * window.innerHeight * 0.6; // Top 60% of screen\n            \n            star.style.left = x + 'px';\n            star.style.top = y + 'px';\n            star.style.transform = 'translate(-50%, -50%)';\n            \n            document.body.appendChild(star);\n            stars.push(star);\n            starsRef.current.push(star);\n            \n            // Set GSAP initial state to ensure they're completely hidden\n            gsap.set(star, {\n              opacity: 0,\n              visibility: 'hidden',\n              scale: 0\n            });\n          }\n          \n          return stars;\n        };\n\n        const stars = createStars();\n\n        const startX = buttonRect ? buttonRect.left + (buttonRect.width / 2) : window.innerWidth / 2;\n        const startY = buttonRect ? buttonRect.top + (buttonRect.height / 2) : window.innerHeight / 2;\n        const targetX = window.innerWidth / 2;\n\n        gsap.set(ballRef.current, {\n          x: startX,\n          y: startY,\n          xPercent: -50,\n          yPercent: -50,\n          position: 'fixed',\n          opacity: 1,\n          scale: 1\n        });\n\n        console.log('Ball starting position:', startX, startY);\n        console.log('Ball target position:', targetX, 15);\n        console.log('Ball element:', ballRef.current);\n\n        // Create firefly path with a graceful loop\n        const createFireflyPath = () => {\n          const pathWidth = window.innerWidth * 0.3; // 30% of screen width for the loop\n          const loopCenterX = startX + (targetX - startX) * 0.4; // Loop positioned 40% of the way\n          const loopCenterY = startY - window.innerHeight * 0.35;\n          const loopRadius = Math.min(pathWidth * 0.3, 120); // Reasonable loop size\n          \n          return [\n            { x: startX, y: startY }, // Start at button\n            { x: startX + 100, y: startY - 150 }, // Rise up and slightly right\n            { x: loopCenterX - loopRadius, y: loopCenterY }, // Approach loop from left\n            { x: loopCenterX, y: loopCenterY - loopRadius }, // Top of loop\n            { x: loopCenterX + loopRadius, y: loopCenterY }, // Right side of loop\n            { x: loopCenterX, y: loopCenterY + loopRadius }, // Bottom of loop\n            { x: loopCenterX - loopRadius * 0.5, y: loopCenterY }, // Exit loop\n            { x: targetX - 100, y: 15 + 100 }, // Approach final position\n            { x: targetX, y: 15 } // Final position at top center\n          ];\n        };\n\n        const fireflyPath = createFireflyPath();\n        console.log('Firefly path points:', fireflyPath);\n\n        gsap.set(lineRef.current, {\n          scaleY: 0,\n          transformOrigin: 'top center',\n          position: 'fixed',\n          left: window.innerWidth / 2,\n          top: 0,\n          transform: 'translateX(-50%) translateY(0%)', // Start at absolute top\n          zIndex: 10000,\n          opacity: 1\n        });\n\n        // Create carousels container\n        // const createCarousels = () => {\n        //   const container = document.createElement('div');\n        //   container.className = 'carousels-container';\n        //   container.style.cssText = `\n        //     position: fixed;\n        //     top: 0;\n        //     left: 0;\n        //     width: 100%;\n        //     height: 100%;\n        //     display: flex;\n        //     justify-content: space-between;\n        //     padding: 20px;\n        //     opacity: 0;\n        //     z-index: 1000;\n        //   `;\n          \n        //   // Create 8 carousels (one for each grade)\n        //   for (let i = 1; i <= 8; i++) {\n        //     const carousel = document.createElement('div');\n        //     carousel.className = `carousel grade-${i}`;\n        //     carousel.style.cssText = `\n        //       width: calc(100% / 8 - 10px);\n        //       height: 100%;\n        //       background: rgba(0, 0, 0, 0.2);\n        //       border-radius: 8px;\n        //       overflow: hidden;\n        //       position: relative;\n        //       opacity: 0;\n        //       transform: translateY(20px);\n        //     `;\n            \n        //     // Add grade label at the top of each carousel\n        //     const label = document.createElement('div');\n        //     label.className = 'carousel-label';\n        //     label.textContent = `Grade ${i}`;\n        //     label.style.cssText = `\n        //       position: absolute;\n        //       top: 10px;\n        //       left: 0;\n        //       right: 0;\n        //       text-align: center;\n        //       color: white;\n        //       font-weight: bold;\n        //       text-shadow: 0 1px 3px rgba(0,0,0,0.5);\n        //       z-index: 2;\n        //       opacity: 0;\n        //     `;\n            \n        //     // Add a placeholder for the video carousel content\n        //     const content = document.createElement('div');\n        //     content.className = 'carousel-content';\n        //     content.style.cssText = `\n        //       width: 100%;\n        //       height: 100%;\n        //       display: flex;\n        //       flex-direction: column;\n        //       align-items: center;\n        //       justify-content: center;\n        //       padding-top: 40px;\n        //       color: white;\n        //       font-size: 14px;\n        //     `;\n            \n        //     // Add a placeholder for the video thumbnail\n        //     const thumbnail = document.createElement('div');\n        //     thumbnail.className = 'video-thumbnail';\n        //     thumbnail.style.cssText = `\n        //       width: 80%;\n        //       aspect-ratio: 16/9;\n        //       background: rgba(255,255,255,0.1);\n        //       margin: 10px 0;\n        //       border-radius: 4px;\n        //       display: flex;\n        //       align-items: center;\n        //       justify-content: center;\n        //       font-size: 12px;\n        //       color: rgba(255,255,255,0.7);\n        //     `;\n        //     thumbnail.textContent = 'Video Preview';\n            \n        //     content.appendChild(thumbnail);\n        //     carousel.appendChild(label);\n        //     carousel.appendChild(content);\n        //     container.appendChild(carousel);\n        //   }\n          \n        //   document.body.appendChild(container);\n        //   return container;\n        // };\n\n        // const carouselsContainer = createCarousels();\n        // const carousels = Array.from(carouselsContainer.querySelectorAll('.carousel'));\n\n        // Clean up on completion\n        animation.current = gsap.timeline({\n          onComplete: () => {\n            // Clean up any existing elements\n            const clones = document.querySelectorAll('.line-clone');\n            clones.forEach(clone => clone.parentNode?.removeChild(clone));\n            \n            // Clean up stars\n            starsRef.current.forEach(star => {\n              if (star.parentNode) {\n                star.parentNode.removeChild(star);\n              }\n            });\n            starsRef.current = [];\n            \n            // Fade in carousels\n        //     gsap.to(carouselsContainer, {\n        //       opacity: 1,\n        //       duration: 0.5,\n        //       onComplete: () => {\n        //         // Animate in each carousel with a slight delay\n        //         carousels.forEach((carousel, index) => {\n        //           const label = carousel.querySelector('.carousel-label');\n                  \n        //           gsap.to(carousel, {\n        //             opacity: 1,\n        //             y: 0,\n        //             duration: 0.5,\n        //             delay: index * 0.05,\n        //             ease: 'power2.out',\n        //             onComplete: () => {\n        //               // Animate in the label after the carousel appears\n        //               if (label) {\n        //                 gsap.to(label, {\n        //                   opacity: 1,\n        //                   y: 0,\n        //                   duration: 0.3,\n        //                   ease: 'power2.out'\n        //                 });\n        //               }\n        //             }\n        //           });\n        //         });\n                \n        //         // Navigate after the carousels have appeared\n                 navigate(to);\n        //         onComplete?.();\n        //       }\n        //     });\n          }\n       });\n\n        animation.current\n          // Background movement - split into two parts: movement + scaling, then just movement\n          .to(backgroundImage, {\n            // First half: move from -65% to -37.5% AND scale from 1.0 to 0.7\n            keyframes: {\n              \"0%\": { transform: \"translateY(-65%) scale(1.0)\" },\n              \"50%\": { transform: \"translateY(-37.5%) scale(0.68)\" },\n              \"100%\": { transform: \"translateY(-10%) scale(0.68)\" } // Second half: just movement, no more scaling\n            },\n            duration: 3.5,\n            ease: 'power1.inOut',\n            transformOrigin: 'center center',\n          }, 0)\n          // Ball animation with elegant firefly path including loop\n          .to(ballRef.current, {\n            motionPath: {\n              path: fireflyPath,\n              curviness: 1.5, // Smooth curves but not too dramatic\n              autoRotate: false\n            },\n            duration: 2.8, // Slightly longer to accommodate the loop\n            ease: 'sine.inOut',\n            scale: 0.9,\n            onStart: function() {\n              console.log('Ball animation started with firefly loop path');\n            }\n          }, 0.3)\n          // Line appears when ball reaches near top\n          .to(lineRef.current, {\n            scaleY: 1,\n            duration: 1.2,\n            ease: 'power2.out',\n          }, 2.9) // Start line animation when ball is near top\n          // Line split - starts immediately after line finishes drawing (2.9 + 1.2 = 4.1)\n          .add(() => {\n            if (!lineRef.current) return;\n            \n            const rightLine = lineRef.current.cloneNode(true) as HTMLElement;\n            rightLine.classList.add('line-clone');\n            rightLine.style.position = 'absolute';\n            rightLine.style.left = '50%';\n            rightLine.style.transform = 'translateX(-50%) translateY(0%)';\n            document.body.appendChild(rightLine);\n\n            gsap.to(lineRef.current, {\n              x: -window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onUpdate: function() {\n                const progress = this.ratio; \n                if (progress > 0.25 && !this._dropped25 && lineRef.current) {\n                  this._dropped25 = true;\n                  dropTrail(lineRef.current, 0.75);\n                }\n                if (progress > 0.5 && !this._dropped50 && lineRef.current) {\n                  this._dropped50 = true;\n                  dropTrail(lineRef.current, 0.5);\n                }\n                if (progress > 0.75 && !this._dropped75 && lineRef.current) {\n                  this._dropped75 = true;\n                  dropTrail(lineRef.current, 0.25);\n                }\n              }\n            });\n\n            gsap.to(rightLine, {\n              x: window.innerWidth / 2,\n              opacity: 0,\n              duration: 0.6,\n              ease: 'power2.inOut',\n              onUpdate: function() {\n                const progress = this.ratio;\n                if (progress > 0.25 && !this._dropped25) {\n                  this._dropped25 = true;\n                  dropTrail(rightLine, 0.75);\n                }\n                if (progress > 0.5 && !this._dropped50) {\n                  this._dropped50 = true;\n                  dropTrail(rightLine, 0.5);\n                }\n                if (progress > 0.75 && !this._dropped75) {\n                  this._dropped75 = true;\n                  dropTrail(rightLine, 0.25);\n                }\n              },\n              onComplete: () => {\n                if (rightLine.parentNode) {\n                  rightLine.parentNode.removeChild(rightLine);\n                }\n              }\n            });\n          }, 4.1) // Exactly when line drawing finishes\n          // Stars appear towards the end of firefly animation\n          .to(stars, {\n            opacity: 1,\n            visibility: 'visible',\n            scale: 1,\n            duration: 0.4,\n            ease: 'back.out(1.7)',\n            stagger: 0.08,\n            onStart: function() {\n              console.log('Stars animation started');\n            },\n            onComplete: function() {\n              console.log('Stars animation completed');\n              // Add the CSS animation back after GSAP animation completes\n              stars.forEach(star => {\n                star.style.animation = 'starTwinkle 2s ease-in-out infinite alternate';\n              });\n            }\n          }, 2.6) // Near the end of firefly animation (firefly ends around 2.8-3.1)\n          // Flash effect - happens during line split\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0.8,\n            duration: 0.1,\n            repeat: 6,\n            yoyo: true,\n            ease: 'power1.inOut',\n          }, 4.2)\n          // Final fade out\n          .to([ballRef.current, lineRef.current], {\n            opacity: 0,\n            duration: 0.6,\n            ease: 'power2.inOut',\n          }, 4.8);\n\n        return () => {\n          if (animation.current) animation.current.kill();\n          if (mainContent) {\n            (mainContent as HTMLElement).style.opacity = '';\n            (mainContent as HTMLElement).style.transition = '';\n          }\n          if (backgroundImage) {\n            backgroundImage.style.transform = 'translateY(-62%)'; // Reset to original\n          }\n          // Clean up stars\n          starsRef.current.forEach(star => {\n            if (star.parentNode) {\n              star.parentNode.removeChild(star);\n            }\n          });\n          starsRef.current = [];\n          document.body.style.overflow = originalOverflow.current;\n        };\n      };\n\n      const cleanup = startTransition();\n      return cleanup;\n    }\n  }, [isActive, buttonRect, navigate, onComplete, to]);\n\n  if (!isActive) return null;\n\n  return (\n    <div className=\"transition-container\">\n      <div className=\"transition-ball\" ref={ballRef} />\n      <div className=\"transition-line\" ref={lineRef} />\n    </div>\n  );\n};\n\nexport default HyperOrangeTransition;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,6BAA6B;;AAEpC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAH,IAAI,CAACI,cAAc,CAACH,gBAAgB,CAAC;AASrC,MAAMI,qBAA2D,GAAGA,CAAC;EACnEC,QAAQ;EACRC,EAAE;EACFC,UAAU;EACVC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,OAAO,GAAGf,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMgB,OAAO,GAAGhB,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMiB,QAAQ,GAAGjB,MAAM,CAAmB,EAAE,CAAC;EAC7C,MAAMkB,SAAS,GAAGlB,MAAM,CAA4B,IAAI,CAAC;EACzD,MAAMmB,gBAAgB,GAAGnB,MAAM,CAASoB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC;EAErE,MAAMC,SAAS,GAAGA,CAACC,YAAyB,EAAEC,OAAe,KAAK;IAChE,IAAI,CAACD,YAAY,EAAE;IAEnB,MAAME,KAAK,GAAGF,YAAY,CAACG,SAAS,CAAC,IAAI,CAAgB;IACzDD,KAAK,CAACE,SAAS,CAACC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC;IAC/CH,KAAK,CAACL,KAAK,CAACI,OAAO,GAAGA,OAAO,CAACK,QAAQ,CAAC,CAAC;IACxCJ,KAAK,CAACL,KAAK,CAACU,QAAQ,GAAG,OAAO;IAC9BL,KAAK,CAACL,KAAK,CAACW,IAAI,GAAGR,YAAY,CAACH,KAAK,CAACW,IAAI;IAC1CN,KAAK,CAACL,KAAK,CAACY,SAAS,GAAGT,YAAY,CAACH,KAAK,CAACY,SAAS;IACpDd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACR,KAAK,CAAC;IAEhCxB,IAAI,CAACO,EAAE,CAACiB,KAAK,EAAE;MACbD,OAAO,EAAE,CAAC;MACVU,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE,GAAG;MACV1B,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAIgB,KAAK,CAACW,UAAU,EAAE;UACpBX,KAAK,CAACW,UAAU,CAACC,WAAW,CAACZ,KAAK,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED1B,SAAS,CAAC,MAAM;IACd,IAAIQ,QAAQ,IAAIG,UAAU,EAAE;MAC1BO,gBAAgB,CAACqB,OAAO,GAAGpB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ;MACvDH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;MAEvC,MAAMkB,eAAe,GAAGA,CAAA,KAAM;QAC5B,MAAMC,eAAe,GAAGtB,QAAQ,CAACuB,cAAc,CAAC,kBAAkB,CAAC;QACnE,MAAMC,eAAe,GAAGxB,QAAQ,CAACyB,gBAAgB,CAAC,sEAAsE,CAAC;QACzH,MAAMC,WAAW,GAAG1B,QAAQ,CAAC2B,aAAa,CAAC,wCAAwC,CAAC;QAEpF5C,IAAI,CAACO,EAAE,CAACkC,eAAe,EAAE;UACvBlB,OAAO,EAAE,CAAC;UACVU,QAAQ,EAAE,GAAG;UACbY,IAAI,EAAE,YAAY;UAClBC,OAAO,EAAE;QACX,CAAC,CAAC;QAEF,IAAIH,WAAW,EAAE;UACdA,WAAW,CAAiBxB,KAAK,CAAC4B,UAAU,GAAG,mBAAmB;UACnEC,UAAU,CAAC,MAAM;YACdL,WAAW,CAAiBxB,KAAK,CAACI,OAAO,GAAG,KAAK;UACpD,CAAC,EAAE,GAAG,CAAC;QACT;;QAEA;QACA,MAAM0B,WAAW,GAAGA,CAAA,KAAM;UACxB,MAAMC,KAAK,GAAG,EAAE;UAChB,MAAMC,QAAQ,GAAG,CAAC,CAAC,CAAC;;UAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;YACjC,MAAMC,IAAI,GAAGpC,QAAQ,CAACqC,aAAa,CAAC,KAAK,CAAC;YAC1CD,IAAI,CAACE,SAAS,GAAG,iBAAiB;YAClCF,IAAI,CAAClC,KAAK,CAACqC,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;;YAED;YACA,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGC,MAAM,CAACC,UAAU;YAC3C,MAAMC,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGC,MAAM,CAACG,WAAW,GAAG,GAAG,CAAC,CAAC;;YAEpDV,IAAI,CAAClC,KAAK,CAACW,IAAI,GAAG2B,CAAC,GAAG,IAAI;YAC1BJ,IAAI,CAAClC,KAAK,CAAC6C,GAAG,GAAGF,CAAC,GAAG,IAAI;YACzBT,IAAI,CAAClC,KAAK,CAACY,SAAS,GAAG,uBAAuB;YAE9Cd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACqB,IAAI,CAAC;YAC/BH,KAAK,CAACe,IAAI,CAACZ,IAAI,CAAC;YAChBvC,QAAQ,CAACuB,OAAO,CAAC4B,IAAI,CAACZ,IAAI,CAAC;;YAE3B;YACArD,IAAI,CAACkE,GAAG,CAACb,IAAI,EAAE;cACb9B,OAAO,EAAE,CAAC;cACV4C,UAAU,EAAE,QAAQ;cACpBC,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;UAEA,OAAOlB,KAAK;QACd,CAAC;QAED,MAAMA,KAAK,GAAGD,WAAW,CAAC,CAAC;QAE3B,MAAMoB,MAAM,GAAG5D,UAAU,GAAGA,UAAU,CAACqB,IAAI,GAAIrB,UAAU,CAAC6D,KAAK,GAAG,CAAE,GAAGV,MAAM,CAACC,UAAU,GAAG,CAAC;QAC5F,MAAMU,MAAM,GAAG9D,UAAU,GAAGA,UAAU,CAACuD,GAAG,GAAIvD,UAAU,CAAC+D,MAAM,GAAG,CAAE,GAAGZ,MAAM,CAACG,WAAW,GAAG,CAAC;QAC7F,MAAMU,OAAO,GAAGb,MAAM,CAACC,UAAU,GAAG,CAAC;QAErC7D,IAAI,CAACkE,GAAG,CAACtD,OAAO,CAACyB,OAAO,EAAE;UACxBoB,CAAC,EAAEY,MAAM;UACTP,CAAC,EAAES,MAAM;UACTG,QAAQ,EAAE,CAAC,EAAE;UACbC,QAAQ,EAAE,CAAC,EAAE;UACb9C,QAAQ,EAAE,OAAO;UACjBN,OAAO,EAAE,CAAC;UACV6C,KAAK,EAAE;QACT,CAAC,CAAC;QAEFQ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,MAAM,EAAEE,MAAM,CAAC;QACtDK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,OAAO,EAAE,EAAE,CAAC;QACjDG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEjE,OAAO,CAACyB,OAAO,CAAC;;QAE7C;QACA,MAAMyC,iBAAiB,GAAGA,CAAA,KAAM;UAC9B,MAAMC,SAAS,GAAGnB,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC,CAAC;UAC3C,MAAMmB,WAAW,GAAGX,MAAM,GAAG,CAACI,OAAO,GAAGJ,MAAM,IAAI,GAAG,CAAC,CAAC;UACvD,MAAMY,WAAW,GAAGV,MAAM,GAAGX,MAAM,CAACG,WAAW,GAAG,IAAI;UACtD,MAAMmB,UAAU,GAAGxB,IAAI,CAACyB,GAAG,CAACJ,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;UAEnD,OAAO,CACL;YAAEtB,CAAC,EAAEY,MAAM;YAAEP,CAAC,EAAES;UAAO,CAAC;UAAE;UAC1B;YAAEd,CAAC,EAAEY,MAAM,GAAG,GAAG;YAAEP,CAAC,EAAES,MAAM,GAAG;UAAI,CAAC;UAAE;UACtC;YAAEd,CAAC,EAAEuB,WAAW,GAAGE,UAAU;YAAEpB,CAAC,EAAEmB;UAAY,CAAC;UAAE;UACjD;YAAExB,CAAC,EAAEuB,WAAW;YAAElB,CAAC,EAAEmB,WAAW,GAAGC;UAAW,CAAC;UAAE;UACjD;YAAEzB,CAAC,EAAEuB,WAAW,GAAGE,UAAU;YAAEpB,CAAC,EAAEmB;UAAY,CAAC;UAAE;UACjD;YAAExB,CAAC,EAAEuB,WAAW;YAAElB,CAAC,EAAEmB,WAAW,GAAGC;UAAW,CAAC;UAAE;UACjD;YAAEzB,CAAC,EAAEuB,WAAW,GAAGE,UAAU,GAAG,GAAG;YAAEpB,CAAC,EAAEmB;UAAY,CAAC;UAAE;UACvD;YAAExB,CAAC,EAAEgB,OAAO,GAAG,GAAG;YAAEX,CAAC,EAAE,EAAE,GAAG;UAAI,CAAC;UAAE;UACnC;YAAEL,CAAC,EAAEgB,OAAO;YAAEX,CAAC,EAAE;UAAG,CAAC,CAAC;UAAA,CACvB;QACH,CAAC;QAED,MAAMsB,WAAW,GAAGN,iBAAiB,CAAC,CAAC;QACvCF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,WAAW,CAAC;QAEhDpF,IAAI,CAACkE,GAAG,CAACrD,OAAO,CAACwB,OAAO,EAAE;UACxBgD,MAAM,EAAE,CAAC;UACTC,eAAe,EAAE,YAAY;UAC7BzD,QAAQ,EAAE,OAAO;UACjBC,IAAI,EAAE8B,MAAM,CAACC,UAAU,GAAG,CAAC;UAC3BG,GAAG,EAAE,CAAC;UACNjC,SAAS,EAAE,iCAAiC;UAAE;UAC9CwD,MAAM,EAAE,KAAK;UACbhE,OAAO,EAAE;QACX,CAAC,CAAC;;QAEF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;;QAEA;QACAR,SAAS,CAACsB,OAAO,GAAGrC,IAAI,CAACwF,QAAQ,CAAC;UAChChF,UAAU,EAAEA,CAAA,KAAM;YAChB;YACA,MAAMiF,MAAM,GAAGxE,QAAQ,CAACyB,gBAAgB,CAAC,aAAa,CAAC;YACvD+C,MAAM,CAACC,OAAO,CAACC,KAAK;cAAA,IAAAC,iBAAA;cAAA,QAAAA,iBAAA,GAAID,KAAK,CAACxD,UAAU,cAAAyD,iBAAA,uBAAhBA,iBAAA,CAAkBxD,WAAW,CAACuD,KAAK,CAAC;YAAA,EAAC;;YAE7D;YACA7E,QAAQ,CAACuB,OAAO,CAACqD,OAAO,CAACrC,IAAI,IAAI;cAC/B,IAAIA,IAAI,CAAClB,UAAU,EAAE;gBACnBkB,IAAI,CAAClB,UAAU,CAACC,WAAW,CAACiB,IAAI,CAAC;cACnC;YACF,CAAC,CAAC;YACFvC,QAAQ,CAACuB,OAAO,GAAG,EAAE;;YAErB;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACS1B,QAAQ,CAACJ,EAAE,CAAC;YACrB;YACA;YACA;UACE;QACH,CAAC,CAAC;QAEDQ,SAAS,CAACsB;QACR;QAAA,CACC9B,EAAE,CAACgC,eAAe,EAAE;UACnB;UACAsD,SAAS,EAAE;YACT,IAAI,EAAE;cAAE9D,SAAS,EAAE;YAA8B,CAAC;YAClD,KAAK,EAAE;cAAEA,SAAS,EAAE;YAAiC,CAAC;YACtD,MAAM,EAAE;cAAEA,SAAS,EAAE;YAA+B,CAAC,CAAC;UACxD,CAAC;UACDE,QAAQ,EAAE,GAAG;UACbY,IAAI,EAAE,cAAc;UACpByC,eAAe,EAAE;QACnB,CAAC,EAAE,CAAC;QACJ;QAAA,CACC/E,EAAE,CAACK,OAAO,CAACyB,OAAO,EAAE;UACnByD,UAAU,EAAE;YACVC,IAAI,EAAEX,WAAW;YACjBY,SAAS,EAAE,GAAG;YAAE;YAChBC,UAAU,EAAE;UACd,CAAC;UACDhE,QAAQ,EAAE,GAAG;UAAE;UACfY,IAAI,EAAE,YAAY;UAClBuB,KAAK,EAAE,GAAG;UACV8B,OAAO,EAAE,SAAAA,CAAA,EAAW;YAClBtB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC9D;QACF,CAAC,EAAE,GAAG;QACN;QAAA,CACCtE,EAAE,CAACM,OAAO,CAACwB,OAAO,EAAE;UACnBgD,MAAM,EAAE,CAAC;UACTpD,QAAQ,EAAE,GAAG;UACbY,IAAI,EAAE;QACR,CAAC,EAAE,GAAG,CAAC,CAAC;QACR;QAAA,CACClB,GAAG,CAAC,MAAM;UACT,IAAI,CAACd,OAAO,CAACwB,OAAO,EAAE;UAEtB,MAAM8D,SAAS,GAAGtF,OAAO,CAACwB,OAAO,CAACZ,SAAS,CAAC,IAAI,CAAgB;UAChE0E,SAAS,CAACzE,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;UACrCwE,SAAS,CAAChF,KAAK,CAACU,QAAQ,GAAG,UAAU;UACrCsE,SAAS,CAAChF,KAAK,CAACW,IAAI,GAAG,KAAK;UAC5BqE,SAAS,CAAChF,KAAK,CAACY,SAAS,GAAG,iCAAiC;UAC7Dd,QAAQ,CAACC,IAAI,CAACc,WAAW,CAACmE,SAAS,CAAC;UAEpCnG,IAAI,CAACO,EAAE,CAACM,OAAO,CAACwB,OAAO,EAAE;YACvBoB,CAAC,EAAE,CAACG,MAAM,CAACC,UAAU,GAAG,CAAC;YACzBtC,OAAO,EAAE,CAAC;YACVU,QAAQ,EAAE,GAAG;YACbY,IAAI,EAAE,cAAc;YACpBuD,QAAQ,EAAE,SAAAA,CAAA,EAAW;cACnB,MAAMC,QAAQ,GAAG,IAAI,CAACC,KAAK;cAC3B,IAAID,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACE,UAAU,IAAI1F,OAAO,CAACwB,OAAO,EAAE;gBAC1D,IAAI,CAACkE,UAAU,GAAG,IAAI;gBACtBlF,SAAS,CAACR,OAAO,CAACwB,OAAO,EAAE,IAAI,CAAC;cAClC;cACA,IAAIgE,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI,CAACG,UAAU,IAAI3F,OAAO,CAACwB,OAAO,EAAE;gBACzD,IAAI,CAACmE,UAAU,GAAG,IAAI;gBACtBnF,SAAS,CAACR,OAAO,CAACwB,OAAO,EAAE,GAAG,CAAC;cACjC;cACA,IAAIgE,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACI,UAAU,IAAI5F,OAAO,CAACwB,OAAO,EAAE;gBAC1D,IAAI,CAACoE,UAAU,GAAG,IAAI;gBACtBpF,SAAS,CAACR,OAAO,CAACwB,OAAO,EAAE,IAAI,CAAC;cAClC;YACF;UACF,CAAC,CAAC;UAEFrC,IAAI,CAACO,EAAE,CAAC4F,SAAS,EAAE;YACjB1C,CAAC,EAAEG,MAAM,CAACC,UAAU,GAAG,CAAC;YACxBtC,OAAO,EAAE,CAAC;YACVU,QAAQ,EAAE,GAAG;YACbY,IAAI,EAAE,cAAc;YACpBuD,QAAQ,EAAE,SAAAA,CAAA,EAAW;cACnB,MAAMC,QAAQ,GAAG,IAAI,CAACC,KAAK;cAC3B,IAAID,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACE,UAAU,EAAE;gBACvC,IAAI,CAACA,UAAU,GAAG,IAAI;gBACtBlF,SAAS,CAAC8E,SAAS,EAAE,IAAI,CAAC;cAC5B;cACA,IAAIE,QAAQ,GAAG,GAAG,IAAI,CAAC,IAAI,CAACG,UAAU,EAAE;gBACtC,IAAI,CAACA,UAAU,GAAG,IAAI;gBACtBnF,SAAS,CAAC8E,SAAS,EAAE,GAAG,CAAC;cAC3B;cACA,IAAIE,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE;gBACvC,IAAI,CAACA,UAAU,GAAG,IAAI;gBACtBpF,SAAS,CAAC8E,SAAS,EAAE,IAAI,CAAC;cAC5B;YACF,CAAC;YACD3F,UAAU,EAAEA,CAAA,KAAM;cAChB,IAAI2F,SAAS,CAAChE,UAAU,EAAE;gBACxBgE,SAAS,CAAChE,UAAU,CAACC,WAAW,CAAC+D,SAAS,CAAC;cAC7C;YACF;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC,CAAC;QACR;QAAA,CACC5F,EAAE,CAAC2C,KAAK,EAAE;UACT3B,OAAO,EAAE,CAAC;UACV4C,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,CAAC;UACRnC,QAAQ,EAAE,GAAG;UACbY,IAAI,EAAE,eAAe;UACrBC,OAAO,EAAE,IAAI;UACboD,OAAO,EAAE,SAAAA,CAAA,EAAW;YAClBtB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;UACxC,CAAC;UACDrE,UAAU,EAAE,SAAAA,CAAA,EAAW;YACrBoE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;YACxC;YACA3B,KAAK,CAACwC,OAAO,CAACrC,IAAI,IAAI;cACpBA,IAAI,CAAClC,KAAK,CAACJ,SAAS,GAAG,+CAA+C;YACxE,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,GAAG,CAAC,CAAC;QACR;QAAA,CACCR,EAAE,CAAC,CAACK,OAAO,CAACyB,OAAO,EAAExB,OAAO,CAACwB,OAAO,CAAC,EAAE;UACtCd,OAAO,EAAE,GAAG;UACZU,QAAQ,EAAE,GAAG;UACbyE,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,IAAI;UACV9D,IAAI,EAAE;QACR,CAAC,EAAE,GAAG;QACN;QAAA,CACCtC,EAAE,CAAC,CAACK,OAAO,CAACyB,OAAO,EAAExB,OAAO,CAACwB,OAAO,CAAC,EAAE;UACtCd,OAAO,EAAE,CAAC;UACVU,QAAQ,EAAE,GAAG;UACbY,IAAI,EAAE;QACR,CAAC,EAAE,GAAG,CAAC;QAET,OAAO,MAAM;UACX,IAAI9B,SAAS,CAACsB,OAAO,EAAEtB,SAAS,CAACsB,OAAO,CAACuE,IAAI,CAAC,CAAC;UAC/C,IAAIjE,WAAW,EAAE;YACdA,WAAW,CAAiBxB,KAAK,CAACI,OAAO,GAAG,EAAE;YAC9CoB,WAAW,CAAiBxB,KAAK,CAAC4B,UAAU,GAAG,EAAE;UACpD;UACA,IAAIR,eAAe,EAAE;YACnBA,eAAe,CAACpB,KAAK,CAACY,SAAS,GAAG,kBAAkB,CAAC,CAAC;UACxD;UACA;UACAjB,QAAQ,CAACuB,OAAO,CAACqD,OAAO,CAACrC,IAAI,IAAI;YAC/B,IAAIA,IAAI,CAAClB,UAAU,EAAE;cACnBkB,IAAI,CAAClB,UAAU,CAACC,WAAW,CAACiB,IAAI,CAAC;YACnC;UACF,CAAC,CAAC;UACFvC,QAAQ,CAACuB,OAAO,GAAG,EAAE;UACrBpB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAGJ,gBAAgB,CAACqB,OAAO;QACzD,CAAC;MACH,CAAC;MAED,MAAMwE,OAAO,GAAGvE,eAAe,CAAC,CAAC;MACjC,OAAOuE,OAAO;IAChB;EACF,CAAC,EAAE,CAACvG,QAAQ,EAAEG,UAAU,EAAEE,QAAQ,EAAEH,UAAU,EAAED,EAAE,CAAC,CAAC;EAEpD,IAAI,CAACD,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEH,OAAA;IAAKoD,SAAS,EAAC,sBAAsB;IAAAuD,QAAA,gBACnC3G,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAACwD,GAAG,EAAEnG;IAAQ;MAAAoG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjDhH,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAACwD,GAAG,EAAElG;IAAQ;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC;AAEV,CAAC;AAACzG,EAAA,CAjdIL,qBAA2D;EAAA,QAM9CN,WAAW;AAAA;AAAAqH,EAAA,GANxB/G,qBAA2D;AAmdjE,eAAeA,qBAAqB;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}