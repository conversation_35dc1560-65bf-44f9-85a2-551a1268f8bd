{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gyroscope = () => {\n  _s();\n  const [rotation, setRotation] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n  const animationRef = useRef();\n\n  // Auto-rotation with momentum\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: prev.x + momentum.x + 0.2,\n          y: prev.y + momentum.y + 0.3,\n          z: prev.z + momentum.z + 0.1\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.98,\n          y: prev.y * 0.98,\n          z: prev.z * 0.98\n        }));\n      }\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.5;\n    setRotation(prev => ({\n      x: prev.x + deltaY * rotationSpeed,\n      y: prev.y + deltaX * rotationSpeed,\n      z: prev.z + (deltaX + deltaY) * 0.1\n    }));\n    setMomentum({\n      x: deltaY * 0.1,\n      y: deltaX * 0.1,\n      z: (deltaX + deltaY) * 0.05\n    });\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseUp = () => setIsDragging(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center min-h-full bg-white p-4\",\n    style: {\n      filter: 'none'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-96 h-96 cursor-grab active:cursor-grabbing select-none\",\n      onMouseDown: handleMouseDown,\n      onMouseMove: handleMouseMove,\n      onMouseUp: handleMouseUp,\n      onMouseLeave: handleMouseUp,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full border-4 border-black shadow-glow animate-pulse-glow\",\n        style: {\n          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n          transition: isDragging ? 'none' : 'transform 0.1s ease-out'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-8 rounded-full border-4 border-gray-700 shadow-ring\",\n          style: {\n            transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-8 rounded-full border-4 border-gray-500 shadow-glow\",\n            style: {\n              transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-8 rounded-full bg-gradient-to-b from-black to-gray-300 shadow-ring animate-float\",\n              style: {\n                transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-1/2 left-1/2 w-4 h-4 -mt-2 -ml-2 rounded-full bg-white animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), [...Array(10)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute w-2 h-2 bg-yellow-300 rounded-full animate-pixie-dust\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 2}s`,\n          animationDuration: `${1 + Math.random() * 2}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -inset-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 left-0 right-0 h-0.5 bg-red-800/50\",\n          style: {\n            transform: `rotateZ(${rotation.x}deg)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 bottom-0 left-1/2 w-0.5 bg-green-800/50\",\n          style: {\n            transform: `rotateZ(${rotation.y}deg)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-1/2 left-1/2 w-24 h-0.5 -ml-12 bg-blue-800/50\",\n          style: {\n            transform: `rotateZ(${rotation.z}deg)`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(Gyroscope, \"3U1YGKIof7c/7aZU2wk84FEABVc=\");\n_c = Gyroscope;\nexport default Gyroscope;\nvar _c;\n$RefreshReg$(_c, \"Gyroscope\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Gyroscope", "_s", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "momentum", "setMomentum", "lastMousePos", "animationRef", "animate", "prev", "current", "requestAnimationFrame", "cancelAnimationFrame", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "deltaX", "deltaY", "rotationSpeed", "handleMouseUp", "className", "style", "filter", "children", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "transform", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "left", "Math", "random", "top", "animationDelay", "animationDuration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\nconst Gyroscope = () => {\n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const lastMousePos = useRef({ x: 0, y: 0 });\n  const animationRef = useRef<number>();\n\n  // Auto-rotation with momentum\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: prev.x + momentum.x + 0.2,\n          y: prev.y + momentum.y + 0.3,\n          z: prev.z + momentum.z + 0.1\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.98,\n          y: prev.y * 0.98,\n          z: prev.z * 0.98\n        }));\n      }\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.5;\n    setRotation(prev => ({\n      x: prev.x + deltaY * rotationSpeed,\n      y: prev.y + deltaX * rotationSpeed,\n      z: prev.z + (deltaX + deltaY) * 0.1\n    }));\n    setMomentum({\n      x: deltaY * 0.1,\n      y: deltaX * 0.1,\n      z: (deltaX + deltaY) * 0.05\n    });\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseUp = () => setIsDragging(false);\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-full bg-white p-4\" style={{ filter: 'none' }}>\n      <div \n        className=\"relative w-96 h-96 cursor-grab active:cursor-grabbing select-none\"\n        onMouseDown={handleMouseDown}\n        onMouseMove={handleMouseMove}\n        onMouseUp={handleMouseUp}\n        onMouseLeave={handleMouseUp}\n      >\n        {/* Outer Ring */}\n        <div \n          className=\"absolute inset-0 rounded-full border-4 border-black shadow-glow animate-pulse-glow\"\n          style={{\n            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n            transition: isDragging ? 'none' : 'transform 0.1s ease-out'\n          }}\n        >\n          {/* Middle Ring */}\n          <div \n            className=\"absolute inset-8 rounded-full border-4 border-gray-700 shadow-ring\"\n            style={{\n              transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n            }}\n          >\n            {/* Inner Ring */}\n            <div \n              className=\"absolute inset-8 rounded-full border-4 border-gray-500 shadow-glow\"\n              style={{\n                transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n              }}\n            >\n              {/* Core */}\n              <div \n                className=\"absolute inset-8 rounded-full bg-gradient-to-b from-black to-gray-300 shadow-ring animate-float\"\n                style={{\n                  transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                }}\n              >\n                {/* Center Dot */}\n                <div className=\"absolute top-1/2 left-1/2 w-4 h-4 -mt-2 -ml-2 rounded-full bg-white animate-pulse\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Particle Effects for Pixie Dust */}\n        {[...Array(10)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-2 h-2 bg-yellow-300 rounded-full animate-pixie-dust\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 2}s`,\n              animationDuration: `${1 + Math.random() * 2}s`\n            }}\n          />\n        ))}\n\n        {/* Rotation Axes Indicators */}\n        <div className=\"absolute -inset-12\">\n          <div className=\"absolute top-1/2 left-0 right-0 h-0.5 bg-red-800/50\" style={{ transform: `rotateZ(${rotation.x}deg)` }} />\n          <div className=\"absolute top-0 bottom-0 left-1/2 w-0.5 bg-green-800/50\" style={{ transform: `rotateZ(${rotation.y}deg)` }} />\n          <div className=\"absolute top-1/2 left-1/2 w-24 h-0.5 -ml-12 bg-blue-800/50\" style={{ transform: `rotateZ(${rotation.z}deg)` }} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Gyroscope;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAMK,YAAY,GAAGd,MAAM,CAAC;IAAEO,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC3C,MAAMO,YAAY,GAAGf,MAAM,CAAS,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMiB,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACN,UAAU,EAAE;QACfJ,WAAW,CAACW,IAAI,KAAK;UACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAGK,QAAQ,CAACL,CAAC,GAAG,GAAG;UAC5BC,CAAC,EAAES,IAAI,CAACT,CAAC,GAAGI,QAAQ,CAACJ,CAAC,GAAG,GAAG;UAC5BC,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAGG,QAAQ,CAACH,CAAC,GAAG;QAC3B,CAAC,CAAC,CAAC;QACHI,WAAW,CAACI,IAAI,KAAK;UACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAES,IAAI,CAACT,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAG;QACd,CAAC,CAAC,CAAC;MACL;MACAM,YAAY,CAACG,OAAO,GAAGC,qBAAqB,CAACH,OAAO,CAAC;IACvD,CAAC;IACDD,YAAY,CAACG,OAAO,GAAGC,qBAAqB,CAACH,OAAO,CAAC;IACrD,OAAO,MAAM;MACX,IAAID,YAAY,CAACG,OAAO,EAAEE,oBAAoB,CAACL,YAAY,CAACG,OAAO,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAACR,UAAU,EAAEE,QAAQ,CAAC,CAAC;EAE1B,MAAMS,eAAe,GAAIC,CAAmB,IAAK;IAC/CX,aAAa,CAAC,IAAI,CAAC;IACnBG,YAAY,CAACI,OAAO,GAAG;MAAEX,CAAC,EAAEe,CAAC,CAACC,OAAO;MAAEf,CAAC,EAAEc,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAmB,IAAK;IAC/C,IAAI,CAACZ,UAAU,EAAE;IACjB,MAAMgB,MAAM,GAAGJ,CAAC,CAACC,OAAO,GAAGT,YAAY,CAACI,OAAO,CAACX,CAAC;IACjD,MAAMoB,MAAM,GAAGL,CAAC,CAACE,OAAO,GAAGV,YAAY,CAACI,OAAO,CAACV,CAAC;IACjD,MAAMoB,aAAa,GAAG,GAAG;IACzBtB,WAAW,CAACW,IAAI,KAAK;MACnBV,CAAC,EAAEU,IAAI,CAACV,CAAC,GAAGoB,MAAM,GAAGC,aAAa;MAClCpB,CAAC,EAAES,IAAI,CAACT,CAAC,GAAGkB,MAAM,GAAGE,aAAa;MAClCnB,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAG,CAACiB,MAAM,GAAGC,MAAM,IAAI;IAClC,CAAC,CAAC,CAAC;IACHd,WAAW,CAAC;MACVN,CAAC,EAAEoB,MAAM,GAAG,GAAG;MACfnB,CAAC,EAAEkB,MAAM,GAAG,GAAG;MACfjB,CAAC,EAAE,CAACiB,MAAM,GAAGC,MAAM,IAAI;IACzB,CAAC,CAAC;IACFb,YAAY,CAACI,OAAO,GAAG;MAAEX,CAAC,EAAEe,CAAC,CAACC,OAAO;MAAEf,CAAC,EAAEc,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAMlB,aAAa,CAAC,KAAK,CAAC;EAEhD,oBACET,OAAA;IAAK4B,SAAS,EAAC,mEAAmE;IAACC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,eAC3G/B,OAAA;MACE4B,SAAS,EAAC,mEAAmE;MAC7EI,WAAW,EAAEb,eAAgB;MAC7Bc,WAAW,EAAEV,eAAgB;MAC7BW,SAAS,EAAEP,aAAc;MACzBQ,YAAY,EAAER,aAAc;MAAAI,QAAA,gBAG5B/B,OAAA;QACE4B,SAAS,EAAC,oFAAoF;QAC9FC,KAAK,EAAE;UACLO,SAAS,EAAE,WAAWjC,QAAQ,CAACE,CAAC,gBAAgBF,QAAQ,CAACG,CAAC,gBAAgBH,QAAQ,CAACI,CAAC,MAAM;UAC1F8B,UAAU,EAAE7B,UAAU,GAAG,MAAM,GAAG;QACpC,CAAE;QAAAuB,QAAA,eAGF/B,OAAA;UACE4B,SAAS,EAAC,oEAAoE;UAC9EC,KAAK,EAAE;YACLO,SAAS,EAAE,WAAWjC,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,CAAC;UACvG,CAAE;UAAAwB,QAAA,eAGF/B,OAAA;YACE4B,SAAS,EAAC,oEAAoE;YAC9EC,KAAK,EAAE;cACLO,SAAS,EAAE,WAAWjC,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,GAAG;YACxG,CAAE;YAAAwB,QAAA,eAGF/B,OAAA;cACE4B,SAAS,EAAC,iGAAiG;cAC3GC,KAAK,EAAE;gBACLO,SAAS,EAAE,WAAWjC,QAAQ,CAACE,CAAC,GAAG,CAAC,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,CAAC,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC;cACxG,CAAE;cAAAwB,QAAA,eAGF/B,OAAA;gBAAK4B,SAAS,EAAC;cAAmF;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB7C,OAAA;QAEE4B,SAAS,EAAC,gEAAgE;QAC1EC,KAAK,EAAE;UACLiB,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GAPGH,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQP,CACF,CAAC,eAGFzC,OAAA;QAAK4B,SAAS,EAAC,oBAAoB;QAAAG,QAAA,gBACjC/B,OAAA;UAAK4B,SAAS,EAAC,qDAAqD;UAACC,KAAK,EAAE;YAAEO,SAAS,EAAE,WAAWjC,QAAQ,CAACE,CAAC;UAAO;QAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1HzC,OAAA;UAAK4B,SAAS,EAAC,wDAAwD;UAACC,KAAK,EAAE;YAAEO,SAAS,EAAE,WAAWjC,QAAQ,CAACG,CAAC;UAAO;QAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7HzC,OAAA;UAAK4B,SAAS,EAAC,4DAA4D;UAACC,KAAK,EAAE;YAAEO,SAAS,EAAE,WAAWjC,QAAQ,CAACI,CAAC;UAAO;QAAE;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9H,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA3HID,SAAS;AAAAmD,EAAA,GAATnD,SAAS;AA6Hf,eAAeA,SAAS;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}