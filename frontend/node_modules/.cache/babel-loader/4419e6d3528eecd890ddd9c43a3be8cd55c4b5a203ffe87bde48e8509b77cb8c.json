{"ast": null, "code": "import * as React from 'react'; // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n// Matches logic in React's `shared/ExecutionEnvironment` file\n\nexport const canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\nexport const useIsomorphicLayoutEffect = canUseDOM ? React.useLayoutEffect : React.useEffect;", "map": {"version": 3, "names": ["React", "canUseDOM", "window", "document", "createElement", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/utils/useIsomorphicLayoutEffect.js"], "sourcesContent": ["import * as React from 'react'; // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n// Matches logic in React's `shared/ExecutionEnvironment` file\n\nexport const canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\nexport const useIsomorphicLayoutEffect = canUseDOM ? React.useLayoutEffect : React.useEffect;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW,IAAI,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,KAAK,WAAW,CAAC;AAC5J,OAAO,MAAMC,yBAAyB,GAAGJ,SAAS,GAAGD,KAAK,CAACM,eAAe,GAAGN,KAAK,CAACO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}