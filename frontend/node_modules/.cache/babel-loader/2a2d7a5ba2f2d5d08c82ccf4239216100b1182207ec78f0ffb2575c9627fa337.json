{"ast": null, "code": "/*!\n * CSSPlugin 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { gsap, _getProperty, _numExp, _numWithUnitExp, getUnit, _isString, _isUndefined, _renderComplexString, _relExp, _forEachName, _sortPropTweensByPriority, _colorStringFilter, _checkPlugin, _replaceRandom, _plugins, GSCache, PropTween, _config, _ticker, _round, _missingPlugin, _getSetter, _getCache, _colorExp, _parseRelative, _setDefaults, _removeLinkedListItem //for the commented-out className feature.\n} from \"./gsap-core.js\";\nvar _win,\n  _doc,\n  _docElement,\n  _pluginInitted,\n  _tempDiv,\n  _tempD<PERSON><PERSON><PERSON><PERSON>,\n  _recentSetter<PERSON>lugin,\n  _reverting,\n  _windowExists = function _windowExists() {\n    return typeof window !== \"undefined\";\n  },\n  _transformProps = {},\n  _RAD2DEG = 180 / Math.PI,\n  _DEG2RAD = Math.PI / 180,\n  _atan2 = Math.atan2,\n  _bigNum = 1e8,\n  _capsExp = /([A-Z])/g,\n  _horizontalExp = /(left|right|width|margin|padding|x)/i,\n  _complexExp = /[\\s,\\(]\\S/,\n  _propertyAliases = {\n    autoAlpha: \"opacity,visibility\",\n    scale: \"scaleX,scaleY\",\n    alpha: \"opacity\"\n  },\n  _renderCSSProp = function _renderCSSProp(ratio, data) {\n    return data.set(data.t, data.p, Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n  },\n  _renderPropWithEnd = function _renderPropWithEnd(ratio, data) {\n    return data.set(data.t, data.p, ratio === 1 ? data.e : Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n  },\n  _renderCSSPropWithBeginning = function _renderCSSPropWithBeginning(ratio, data) {\n    return data.set(data.t, data.p, ratio ? Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u : data.b, data);\n  },\n  //if units change, we need a way to render the original unit/value when the tween goes all the way back to the beginning (ratio:0)\n  _renderRoundedCSSProp = function _renderRoundedCSSProp(ratio, data) {\n    var value = data.s + data.c * ratio;\n    data.set(data.t, data.p, ~~(value + (value < 0 ? -.5 : .5)) + data.u, data);\n  },\n  _renderNonTweeningValue = function _renderNonTweeningValue(ratio, data) {\n    return data.set(data.t, data.p, ratio ? data.e : data.b, data);\n  },\n  _renderNonTweeningValueOnlyAtEnd = function _renderNonTweeningValueOnlyAtEnd(ratio, data) {\n    return data.set(data.t, data.p, ratio !== 1 ? data.b : data.e, data);\n  },\n  _setterCSSStyle = function _setterCSSStyle(target, property, value) {\n    return target.style[property] = value;\n  },\n  _setterCSSProp = function _setterCSSProp(target, property, value) {\n    return target.style.setProperty(property, value);\n  },\n  _setterTransform = function _setterTransform(target, property, value) {\n    return target._gsap[property] = value;\n  },\n  _setterScale = function _setterScale(target, property, value) {\n    return target._gsap.scaleX = target._gsap.scaleY = value;\n  },\n  _setterScaleWithRender = function _setterScaleWithRender(target, property, value, data, ratio) {\n    var cache = target._gsap;\n    cache.scaleX = cache.scaleY = value;\n    cache.renderTransform(ratio, cache);\n  },\n  _setterTransformWithRender = function _setterTransformWithRender(target, property, value, data, ratio) {\n    var cache = target._gsap;\n    cache[property] = value;\n    cache.renderTransform(ratio, cache);\n  },\n  _transformProp = \"transform\",\n  _transformOriginProp = _transformProp + \"Origin\",\n  _saveStyle = function _saveStyle(property, isNotCSS) {\n    var _this = this;\n    var target = this.target,\n      style = target.style,\n      cache = target._gsap;\n    if (property in _transformProps && style) {\n      this.tfm = this.tfm || {};\n      if (property !== \"transform\") {\n        property = _propertyAliases[property] || property;\n        ~property.indexOf(\",\") ? property.split(\",\").forEach(function (a) {\n          return _this.tfm[a] = _get(target, a);\n        }) : this.tfm[property] = cache.x ? cache[property] : _get(target, property); // note: scale would map to \"scaleX,scaleY\", thus we loop and apply them both.\n\n        property === _transformOriginProp && (this.tfm.zOrigin = cache.zOrigin);\n      } else {\n        return _propertyAliases.transform.split(\",\").forEach(function (p) {\n          return _saveStyle.call(_this, p, isNotCSS);\n        });\n      }\n      if (this.props.indexOf(_transformProp) >= 0) {\n        return;\n      }\n      if (cache.svg) {\n        this.svgo = target.getAttribute(\"data-svg-origin\");\n        this.props.push(_transformOriginProp, isNotCSS, \"\");\n      }\n      property = _transformProp;\n    }\n    (style || isNotCSS) && this.props.push(property, isNotCSS, style[property]);\n  },\n  _removeIndependentTransforms = function _removeIndependentTransforms(style) {\n    if (style.translate) {\n      style.removeProperty(\"translate\");\n      style.removeProperty(\"scale\");\n      style.removeProperty(\"rotate\");\n    }\n  },\n  _revertStyle = function _revertStyle() {\n    var props = this.props,\n      target = this.target,\n      style = target.style,\n      cache = target._gsap,\n      i,\n      p;\n    for (i = 0; i < props.length; i += 3) {\n      // stored like this: property, isNotCSS, value\n      if (!props[i + 1]) {\n        props[i + 2] ? style[props[i]] = props[i + 2] : style.removeProperty(props[i].substr(0, 2) === \"--\" ? props[i] : props[i].replace(_capsExp, \"-$1\").toLowerCase());\n      } else if (props[i + 1] === 2) {\n        // non-CSS value (function-based)\n        target[props[i]](props[i + 2]);\n      } else {\n        // non-CSS value (not function-based)\n        target[props[i]] = props[i + 2];\n      }\n    }\n    if (this.tfm) {\n      for (p in this.tfm) {\n        cache[p] = this.tfm[p];\n      }\n      if (cache.svg) {\n        cache.renderTransform();\n        target.setAttribute(\"data-svg-origin\", this.svgo || \"\");\n      }\n      i = _reverting();\n      if ((!i || !i.isStart) && !style[_transformProp]) {\n        _removeIndependentTransforms(style);\n        if (cache.zOrigin && style[_transformOriginProp]) {\n          style[_transformOriginProp] += \" \" + cache.zOrigin + \"px\"; // since we're uncaching, we must put the zOrigin back into the transformOrigin so that we can pull it out accurately when we parse again. Otherwise, we'd lose the z portion of the origin since we extract it to protect from Safari bugs.\n\n          cache.zOrigin = 0;\n          cache.renderTransform();\n        }\n        cache.uncache = 1; // if it's a startAt that's being reverted in the _initTween() of the core, we don't need to uncache transforms. This is purely a performance optimization.\n      }\n    }\n  },\n  _getStyleSaver = function _getStyleSaver(target, properties) {\n    var saver = {\n      target: target,\n      props: [],\n      revert: _revertStyle,\n      save: _saveStyle\n    };\n    target._gsap || gsap.core.getCache(target); // just make sure there's a _gsap cache defined because we read from it in _saveStyle() and it's more efficient to just check it here once.\n\n    properties && target.style && target.nodeType && properties.split(\",\").forEach(function (p) {\n      return saver.save(p);\n    }); // make sure it's a DOM node too.\n\n    return saver;\n  },\n  _supports3D,\n  _createElement = function _createElement(type, ns) {\n    var e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\n    return e && e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://gsap.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n  },\n  _getComputedProperty = function _getComputedProperty(target, property, skipPrefixFallback) {\n    var cs = getComputedStyle(target);\n    return cs[property] || cs.getPropertyValue(property.replace(_capsExp, \"-$1\").toLowerCase()) || cs.getPropertyValue(property) || !skipPrefixFallback && _getComputedProperty(target, _checkPropPrefix(property) || property, 1) || \"\"; //css variables may not need caps swapped out for dashes and lowercase.\n  },\n  _prefixes = \"O,Moz,ms,Ms,Webkit\".split(\",\"),\n  _checkPropPrefix = function _checkPropPrefix(property, element, preferPrefix) {\n    var e = element || _tempDiv,\n      s = e.style,\n      i = 5;\n    if (property in s && !preferPrefix) {\n      return property;\n    }\n    property = property.charAt(0).toUpperCase() + property.substr(1);\n    while (i-- && !(_prefixes[i] + property in s)) {}\n    return i < 0 ? null : (i === 3 ? \"ms\" : i >= 0 ? _prefixes[i] : \"\") + property;\n  },\n  _initCore = function _initCore() {\n    if (_windowExists() && window.document) {\n      _win = window;\n      _doc = _win.document;\n      _docElement = _doc.documentElement;\n      _tempDiv = _createElement(\"div\") || {\n        style: {}\n      };\n      _tempDivStyler = _createElement(\"div\");\n      _transformProp = _checkPropPrefix(_transformProp);\n      _transformOriginProp = _transformProp + \"Origin\";\n      _tempDiv.style.cssText = \"border-width:0;line-height:0;position:absolute;padding:0\"; //make sure to override certain properties that may contaminate measurements, in case the user has overreaching style sheets.\n\n      _supports3D = !!_checkPropPrefix(\"perspective\");\n      _reverting = gsap.core.reverting;\n      _pluginInitted = 1;\n    }\n  },\n  _getReparentedCloneBBox = function _getReparentedCloneBBox(target) {\n    //works around issues in some browsers (like Firefox) that don't correctly report getBBox() on SVG elements inside a <defs> element and/or <mask>. We try creating an SVG, adding it to the documentElement and toss the element in there so that it's definitely part of the rendering tree, then grab the bbox and if it works, we actually swap out the original getBBox() method for our own that does these extra steps whenever getBBox is needed. This helps ensure that performance is optimal (only do all these extra steps when absolutely necessary...most elements don't need it).\n    var owner = target.ownerSVGElement,\n      svg = _createElement(\"svg\", owner && owner.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\"),\n      clone = target.cloneNode(true),\n      bbox;\n    clone.style.display = \"block\";\n    svg.appendChild(clone);\n    _docElement.appendChild(svg);\n    try {\n      bbox = clone.getBBox();\n    } catch (e) {}\n    svg.removeChild(clone);\n    _docElement.removeChild(svg);\n    return bbox;\n  },\n  _getAttributeFallbacks = function _getAttributeFallbacks(target, attributesArray) {\n    var i = attributesArray.length;\n    while (i--) {\n      if (target.hasAttribute(attributesArray[i])) {\n        return target.getAttribute(attributesArray[i]);\n      }\n    }\n  },\n  _getBBox = function _getBBox(target) {\n    var bounds, cloned;\n    try {\n      bounds = target.getBBox(); //Firefox throws errors if you try calling getBBox() on an SVG element that's not rendered (like in a <symbol> or <defs>). https://bugzilla.mozilla.org/show_bug.cgi?id=612118\n    } catch (error) {\n      bounds = _getReparentedCloneBBox(target);\n      cloned = 1;\n    }\n    bounds && (bounds.width || bounds.height) || cloned || (bounds = _getReparentedCloneBBox(target)); //some browsers (like Firefox) misreport the bounds if the element has zero width and height (it just assumes it's at x:0, y:0), thus we need to manually grab the position in that case.\n\n    return bounds && !bounds.width && !bounds.x && !bounds.y ? {\n      x: +_getAttributeFallbacks(target, [\"x\", \"cx\", \"x1\"]) || 0,\n      y: +_getAttributeFallbacks(target, [\"y\", \"cy\", \"y1\"]) || 0,\n      width: 0,\n      height: 0\n    } : bounds;\n  },\n  _isSVG = function _isSVG(e) {\n    return !!(e.getCTM && (!e.parentNode || e.ownerSVGElement) && _getBBox(e));\n  },\n  //reports if the element is an SVG on which getBBox() actually works\n  _removeProperty = function _removeProperty(target, property) {\n    if (property) {\n      var style = target.style,\n        first2Chars;\n      if (property in _transformProps && property !== _transformOriginProp) {\n        property = _transformProp;\n      }\n      if (style.removeProperty) {\n        first2Chars = property.substr(0, 2);\n        if (first2Chars === \"ms\" || property.substr(0, 6) === \"webkit\") {\n          //Microsoft and some Webkit browsers don't conform to the standard of capitalizing the first prefix character, so we adjust so that when we prefix the caps with a dash, it's correct (otherwise it'd be \"ms-transform\" instead of \"-ms-transform\" for IE9, for example)\n          property = \"-\" + property;\n        }\n        style.removeProperty(first2Chars === \"--\" ? property : property.replace(_capsExp, \"-$1\").toLowerCase());\n      } else {\n        //note: old versions of IE use \"removeAttribute()\" instead of \"removeProperty()\"\n        style.removeAttribute(property);\n      }\n    }\n  },\n  _addNonTweeningPT = function _addNonTweeningPT(plugin, target, property, beginning, end, onlySetAtEnd) {\n    var pt = new PropTween(plugin._pt, target, property, 0, 1, onlySetAtEnd ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue);\n    plugin._pt = pt;\n    pt.b = beginning;\n    pt.e = end;\n    plugin._props.push(property);\n    return pt;\n  },\n  _nonConvertibleUnits = {\n    deg: 1,\n    rad: 1,\n    turn: 1\n  },\n  _nonStandardLayouts = {\n    grid: 1,\n    flex: 1\n  },\n  //takes a single value like 20px and converts it to the unit specified, like \"%\", returning only the numeric amount.\n  _convertToUnit = function _convertToUnit(target, property, value, unit) {\n    var curValue = parseFloat(value) || 0,\n      curUnit = (value + \"\").trim().substr((curValue + \"\").length) || \"px\",\n      // some browsers leave extra whitespace at the beginning of CSS variables, hence the need to trim()\n      style = _tempDiv.style,\n      horizontal = _horizontalExp.test(property),\n      isRootSVG = target.tagName.toLowerCase() === \"svg\",\n      measureProperty = (isRootSVG ? \"client\" : \"offset\") + (horizontal ? \"Width\" : \"Height\"),\n      amount = 100,\n      toPixels = unit === \"px\",\n      toPercent = unit === \"%\",\n      px,\n      parent,\n      cache,\n      isSVG;\n    if (unit === curUnit || !curValue || _nonConvertibleUnits[unit] || _nonConvertibleUnits[curUnit]) {\n      return curValue;\n    }\n    curUnit !== \"px\" && !toPixels && (curValue = _convertToUnit(target, property, value, \"px\"));\n    isSVG = target.getCTM && _isSVG(target);\n    if ((toPercent || curUnit === \"%\") && (_transformProps[property] || ~property.indexOf(\"adius\"))) {\n      px = isSVG ? target.getBBox()[horizontal ? \"width\" : \"height\"] : target[measureProperty];\n      return _round(toPercent ? curValue / px * amount : curValue / 100 * px);\n    }\n    style[horizontal ? \"width\" : \"height\"] = amount + (toPixels ? curUnit : unit);\n    parent = unit !== \"rem\" && ~property.indexOf(\"adius\") || unit === \"em\" && target.appendChild && !isRootSVG ? target : target.parentNode;\n    if (isSVG) {\n      parent = (target.ownerSVGElement || {}).parentNode;\n    }\n    if (!parent || parent === _doc || !parent.appendChild) {\n      parent = _doc.body;\n    }\n    cache = parent._gsap;\n    if (cache && toPercent && cache.width && horizontal && cache.time === _ticker.time && !cache.uncache) {\n      return _round(curValue / cache.width * amount);\n    } else {\n      if (toPercent && (property === \"height\" || property === \"width\")) {\n        // if we're dealing with width/height that's inside a container with padding and/or it's a flexbox/grid container, we must apply it to the target itself rather than the _tempDiv in order to ensure complete accuracy, factoring in the parent's padding.\n        var v = target.style[property];\n        target.style[property] = amount + unit;\n        px = target[measureProperty];\n        v ? target.style[property] = v : _removeProperty(target, property);\n      } else {\n        (toPercent || curUnit === \"%\") && !_nonStandardLayouts[_getComputedProperty(parent, \"display\")] && (style.position = _getComputedProperty(target, \"position\"));\n        parent === target && (style.position = \"static\"); // like for borderRadius, if it's a % we must have it relative to the target itself but that may not have position: relative or position: absolute in which case it'd go up the chain until it finds its offsetParent (bad). position: static protects against that.\n\n        parent.appendChild(_tempDiv);\n        px = _tempDiv[measureProperty];\n        parent.removeChild(_tempDiv);\n        style.position = \"absolute\";\n      }\n      if (horizontal && toPercent) {\n        cache = _getCache(parent);\n        cache.time = _ticker.time;\n        cache.width = parent[measureProperty];\n      }\n    }\n    return _round(toPixels ? px * curValue / amount : px && curValue ? amount / px * curValue : 0);\n  },\n  _get = function _get(target, property, unit, uncache) {\n    var value;\n    _pluginInitted || _initCore();\n    if (property in _propertyAliases && property !== \"transform\") {\n      property = _propertyAliases[property];\n      if (~property.indexOf(\",\")) {\n        property = property.split(\",\")[0];\n      }\n    }\n    if (_transformProps[property] && property !== \"transform\") {\n      value = _parseTransform(target, uncache);\n      value = property !== \"transformOrigin\" ? value[property] : value.svg ? value.origin : _firstTwoOnly(_getComputedProperty(target, _transformOriginProp)) + \" \" + value.zOrigin + \"px\";\n    } else {\n      value = target.style[property];\n      if (!value || value === \"auto\" || uncache || ~(value + \"\").indexOf(\"calc(\")) {\n        value = _specialProps[property] && _specialProps[property](target, property, unit) || _getComputedProperty(target, property) || _getProperty(target, property) || (property === \"opacity\" ? 1 : 0); // note: some browsers, like Firefox, don't report borderRadius correctly! Instead, it only reports every corner like  borderTopLeftRadius\n      }\n    }\n    return unit && !~(value + \"\").trim().indexOf(\" \") ? _convertToUnit(target, property, value, unit) + unit : value;\n  },\n  _tweenComplexCSSString = function _tweenComplexCSSString(target, prop, start, end) {\n    // note: we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within a plugin too, thus \"this\" would refer to the plugin.\n    if (!start || start === \"none\") {\n      // some browsers like Safari actually PREFER the prefixed property and mis-report the unprefixed value like clipPath (BUG). In other words, even though clipPath exists in the style (\"clipPath\" in target.style) and it's set in the CSS properly (along with -webkit-clip-path), Safari reports clipPath as \"none\" whereas WebkitClipPath reports accurately like \"ellipse(100% 0% at 50% 0%)\", so in this case we must SWITCH to using the prefixed property instead. See https://gsap.com/forums/topic/18310-clippath-doesnt-work-on-ios/\n      var p = _checkPropPrefix(prop, target, 1),\n        s = p && _getComputedProperty(target, p, 1);\n      if (s && s !== start) {\n        prop = p;\n        start = s;\n      } else if (prop === \"borderColor\") {\n        start = _getComputedProperty(target, \"borderTopColor\"); // Firefox bug: always reports \"borderColor\" as \"\", so we must fall back to borderTopColor. See https://gsap.com/forums/topic/24583-how-to-return-colors-that-i-had-after-reverse/\n      }\n    }\n    var pt = new PropTween(this._pt, target.style, prop, 0, 1, _renderComplexString),\n      index = 0,\n      matchIndex = 0,\n      a,\n      result,\n      startValues,\n      startNum,\n      color,\n      startValue,\n      endValue,\n      endNum,\n      chunk,\n      endUnit,\n      startUnit,\n      endValues;\n    pt.b = start;\n    pt.e = end;\n    start += \"\"; // ensure values are strings\n\n    end += \"\";\n    if (end.substring(0, 6) === \"var(--\") {\n      end = _getComputedProperty(target, end.substring(4, end.indexOf(\")\")));\n    }\n    if (end === \"auto\") {\n      startValue = target.style[prop];\n      target.style[prop] = end;\n      end = _getComputedProperty(target, prop) || end;\n      startValue ? target.style[prop] = startValue : _removeProperty(target, prop);\n    }\n    a = [start, end];\n    _colorStringFilter(a); // pass an array with the starting and ending values and let the filter do whatever it needs to the values. If colors are found, it returns true and then we must match where the color shows up order-wise because for things like boxShadow, sometimes the browser provides the computed values with the color FIRST, but the user provides it with the color LAST, so flip them if necessary. Same for drop-shadow().\n\n    start = a[0];\n    end = a[1];\n    startValues = start.match(_numWithUnitExp) || [];\n    endValues = end.match(_numWithUnitExp) || [];\n    if (endValues.length) {\n      while (result = _numWithUnitExp.exec(end)) {\n        endValue = result[0];\n        chunk = end.substring(index, result.index);\n        if (color) {\n          color = (color + 1) % 5;\n        } else if (chunk.substr(-5) === \"rgba(\" || chunk.substr(-5) === \"hsla(\") {\n          color = 1;\n        }\n        if (endValue !== (startValue = startValues[matchIndex++] || \"\")) {\n          startNum = parseFloat(startValue) || 0;\n          startUnit = startValue.substr((startNum + \"\").length);\n          endValue.charAt(1) === \"=\" && (endValue = _parseRelative(startNum, endValue) + startUnit);\n          endNum = parseFloat(endValue);\n          endUnit = endValue.substr((endNum + \"\").length);\n          index = _numWithUnitExp.lastIndex - endUnit.length;\n          if (!endUnit) {\n            //if something like \"perspective:300\" is passed in and we must add a unit to the end\n            endUnit = endUnit || _config.units[prop] || startUnit;\n            if (index === end.length) {\n              end += endUnit;\n              pt.e += endUnit;\n            }\n          }\n          if (startUnit !== endUnit) {\n            startNum = _convertToUnit(target, prop, startValue, endUnit) || 0;\n          } // these nested PropTweens are handled in a special way - we'll never actually call a render or setter method on them. We'll just loop through them in the parent complex string PropTween's render method.\n\n          pt._pt = {\n            _next: pt._pt,\n            p: chunk || matchIndex === 1 ? chunk : \",\",\n            //note: SVG spec allows omission of comma/space when a negative sign is wedged between two numbers, like 2.5-5.3 instead of 2.5,-5.3 but when tweening, the negative value may switch to positive, so we insert the comma just in case.\n            s: startNum,\n            c: endNum - startNum,\n            m: color && color < 4 || prop === \"zIndex\" ? Math.round : 0\n          };\n        }\n      }\n      pt.c = index < end.length ? end.substring(index, end.length) : \"\"; //we use the \"c\" of the PropTween to store the final part of the string (after the last number)\n    } else {\n      pt.r = prop === \"display\" && end === \"none\" ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue;\n    }\n    _relExp.test(end) && (pt.e = 0); //if the end string contains relative values or dynamic random(...) values, delete the end it so that on the final render we don't actually set it to the string with += or -= characters (forces it to use the calculated value).\n\n    this._pt = pt; //start the linked list with this new PropTween. Remember, we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within another plugin too, thus \"this\" would refer to the plugin.\n\n    return pt;\n  },\n  _keywordToPercent = {\n    top: \"0%\",\n    bottom: \"100%\",\n    left: \"0%\",\n    right: \"100%\",\n    center: \"50%\"\n  },\n  _convertKeywordsToPercentages = function _convertKeywordsToPercentages(value) {\n    var split = value.split(\" \"),\n      x = split[0],\n      y = split[1] || \"50%\";\n    if (x === \"top\" || x === \"bottom\" || y === \"left\" || y === \"right\") {\n      //the user provided them in the wrong order, so flip them\n      value = x;\n      x = y;\n      y = value;\n    }\n    split[0] = _keywordToPercent[x] || x;\n    split[1] = _keywordToPercent[y] || y;\n    return split.join(\" \");\n  },\n  _renderClearProps = function _renderClearProps(ratio, data) {\n    if (data.tween && data.tween._time === data.tween._dur) {\n      var target = data.t,\n        style = target.style,\n        props = data.u,\n        cache = target._gsap,\n        prop,\n        clearTransforms,\n        i;\n      if (props === \"all\" || props === true) {\n        style.cssText = \"\";\n        clearTransforms = 1;\n      } else {\n        props = props.split(\",\");\n        i = props.length;\n        while (--i > -1) {\n          prop = props[i];\n          if (_transformProps[prop]) {\n            clearTransforms = 1;\n            prop = prop === \"transformOrigin\" ? _transformOriginProp : _transformProp;\n          }\n          _removeProperty(target, prop);\n        }\n      }\n      if (clearTransforms) {\n        _removeProperty(target, _transformProp);\n        if (cache) {\n          cache.svg && target.removeAttribute(\"transform\");\n          style.scale = style.rotate = style.translate = \"none\";\n          _parseTransform(target, 1); // force all the cached values back to \"normal\"/identity, otherwise if there's another tween that's already set to render transforms on this element, it could display the wrong values.\n\n          cache.uncache = 1;\n          _removeIndependentTransforms(style);\n        }\n      }\n    }\n  },\n  // note: specialProps should return 1 if (and only if) they have a non-zero priority. It indicates we need to sort the linked list.\n  _specialProps = {\n    clearProps: function clearProps(plugin, target, property, endValue, tween) {\n      if (tween.data !== \"isFromStart\") {\n        var pt = plugin._pt = new PropTween(plugin._pt, target, property, 0, 0, _renderClearProps);\n        pt.u = endValue;\n        pt.pr = -10;\n        pt.tween = tween;\n        plugin._props.push(property);\n        return 1;\n      }\n    }\n    /* className feature (about 0.4kb gzipped).\n    , className(plugin, target, property, endValue, tween) {\n    \tlet _renderClassName = (ratio, data) => {\n    \t\t\tdata.css.render(ratio, data.css);\n    \t\t\tif (!ratio || ratio === 1) {\n    \t\t\t\tlet inline = data.rmv,\n    \t\t\t\t\ttarget = data.t,\n    \t\t\t\t\tp;\n    \t\t\t\ttarget.setAttribute(\"class\", ratio ? data.e : data.b);\n    \t\t\t\tfor (p in inline) {\n    \t\t\t\t\t_removeProperty(target, p);\n    \t\t\t\t}\n    \t\t\t}\n    \t\t},\n    \t\t_getAllStyles = (target) => {\n    \t\t\tlet styles = {},\n    \t\t\t\tcomputed = getComputedStyle(target),\n    \t\t\t\tp;\n    \t\t\tfor (p in computed) {\n    \t\t\t\tif (isNaN(p) && p !== \"cssText\" && p !== \"length\") {\n    \t\t\t\t\tstyles[p] = computed[p];\n    \t\t\t\t}\n    \t\t\t}\n    \t\t\t_setDefaults(styles, _parseTransform(target, 1));\n    \t\t\treturn styles;\n    \t\t},\n    \t\tstartClassList = target.getAttribute(\"class\"),\n    \t\tstyle = target.style,\n    \t\tcssText = style.cssText,\n    \t\tcache = target._gsap,\n    \t\tclassPT = cache.classPT,\n    \t\tinlineToRemoveAtEnd = {},\n    \t\tdata = {t:target, plugin:plugin, rmv:inlineToRemoveAtEnd, b:startClassList, e:(endValue.charAt(1) !== \"=\") ? endValue : startClassList.replace(new RegExp(\"(?:\\\\s|^)\" + endValue.substr(2) + \"(?![\\\\w-])\"), \"\") + ((endValue.charAt(0) === \"+\") ? \" \" + endValue.substr(2) : \"\")},\n    \t\tchangingVars = {},\n    \t\tstartVars = _getAllStyles(target),\n    \t\ttransformRelated = /(transform|perspective)/i,\n    \t\tendVars, p;\n    \tif (classPT) {\n    \t\tclassPT.r(1, classPT.d);\n    \t\t_removeLinkedListItem(classPT.d.plugin, classPT, \"_pt\");\n    \t}\n    \ttarget.setAttribute(\"class\", data.e);\n    \tendVars = _getAllStyles(target, true);\n    \ttarget.setAttribute(\"class\", startClassList);\n    \tfor (p in endVars) {\n    \t\tif (endVars[p] !== startVars[p] && !transformRelated.test(p)) {\n    \t\t\tchangingVars[p] = endVars[p];\n    \t\t\tif (!style[p] && style[p] !== \"0\") {\n    \t\t\t\tinlineToRemoveAtEnd[p] = 1;\n    \t\t\t}\n    \t\t}\n    \t}\n    \tcache.classPT = plugin._pt = new PropTween(plugin._pt, target, \"className\", 0, 0, _renderClassName, data, 0, -11);\n    \tif (style.cssText !== cssText) { //only apply if things change. Otherwise, in cases like a background-image that's pulled dynamically, it could cause a refresh. See https://gsap.com/forums/topic/20368-possible-gsap-bug-switching-classnames-in-chrome/.\n    \t\tstyle.cssText = cssText; //we recorded cssText before we swapped classes and ran _getAllStyles() because in cases when a className tween is overwritten, we remove all the related tweening properties from that class change (otherwise class-specific stuff can't override properties we've directly set on the target's style object due to specificity).\n    \t}\n    \t_parseTransform(target, true); //to clear the caching of transforms\n    \tdata.css = new gsap.plugins.css();\n    \tdata.css.init(target, changingVars, tween);\n    \tplugin._props.push(...data.css._props);\n    \treturn 1;\n    }\n    */\n  },\n  /*\n   * --------------------------------------------------------------------------------------\n   * TRANSFORMS\n   * --------------------------------------------------------------------------------------\n   */\n  _identity2DMatrix = [1, 0, 0, 1, 0, 0],\n  _rotationalProperties = {},\n  _isNullTransform = function _isNullTransform(value) {\n    return value === \"matrix(1, 0, 0, 1, 0, 0)\" || value === \"none\" || !value;\n  },\n  _getComputedTransformMatrixAsArray = function _getComputedTransformMatrixAsArray(target) {\n    var matrixString = _getComputedProperty(target, _transformProp);\n    return _isNullTransform(matrixString) ? _identity2DMatrix : matrixString.substr(7).match(_numExp).map(_round);\n  },\n  _getMatrix = function _getMatrix(target, force2D) {\n    var cache = target._gsap || _getCache(target),\n      style = target.style,\n      matrix = _getComputedTransformMatrixAsArray(target),\n      parent,\n      nextSibling,\n      temp,\n      addedToDOM;\n    if (cache.svg && target.getAttribute(\"transform\")) {\n      temp = target.transform.baseVal.consolidate().matrix; //ensures that even complex values like \"translate(50,60) rotate(135,0,0)\" are parsed because it mashes it into a matrix.\n\n      matrix = [temp.a, temp.b, temp.c, temp.d, temp.e, temp.f];\n      return matrix.join(\",\") === \"1,0,0,1,0,0\" ? _identity2DMatrix : matrix;\n    } else if (matrix === _identity2DMatrix && !target.offsetParent && target !== _docElement && !cache.svg) {\n      //note: if offsetParent is null, that means the element isn't in the normal document flow, like if it has display:none or one of its ancestors has display:none). Firefox returns null for getComputedStyle() if the element is in an iframe that has display:none. https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n      //browsers don't report transforms accurately unless the element is in the DOM and has a display value that's not \"none\". Firefox and Microsoft browsers have a partial bug where they'll report transforms even if display:none BUT not any percentage-based values like translate(-50%, 8px) will be reported as if it's translate(0, 8px).\n      temp = style.display;\n      style.display = \"block\";\n      parent = target.parentNode;\n      if (!parent || !target.offsetParent && !target.getBoundingClientRect().width) {\n        // note: in 3.3.0 we switched target.offsetParent to _doc.body.contains(target) to avoid [sometimes unnecessary] MutationObserver calls but that wasn't adequate because there are edge cases where nested position: fixed elements need to get reparented to accurately sense transforms. See https://github.com/greensock/GSAP/issues/388 and https://github.com/greensock/GSAP/issues/375. Note: position: fixed elements report a null offsetParent but they could also be invisible because they're in an ancestor with display: none, so we check getBoundingClientRect(). We only want to alter the DOM if we absolutely have to because it can cause iframe content to reload, like a Vimeo video.\n        addedToDOM = 1; //flag\n\n        nextSibling = target.nextElementSibling;\n        _docElement.appendChild(target); //we must add it to the DOM in order to get values properly\n      }\n      matrix = _getComputedTransformMatrixAsArray(target);\n      temp ? style.display = temp : _removeProperty(target, \"display\");\n      if (addedToDOM) {\n        nextSibling ? parent.insertBefore(target, nextSibling) : parent ? parent.appendChild(target) : _docElement.removeChild(target);\n      }\n    }\n    return force2D && matrix.length > 6 ? [matrix[0], matrix[1], matrix[4], matrix[5], matrix[12], matrix[13]] : matrix;\n  },\n  _applySVGOrigin = function _applySVGOrigin(target, origin, originIsAbsolute, smooth, matrixArray, pluginToAddPropTweensTo) {\n    var cache = target._gsap,\n      matrix = matrixArray || _getMatrix(target, true),\n      xOriginOld = cache.xOrigin || 0,\n      yOriginOld = cache.yOrigin || 0,\n      xOffsetOld = cache.xOffset || 0,\n      yOffsetOld = cache.yOffset || 0,\n      a = matrix[0],\n      b = matrix[1],\n      c = matrix[2],\n      d = matrix[3],\n      tx = matrix[4],\n      ty = matrix[5],\n      originSplit = origin.split(\" \"),\n      xOrigin = parseFloat(originSplit[0]) || 0,\n      yOrigin = parseFloat(originSplit[1]) || 0,\n      bounds,\n      determinant,\n      x,\n      y;\n    if (!originIsAbsolute) {\n      bounds = _getBBox(target);\n      xOrigin = bounds.x + (~originSplit[0].indexOf(\"%\") ? xOrigin / 100 * bounds.width : xOrigin);\n      yOrigin = bounds.y + (~(originSplit[1] || originSplit[0]).indexOf(\"%\") ? yOrigin / 100 * bounds.height : yOrigin); // if (!(\"xOrigin\" in cache) && (xOrigin || yOrigin)) { // added in 3.12.3, reverted in 3.12.4; requires more exploration\n      // \txOrigin -= bounds.x;\n      // \tyOrigin -= bounds.y;\n      // }\n    } else if (matrix !== _identity2DMatrix && (determinant = a * d - b * c)) {\n      //if it's zero (like if scaleX and scaleY are zero), skip it to avoid errors with dividing by zero.\n      x = xOrigin * (d / determinant) + yOrigin * (-c / determinant) + (c * ty - d * tx) / determinant;\n      y = xOrigin * (-b / determinant) + yOrigin * (a / determinant) - (a * ty - b * tx) / determinant;\n      xOrigin = x;\n      yOrigin = y; // theory: we only had to do this for smoothing and it assumes that the previous one was not originIsAbsolute.\n    }\n    if (smooth || smooth !== false && cache.smooth) {\n      tx = xOrigin - xOriginOld;\n      ty = yOrigin - yOriginOld;\n      cache.xOffset = xOffsetOld + (tx * a + ty * c) - tx;\n      cache.yOffset = yOffsetOld + (tx * b + ty * d) - ty;\n    } else {\n      cache.xOffset = cache.yOffset = 0;\n    }\n    cache.xOrigin = xOrigin;\n    cache.yOrigin = yOrigin;\n    cache.smooth = !!smooth;\n    cache.origin = origin;\n    cache.originIsAbsolute = !!originIsAbsolute;\n    target.style[_transformOriginProp] = \"0px 0px\"; //otherwise, if someone sets  an origin via CSS, it will likely interfere with the SVG transform attribute ones (because remember, we're baking the origin into the matrix() value).\n\n    if (pluginToAddPropTweensTo) {\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOrigin\", xOriginOld, xOrigin);\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOrigin\", yOriginOld, yOrigin);\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOffset\", xOffsetOld, cache.xOffset);\n      _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOffset\", yOffsetOld, cache.yOffset);\n    }\n    target.setAttribute(\"data-svg-origin\", xOrigin + \" \" + yOrigin);\n  },\n  _parseTransform = function _parseTransform(target, uncache) {\n    var cache = target._gsap || new GSCache(target);\n    if (\"x\" in cache && !uncache && !cache.uncache) {\n      return cache;\n    }\n    var style = target.style,\n      invertedScaleX = cache.scaleX < 0,\n      px = \"px\",\n      deg = \"deg\",\n      cs = getComputedStyle(target),\n      origin = _getComputedProperty(target, _transformOriginProp) || \"0\",\n      x,\n      y,\n      z,\n      scaleX,\n      scaleY,\n      rotation,\n      rotationX,\n      rotationY,\n      skewX,\n      skewY,\n      perspective,\n      xOrigin,\n      yOrigin,\n      matrix,\n      angle,\n      cos,\n      sin,\n      a,\n      b,\n      c,\n      d,\n      a12,\n      a22,\n      t1,\n      t2,\n      t3,\n      a13,\n      a23,\n      a33,\n      a42,\n      a43,\n      a32;\n    x = y = z = rotation = rotationX = rotationY = skewX = skewY = perspective = 0;\n    scaleX = scaleY = 1;\n    cache.svg = !!(target.getCTM && _isSVG(target));\n    if (cs.translate) {\n      // accommodate independent transforms by combining them into normal ones.\n      if (cs.translate !== \"none\" || cs.scale !== \"none\" || cs.rotate !== \"none\") {\n        style[_transformProp] = (cs.translate !== \"none\" ? \"translate3d(\" + (cs.translate + \" 0 0\").split(\" \").slice(0, 3).join(\", \") + \") \" : \"\") + (cs.rotate !== \"none\" ? \"rotate(\" + cs.rotate + \") \" : \"\") + (cs.scale !== \"none\" ? \"scale(\" + cs.scale.split(\" \").join(\",\") + \") \" : \"\") + (cs[_transformProp] !== \"none\" ? cs[_transformProp] : \"\");\n      }\n      style.scale = style.rotate = style.translate = \"none\";\n    }\n    matrix = _getMatrix(target, cache.svg);\n    if (cache.svg) {\n      if (cache.uncache) {\n        // if cache.uncache is true (and maybe if origin is 0,0), we need to set element.style.transformOrigin = (cache.xOrigin - bbox.x) + \"px \" + (cache.yOrigin - bbox.y) + \"px\". Previously we let the data-svg-origin stay instead, but when introducing revert(), it complicated things.\n        t2 = target.getBBox();\n        origin = cache.xOrigin - t2.x + \"px \" + (cache.yOrigin - t2.y) + \"px\";\n        t1 = \"\";\n      } else {\n        t1 = !uncache && target.getAttribute(\"data-svg-origin\"); //  Remember, to work around browser inconsistencies we always force SVG elements' transformOrigin to 0,0 and offset the translation accordingly.\n      }\n      _applySVGOrigin(target, t1 || origin, !!t1 || cache.originIsAbsolute, cache.smooth !== false, matrix);\n    }\n    xOrigin = cache.xOrigin || 0;\n    yOrigin = cache.yOrigin || 0;\n    if (matrix !== _identity2DMatrix) {\n      a = matrix[0]; //a11\n\n      b = matrix[1]; //a21\n\n      c = matrix[2]; //a31\n\n      d = matrix[3]; //a41\n\n      x = a12 = matrix[4];\n      y = a22 = matrix[5]; //2D matrix\n\n      if (matrix.length === 6) {\n        scaleX = Math.sqrt(a * a + b * b);\n        scaleY = Math.sqrt(d * d + c * c);\n        rotation = a || b ? _atan2(b, a) * _RAD2DEG : 0; //note: if scaleX is 0, we cannot accurately measure rotation. Same for skewX with a scaleY of 0. Therefore, we default to the previously recorded value (or zero if that doesn't exist).\n\n        skewX = c || d ? _atan2(c, d) * _RAD2DEG + rotation : 0;\n        skewX && (scaleY *= Math.abs(Math.cos(skewX * _DEG2RAD)));\n        if (cache.svg) {\n          x -= xOrigin - (xOrigin * a + yOrigin * c);\n          y -= yOrigin - (xOrigin * b + yOrigin * d);\n        } //3D matrix\n      } else {\n        a32 = matrix[6];\n        a42 = matrix[7];\n        a13 = matrix[8];\n        a23 = matrix[9];\n        a33 = matrix[10];\n        a43 = matrix[11];\n        x = matrix[12];\n        y = matrix[13];\n        z = matrix[14];\n        angle = _atan2(a32, a33);\n        rotationX = angle * _RAD2DEG; //rotationX\n\n        if (angle) {\n          cos = Math.cos(-angle);\n          sin = Math.sin(-angle);\n          t1 = a12 * cos + a13 * sin;\n          t2 = a22 * cos + a23 * sin;\n          t3 = a32 * cos + a33 * sin;\n          a13 = a12 * -sin + a13 * cos;\n          a23 = a22 * -sin + a23 * cos;\n          a33 = a32 * -sin + a33 * cos;\n          a43 = a42 * -sin + a43 * cos;\n          a12 = t1;\n          a22 = t2;\n          a32 = t3;\n        } //rotationY\n\n        angle = _atan2(-c, a33);\n        rotationY = angle * _RAD2DEG;\n        if (angle) {\n          cos = Math.cos(-angle);\n          sin = Math.sin(-angle);\n          t1 = a * cos - a13 * sin;\n          t2 = b * cos - a23 * sin;\n          t3 = c * cos - a33 * sin;\n          a43 = d * sin + a43 * cos;\n          a = t1;\n          b = t2;\n          c = t3;\n        } //rotationZ\n\n        angle = _atan2(b, a);\n        rotation = angle * _RAD2DEG;\n        if (angle) {\n          cos = Math.cos(angle);\n          sin = Math.sin(angle);\n          t1 = a * cos + b * sin;\n          t2 = a12 * cos + a22 * sin;\n          b = b * cos - a * sin;\n          a22 = a22 * cos - a12 * sin;\n          a = t1;\n          a12 = t2;\n        }\n        if (rotationX && Math.abs(rotationX) + Math.abs(rotation) > 359.9) {\n          //when rotationY is set, it will often be parsed as 180 degrees different than it should be, and rotationX and rotation both being 180 (it looks the same), so we adjust for that here.\n          rotationX = rotation = 0;\n          rotationY = 180 - rotationY;\n        }\n        scaleX = _round(Math.sqrt(a * a + b * b + c * c));\n        scaleY = _round(Math.sqrt(a22 * a22 + a32 * a32));\n        angle = _atan2(a12, a22);\n        skewX = Math.abs(angle) > 0.0002 ? angle * _RAD2DEG : 0;\n        perspective = a43 ? 1 / (a43 < 0 ? -a43 : a43) : 0;\n      }\n      if (cache.svg) {\n        //sense if there are CSS transforms applied on an SVG element in which case we must overwrite them when rendering. The transform attribute is more reliable cross-browser, but we can't just remove the CSS ones because they may be applied in a CSS rule somewhere (not just inline).\n        t1 = target.getAttribute(\"transform\");\n        cache.forceCSS = target.setAttribute(\"transform\", \"\") || !_isNullTransform(_getComputedProperty(target, _transformProp));\n        t1 && target.setAttribute(\"transform\", t1);\n      }\n    }\n    if (Math.abs(skewX) > 90 && Math.abs(skewX) < 270) {\n      if (invertedScaleX) {\n        scaleX *= -1;\n        skewX += rotation <= 0 ? 180 : -180;\n        rotation += rotation <= 0 ? 180 : -180;\n      } else {\n        scaleY *= -1;\n        skewX += skewX <= 0 ? 180 : -180;\n      }\n    }\n    uncache = uncache || cache.uncache;\n    cache.x = x - ((cache.xPercent = x && (!uncache && cache.xPercent || (Math.round(target.offsetWidth / 2) === Math.round(-x) ? -50 : 0))) ? target.offsetWidth * cache.xPercent / 100 : 0) + px;\n    cache.y = y - ((cache.yPercent = y && (!uncache && cache.yPercent || (Math.round(target.offsetHeight / 2) === Math.round(-y) ? -50 : 0))) ? target.offsetHeight * cache.yPercent / 100 : 0) + px;\n    cache.z = z + px;\n    cache.scaleX = _round(scaleX);\n    cache.scaleY = _round(scaleY);\n    cache.rotation = _round(rotation) + deg;\n    cache.rotationX = _round(rotationX) + deg;\n    cache.rotationY = _round(rotationY) + deg;\n    cache.skewX = skewX + deg;\n    cache.skewY = skewY + deg;\n    cache.transformPerspective = perspective + px;\n    if (cache.zOrigin = parseFloat(origin.split(\" \")[2]) || !uncache && cache.zOrigin || 0) {\n      style[_transformOriginProp] = _firstTwoOnly(origin);\n    }\n    cache.xOffset = cache.yOffset = 0;\n    cache.force3D = _config.force3D;\n    cache.renderTransform = cache.svg ? _renderSVGTransforms : _supports3D ? _renderCSSTransforms : _renderNon3DTransforms;\n    cache.uncache = 0;\n    return cache;\n  },\n  _firstTwoOnly = function _firstTwoOnly(value) {\n    return (value = value.split(\" \"))[0] + \" \" + value[1];\n  },\n  //for handling transformOrigin values, stripping out the 3rd dimension\n  _addPxTranslate = function _addPxTranslate(target, start, value) {\n    var unit = getUnit(start);\n    return _round(parseFloat(start) + parseFloat(_convertToUnit(target, \"x\", value + \"px\", unit))) + unit;\n  },\n  _renderNon3DTransforms = function _renderNon3DTransforms(ratio, cache) {\n    cache.z = \"0px\";\n    cache.rotationY = cache.rotationX = \"0deg\";\n    cache.force3D = 0;\n    _renderCSSTransforms(ratio, cache);\n  },\n  _zeroDeg = \"0deg\",\n  _zeroPx = \"0px\",\n  _endParenthesis = \") \",\n  _renderCSSTransforms = function _renderCSSTransforms(ratio, cache) {\n    var _ref = cache || this,\n      xPercent = _ref.xPercent,\n      yPercent = _ref.yPercent,\n      x = _ref.x,\n      y = _ref.y,\n      z = _ref.z,\n      rotation = _ref.rotation,\n      rotationY = _ref.rotationY,\n      rotationX = _ref.rotationX,\n      skewX = _ref.skewX,\n      skewY = _ref.skewY,\n      scaleX = _ref.scaleX,\n      scaleY = _ref.scaleY,\n      transformPerspective = _ref.transformPerspective,\n      force3D = _ref.force3D,\n      target = _ref.target,\n      zOrigin = _ref.zOrigin,\n      transforms = \"\",\n      use3D = force3D === \"auto\" && ratio && ratio !== 1 || force3D === true; // Safari has a bug that causes it not to render 3D transform-origin values properly, so we force the z origin to 0, record it in the cache, and then do the math here to offset the translate values accordingly (basically do the 3D transform-origin part manually)\n\n    if (zOrigin && (rotationX !== _zeroDeg || rotationY !== _zeroDeg)) {\n      var angle = parseFloat(rotationY) * _DEG2RAD,\n        a13 = Math.sin(angle),\n        a33 = Math.cos(angle),\n        cos;\n      angle = parseFloat(rotationX) * _DEG2RAD;\n      cos = Math.cos(angle);\n      x = _addPxTranslate(target, x, a13 * cos * -zOrigin);\n      y = _addPxTranslate(target, y, -Math.sin(angle) * -zOrigin);\n      z = _addPxTranslate(target, z, a33 * cos * -zOrigin + zOrigin);\n    }\n    if (transformPerspective !== _zeroPx) {\n      transforms += \"perspective(\" + transformPerspective + _endParenthesis;\n    }\n    if (xPercent || yPercent) {\n      transforms += \"translate(\" + xPercent + \"%, \" + yPercent + \"%) \";\n    }\n    if (use3D || x !== _zeroPx || y !== _zeroPx || z !== _zeroPx) {\n      transforms += z !== _zeroPx || use3D ? \"translate3d(\" + x + \", \" + y + \", \" + z + \") \" : \"translate(\" + x + \", \" + y + _endParenthesis;\n    }\n    if (rotation !== _zeroDeg) {\n      transforms += \"rotate(\" + rotation + _endParenthesis;\n    }\n    if (rotationY !== _zeroDeg) {\n      transforms += \"rotateY(\" + rotationY + _endParenthesis;\n    }\n    if (rotationX !== _zeroDeg) {\n      transforms += \"rotateX(\" + rotationX + _endParenthesis;\n    }\n    if (skewX !== _zeroDeg || skewY !== _zeroDeg) {\n      transforms += \"skew(\" + skewX + \", \" + skewY + _endParenthesis;\n    }\n    if (scaleX !== 1 || scaleY !== 1) {\n      transforms += \"scale(\" + scaleX + \", \" + scaleY + _endParenthesis;\n    }\n    target.style[_transformProp] = transforms || \"translate(0, 0)\";\n  },\n  _renderSVGTransforms = function _renderSVGTransforms(ratio, cache) {\n    var _ref2 = cache || this,\n      xPercent = _ref2.xPercent,\n      yPercent = _ref2.yPercent,\n      x = _ref2.x,\n      y = _ref2.y,\n      rotation = _ref2.rotation,\n      skewX = _ref2.skewX,\n      skewY = _ref2.skewY,\n      scaleX = _ref2.scaleX,\n      scaleY = _ref2.scaleY,\n      target = _ref2.target,\n      xOrigin = _ref2.xOrigin,\n      yOrigin = _ref2.yOrigin,\n      xOffset = _ref2.xOffset,\n      yOffset = _ref2.yOffset,\n      forceCSS = _ref2.forceCSS,\n      tx = parseFloat(x),\n      ty = parseFloat(y),\n      a11,\n      a21,\n      a12,\n      a22,\n      temp;\n    rotation = parseFloat(rotation);\n    skewX = parseFloat(skewX);\n    skewY = parseFloat(skewY);\n    if (skewY) {\n      //for performance reasons, we combine all skewing into the skewX and rotation values. Remember, a skewY of 10 degrees looks the same as a rotation of 10 degrees plus a skewX of 10 degrees.\n      skewY = parseFloat(skewY);\n      skewX += skewY;\n      rotation += skewY;\n    }\n    if (rotation || skewX) {\n      rotation *= _DEG2RAD;\n      skewX *= _DEG2RAD;\n      a11 = Math.cos(rotation) * scaleX;\n      a21 = Math.sin(rotation) * scaleX;\n      a12 = Math.sin(rotation - skewX) * -scaleY;\n      a22 = Math.cos(rotation - skewX) * scaleY;\n      if (skewX) {\n        skewY *= _DEG2RAD;\n        temp = Math.tan(skewX - skewY);\n        temp = Math.sqrt(1 + temp * temp);\n        a12 *= temp;\n        a22 *= temp;\n        if (skewY) {\n          temp = Math.tan(skewY);\n          temp = Math.sqrt(1 + temp * temp);\n          a11 *= temp;\n          a21 *= temp;\n        }\n      }\n      a11 = _round(a11);\n      a21 = _round(a21);\n      a12 = _round(a12);\n      a22 = _round(a22);\n    } else {\n      a11 = scaleX;\n      a22 = scaleY;\n      a21 = a12 = 0;\n    }\n    if (tx && !~(x + \"\").indexOf(\"px\") || ty && !~(y + \"\").indexOf(\"px\")) {\n      tx = _convertToUnit(target, \"x\", x, \"px\");\n      ty = _convertToUnit(target, \"y\", y, \"px\");\n    }\n    if (xOrigin || yOrigin || xOffset || yOffset) {\n      tx = _round(tx + xOrigin - (xOrigin * a11 + yOrigin * a12) + xOffset);\n      ty = _round(ty + yOrigin - (xOrigin * a21 + yOrigin * a22) + yOffset);\n    }\n    if (xPercent || yPercent) {\n      //The SVG spec doesn't support percentage-based translation in the \"transform\" attribute, so we merge it into the translation to simulate it.\n      temp = target.getBBox();\n      tx = _round(tx + xPercent / 100 * temp.width);\n      ty = _round(ty + yPercent / 100 * temp.height);\n    }\n    temp = \"matrix(\" + a11 + \",\" + a21 + \",\" + a12 + \",\" + a22 + \",\" + tx + \",\" + ty + \")\";\n    target.setAttribute(\"transform\", temp);\n    forceCSS && (target.style[_transformProp] = temp); //some browsers prioritize CSS transforms over the transform attribute. When we sense that the user has CSS transforms applied, we must overwrite them this way (otherwise some browser simply won't render the transform attribute changes!)\n  },\n  _addRotationalPropTween = function _addRotationalPropTween(plugin, target, property, startNum, endValue) {\n    var cap = 360,\n      isString = _isString(endValue),\n      endNum = parseFloat(endValue) * (isString && ~endValue.indexOf(\"rad\") ? _RAD2DEG : 1),\n      change = endNum - startNum,\n      finalValue = startNum + change + \"deg\",\n      direction,\n      pt;\n    if (isString) {\n      direction = endValue.split(\"_\")[1];\n      if (direction === \"short\") {\n        change %= cap;\n        if (change !== change % (cap / 2)) {\n          change += change < 0 ? cap : -cap;\n        }\n      }\n      if (direction === \"cw\" && change < 0) {\n        change = (change + cap * _bigNum) % cap - ~~(change / cap) * cap;\n      } else if (direction === \"ccw\" && change > 0) {\n        change = (change - cap * _bigNum) % cap - ~~(change / cap) * cap;\n      }\n    }\n    plugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n    pt.e = finalValue;\n    pt.u = \"deg\";\n    plugin._props.push(property);\n    return pt;\n  },\n  _assign = function _assign(target, source) {\n    // Internet Explorer doesn't have Object.assign(), so we recreate it here.\n    for (var p in source) {\n      target[p] = source[p];\n    }\n    return target;\n  },\n  _addRawTransformPTs = function _addRawTransformPTs(plugin, transforms, target) {\n    //for handling cases where someone passes in a whole transform string, like transform: \"scale(2, 3) rotate(20deg) translateY(30em)\"\n    var startCache = _assign({}, target._gsap),\n      exclude = \"perspective,force3D,transformOrigin,svgOrigin\",\n      style = target.style,\n      endCache,\n      p,\n      startValue,\n      endValue,\n      startNum,\n      endNum,\n      startUnit,\n      endUnit;\n    if (startCache.svg) {\n      startValue = target.getAttribute(\"transform\");\n      target.setAttribute(\"transform\", \"\");\n      style[_transformProp] = transforms;\n      endCache = _parseTransform(target, 1);\n      _removeProperty(target, _transformProp);\n      target.setAttribute(\"transform\", startValue);\n    } else {\n      startValue = getComputedStyle(target)[_transformProp];\n      style[_transformProp] = transforms;\n      endCache = _parseTransform(target, 1);\n      style[_transformProp] = startValue;\n    }\n    for (p in _transformProps) {\n      startValue = startCache[p];\n      endValue = endCache[p];\n      if (startValue !== endValue && exclude.indexOf(p) < 0) {\n        //tweening to no perspective gives very unintuitive results - just keep the same perspective in that case.\n        startUnit = getUnit(startValue);\n        endUnit = getUnit(endValue);\n        startNum = startUnit !== endUnit ? _convertToUnit(target, p, startValue, endUnit) : parseFloat(startValue);\n        endNum = parseFloat(endValue);\n        plugin._pt = new PropTween(plugin._pt, endCache, p, startNum, endNum - startNum, _renderCSSProp);\n        plugin._pt.u = endUnit || 0;\n        plugin._props.push(p);\n      }\n    }\n    _assign(endCache, startCache);\n  }; // handle splitting apart padding, margin, borderWidth, and borderRadius into their 4 components. Firefox, for example, won't report borderRadius correctly - it will only do borderTopLeftRadius and the other corners. We also want to handle paddingTop, marginLeft, borderRightWidth, etc.\n\n_forEachName(\"padding,margin,Width,Radius\", function (name, index) {\n  var t = \"Top\",\n    r = \"Right\",\n    b = \"Bottom\",\n    l = \"Left\",\n    props = (index < 3 ? [t, r, b, l] : [t + l, t + r, b + r, b + l]).map(function (side) {\n      return index < 2 ? name + side : \"border\" + side + name;\n    });\n  _specialProps[index > 1 ? \"border\" + name : name] = function (plugin, target, property, endValue, tween) {\n    var a, vars;\n    if (arguments.length < 4) {\n      // getter, passed target, property, and unit (from _get())\n      a = props.map(function (prop) {\n        return _get(plugin, prop, property);\n      });\n      vars = a.join(\" \");\n      return vars.split(a[0]).length === 5 ? a[0] : vars;\n    }\n    a = (endValue + \"\").split(\" \");\n    vars = {};\n    props.forEach(function (prop, i) {\n      return vars[prop] = a[i] = a[i] || a[(i - 1) / 2 | 0];\n    });\n    plugin.init(target, vars, tween);\n  };\n});\nexport var CSSPlugin = {\n  name: \"css\",\n  register: _initCore,\n  targetTest: function targetTest(target) {\n    return target.style && target.nodeType;\n  },\n  init: function init(target, vars, tween, index, targets) {\n    var props = this._props,\n      style = target.style,\n      startAt = tween.vars.startAt,\n      startValue,\n      endValue,\n      endNum,\n      startNum,\n      type,\n      specialProp,\n      p,\n      startUnit,\n      endUnit,\n      relative,\n      isTransformRelated,\n      transformPropTween,\n      cache,\n      smooth,\n      hasPriority,\n      inlineProps;\n    _pluginInitted || _initCore(); // we may call init() multiple times on the same plugin instance, like when adding special properties, so make sure we don't overwrite the revert data or inlineProps\n\n    this.styles = this.styles || _getStyleSaver(target);\n    inlineProps = this.styles.props;\n    this.tween = tween;\n    for (p in vars) {\n      if (p === \"autoRound\") {\n        continue;\n      }\n      endValue = vars[p];\n      if (_plugins[p] && _checkPlugin(p, vars, tween, index, target, targets)) {\n        // plugins\n        continue;\n      }\n      type = typeof endValue;\n      specialProp = _specialProps[p];\n      if (type === \"function\") {\n        endValue = endValue.call(tween, index, target, targets);\n        type = typeof endValue;\n      }\n      if (type === \"string\" && ~endValue.indexOf(\"random(\")) {\n        endValue = _replaceRandom(endValue);\n      }\n      if (specialProp) {\n        specialProp(this, target, p, endValue, tween) && (hasPriority = 1);\n      } else if (p.substr(0, 2) === \"--\") {\n        //CSS variable\n        startValue = (getComputedStyle(target).getPropertyValue(p) + \"\").trim();\n        endValue += \"\";\n        _colorExp.lastIndex = 0;\n        if (!_colorExp.test(startValue)) {\n          // colors don't have units\n          startUnit = getUnit(startValue);\n          endUnit = getUnit(endValue);\n        }\n        endUnit ? startUnit !== endUnit && (startValue = _convertToUnit(target, p, startValue, endUnit) + endUnit) : startUnit && (endValue += startUnit);\n        this.add(style, \"setProperty\", startValue, endValue, index, targets, 0, 0, p);\n        props.push(p);\n        inlineProps.push(p, 0, style[p]);\n      } else if (type !== \"undefined\") {\n        if (startAt && p in startAt) {\n          // in case someone hard-codes a complex value as the start, like top: \"calc(2vh / 2)\". Without this, it'd use the computed value (always in px)\n          startValue = typeof startAt[p] === \"function\" ? startAt[p].call(tween, index, target, targets) : startAt[p];\n          _isString(startValue) && ~startValue.indexOf(\"random(\") && (startValue = _replaceRandom(startValue));\n          getUnit(startValue + \"\") || startValue === \"auto\" || (startValue += _config.units[p] || getUnit(_get(target, p)) || \"\"); // for cases when someone passes in a unitless value like {x: 100}; if we try setting translate(100, 0px) it won't work.\n\n          (startValue + \"\").charAt(1) === \"=\" && (startValue = _get(target, p)); // can't work with relative values\n        } else {\n          startValue = _get(target, p);\n        }\n        startNum = parseFloat(startValue);\n        relative = type === \"string\" && endValue.charAt(1) === \"=\" && endValue.substr(0, 2);\n        relative && (endValue = endValue.substr(2));\n        endNum = parseFloat(endValue);\n        if (p in _propertyAliases) {\n          if (p === \"autoAlpha\") {\n            //special case where we control the visibility along with opacity. We still allow the opacity value to pass through and get tweened.\n            if (startNum === 1 && _get(target, \"visibility\") === \"hidden\" && endNum) {\n              //if visibility is initially set to \"hidden\", we should interpret that as intent to make opacity 0 (a convenience)\n              startNum = 0;\n            }\n            inlineProps.push(\"visibility\", 0, style.visibility);\n            _addNonTweeningPT(this, style, \"visibility\", startNum ? \"inherit\" : \"hidden\", endNum ? \"inherit\" : \"hidden\", !endNum);\n          }\n          if (p !== \"scale\" && p !== \"transform\") {\n            p = _propertyAliases[p];\n            ~p.indexOf(\",\") && (p = p.split(\",\")[0]);\n          }\n        }\n        isTransformRelated = p in _transformProps; //--- TRANSFORM-RELATED ---\n\n        if (isTransformRelated) {\n          this.styles.save(p);\n          if (type === \"string\" && endValue.substring(0, 6) === \"var(--\") {\n            endValue = _getComputedProperty(target, endValue.substring(4, endValue.indexOf(\")\")));\n            endNum = parseFloat(endValue);\n          }\n          if (!transformPropTween) {\n            cache = target._gsap;\n            cache.renderTransform && !vars.parseTransform || _parseTransform(target, vars.parseTransform); // if, for example, gsap.set(... {transform:\"translateX(50vw)\"}), the _get() call doesn't parse the transform, thus cache.renderTransform won't be set yet so force the parsing of the transform here.\n\n            smooth = vars.smoothOrigin !== false && cache.smooth;\n            transformPropTween = this._pt = new PropTween(this._pt, style, _transformProp, 0, 1, cache.renderTransform, cache, 0, -1); //the first time through, create the rendering PropTween so that it runs LAST (in the linked list, we keep adding to the beginning)\n\n            transformPropTween.dep = 1; //flag it as dependent so that if things get killed/overwritten and this is the only PropTween left, we can safely kill the whole tween.\n          }\n          if (p === \"scale\") {\n            this._pt = new PropTween(this._pt, cache, \"scaleY\", cache.scaleY, (relative ? _parseRelative(cache.scaleY, relative + endNum) : endNum) - cache.scaleY || 0, _renderCSSProp);\n            this._pt.u = 0;\n            props.push(\"scaleY\", p);\n            p += \"X\";\n          } else if (p === \"transformOrigin\") {\n            inlineProps.push(_transformOriginProp, 0, style[_transformOriginProp]);\n            endValue = _convertKeywordsToPercentages(endValue); //in case something like \"left top\" or \"bottom right\" is passed in. Convert to percentages.\n\n            if (cache.svg) {\n              _applySVGOrigin(target, endValue, 0, smooth, 0, this);\n            } else {\n              endUnit = parseFloat(endValue.split(\" \")[2]) || 0; //handle the zOrigin separately!\n\n              endUnit !== cache.zOrigin && _addNonTweeningPT(this, cache, \"zOrigin\", cache.zOrigin, endUnit);\n              _addNonTweeningPT(this, style, p, _firstTwoOnly(startValue), _firstTwoOnly(endValue));\n            }\n            continue;\n          } else if (p === \"svgOrigin\") {\n            _applySVGOrigin(target, endValue, 1, smooth, 0, this);\n            continue;\n          } else if (p in _rotationalProperties) {\n            _addRotationalPropTween(this, cache, p, startNum, relative ? _parseRelative(startNum, relative + endValue) : endValue);\n            continue;\n          } else if (p === \"smoothOrigin\") {\n            _addNonTweeningPT(this, cache, \"smooth\", cache.smooth, endValue);\n            continue;\n          } else if (p === \"force3D\") {\n            cache[p] = endValue;\n            continue;\n          } else if (p === \"transform\") {\n            _addRawTransformPTs(this, endValue, target);\n            continue;\n          }\n        } else if (!(p in style)) {\n          p = _checkPropPrefix(p) || p;\n        }\n        if (isTransformRelated || (endNum || endNum === 0) && (startNum || startNum === 0) && !_complexExp.test(endValue) && p in style) {\n          startUnit = (startValue + \"\").substr((startNum + \"\").length);\n          endNum || (endNum = 0); // protect against NaN\n\n          endUnit = getUnit(endValue) || (p in _config.units ? _config.units[p] : startUnit);\n          startUnit !== endUnit && (startNum = _convertToUnit(target, p, startValue, endUnit));\n          this._pt = new PropTween(this._pt, isTransformRelated ? cache : style, p, startNum, (relative ? _parseRelative(startNum, relative + endNum) : endNum) - startNum, !isTransformRelated && (endUnit === \"px\" || p === \"zIndex\") && vars.autoRound !== false ? _renderRoundedCSSProp : _renderCSSProp);\n          this._pt.u = endUnit || 0;\n          if (startUnit !== endUnit && endUnit !== \"%\") {\n            //when the tween goes all the way back to the beginning, we need to revert it to the OLD/ORIGINAL value (with those units). We record that as a \"b\" (beginning) property and point to a render method that handles that. (performance optimization)\n            this._pt.b = startValue;\n            this._pt.r = _renderCSSPropWithBeginning;\n          }\n        } else if (!(p in style)) {\n          if (p in target) {\n            //maybe it's not a style - it could be a property added directly to an element in which case we'll try to animate that.\n            this.add(target, p, startValue || target[p], relative ? relative + endValue : endValue, index, targets);\n          } else if (p !== \"parseTransform\") {\n            _missingPlugin(p, endValue);\n            continue;\n          }\n        } else {\n          _tweenComplexCSSString.call(this, target, p, startValue, relative ? relative + endValue : endValue);\n        }\n        isTransformRelated || (p in style ? inlineProps.push(p, 0, style[p]) : typeof target[p] === \"function\" ? inlineProps.push(p, 2, target[p]()) : inlineProps.push(p, 1, startValue || target[p]));\n        props.push(p);\n      }\n    }\n    hasPriority && _sortPropTweensByPriority(this);\n  },\n  render: function render(ratio, data) {\n    if (data.tween._time || !_reverting()) {\n      var pt = data._pt;\n      while (pt) {\n        pt.r(ratio, pt.d);\n        pt = pt._next;\n      }\n    } else {\n      data.styles.revert();\n    }\n  },\n  get: _get,\n  aliases: _propertyAliases,\n  getSetter: function getSetter(target, property, plugin) {\n    //returns a setter function that accepts target, property, value and applies it accordingly. Remember, properties like \"x\" aren't as simple as target.style.property = value because they've got to be applied to a proxy object and then merged into a transform string in a renderer.\n    var p = _propertyAliases[property];\n    p && p.indexOf(\",\") < 0 && (property = p);\n    return property in _transformProps && property !== _transformOriginProp && (target._gsap.x || _get(target, \"x\")) ? plugin && _recentSetterPlugin === plugin ? property === \"scale\" ? _setterScale : _setterTransform : (_recentSetterPlugin = plugin || {}) && (property === \"scale\" ? _setterScaleWithRender : _setterTransformWithRender) : target.style && !_isUndefined(target.style[property]) ? _setterCSSStyle : ~property.indexOf(\"-\") ? _setterCSSProp : _getSetter(target, property);\n  },\n  core: {\n    _removeProperty: _removeProperty,\n    _getMatrix: _getMatrix\n  }\n};\ngsap.utils.checkPrefix = _checkPropPrefix;\ngsap.core.getStyleSaver = _getStyleSaver;\n(function (positionAndScale, rotation, others, aliases) {\n  var all = _forEachName(positionAndScale + \",\" + rotation + \",\" + others, function (name) {\n    _transformProps[name] = 1;\n  });\n  _forEachName(rotation, function (name) {\n    _config.units[name] = \"deg\";\n    _rotationalProperties[name] = 1;\n  });\n  _propertyAliases[all[13]] = positionAndScale + \",\" + rotation;\n  _forEachName(aliases, function (name) {\n    var split = name.split(\":\");\n    _propertyAliases[split[1]] = all[split[0]];\n  });\n})(\"x,y,z,scale,scaleX,scaleY,xPercent,yPercent\", \"rotation,rotationX,rotationY,skewX,skewY\", \"transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective\", \"0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY\");\n_forEachName(\"x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective\", function (name) {\n  _config.units[name] = \"px\";\n});\ngsap.registerPlugin(CSSPlugin);\nexport { CSSPlugin as default, _getBBox, _createElement, _checkPropPrefix as checkPrefix };", "map": {"version": 3, "names": ["gsap", "_getProperty", "_numExp", "_numWithUnitExp", "getUnit", "_isString", "_isUndefined", "_renderComplexString", "_relExp", "_forEachName", "_sortPropTweensByPriority", "_colorStringFilter", "_checkPlugin", "_replaceRandom", "_plugins", "<PERSON><PERSON><PERSON>", "PropTween", "_config", "_ticker", "_round", "_missingPlugin", "_getSetter", "_getCache", "_colorExp", "_parseRelative", "_setDefaults", "_removeLinkedListItem", "_win", "_doc", "_doc<PERSON>lement", "_pluginInitted", "_tempDiv", "_tempDivStyler", "_recentSetterPlugin", "_reverting", "_windowExists", "window", "_transformProps", "_RAD2DEG", "Math", "PI", "_DEG2RAD", "_atan2", "atan2", "_bigNum", "_capsExp", "_horizontalExp", "_complexExp", "_propertyAliases", "autoAlpha", "scale", "alpha", "_renderCSSProp", "ratio", "data", "set", "t", "p", "round", "s", "c", "u", "_renderPropWithEnd", "e", "_renderCSSPropWithBeginning", "b", "_renderRoundedCSSProp", "value", "_renderNonTweeningValue", "_renderNonTweeningValueOnlyAtEnd", "_setterCSSStyle", "target", "property", "style", "_setterCS<PERSON>rop", "setProperty", "_setterTransform", "_gsap", "_setterScale", "scaleX", "scaleY", "_setterScaleWithRender", "cache", "renderTransform", "_setterTransformWithRender", "_transformProp", "_transformOriginProp", "_saveStyle", "isNotCSS", "_this", "tfm", "indexOf", "split", "for<PERSON>ach", "a", "_get", "x", "<PERSON><PERSON><PERSON><PERSON>", "transform", "call", "props", "svg", "svgo", "getAttribute", "push", "_removeIndependentTransforms", "translate", "removeProperty", "_revertStyle", "i", "length", "substr", "replace", "toLowerCase", "setAttribute", "isStart", "uncache", "_getStyleSaver", "properties", "saver", "revert", "save", "core", "getCache", "nodeType", "_supports3D", "_createElement", "type", "ns", "createElementNS", "createElement", "_getComputedProperty", "skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cs", "getComputedStyle", "getPropertyValue", "_checkPropPrefix", "_prefixes", "element", "preferPrefix", "char<PERSON>t", "toUpperCase", "_initCore", "document", "documentElement", "cssText", "reverting", "_getReparentedCloneBBox", "owner", "ownerSVGElement", "clone", "cloneNode", "bbox", "display", "append<PERSON><PERSON><PERSON>", "getBBox", "<PERSON><PERSON><PERSON><PERSON>", "_getAttributeFallbacks", "attributesArray", "hasAttribute", "_get<PERSON><PERSON>", "bounds", "cloned", "error", "width", "height", "y", "_isSVG", "getCTM", "parentNode", "_removeProperty", "first2Chars", "removeAttribute", "_addNonTweeningPT", "plugin", "beginning", "end", "onlySetAtEnd", "pt", "_pt", "_props", "_nonConvertibleUnits", "deg", "rad", "turn", "_nonStandardLayouts", "grid", "flex", "_convertToUnit", "unit", "curValue", "parseFloat", "curUnit", "trim", "horizontal", "test", "isRootSVG", "tagName", "measureProperty", "amount", "toPixels", "toPercent", "px", "parent", "isSVG", "body", "time", "v", "position", "_parseTransform", "origin", "_firstTwoOnly", "_specialProps", "_tweenComplexCSSString", "prop", "start", "index", "matchIndex", "result", "startValues", "startNum", "color", "startValue", "endValue", "endNum", "chunk", "endUnit", "startUnit", "endValues", "substring", "match", "exec", "lastIndex", "units", "_next", "m", "r", "_keywordToPercent", "top", "bottom", "left", "right", "center", "_convertKeywordsToPercentages", "join", "_renderClearProps", "tween", "_time", "_dur", "clearTransforms", "rotate", "clearProps", "pr", "_identity2DMatrix", "_rotationalProperties", "_isNullTransform", "_getComputedTransformMatrixAsArray", "matrixString", "map", "_getMatrix", "force2D", "matrix", "nextS<PERSON>ling", "temp", "addedToDOM", "baseVal", "consolidate", "d", "f", "offsetParent", "getBoundingClientRect", "nextElement<PERSON><PERSON>ling", "insertBefore", "_applySVGO<PERSON>in", "originIsAbsolute", "smooth", "matrixArray", "pluginToAddPropTweensTo", "xOriginOld", "xOrigin", "yOriginOld", "y<PERSON><PERSON><PERSON>", "xOffsetOld", "xOffset", "yOffsetOld", "yOffset", "tx", "ty", "originSplit", "determinant", "invertedScaleX", "z", "rotation", "rotationX", "rotationY", "skewX", "skewY", "perspective", "angle", "cos", "sin", "a12", "a22", "t1", "t2", "t3", "a13", "a23", "a33", "a42", "a43", "a32", "slice", "sqrt", "abs", "forceCSS", "xPercent", "offsetWidth", "yPercent", "offsetHeight", "transformPerspective", "force3D", "_renderSVGTransforms", "_renderCSSTransforms", "_renderNon3DTransforms", "_addPxTranslate", "_zeroDeg", "_zeroPx", "_endParenthesis", "_ref", "transforms", "use3D", "_ref2", "a11", "a21", "tan", "_addRotationalPropTween", "cap", "isString", "change", "finalValue", "direction", "_assign", "source", "_addRawTransformPTs", "startCache", "exclude", "endCache", "name", "l", "side", "vars", "arguments", "init", "CSSPlugin", "register", "targetTest", "targets", "startAt", "specialProp", "relative", "isTransformRelated", "transformPropTween", "hasPriority", "inlineProps", "styles", "add", "visibility", "parseTransform", "smoothOrigin", "dep", "autoRound", "render", "get", "aliases", "getSetter", "utils", "checkPrefix", "getStyleSaver", "positionAndScale", "others", "all", "registerPlugin", "default"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/gsap/CSSPlugin.js"], "sourcesContent": ["/*!\n * CSSPlugin 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { gsap, _getProperty, _numExp, _numWithUnitExp, getUnit, _isString, _isUndefined, _renderComplexString, _relExp, _forEachName, _sortPropTweensByPriority, _colorStringFilter, _checkPlugin, _replaceRandom, _plugins, GSCache, PropTween, _config, _ticker, _round, _missingPlugin, _getSetter, _getCache, _colorExp, _parseRelative, _setDefaults, _removeLinkedListItem //for the commented-out className feature.\n} from \"./gsap-core.js\";\n\nvar _win,\n    _doc,\n    _docElement,\n    _pluginInitted,\n    _tempDiv,\n    _tempD<PERSON><PERSON><PERSON><PERSON>,\n    _recentSetter<PERSON>lugin,\n    _reverting,\n    _windowExists = function _windowExists() {\n  return typeof window !== \"undefined\";\n},\n    _transformProps = {},\n    _RAD2DEG = 180 / Math.PI,\n    _DEG2RAD = Math.PI / 180,\n    _atan2 = Math.atan2,\n    _bigNum = 1e8,\n    _capsExp = /([A-Z])/g,\n    _horizontalExp = /(left|right|width|margin|padding|x)/i,\n    _complexExp = /[\\s,\\(]\\S/,\n    _propertyAliases = {\n  autoAlpha: \"opacity,visibility\",\n  scale: \"scaleX,scaleY\",\n  alpha: \"opacity\"\n},\n    _renderCSSProp = function _renderCSSProp(ratio, data) {\n  return data.set(data.t, data.p, Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n},\n    _renderPropWithEnd = function _renderPropWithEnd(ratio, data) {\n  return data.set(data.t, data.p, ratio === 1 ? data.e : Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u, data);\n},\n    _renderCSSPropWithBeginning = function _renderCSSPropWithBeginning(ratio, data) {\n  return data.set(data.t, data.p, ratio ? Math.round((data.s + data.c * ratio) * 10000) / 10000 + data.u : data.b, data);\n},\n    //if units change, we need a way to render the original unit/value when the tween goes all the way back to the beginning (ratio:0)\n_renderRoundedCSSProp = function _renderRoundedCSSProp(ratio, data) {\n  var value = data.s + data.c * ratio;\n  data.set(data.t, data.p, ~~(value + (value < 0 ? -.5 : .5)) + data.u, data);\n},\n    _renderNonTweeningValue = function _renderNonTweeningValue(ratio, data) {\n  return data.set(data.t, data.p, ratio ? data.e : data.b, data);\n},\n    _renderNonTweeningValueOnlyAtEnd = function _renderNonTweeningValueOnlyAtEnd(ratio, data) {\n  return data.set(data.t, data.p, ratio !== 1 ? data.b : data.e, data);\n},\n    _setterCSSStyle = function _setterCSSStyle(target, property, value) {\n  return target.style[property] = value;\n},\n    _setterCSSProp = function _setterCSSProp(target, property, value) {\n  return target.style.setProperty(property, value);\n},\n    _setterTransform = function _setterTransform(target, property, value) {\n  return target._gsap[property] = value;\n},\n    _setterScale = function _setterScale(target, property, value) {\n  return target._gsap.scaleX = target._gsap.scaleY = value;\n},\n    _setterScaleWithRender = function _setterScaleWithRender(target, property, value, data, ratio) {\n  var cache = target._gsap;\n  cache.scaleX = cache.scaleY = value;\n  cache.renderTransform(ratio, cache);\n},\n    _setterTransformWithRender = function _setterTransformWithRender(target, property, value, data, ratio) {\n  var cache = target._gsap;\n  cache[property] = value;\n  cache.renderTransform(ratio, cache);\n},\n    _transformProp = \"transform\",\n    _transformOriginProp = _transformProp + \"Origin\",\n    _saveStyle = function _saveStyle(property, isNotCSS) {\n  var _this = this;\n\n  var target = this.target,\n      style = target.style,\n      cache = target._gsap;\n\n  if (property in _transformProps && style) {\n    this.tfm = this.tfm || {};\n\n    if (property !== \"transform\") {\n      property = _propertyAliases[property] || property;\n      ~property.indexOf(\",\") ? property.split(\",\").forEach(function (a) {\n        return _this.tfm[a] = _get(target, a);\n      }) : this.tfm[property] = cache.x ? cache[property] : _get(target, property); // note: scale would map to \"scaleX,scaleY\", thus we loop and apply them both.\n\n      property === _transformOriginProp && (this.tfm.zOrigin = cache.zOrigin);\n    } else {\n      return _propertyAliases.transform.split(\",\").forEach(function (p) {\n        return _saveStyle.call(_this, p, isNotCSS);\n      });\n    }\n\n    if (this.props.indexOf(_transformProp) >= 0) {\n      return;\n    }\n\n    if (cache.svg) {\n      this.svgo = target.getAttribute(\"data-svg-origin\");\n      this.props.push(_transformOriginProp, isNotCSS, \"\");\n    }\n\n    property = _transformProp;\n  }\n\n  (style || isNotCSS) && this.props.push(property, isNotCSS, style[property]);\n},\n    _removeIndependentTransforms = function _removeIndependentTransforms(style) {\n  if (style.translate) {\n    style.removeProperty(\"translate\");\n    style.removeProperty(\"scale\");\n    style.removeProperty(\"rotate\");\n  }\n},\n    _revertStyle = function _revertStyle() {\n  var props = this.props,\n      target = this.target,\n      style = target.style,\n      cache = target._gsap,\n      i,\n      p;\n\n  for (i = 0; i < props.length; i += 3) {\n    // stored like this: property, isNotCSS, value\n    if (!props[i + 1]) {\n      props[i + 2] ? style[props[i]] = props[i + 2] : style.removeProperty(props[i].substr(0, 2) === \"--\" ? props[i] : props[i].replace(_capsExp, \"-$1\").toLowerCase());\n    } else if (props[i + 1] === 2) {\n      // non-CSS value (function-based)\n      target[props[i]](props[i + 2]);\n    } else {\n      // non-CSS value (not function-based)\n      target[props[i]] = props[i + 2];\n    }\n  }\n\n  if (this.tfm) {\n    for (p in this.tfm) {\n      cache[p] = this.tfm[p];\n    }\n\n    if (cache.svg) {\n      cache.renderTransform();\n      target.setAttribute(\"data-svg-origin\", this.svgo || \"\");\n    }\n\n    i = _reverting();\n\n    if ((!i || !i.isStart) && !style[_transformProp]) {\n      _removeIndependentTransforms(style);\n\n      if (cache.zOrigin && style[_transformOriginProp]) {\n        style[_transformOriginProp] += \" \" + cache.zOrigin + \"px\"; // since we're uncaching, we must put the zOrigin back into the transformOrigin so that we can pull it out accurately when we parse again. Otherwise, we'd lose the z portion of the origin since we extract it to protect from Safari bugs.\n\n        cache.zOrigin = 0;\n        cache.renderTransform();\n      }\n\n      cache.uncache = 1; // if it's a startAt that's being reverted in the _initTween() of the core, we don't need to uncache transforms. This is purely a performance optimization.\n    }\n  }\n},\n    _getStyleSaver = function _getStyleSaver(target, properties) {\n  var saver = {\n    target: target,\n    props: [],\n    revert: _revertStyle,\n    save: _saveStyle\n  };\n  target._gsap || gsap.core.getCache(target); // just make sure there's a _gsap cache defined because we read from it in _saveStyle() and it's more efficient to just check it here once.\n\n  properties && target.style && target.nodeType && properties.split(\",\").forEach(function (p) {\n    return saver.save(p);\n  }); // make sure it's a DOM node too.\n\n  return saver;\n},\n    _supports3D,\n    _createElement = function _createElement(type, ns) {\n  var e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\n  return e && e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://gsap.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n},\n    _getComputedProperty = function _getComputedProperty(target, property, skipPrefixFallback) {\n  var cs = getComputedStyle(target);\n  return cs[property] || cs.getPropertyValue(property.replace(_capsExp, \"-$1\").toLowerCase()) || cs.getPropertyValue(property) || !skipPrefixFallback && _getComputedProperty(target, _checkPropPrefix(property) || property, 1) || \"\"; //css variables may not need caps swapped out for dashes and lowercase.\n},\n    _prefixes = \"O,Moz,ms,Ms,Webkit\".split(\",\"),\n    _checkPropPrefix = function _checkPropPrefix(property, element, preferPrefix) {\n  var e = element || _tempDiv,\n      s = e.style,\n      i = 5;\n\n  if (property in s && !preferPrefix) {\n    return property;\n  }\n\n  property = property.charAt(0).toUpperCase() + property.substr(1);\n\n  while (i-- && !(_prefixes[i] + property in s)) {}\n\n  return i < 0 ? null : (i === 3 ? \"ms\" : i >= 0 ? _prefixes[i] : \"\") + property;\n},\n    _initCore = function _initCore() {\n  if (_windowExists() && window.document) {\n    _win = window;\n    _doc = _win.document;\n    _docElement = _doc.documentElement;\n    _tempDiv = _createElement(\"div\") || {\n      style: {}\n    };\n    _tempDivStyler = _createElement(\"div\");\n    _transformProp = _checkPropPrefix(_transformProp);\n    _transformOriginProp = _transformProp + \"Origin\";\n    _tempDiv.style.cssText = \"border-width:0;line-height:0;position:absolute;padding:0\"; //make sure to override certain properties that may contaminate measurements, in case the user has overreaching style sheets.\n\n    _supports3D = !!_checkPropPrefix(\"perspective\");\n    _reverting = gsap.core.reverting;\n    _pluginInitted = 1;\n  }\n},\n    _getReparentedCloneBBox = function _getReparentedCloneBBox(target) {\n  //works around issues in some browsers (like Firefox) that don't correctly report getBBox() on SVG elements inside a <defs> element and/or <mask>. We try creating an SVG, adding it to the documentElement and toss the element in there so that it's definitely part of the rendering tree, then grab the bbox and if it works, we actually swap out the original getBBox() method for our own that does these extra steps whenever getBBox is needed. This helps ensure that performance is optimal (only do all these extra steps when absolutely necessary...most elements don't need it).\n  var owner = target.ownerSVGElement,\n      svg = _createElement(\"svg\", owner && owner.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\"),\n      clone = target.cloneNode(true),\n      bbox;\n\n  clone.style.display = \"block\";\n  svg.appendChild(clone);\n\n  _docElement.appendChild(svg);\n\n  try {\n    bbox = clone.getBBox();\n  } catch (e) {}\n\n  svg.removeChild(clone);\n\n  _docElement.removeChild(svg);\n\n  return bbox;\n},\n    _getAttributeFallbacks = function _getAttributeFallbacks(target, attributesArray) {\n  var i = attributesArray.length;\n\n  while (i--) {\n    if (target.hasAttribute(attributesArray[i])) {\n      return target.getAttribute(attributesArray[i]);\n    }\n  }\n},\n    _getBBox = function _getBBox(target) {\n  var bounds, cloned;\n\n  try {\n    bounds = target.getBBox(); //Firefox throws errors if you try calling getBBox() on an SVG element that's not rendered (like in a <symbol> or <defs>). https://bugzilla.mozilla.org/show_bug.cgi?id=612118\n  } catch (error) {\n    bounds = _getReparentedCloneBBox(target);\n    cloned = 1;\n  }\n\n  bounds && (bounds.width || bounds.height) || cloned || (bounds = _getReparentedCloneBBox(target)); //some browsers (like Firefox) misreport the bounds if the element has zero width and height (it just assumes it's at x:0, y:0), thus we need to manually grab the position in that case.\n\n  return bounds && !bounds.width && !bounds.x && !bounds.y ? {\n    x: +_getAttributeFallbacks(target, [\"x\", \"cx\", \"x1\"]) || 0,\n    y: +_getAttributeFallbacks(target, [\"y\", \"cy\", \"y1\"]) || 0,\n    width: 0,\n    height: 0\n  } : bounds;\n},\n    _isSVG = function _isSVG(e) {\n  return !!(e.getCTM && (!e.parentNode || e.ownerSVGElement) && _getBBox(e));\n},\n    //reports if the element is an SVG on which getBBox() actually works\n_removeProperty = function _removeProperty(target, property) {\n  if (property) {\n    var style = target.style,\n        first2Chars;\n\n    if (property in _transformProps && property !== _transformOriginProp) {\n      property = _transformProp;\n    }\n\n    if (style.removeProperty) {\n      first2Chars = property.substr(0, 2);\n\n      if (first2Chars === \"ms\" || property.substr(0, 6) === \"webkit\") {\n        //Microsoft and some Webkit browsers don't conform to the standard of capitalizing the first prefix character, so we adjust so that when we prefix the caps with a dash, it's correct (otherwise it'd be \"ms-transform\" instead of \"-ms-transform\" for IE9, for example)\n        property = \"-\" + property;\n      }\n\n      style.removeProperty(first2Chars === \"--\" ? property : property.replace(_capsExp, \"-$1\").toLowerCase());\n    } else {\n      //note: old versions of IE use \"removeAttribute()\" instead of \"removeProperty()\"\n      style.removeAttribute(property);\n    }\n  }\n},\n    _addNonTweeningPT = function _addNonTweeningPT(plugin, target, property, beginning, end, onlySetAtEnd) {\n  var pt = new PropTween(plugin._pt, target, property, 0, 1, onlySetAtEnd ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue);\n  plugin._pt = pt;\n  pt.b = beginning;\n  pt.e = end;\n\n  plugin._props.push(property);\n\n  return pt;\n},\n    _nonConvertibleUnits = {\n  deg: 1,\n  rad: 1,\n  turn: 1\n},\n    _nonStandardLayouts = {\n  grid: 1,\n  flex: 1\n},\n    //takes a single value like 20px and converts it to the unit specified, like \"%\", returning only the numeric amount.\n_convertToUnit = function _convertToUnit(target, property, value, unit) {\n  var curValue = parseFloat(value) || 0,\n      curUnit = (value + \"\").trim().substr((curValue + \"\").length) || \"px\",\n      // some browsers leave extra whitespace at the beginning of CSS variables, hence the need to trim()\n  style = _tempDiv.style,\n      horizontal = _horizontalExp.test(property),\n      isRootSVG = target.tagName.toLowerCase() === \"svg\",\n      measureProperty = (isRootSVG ? \"client\" : \"offset\") + (horizontal ? \"Width\" : \"Height\"),\n      amount = 100,\n      toPixels = unit === \"px\",\n      toPercent = unit === \"%\",\n      px,\n      parent,\n      cache,\n      isSVG;\n\n  if (unit === curUnit || !curValue || _nonConvertibleUnits[unit] || _nonConvertibleUnits[curUnit]) {\n    return curValue;\n  }\n\n  curUnit !== \"px\" && !toPixels && (curValue = _convertToUnit(target, property, value, \"px\"));\n  isSVG = target.getCTM && _isSVG(target);\n\n  if ((toPercent || curUnit === \"%\") && (_transformProps[property] || ~property.indexOf(\"adius\"))) {\n    px = isSVG ? target.getBBox()[horizontal ? \"width\" : \"height\"] : target[measureProperty];\n    return _round(toPercent ? curValue / px * amount : curValue / 100 * px);\n  }\n\n  style[horizontal ? \"width\" : \"height\"] = amount + (toPixels ? curUnit : unit);\n  parent = unit !== \"rem\" && ~property.indexOf(\"adius\") || unit === \"em\" && target.appendChild && !isRootSVG ? target : target.parentNode;\n\n  if (isSVG) {\n    parent = (target.ownerSVGElement || {}).parentNode;\n  }\n\n  if (!parent || parent === _doc || !parent.appendChild) {\n    parent = _doc.body;\n  }\n\n  cache = parent._gsap;\n\n  if (cache && toPercent && cache.width && horizontal && cache.time === _ticker.time && !cache.uncache) {\n    return _round(curValue / cache.width * amount);\n  } else {\n    if (toPercent && (property === \"height\" || property === \"width\")) {\n      // if we're dealing with width/height that's inside a container with padding and/or it's a flexbox/grid container, we must apply it to the target itself rather than the _tempDiv in order to ensure complete accuracy, factoring in the parent's padding.\n      var v = target.style[property];\n      target.style[property] = amount + unit;\n      px = target[measureProperty];\n      v ? target.style[property] = v : _removeProperty(target, property);\n    } else {\n      (toPercent || curUnit === \"%\") && !_nonStandardLayouts[_getComputedProperty(parent, \"display\")] && (style.position = _getComputedProperty(target, \"position\"));\n      parent === target && (style.position = \"static\"); // like for borderRadius, if it's a % we must have it relative to the target itself but that may not have position: relative or position: absolute in which case it'd go up the chain until it finds its offsetParent (bad). position: static protects against that.\n\n      parent.appendChild(_tempDiv);\n      px = _tempDiv[measureProperty];\n      parent.removeChild(_tempDiv);\n      style.position = \"absolute\";\n    }\n\n    if (horizontal && toPercent) {\n      cache = _getCache(parent);\n      cache.time = _ticker.time;\n      cache.width = parent[measureProperty];\n    }\n  }\n\n  return _round(toPixels ? px * curValue / amount : px && curValue ? amount / px * curValue : 0);\n},\n    _get = function _get(target, property, unit, uncache) {\n  var value;\n  _pluginInitted || _initCore();\n\n  if (property in _propertyAliases && property !== \"transform\") {\n    property = _propertyAliases[property];\n\n    if (~property.indexOf(\",\")) {\n      property = property.split(\",\")[0];\n    }\n  }\n\n  if (_transformProps[property] && property !== \"transform\") {\n    value = _parseTransform(target, uncache);\n    value = property !== \"transformOrigin\" ? value[property] : value.svg ? value.origin : _firstTwoOnly(_getComputedProperty(target, _transformOriginProp)) + \" \" + value.zOrigin + \"px\";\n  } else {\n    value = target.style[property];\n\n    if (!value || value === \"auto\" || uncache || ~(value + \"\").indexOf(\"calc(\")) {\n      value = _specialProps[property] && _specialProps[property](target, property, unit) || _getComputedProperty(target, property) || _getProperty(target, property) || (property === \"opacity\" ? 1 : 0); // note: some browsers, like Firefox, don't report borderRadius correctly! Instead, it only reports every corner like  borderTopLeftRadius\n    }\n  }\n\n  return unit && !~(value + \"\").trim().indexOf(\" \") ? _convertToUnit(target, property, value, unit) + unit : value;\n},\n    _tweenComplexCSSString = function _tweenComplexCSSString(target, prop, start, end) {\n  // note: we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within a plugin too, thus \"this\" would refer to the plugin.\n  if (!start || start === \"none\") {\n    // some browsers like Safari actually PREFER the prefixed property and mis-report the unprefixed value like clipPath (BUG). In other words, even though clipPath exists in the style (\"clipPath\" in target.style) and it's set in the CSS properly (along with -webkit-clip-path), Safari reports clipPath as \"none\" whereas WebkitClipPath reports accurately like \"ellipse(100% 0% at 50% 0%)\", so in this case we must SWITCH to using the prefixed property instead. See https://gsap.com/forums/topic/18310-clippath-doesnt-work-on-ios/\n    var p = _checkPropPrefix(prop, target, 1),\n        s = p && _getComputedProperty(target, p, 1);\n\n    if (s && s !== start) {\n      prop = p;\n      start = s;\n    } else if (prop === \"borderColor\") {\n      start = _getComputedProperty(target, \"borderTopColor\"); // Firefox bug: always reports \"borderColor\" as \"\", so we must fall back to borderTopColor. See https://gsap.com/forums/topic/24583-how-to-return-colors-that-i-had-after-reverse/\n    }\n  }\n\n  var pt = new PropTween(this._pt, target.style, prop, 0, 1, _renderComplexString),\n      index = 0,\n      matchIndex = 0,\n      a,\n      result,\n      startValues,\n      startNum,\n      color,\n      startValue,\n      endValue,\n      endNum,\n      chunk,\n      endUnit,\n      startUnit,\n      endValues;\n  pt.b = start;\n  pt.e = end;\n  start += \"\"; // ensure values are strings\n\n  end += \"\";\n\n  if (end.substring(0, 6) === \"var(--\") {\n    end = _getComputedProperty(target, end.substring(4, end.indexOf(\")\")));\n  }\n\n  if (end === \"auto\") {\n    startValue = target.style[prop];\n    target.style[prop] = end;\n    end = _getComputedProperty(target, prop) || end;\n    startValue ? target.style[prop] = startValue : _removeProperty(target, prop);\n  }\n\n  a = [start, end];\n\n  _colorStringFilter(a); // pass an array with the starting and ending values and let the filter do whatever it needs to the values. If colors are found, it returns true and then we must match where the color shows up order-wise because for things like boxShadow, sometimes the browser provides the computed values with the color FIRST, but the user provides it with the color LAST, so flip them if necessary. Same for drop-shadow().\n\n\n  start = a[0];\n  end = a[1];\n  startValues = start.match(_numWithUnitExp) || [];\n  endValues = end.match(_numWithUnitExp) || [];\n\n  if (endValues.length) {\n    while (result = _numWithUnitExp.exec(end)) {\n      endValue = result[0];\n      chunk = end.substring(index, result.index);\n\n      if (color) {\n        color = (color + 1) % 5;\n      } else if (chunk.substr(-5) === \"rgba(\" || chunk.substr(-5) === \"hsla(\") {\n        color = 1;\n      }\n\n      if (endValue !== (startValue = startValues[matchIndex++] || \"\")) {\n        startNum = parseFloat(startValue) || 0;\n        startUnit = startValue.substr((startNum + \"\").length);\n        endValue.charAt(1) === \"=\" && (endValue = _parseRelative(startNum, endValue) + startUnit);\n        endNum = parseFloat(endValue);\n        endUnit = endValue.substr((endNum + \"\").length);\n        index = _numWithUnitExp.lastIndex - endUnit.length;\n\n        if (!endUnit) {\n          //if something like \"perspective:300\" is passed in and we must add a unit to the end\n          endUnit = endUnit || _config.units[prop] || startUnit;\n\n          if (index === end.length) {\n            end += endUnit;\n            pt.e += endUnit;\n          }\n        }\n\n        if (startUnit !== endUnit) {\n          startNum = _convertToUnit(target, prop, startValue, endUnit) || 0;\n        } // these nested PropTweens are handled in a special way - we'll never actually call a render or setter method on them. We'll just loop through them in the parent complex string PropTween's render method.\n\n\n        pt._pt = {\n          _next: pt._pt,\n          p: chunk || matchIndex === 1 ? chunk : \",\",\n          //note: SVG spec allows omission of comma/space when a negative sign is wedged between two numbers, like 2.5-5.3 instead of 2.5,-5.3 but when tweening, the negative value may switch to positive, so we insert the comma just in case.\n          s: startNum,\n          c: endNum - startNum,\n          m: color && color < 4 || prop === \"zIndex\" ? Math.round : 0\n        };\n      }\n    }\n\n    pt.c = index < end.length ? end.substring(index, end.length) : \"\"; //we use the \"c\" of the PropTween to store the final part of the string (after the last number)\n  } else {\n    pt.r = prop === \"display\" && end === \"none\" ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue;\n  }\n\n  _relExp.test(end) && (pt.e = 0); //if the end string contains relative values or dynamic random(...) values, delete the end it so that on the final render we don't actually set it to the string with += or -= characters (forces it to use the calculated value).\n\n  this._pt = pt; //start the linked list with this new PropTween. Remember, we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within another plugin too, thus \"this\" would refer to the plugin.\n\n  return pt;\n},\n    _keywordToPercent = {\n  top: \"0%\",\n  bottom: \"100%\",\n  left: \"0%\",\n  right: \"100%\",\n  center: \"50%\"\n},\n    _convertKeywordsToPercentages = function _convertKeywordsToPercentages(value) {\n  var split = value.split(\" \"),\n      x = split[0],\n      y = split[1] || \"50%\";\n\n  if (x === \"top\" || x === \"bottom\" || y === \"left\" || y === \"right\") {\n    //the user provided them in the wrong order, so flip them\n    value = x;\n    x = y;\n    y = value;\n  }\n\n  split[0] = _keywordToPercent[x] || x;\n  split[1] = _keywordToPercent[y] || y;\n  return split.join(\" \");\n},\n    _renderClearProps = function _renderClearProps(ratio, data) {\n  if (data.tween && data.tween._time === data.tween._dur) {\n    var target = data.t,\n        style = target.style,\n        props = data.u,\n        cache = target._gsap,\n        prop,\n        clearTransforms,\n        i;\n\n    if (props === \"all\" || props === true) {\n      style.cssText = \"\";\n      clearTransforms = 1;\n    } else {\n      props = props.split(\",\");\n      i = props.length;\n\n      while (--i > -1) {\n        prop = props[i];\n\n        if (_transformProps[prop]) {\n          clearTransforms = 1;\n          prop = prop === \"transformOrigin\" ? _transformOriginProp : _transformProp;\n        }\n\n        _removeProperty(target, prop);\n      }\n    }\n\n    if (clearTransforms) {\n      _removeProperty(target, _transformProp);\n\n      if (cache) {\n        cache.svg && target.removeAttribute(\"transform\");\n        style.scale = style.rotate = style.translate = \"none\";\n\n        _parseTransform(target, 1); // force all the cached values back to \"normal\"/identity, otherwise if there's another tween that's already set to render transforms on this element, it could display the wrong values.\n\n\n        cache.uncache = 1;\n\n        _removeIndependentTransforms(style);\n      }\n    }\n  }\n},\n    // note: specialProps should return 1 if (and only if) they have a non-zero priority. It indicates we need to sort the linked list.\n_specialProps = {\n  clearProps: function clearProps(plugin, target, property, endValue, tween) {\n    if (tween.data !== \"isFromStart\") {\n      var pt = plugin._pt = new PropTween(plugin._pt, target, property, 0, 0, _renderClearProps);\n      pt.u = endValue;\n      pt.pr = -10;\n      pt.tween = tween;\n\n      plugin._props.push(property);\n\n      return 1;\n    }\n  }\n  /* className feature (about 0.4kb gzipped).\n  , className(plugin, target, property, endValue, tween) {\n  \tlet _renderClassName = (ratio, data) => {\n  \t\t\tdata.css.render(ratio, data.css);\n  \t\t\tif (!ratio || ratio === 1) {\n  \t\t\t\tlet inline = data.rmv,\n  \t\t\t\t\ttarget = data.t,\n  \t\t\t\t\tp;\n  \t\t\t\ttarget.setAttribute(\"class\", ratio ? data.e : data.b);\n  \t\t\t\tfor (p in inline) {\n  \t\t\t\t\t_removeProperty(target, p);\n  \t\t\t\t}\n  \t\t\t}\n  \t\t},\n  \t\t_getAllStyles = (target) => {\n  \t\t\tlet styles = {},\n  \t\t\t\tcomputed = getComputedStyle(target),\n  \t\t\t\tp;\n  \t\t\tfor (p in computed) {\n  \t\t\t\tif (isNaN(p) && p !== \"cssText\" && p !== \"length\") {\n  \t\t\t\t\tstyles[p] = computed[p];\n  \t\t\t\t}\n  \t\t\t}\n  \t\t\t_setDefaults(styles, _parseTransform(target, 1));\n  \t\t\treturn styles;\n  \t\t},\n  \t\tstartClassList = target.getAttribute(\"class\"),\n  \t\tstyle = target.style,\n  \t\tcssText = style.cssText,\n  \t\tcache = target._gsap,\n  \t\tclassPT = cache.classPT,\n  \t\tinlineToRemoveAtEnd = {},\n  \t\tdata = {t:target, plugin:plugin, rmv:inlineToRemoveAtEnd, b:startClassList, e:(endValue.charAt(1) !== \"=\") ? endValue : startClassList.replace(new RegExp(\"(?:\\\\s|^)\" + endValue.substr(2) + \"(?![\\\\w-])\"), \"\") + ((endValue.charAt(0) === \"+\") ? \" \" + endValue.substr(2) : \"\")},\n  \t\tchangingVars = {},\n  \t\tstartVars = _getAllStyles(target),\n  \t\ttransformRelated = /(transform|perspective)/i,\n  \t\tendVars, p;\n  \tif (classPT) {\n  \t\tclassPT.r(1, classPT.d);\n  \t\t_removeLinkedListItem(classPT.d.plugin, classPT, \"_pt\");\n  \t}\n  \ttarget.setAttribute(\"class\", data.e);\n  \tendVars = _getAllStyles(target, true);\n  \ttarget.setAttribute(\"class\", startClassList);\n  \tfor (p in endVars) {\n  \t\tif (endVars[p] !== startVars[p] && !transformRelated.test(p)) {\n  \t\t\tchangingVars[p] = endVars[p];\n  \t\t\tif (!style[p] && style[p] !== \"0\") {\n  \t\t\t\tinlineToRemoveAtEnd[p] = 1;\n  \t\t\t}\n  \t\t}\n  \t}\n  \tcache.classPT = plugin._pt = new PropTween(plugin._pt, target, \"className\", 0, 0, _renderClassName, data, 0, -11);\n  \tif (style.cssText !== cssText) { //only apply if things change. Otherwise, in cases like a background-image that's pulled dynamically, it could cause a refresh. See https://gsap.com/forums/topic/20368-possible-gsap-bug-switching-classnames-in-chrome/.\n  \t\tstyle.cssText = cssText; //we recorded cssText before we swapped classes and ran _getAllStyles() because in cases when a className tween is overwritten, we remove all the related tweening properties from that class change (otherwise class-specific stuff can't override properties we've directly set on the target's style object due to specificity).\n  \t}\n  \t_parseTransform(target, true); //to clear the caching of transforms\n  \tdata.css = new gsap.plugins.css();\n  \tdata.css.init(target, changingVars, tween);\n  \tplugin._props.push(...data.css._props);\n  \treturn 1;\n  }\n  */\n\n},\n\n/*\n * --------------------------------------------------------------------------------------\n * TRANSFORMS\n * --------------------------------------------------------------------------------------\n */\n_identity2DMatrix = [1, 0, 0, 1, 0, 0],\n    _rotationalProperties = {},\n    _isNullTransform = function _isNullTransform(value) {\n  return value === \"matrix(1, 0, 0, 1, 0, 0)\" || value === \"none\" || !value;\n},\n    _getComputedTransformMatrixAsArray = function _getComputedTransformMatrixAsArray(target) {\n  var matrixString = _getComputedProperty(target, _transformProp);\n\n  return _isNullTransform(matrixString) ? _identity2DMatrix : matrixString.substr(7).match(_numExp).map(_round);\n},\n    _getMatrix = function _getMatrix(target, force2D) {\n  var cache = target._gsap || _getCache(target),\n      style = target.style,\n      matrix = _getComputedTransformMatrixAsArray(target),\n      parent,\n      nextSibling,\n      temp,\n      addedToDOM;\n\n  if (cache.svg && target.getAttribute(\"transform\")) {\n    temp = target.transform.baseVal.consolidate().matrix; //ensures that even complex values like \"translate(50,60) rotate(135,0,0)\" are parsed because it mashes it into a matrix.\n\n    matrix = [temp.a, temp.b, temp.c, temp.d, temp.e, temp.f];\n    return matrix.join(\",\") === \"1,0,0,1,0,0\" ? _identity2DMatrix : matrix;\n  } else if (matrix === _identity2DMatrix && !target.offsetParent && target !== _docElement && !cache.svg) {\n    //note: if offsetParent is null, that means the element isn't in the normal document flow, like if it has display:none or one of its ancestors has display:none). Firefox returns null for getComputedStyle() if the element is in an iframe that has display:none. https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n    //browsers don't report transforms accurately unless the element is in the DOM and has a display value that's not \"none\". Firefox and Microsoft browsers have a partial bug where they'll report transforms even if display:none BUT not any percentage-based values like translate(-50%, 8px) will be reported as if it's translate(0, 8px).\n    temp = style.display;\n    style.display = \"block\";\n    parent = target.parentNode;\n\n    if (!parent || !target.offsetParent && !target.getBoundingClientRect().width) {\n      // note: in 3.3.0 we switched target.offsetParent to _doc.body.contains(target) to avoid [sometimes unnecessary] MutationObserver calls but that wasn't adequate because there are edge cases where nested position: fixed elements need to get reparented to accurately sense transforms. See https://github.com/greensock/GSAP/issues/388 and https://github.com/greensock/GSAP/issues/375. Note: position: fixed elements report a null offsetParent but they could also be invisible because they're in an ancestor with display: none, so we check getBoundingClientRect(). We only want to alter the DOM if we absolutely have to because it can cause iframe content to reload, like a Vimeo video.\n      addedToDOM = 1; //flag\n\n      nextSibling = target.nextElementSibling;\n\n      _docElement.appendChild(target); //we must add it to the DOM in order to get values properly\n\n    }\n\n    matrix = _getComputedTransformMatrixAsArray(target);\n    temp ? style.display = temp : _removeProperty(target, \"display\");\n\n    if (addedToDOM) {\n      nextSibling ? parent.insertBefore(target, nextSibling) : parent ? parent.appendChild(target) : _docElement.removeChild(target);\n    }\n  }\n\n  return force2D && matrix.length > 6 ? [matrix[0], matrix[1], matrix[4], matrix[5], matrix[12], matrix[13]] : matrix;\n},\n    _applySVGOrigin = function _applySVGOrigin(target, origin, originIsAbsolute, smooth, matrixArray, pluginToAddPropTweensTo) {\n  var cache = target._gsap,\n      matrix = matrixArray || _getMatrix(target, true),\n      xOriginOld = cache.xOrigin || 0,\n      yOriginOld = cache.yOrigin || 0,\n      xOffsetOld = cache.xOffset || 0,\n      yOffsetOld = cache.yOffset || 0,\n      a = matrix[0],\n      b = matrix[1],\n      c = matrix[2],\n      d = matrix[3],\n      tx = matrix[4],\n      ty = matrix[5],\n      originSplit = origin.split(\" \"),\n      xOrigin = parseFloat(originSplit[0]) || 0,\n      yOrigin = parseFloat(originSplit[1]) || 0,\n      bounds,\n      determinant,\n      x,\n      y;\n\n  if (!originIsAbsolute) {\n    bounds = _getBBox(target);\n    xOrigin = bounds.x + (~originSplit[0].indexOf(\"%\") ? xOrigin / 100 * bounds.width : xOrigin);\n    yOrigin = bounds.y + (~(originSplit[1] || originSplit[0]).indexOf(\"%\") ? yOrigin / 100 * bounds.height : yOrigin); // if (!(\"xOrigin\" in cache) && (xOrigin || yOrigin)) { // added in 3.12.3, reverted in 3.12.4; requires more exploration\n    // \txOrigin -= bounds.x;\n    // \tyOrigin -= bounds.y;\n    // }\n  } else if (matrix !== _identity2DMatrix && (determinant = a * d - b * c)) {\n    //if it's zero (like if scaleX and scaleY are zero), skip it to avoid errors with dividing by zero.\n    x = xOrigin * (d / determinant) + yOrigin * (-c / determinant) + (c * ty - d * tx) / determinant;\n    y = xOrigin * (-b / determinant) + yOrigin * (a / determinant) - (a * ty - b * tx) / determinant;\n    xOrigin = x;\n    yOrigin = y; // theory: we only had to do this for smoothing and it assumes that the previous one was not originIsAbsolute.\n  }\n\n  if (smooth || smooth !== false && cache.smooth) {\n    tx = xOrigin - xOriginOld;\n    ty = yOrigin - yOriginOld;\n    cache.xOffset = xOffsetOld + (tx * a + ty * c) - tx;\n    cache.yOffset = yOffsetOld + (tx * b + ty * d) - ty;\n  } else {\n    cache.xOffset = cache.yOffset = 0;\n  }\n\n  cache.xOrigin = xOrigin;\n  cache.yOrigin = yOrigin;\n  cache.smooth = !!smooth;\n  cache.origin = origin;\n  cache.originIsAbsolute = !!originIsAbsolute;\n  target.style[_transformOriginProp] = \"0px 0px\"; //otherwise, if someone sets  an origin via CSS, it will likely interfere with the SVG transform attribute ones (because remember, we're baking the origin into the matrix() value).\n\n  if (pluginToAddPropTweensTo) {\n    _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOrigin\", xOriginOld, xOrigin);\n\n    _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOrigin\", yOriginOld, yOrigin);\n\n    _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOffset\", xOffsetOld, cache.xOffset);\n\n    _addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOffset\", yOffsetOld, cache.yOffset);\n  }\n\n  target.setAttribute(\"data-svg-origin\", xOrigin + \" \" + yOrigin);\n},\n    _parseTransform = function _parseTransform(target, uncache) {\n  var cache = target._gsap || new GSCache(target);\n\n  if (\"x\" in cache && !uncache && !cache.uncache) {\n    return cache;\n  }\n\n  var style = target.style,\n      invertedScaleX = cache.scaleX < 0,\n      px = \"px\",\n      deg = \"deg\",\n      cs = getComputedStyle(target),\n      origin = _getComputedProperty(target, _transformOriginProp) || \"0\",\n      x,\n      y,\n      z,\n      scaleX,\n      scaleY,\n      rotation,\n      rotationX,\n      rotationY,\n      skewX,\n      skewY,\n      perspective,\n      xOrigin,\n      yOrigin,\n      matrix,\n      angle,\n      cos,\n      sin,\n      a,\n      b,\n      c,\n      d,\n      a12,\n      a22,\n      t1,\n      t2,\n      t3,\n      a13,\n      a23,\n      a33,\n      a42,\n      a43,\n      a32;\n  x = y = z = rotation = rotationX = rotationY = skewX = skewY = perspective = 0;\n  scaleX = scaleY = 1;\n  cache.svg = !!(target.getCTM && _isSVG(target));\n\n  if (cs.translate) {\n    // accommodate independent transforms by combining them into normal ones.\n    if (cs.translate !== \"none\" || cs.scale !== \"none\" || cs.rotate !== \"none\") {\n      style[_transformProp] = (cs.translate !== \"none\" ? \"translate3d(\" + (cs.translate + \" 0 0\").split(\" \").slice(0, 3).join(\", \") + \") \" : \"\") + (cs.rotate !== \"none\" ? \"rotate(\" + cs.rotate + \") \" : \"\") + (cs.scale !== \"none\" ? \"scale(\" + cs.scale.split(\" \").join(\",\") + \") \" : \"\") + (cs[_transformProp] !== \"none\" ? cs[_transformProp] : \"\");\n    }\n\n    style.scale = style.rotate = style.translate = \"none\";\n  }\n\n  matrix = _getMatrix(target, cache.svg);\n\n  if (cache.svg) {\n    if (cache.uncache) {\n      // if cache.uncache is true (and maybe if origin is 0,0), we need to set element.style.transformOrigin = (cache.xOrigin - bbox.x) + \"px \" + (cache.yOrigin - bbox.y) + \"px\". Previously we let the data-svg-origin stay instead, but when introducing revert(), it complicated things.\n      t2 = target.getBBox();\n      origin = cache.xOrigin - t2.x + \"px \" + (cache.yOrigin - t2.y) + \"px\";\n      t1 = \"\";\n    } else {\n      t1 = !uncache && target.getAttribute(\"data-svg-origin\"); //  Remember, to work around browser inconsistencies we always force SVG elements' transformOrigin to 0,0 and offset the translation accordingly.\n    }\n\n    _applySVGOrigin(target, t1 || origin, !!t1 || cache.originIsAbsolute, cache.smooth !== false, matrix);\n  }\n\n  xOrigin = cache.xOrigin || 0;\n  yOrigin = cache.yOrigin || 0;\n\n  if (matrix !== _identity2DMatrix) {\n    a = matrix[0]; //a11\n\n    b = matrix[1]; //a21\n\n    c = matrix[2]; //a31\n\n    d = matrix[3]; //a41\n\n    x = a12 = matrix[4];\n    y = a22 = matrix[5]; //2D matrix\n\n    if (matrix.length === 6) {\n      scaleX = Math.sqrt(a * a + b * b);\n      scaleY = Math.sqrt(d * d + c * c);\n      rotation = a || b ? _atan2(b, a) * _RAD2DEG : 0; //note: if scaleX is 0, we cannot accurately measure rotation. Same for skewX with a scaleY of 0. Therefore, we default to the previously recorded value (or zero if that doesn't exist).\n\n      skewX = c || d ? _atan2(c, d) * _RAD2DEG + rotation : 0;\n      skewX && (scaleY *= Math.abs(Math.cos(skewX * _DEG2RAD)));\n\n      if (cache.svg) {\n        x -= xOrigin - (xOrigin * a + yOrigin * c);\n        y -= yOrigin - (xOrigin * b + yOrigin * d);\n      } //3D matrix\n\n    } else {\n      a32 = matrix[6];\n      a42 = matrix[7];\n      a13 = matrix[8];\n      a23 = matrix[9];\n      a33 = matrix[10];\n      a43 = matrix[11];\n      x = matrix[12];\n      y = matrix[13];\n      z = matrix[14];\n      angle = _atan2(a32, a33);\n      rotationX = angle * _RAD2DEG; //rotationX\n\n      if (angle) {\n        cos = Math.cos(-angle);\n        sin = Math.sin(-angle);\n        t1 = a12 * cos + a13 * sin;\n        t2 = a22 * cos + a23 * sin;\n        t3 = a32 * cos + a33 * sin;\n        a13 = a12 * -sin + a13 * cos;\n        a23 = a22 * -sin + a23 * cos;\n        a33 = a32 * -sin + a33 * cos;\n        a43 = a42 * -sin + a43 * cos;\n        a12 = t1;\n        a22 = t2;\n        a32 = t3;\n      } //rotationY\n\n\n      angle = _atan2(-c, a33);\n      rotationY = angle * _RAD2DEG;\n\n      if (angle) {\n        cos = Math.cos(-angle);\n        sin = Math.sin(-angle);\n        t1 = a * cos - a13 * sin;\n        t2 = b * cos - a23 * sin;\n        t3 = c * cos - a33 * sin;\n        a43 = d * sin + a43 * cos;\n        a = t1;\n        b = t2;\n        c = t3;\n      } //rotationZ\n\n\n      angle = _atan2(b, a);\n      rotation = angle * _RAD2DEG;\n\n      if (angle) {\n        cos = Math.cos(angle);\n        sin = Math.sin(angle);\n        t1 = a * cos + b * sin;\n        t2 = a12 * cos + a22 * sin;\n        b = b * cos - a * sin;\n        a22 = a22 * cos - a12 * sin;\n        a = t1;\n        a12 = t2;\n      }\n\n      if (rotationX && Math.abs(rotationX) + Math.abs(rotation) > 359.9) {\n        //when rotationY is set, it will often be parsed as 180 degrees different than it should be, and rotationX and rotation both being 180 (it looks the same), so we adjust for that here.\n        rotationX = rotation = 0;\n        rotationY = 180 - rotationY;\n      }\n\n      scaleX = _round(Math.sqrt(a * a + b * b + c * c));\n      scaleY = _round(Math.sqrt(a22 * a22 + a32 * a32));\n      angle = _atan2(a12, a22);\n      skewX = Math.abs(angle) > 0.0002 ? angle * _RAD2DEG : 0;\n      perspective = a43 ? 1 / (a43 < 0 ? -a43 : a43) : 0;\n    }\n\n    if (cache.svg) {\n      //sense if there are CSS transforms applied on an SVG element in which case we must overwrite them when rendering. The transform attribute is more reliable cross-browser, but we can't just remove the CSS ones because they may be applied in a CSS rule somewhere (not just inline).\n      t1 = target.getAttribute(\"transform\");\n      cache.forceCSS = target.setAttribute(\"transform\", \"\") || !_isNullTransform(_getComputedProperty(target, _transformProp));\n      t1 && target.setAttribute(\"transform\", t1);\n    }\n  }\n\n  if (Math.abs(skewX) > 90 && Math.abs(skewX) < 270) {\n    if (invertedScaleX) {\n      scaleX *= -1;\n      skewX += rotation <= 0 ? 180 : -180;\n      rotation += rotation <= 0 ? 180 : -180;\n    } else {\n      scaleY *= -1;\n      skewX += skewX <= 0 ? 180 : -180;\n    }\n  }\n\n  uncache = uncache || cache.uncache;\n  cache.x = x - ((cache.xPercent = x && (!uncache && cache.xPercent || (Math.round(target.offsetWidth / 2) === Math.round(-x) ? -50 : 0))) ? target.offsetWidth * cache.xPercent / 100 : 0) + px;\n  cache.y = y - ((cache.yPercent = y && (!uncache && cache.yPercent || (Math.round(target.offsetHeight / 2) === Math.round(-y) ? -50 : 0))) ? target.offsetHeight * cache.yPercent / 100 : 0) + px;\n  cache.z = z + px;\n  cache.scaleX = _round(scaleX);\n  cache.scaleY = _round(scaleY);\n  cache.rotation = _round(rotation) + deg;\n  cache.rotationX = _round(rotationX) + deg;\n  cache.rotationY = _round(rotationY) + deg;\n  cache.skewX = skewX + deg;\n  cache.skewY = skewY + deg;\n  cache.transformPerspective = perspective + px;\n\n  if (cache.zOrigin = parseFloat(origin.split(\" \")[2]) || !uncache && cache.zOrigin || 0) {\n    style[_transformOriginProp] = _firstTwoOnly(origin);\n  }\n\n  cache.xOffset = cache.yOffset = 0;\n  cache.force3D = _config.force3D;\n  cache.renderTransform = cache.svg ? _renderSVGTransforms : _supports3D ? _renderCSSTransforms : _renderNon3DTransforms;\n  cache.uncache = 0;\n  return cache;\n},\n    _firstTwoOnly = function _firstTwoOnly(value) {\n  return (value = value.split(\" \"))[0] + \" \" + value[1];\n},\n    //for handling transformOrigin values, stripping out the 3rd dimension\n_addPxTranslate = function _addPxTranslate(target, start, value) {\n  var unit = getUnit(start);\n  return _round(parseFloat(start) + parseFloat(_convertToUnit(target, \"x\", value + \"px\", unit))) + unit;\n},\n    _renderNon3DTransforms = function _renderNon3DTransforms(ratio, cache) {\n  cache.z = \"0px\";\n  cache.rotationY = cache.rotationX = \"0deg\";\n  cache.force3D = 0;\n\n  _renderCSSTransforms(ratio, cache);\n},\n    _zeroDeg = \"0deg\",\n    _zeroPx = \"0px\",\n    _endParenthesis = \") \",\n    _renderCSSTransforms = function _renderCSSTransforms(ratio, cache) {\n  var _ref = cache || this,\n      xPercent = _ref.xPercent,\n      yPercent = _ref.yPercent,\n      x = _ref.x,\n      y = _ref.y,\n      z = _ref.z,\n      rotation = _ref.rotation,\n      rotationY = _ref.rotationY,\n      rotationX = _ref.rotationX,\n      skewX = _ref.skewX,\n      skewY = _ref.skewY,\n      scaleX = _ref.scaleX,\n      scaleY = _ref.scaleY,\n      transformPerspective = _ref.transformPerspective,\n      force3D = _ref.force3D,\n      target = _ref.target,\n      zOrigin = _ref.zOrigin,\n      transforms = \"\",\n      use3D = force3D === \"auto\" && ratio && ratio !== 1 || force3D === true; // Safari has a bug that causes it not to render 3D transform-origin values properly, so we force the z origin to 0, record it in the cache, and then do the math here to offset the translate values accordingly (basically do the 3D transform-origin part manually)\n\n\n  if (zOrigin && (rotationX !== _zeroDeg || rotationY !== _zeroDeg)) {\n    var angle = parseFloat(rotationY) * _DEG2RAD,\n        a13 = Math.sin(angle),\n        a33 = Math.cos(angle),\n        cos;\n\n    angle = parseFloat(rotationX) * _DEG2RAD;\n    cos = Math.cos(angle);\n    x = _addPxTranslate(target, x, a13 * cos * -zOrigin);\n    y = _addPxTranslate(target, y, -Math.sin(angle) * -zOrigin);\n    z = _addPxTranslate(target, z, a33 * cos * -zOrigin + zOrigin);\n  }\n\n  if (transformPerspective !== _zeroPx) {\n    transforms += \"perspective(\" + transformPerspective + _endParenthesis;\n  }\n\n  if (xPercent || yPercent) {\n    transforms += \"translate(\" + xPercent + \"%, \" + yPercent + \"%) \";\n  }\n\n  if (use3D || x !== _zeroPx || y !== _zeroPx || z !== _zeroPx) {\n    transforms += z !== _zeroPx || use3D ? \"translate3d(\" + x + \", \" + y + \", \" + z + \") \" : \"translate(\" + x + \", \" + y + _endParenthesis;\n  }\n\n  if (rotation !== _zeroDeg) {\n    transforms += \"rotate(\" + rotation + _endParenthesis;\n  }\n\n  if (rotationY !== _zeroDeg) {\n    transforms += \"rotateY(\" + rotationY + _endParenthesis;\n  }\n\n  if (rotationX !== _zeroDeg) {\n    transforms += \"rotateX(\" + rotationX + _endParenthesis;\n  }\n\n  if (skewX !== _zeroDeg || skewY !== _zeroDeg) {\n    transforms += \"skew(\" + skewX + \", \" + skewY + _endParenthesis;\n  }\n\n  if (scaleX !== 1 || scaleY !== 1) {\n    transforms += \"scale(\" + scaleX + \", \" + scaleY + _endParenthesis;\n  }\n\n  target.style[_transformProp] = transforms || \"translate(0, 0)\";\n},\n    _renderSVGTransforms = function _renderSVGTransforms(ratio, cache) {\n  var _ref2 = cache || this,\n      xPercent = _ref2.xPercent,\n      yPercent = _ref2.yPercent,\n      x = _ref2.x,\n      y = _ref2.y,\n      rotation = _ref2.rotation,\n      skewX = _ref2.skewX,\n      skewY = _ref2.skewY,\n      scaleX = _ref2.scaleX,\n      scaleY = _ref2.scaleY,\n      target = _ref2.target,\n      xOrigin = _ref2.xOrigin,\n      yOrigin = _ref2.yOrigin,\n      xOffset = _ref2.xOffset,\n      yOffset = _ref2.yOffset,\n      forceCSS = _ref2.forceCSS,\n      tx = parseFloat(x),\n      ty = parseFloat(y),\n      a11,\n      a21,\n      a12,\n      a22,\n      temp;\n\n  rotation = parseFloat(rotation);\n  skewX = parseFloat(skewX);\n  skewY = parseFloat(skewY);\n\n  if (skewY) {\n    //for performance reasons, we combine all skewing into the skewX and rotation values. Remember, a skewY of 10 degrees looks the same as a rotation of 10 degrees plus a skewX of 10 degrees.\n    skewY = parseFloat(skewY);\n    skewX += skewY;\n    rotation += skewY;\n  }\n\n  if (rotation || skewX) {\n    rotation *= _DEG2RAD;\n    skewX *= _DEG2RAD;\n    a11 = Math.cos(rotation) * scaleX;\n    a21 = Math.sin(rotation) * scaleX;\n    a12 = Math.sin(rotation - skewX) * -scaleY;\n    a22 = Math.cos(rotation - skewX) * scaleY;\n\n    if (skewX) {\n      skewY *= _DEG2RAD;\n      temp = Math.tan(skewX - skewY);\n      temp = Math.sqrt(1 + temp * temp);\n      a12 *= temp;\n      a22 *= temp;\n\n      if (skewY) {\n        temp = Math.tan(skewY);\n        temp = Math.sqrt(1 + temp * temp);\n        a11 *= temp;\n        a21 *= temp;\n      }\n    }\n\n    a11 = _round(a11);\n    a21 = _round(a21);\n    a12 = _round(a12);\n    a22 = _round(a22);\n  } else {\n    a11 = scaleX;\n    a22 = scaleY;\n    a21 = a12 = 0;\n  }\n\n  if (tx && !~(x + \"\").indexOf(\"px\") || ty && !~(y + \"\").indexOf(\"px\")) {\n    tx = _convertToUnit(target, \"x\", x, \"px\");\n    ty = _convertToUnit(target, \"y\", y, \"px\");\n  }\n\n  if (xOrigin || yOrigin || xOffset || yOffset) {\n    tx = _round(tx + xOrigin - (xOrigin * a11 + yOrigin * a12) + xOffset);\n    ty = _round(ty + yOrigin - (xOrigin * a21 + yOrigin * a22) + yOffset);\n  }\n\n  if (xPercent || yPercent) {\n    //The SVG spec doesn't support percentage-based translation in the \"transform\" attribute, so we merge it into the translation to simulate it.\n    temp = target.getBBox();\n    tx = _round(tx + xPercent / 100 * temp.width);\n    ty = _round(ty + yPercent / 100 * temp.height);\n  }\n\n  temp = \"matrix(\" + a11 + \",\" + a21 + \",\" + a12 + \",\" + a22 + \",\" + tx + \",\" + ty + \")\";\n  target.setAttribute(\"transform\", temp);\n  forceCSS && (target.style[_transformProp] = temp); //some browsers prioritize CSS transforms over the transform attribute. When we sense that the user has CSS transforms applied, we must overwrite them this way (otherwise some browser simply won't render the transform attribute changes!)\n},\n    _addRotationalPropTween = function _addRotationalPropTween(plugin, target, property, startNum, endValue) {\n  var cap = 360,\n      isString = _isString(endValue),\n      endNum = parseFloat(endValue) * (isString && ~endValue.indexOf(\"rad\") ? _RAD2DEG : 1),\n      change = endNum - startNum,\n      finalValue = startNum + change + \"deg\",\n      direction,\n      pt;\n\n  if (isString) {\n    direction = endValue.split(\"_\")[1];\n\n    if (direction === \"short\") {\n      change %= cap;\n\n      if (change !== change % (cap / 2)) {\n        change += change < 0 ? cap : -cap;\n      }\n    }\n\n    if (direction === \"cw\" && change < 0) {\n      change = (change + cap * _bigNum) % cap - ~~(change / cap) * cap;\n    } else if (direction === \"ccw\" && change > 0) {\n      change = (change - cap * _bigNum) % cap - ~~(change / cap) * cap;\n    }\n  }\n\n  plugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n  pt.e = finalValue;\n  pt.u = \"deg\";\n\n  plugin._props.push(property);\n\n  return pt;\n},\n    _assign = function _assign(target, source) {\n  // Internet Explorer doesn't have Object.assign(), so we recreate it here.\n  for (var p in source) {\n    target[p] = source[p];\n  }\n\n  return target;\n},\n    _addRawTransformPTs = function _addRawTransformPTs(plugin, transforms, target) {\n  //for handling cases where someone passes in a whole transform string, like transform: \"scale(2, 3) rotate(20deg) translateY(30em)\"\n  var startCache = _assign({}, target._gsap),\n      exclude = \"perspective,force3D,transformOrigin,svgOrigin\",\n      style = target.style,\n      endCache,\n      p,\n      startValue,\n      endValue,\n      startNum,\n      endNum,\n      startUnit,\n      endUnit;\n\n  if (startCache.svg) {\n    startValue = target.getAttribute(\"transform\");\n    target.setAttribute(\"transform\", \"\");\n    style[_transformProp] = transforms;\n    endCache = _parseTransform(target, 1);\n\n    _removeProperty(target, _transformProp);\n\n    target.setAttribute(\"transform\", startValue);\n  } else {\n    startValue = getComputedStyle(target)[_transformProp];\n    style[_transformProp] = transforms;\n    endCache = _parseTransform(target, 1);\n    style[_transformProp] = startValue;\n  }\n\n  for (p in _transformProps) {\n    startValue = startCache[p];\n    endValue = endCache[p];\n\n    if (startValue !== endValue && exclude.indexOf(p) < 0) {\n      //tweening to no perspective gives very unintuitive results - just keep the same perspective in that case.\n      startUnit = getUnit(startValue);\n      endUnit = getUnit(endValue);\n      startNum = startUnit !== endUnit ? _convertToUnit(target, p, startValue, endUnit) : parseFloat(startValue);\n      endNum = parseFloat(endValue);\n      plugin._pt = new PropTween(plugin._pt, endCache, p, startNum, endNum - startNum, _renderCSSProp);\n      plugin._pt.u = endUnit || 0;\n\n      plugin._props.push(p);\n    }\n  }\n\n  _assign(endCache, startCache);\n}; // handle splitting apart padding, margin, borderWidth, and borderRadius into their 4 components. Firefox, for example, won't report borderRadius correctly - it will only do borderTopLeftRadius and the other corners. We also want to handle paddingTop, marginLeft, borderRightWidth, etc.\n\n\n_forEachName(\"padding,margin,Width,Radius\", function (name, index) {\n  var t = \"Top\",\n      r = \"Right\",\n      b = \"Bottom\",\n      l = \"Left\",\n      props = (index < 3 ? [t, r, b, l] : [t + l, t + r, b + r, b + l]).map(function (side) {\n    return index < 2 ? name + side : \"border\" + side + name;\n  });\n\n  _specialProps[index > 1 ? \"border\" + name : name] = function (plugin, target, property, endValue, tween) {\n    var a, vars;\n\n    if (arguments.length < 4) {\n      // getter, passed target, property, and unit (from _get())\n      a = props.map(function (prop) {\n        return _get(plugin, prop, property);\n      });\n      vars = a.join(\" \");\n      return vars.split(a[0]).length === 5 ? a[0] : vars;\n    }\n\n    a = (endValue + \"\").split(\" \");\n    vars = {};\n    props.forEach(function (prop, i) {\n      return vars[prop] = a[i] = a[i] || a[(i - 1) / 2 | 0];\n    });\n    plugin.init(target, vars, tween);\n  };\n});\n\nexport var CSSPlugin = {\n  name: \"css\",\n  register: _initCore,\n  targetTest: function targetTest(target) {\n    return target.style && target.nodeType;\n  },\n  init: function init(target, vars, tween, index, targets) {\n    var props = this._props,\n        style = target.style,\n        startAt = tween.vars.startAt,\n        startValue,\n        endValue,\n        endNum,\n        startNum,\n        type,\n        specialProp,\n        p,\n        startUnit,\n        endUnit,\n        relative,\n        isTransformRelated,\n        transformPropTween,\n        cache,\n        smooth,\n        hasPriority,\n        inlineProps;\n    _pluginInitted || _initCore(); // we may call init() multiple times on the same plugin instance, like when adding special properties, so make sure we don't overwrite the revert data or inlineProps\n\n    this.styles = this.styles || _getStyleSaver(target);\n    inlineProps = this.styles.props;\n    this.tween = tween;\n\n    for (p in vars) {\n      if (p === \"autoRound\") {\n        continue;\n      }\n\n      endValue = vars[p];\n\n      if (_plugins[p] && _checkPlugin(p, vars, tween, index, target, targets)) {\n        // plugins\n        continue;\n      }\n\n      type = typeof endValue;\n      specialProp = _specialProps[p];\n\n      if (type === \"function\") {\n        endValue = endValue.call(tween, index, target, targets);\n        type = typeof endValue;\n      }\n\n      if (type === \"string\" && ~endValue.indexOf(\"random(\")) {\n        endValue = _replaceRandom(endValue);\n      }\n\n      if (specialProp) {\n        specialProp(this, target, p, endValue, tween) && (hasPriority = 1);\n      } else if (p.substr(0, 2) === \"--\") {\n        //CSS variable\n        startValue = (getComputedStyle(target).getPropertyValue(p) + \"\").trim();\n        endValue += \"\";\n        _colorExp.lastIndex = 0;\n\n        if (!_colorExp.test(startValue)) {\n          // colors don't have units\n          startUnit = getUnit(startValue);\n          endUnit = getUnit(endValue);\n        }\n\n        endUnit ? startUnit !== endUnit && (startValue = _convertToUnit(target, p, startValue, endUnit) + endUnit) : startUnit && (endValue += startUnit);\n        this.add(style, \"setProperty\", startValue, endValue, index, targets, 0, 0, p);\n        props.push(p);\n        inlineProps.push(p, 0, style[p]);\n      } else if (type !== \"undefined\") {\n        if (startAt && p in startAt) {\n          // in case someone hard-codes a complex value as the start, like top: \"calc(2vh / 2)\". Without this, it'd use the computed value (always in px)\n          startValue = typeof startAt[p] === \"function\" ? startAt[p].call(tween, index, target, targets) : startAt[p];\n          _isString(startValue) && ~startValue.indexOf(\"random(\") && (startValue = _replaceRandom(startValue));\n          getUnit(startValue + \"\") || startValue === \"auto\" || (startValue += _config.units[p] || getUnit(_get(target, p)) || \"\"); // for cases when someone passes in a unitless value like {x: 100}; if we try setting translate(100, 0px) it won't work.\n\n          (startValue + \"\").charAt(1) === \"=\" && (startValue = _get(target, p)); // can't work with relative values\n        } else {\n          startValue = _get(target, p);\n        }\n\n        startNum = parseFloat(startValue);\n        relative = type === \"string\" && endValue.charAt(1) === \"=\" && endValue.substr(0, 2);\n        relative && (endValue = endValue.substr(2));\n        endNum = parseFloat(endValue);\n\n        if (p in _propertyAliases) {\n          if (p === \"autoAlpha\") {\n            //special case where we control the visibility along with opacity. We still allow the opacity value to pass through and get tweened.\n            if (startNum === 1 && _get(target, \"visibility\") === \"hidden\" && endNum) {\n              //if visibility is initially set to \"hidden\", we should interpret that as intent to make opacity 0 (a convenience)\n              startNum = 0;\n            }\n\n            inlineProps.push(\"visibility\", 0, style.visibility);\n\n            _addNonTweeningPT(this, style, \"visibility\", startNum ? \"inherit\" : \"hidden\", endNum ? \"inherit\" : \"hidden\", !endNum);\n          }\n\n          if (p !== \"scale\" && p !== \"transform\") {\n            p = _propertyAliases[p];\n            ~p.indexOf(\",\") && (p = p.split(\",\")[0]);\n          }\n        }\n\n        isTransformRelated = p in _transformProps; //--- TRANSFORM-RELATED ---\n\n        if (isTransformRelated) {\n          this.styles.save(p);\n\n          if (type === \"string\" && endValue.substring(0, 6) === \"var(--\") {\n            endValue = _getComputedProperty(target, endValue.substring(4, endValue.indexOf(\")\")));\n            endNum = parseFloat(endValue);\n          }\n\n          if (!transformPropTween) {\n            cache = target._gsap;\n            cache.renderTransform && !vars.parseTransform || _parseTransform(target, vars.parseTransform); // if, for example, gsap.set(... {transform:\"translateX(50vw)\"}), the _get() call doesn't parse the transform, thus cache.renderTransform won't be set yet so force the parsing of the transform here.\n\n            smooth = vars.smoothOrigin !== false && cache.smooth;\n            transformPropTween = this._pt = new PropTween(this._pt, style, _transformProp, 0, 1, cache.renderTransform, cache, 0, -1); //the first time through, create the rendering PropTween so that it runs LAST (in the linked list, we keep adding to the beginning)\n\n            transformPropTween.dep = 1; //flag it as dependent so that if things get killed/overwritten and this is the only PropTween left, we can safely kill the whole tween.\n          }\n\n          if (p === \"scale\") {\n            this._pt = new PropTween(this._pt, cache, \"scaleY\", cache.scaleY, (relative ? _parseRelative(cache.scaleY, relative + endNum) : endNum) - cache.scaleY || 0, _renderCSSProp);\n            this._pt.u = 0;\n            props.push(\"scaleY\", p);\n            p += \"X\";\n          } else if (p === \"transformOrigin\") {\n            inlineProps.push(_transformOriginProp, 0, style[_transformOriginProp]);\n            endValue = _convertKeywordsToPercentages(endValue); //in case something like \"left top\" or \"bottom right\" is passed in. Convert to percentages.\n\n            if (cache.svg) {\n              _applySVGOrigin(target, endValue, 0, smooth, 0, this);\n            } else {\n              endUnit = parseFloat(endValue.split(\" \")[2]) || 0; //handle the zOrigin separately!\n\n              endUnit !== cache.zOrigin && _addNonTweeningPT(this, cache, \"zOrigin\", cache.zOrigin, endUnit);\n\n              _addNonTweeningPT(this, style, p, _firstTwoOnly(startValue), _firstTwoOnly(endValue));\n            }\n\n            continue;\n          } else if (p === \"svgOrigin\") {\n            _applySVGOrigin(target, endValue, 1, smooth, 0, this);\n\n            continue;\n          } else if (p in _rotationalProperties) {\n            _addRotationalPropTween(this, cache, p, startNum, relative ? _parseRelative(startNum, relative + endValue) : endValue);\n\n            continue;\n          } else if (p === \"smoothOrigin\") {\n            _addNonTweeningPT(this, cache, \"smooth\", cache.smooth, endValue);\n\n            continue;\n          } else if (p === \"force3D\") {\n            cache[p] = endValue;\n            continue;\n          } else if (p === \"transform\") {\n            _addRawTransformPTs(this, endValue, target);\n\n            continue;\n          }\n        } else if (!(p in style)) {\n          p = _checkPropPrefix(p) || p;\n        }\n\n        if (isTransformRelated || (endNum || endNum === 0) && (startNum || startNum === 0) && !_complexExp.test(endValue) && p in style) {\n          startUnit = (startValue + \"\").substr((startNum + \"\").length);\n          endNum || (endNum = 0); // protect against NaN\n\n          endUnit = getUnit(endValue) || (p in _config.units ? _config.units[p] : startUnit);\n          startUnit !== endUnit && (startNum = _convertToUnit(target, p, startValue, endUnit));\n          this._pt = new PropTween(this._pt, isTransformRelated ? cache : style, p, startNum, (relative ? _parseRelative(startNum, relative + endNum) : endNum) - startNum, !isTransformRelated && (endUnit === \"px\" || p === \"zIndex\") && vars.autoRound !== false ? _renderRoundedCSSProp : _renderCSSProp);\n          this._pt.u = endUnit || 0;\n\n          if (startUnit !== endUnit && endUnit !== \"%\") {\n            //when the tween goes all the way back to the beginning, we need to revert it to the OLD/ORIGINAL value (with those units). We record that as a \"b\" (beginning) property and point to a render method that handles that. (performance optimization)\n            this._pt.b = startValue;\n            this._pt.r = _renderCSSPropWithBeginning;\n          }\n        } else if (!(p in style)) {\n          if (p in target) {\n            //maybe it's not a style - it could be a property added directly to an element in which case we'll try to animate that.\n            this.add(target, p, startValue || target[p], relative ? relative + endValue : endValue, index, targets);\n          } else if (p !== \"parseTransform\") {\n            _missingPlugin(p, endValue);\n\n            continue;\n          }\n        } else {\n          _tweenComplexCSSString.call(this, target, p, startValue, relative ? relative + endValue : endValue);\n        }\n\n        isTransformRelated || (p in style ? inlineProps.push(p, 0, style[p]) : typeof target[p] === \"function\" ? inlineProps.push(p, 2, target[p]()) : inlineProps.push(p, 1, startValue || target[p]));\n        props.push(p);\n      }\n    }\n\n    hasPriority && _sortPropTweensByPriority(this);\n  },\n  render: function render(ratio, data) {\n    if (data.tween._time || !_reverting()) {\n      var pt = data._pt;\n\n      while (pt) {\n        pt.r(ratio, pt.d);\n        pt = pt._next;\n      }\n    } else {\n      data.styles.revert();\n    }\n  },\n  get: _get,\n  aliases: _propertyAliases,\n  getSetter: function getSetter(target, property, plugin) {\n    //returns a setter function that accepts target, property, value and applies it accordingly. Remember, properties like \"x\" aren't as simple as target.style.property = value because they've got to be applied to a proxy object and then merged into a transform string in a renderer.\n    var p = _propertyAliases[property];\n    p && p.indexOf(\",\") < 0 && (property = p);\n    return property in _transformProps && property !== _transformOriginProp && (target._gsap.x || _get(target, \"x\")) ? plugin && _recentSetterPlugin === plugin ? property === \"scale\" ? _setterScale : _setterTransform : (_recentSetterPlugin = plugin || {}) && (property === \"scale\" ? _setterScaleWithRender : _setterTransformWithRender) : target.style && !_isUndefined(target.style[property]) ? _setterCSSStyle : ~property.indexOf(\"-\") ? _setterCSSProp : _getSetter(target, property);\n  },\n  core: {\n    _removeProperty: _removeProperty,\n    _getMatrix: _getMatrix\n  }\n};\ngsap.utils.checkPrefix = _checkPropPrefix;\ngsap.core.getStyleSaver = _getStyleSaver;\n\n(function (positionAndScale, rotation, others, aliases) {\n  var all = _forEachName(positionAndScale + \",\" + rotation + \",\" + others, function (name) {\n    _transformProps[name] = 1;\n  });\n\n  _forEachName(rotation, function (name) {\n    _config.units[name] = \"deg\";\n    _rotationalProperties[name] = 1;\n  });\n\n  _propertyAliases[all[13]] = positionAndScale + \",\" + rotation;\n\n  _forEachName(aliases, function (name) {\n    var split = name.split(\":\");\n    _propertyAliases[split[1]] = all[split[0]];\n  });\n})(\"x,y,z,scale,scaleX,scaleY,xPercent,yPercent\", \"rotation,rotationX,rotationY,skewX,skewY\", \"transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective\", \"0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY\");\n\n_forEachName(\"x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective\", function (name) {\n  _config.units[name] = \"px\";\n});\n\ngsap.registerPlugin(CSSPlugin);\nexport { CSSPlugin as default, _getBBox, _createElement, _checkPropPrefix as checkPrefix };"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAEC,eAAe,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,OAAO,EAAEC,YAAY,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,YAAY,EAAEC,qBAAqB,CAAC;AAAA,OAC1W,gBAAgB;AAEvB,IAAIC,IAAI;EACJC,IAAI;EACJC,WAAW;EACXC,cAAc;EACdC,QAAQ;EACRC,cAAc;EACdC,mBAAmB;EACnBC,UAAU;EACVC,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3C,OAAO,OAAOC,MAAM,KAAK,WAAW;EACtC,CAAC;EACGC,eAAe,GAAG,CAAC,CAAC;EACpBC,QAAQ,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE;EACxBC,QAAQ,GAAGF,IAAI,CAACC,EAAE,GAAG,GAAG;EACxBE,MAAM,GAAGH,IAAI,CAACI,KAAK;EACnBC,OAAO,GAAG,GAAG;EACbC,QAAQ,GAAG,UAAU;EACrBC,cAAc,GAAG,sCAAsC;EACvDC,WAAW,GAAG,WAAW;EACzBC,gBAAgB,GAAG;IACrBC,SAAS,EAAE,oBAAoB;IAC/BC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE;EACT,CAAC;EACGC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACxD,OAAOA,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAElB,IAAI,CAACmB,KAAK,CAAC,CAACJ,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,CAAC,GAAGP,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACO,CAAC,EAAEP,IAAI,CAAC;EACvG,CAAC;EACGQ,kBAAkB,GAAG,SAASA,kBAAkBA,CAACT,KAAK,EAAEC,IAAI,EAAE;IAChE,OAAOA,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAEJ,KAAK,KAAK,CAAC,GAAGC,IAAI,CAACS,CAAC,GAAGxB,IAAI,CAACmB,KAAK,CAAC,CAACJ,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,CAAC,GAAGP,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACO,CAAC,EAAEP,IAAI,CAAC;EAC9H,CAAC;EACGU,2BAA2B,GAAG,SAASA,2BAA2BA,CAACX,KAAK,EAAEC,IAAI,EAAE;IAClF,OAAOA,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAEJ,KAAK,GAAGd,IAAI,CAACmB,KAAK,CAAC,CAACJ,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,CAAC,GAAGP,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,GAAGC,IAAI,CAACO,CAAC,GAAGP,IAAI,CAACW,CAAC,EAAEX,IAAI,CAAC;EACxH,CAAC;EACG;EACJY,qBAAqB,GAAG,SAASA,qBAAqBA,CAACb,KAAK,EAAEC,IAAI,EAAE;IAClE,IAAIa,KAAK,GAAGb,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,CAAC,GAAGP,KAAK;IACnCC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAE,CAAC,EAAEU,KAAK,IAAIA,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAGb,IAAI,CAACO,CAAC,EAAEP,IAAI,CAAC;EAC7E,CAAC;EACGc,uBAAuB,GAAG,SAASA,uBAAuBA,CAACf,KAAK,EAAEC,IAAI,EAAE;IAC1E,OAAOA,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAEJ,KAAK,GAAGC,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACW,CAAC,EAAEX,IAAI,CAAC;EAChE,CAAC;EACGe,gCAAgC,GAAG,SAASA,gCAAgCA,CAAChB,KAAK,EAAEC,IAAI,EAAE;IAC5F,OAAOA,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAEJ,KAAK,KAAK,CAAC,GAAGC,IAAI,CAACW,CAAC,GAAGX,IAAI,CAACS,CAAC,EAAET,IAAI,CAAC;EACtE,CAAC;EACGgB,eAAe,GAAG,SAASA,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE;IACtE,OAAOI,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC,GAAGL,KAAK;EACvC,CAAC;EACGO,cAAc,GAAG,SAASA,cAAcA,CAACH,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE;IACpE,OAAOI,MAAM,CAACE,KAAK,CAACE,WAAW,CAACH,QAAQ,EAAEL,KAAK,CAAC;EAClD,CAAC;EACGS,gBAAgB,GAAG,SAASA,gBAAgBA,CAACL,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE;IACxE,OAAOI,MAAM,CAACM,KAAK,CAACL,QAAQ,CAAC,GAAGL,KAAK;EACvC,CAAC;EACGW,YAAY,GAAG,SAASA,YAAYA,CAACP,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE;IAChE,OAAOI,MAAM,CAACM,KAAK,CAACE,MAAM,GAAGR,MAAM,CAACM,KAAK,CAACG,MAAM,GAAGb,KAAK;EAC1D,CAAC;EACGc,sBAAsB,GAAG,SAASA,sBAAsBA,CAACV,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAEb,IAAI,EAAED,KAAK,EAAE;IACjG,IAAI6B,KAAK,GAAGX,MAAM,CAACM,KAAK;IACxBK,KAAK,CAACH,MAAM,GAAGG,KAAK,CAACF,MAAM,GAAGb,KAAK;IACnCe,KAAK,CAACC,eAAe,CAAC9B,KAAK,EAAE6B,KAAK,CAAC;EACrC,CAAC;EACGE,0BAA0B,GAAG,SAASA,0BAA0BA,CAACb,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAEb,IAAI,EAAED,KAAK,EAAE;IACzG,IAAI6B,KAAK,GAAGX,MAAM,CAACM,KAAK;IACxBK,KAAK,CAACV,QAAQ,CAAC,GAAGL,KAAK;IACvBe,KAAK,CAACC,eAAe,CAAC9B,KAAK,EAAE6B,KAAK,CAAC;EACrC,CAAC;EACGG,cAAc,GAAG,WAAW;EAC5BC,oBAAoB,GAAGD,cAAc,GAAG,QAAQ;EAChDE,UAAU,GAAG,SAASA,UAAUA,CAACf,QAAQ,EAAEgB,QAAQ,EAAE;IACvD,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAIlB,MAAM,GAAG,IAAI,CAACA,MAAM;MACpBE,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpBS,KAAK,GAAGX,MAAM,CAACM,KAAK;IAExB,IAAIL,QAAQ,IAAInC,eAAe,IAAIoC,KAAK,EAAE;MACxC,IAAI,CAACiB,GAAG,GAAG,IAAI,CAACA,GAAG,IAAI,CAAC,CAAC;MAEzB,IAAIlB,QAAQ,KAAK,WAAW,EAAE;QAC5BA,QAAQ,GAAGxB,gBAAgB,CAACwB,QAAQ,CAAC,IAAIA,QAAQ;QACjD,CAACA,QAAQ,CAACmB,OAAO,CAAC,GAAG,CAAC,GAAGnB,QAAQ,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;UAChE,OAAOL,KAAK,CAACC,GAAG,CAACI,CAAC,CAAC,GAAGC,IAAI,CAACxB,MAAM,EAAEuB,CAAC,CAAC;QACvC,CAAC,CAAC,GAAG,IAAI,CAACJ,GAAG,CAAClB,QAAQ,CAAC,GAAGU,KAAK,CAACc,CAAC,GAAGd,KAAK,CAACV,QAAQ,CAAC,GAAGuB,IAAI,CAACxB,MAAM,EAAEC,QAAQ,CAAC,CAAC,CAAC;;QAE9EA,QAAQ,KAAKc,oBAAoB,KAAK,IAAI,CAACI,GAAG,CAACO,OAAO,GAAGf,KAAK,CAACe,OAAO,CAAC;MACzE,CAAC,MAAM;QACL,OAAOjD,gBAAgB,CAACkD,SAAS,CAACN,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUpC,CAAC,EAAE;UAChE,OAAO8B,UAAU,CAACY,IAAI,CAACV,KAAK,EAAEhC,CAAC,EAAE+B,QAAQ,CAAC;QAC5C,CAAC,CAAC;MACJ;MAEA,IAAI,IAAI,CAACY,KAAK,CAACT,OAAO,CAACN,cAAc,CAAC,IAAI,CAAC,EAAE;QAC3C;MACF;MAEA,IAAIH,KAAK,CAACmB,GAAG,EAAE;QACb,IAAI,CAACC,IAAI,GAAG/B,MAAM,CAACgC,YAAY,CAAC,iBAAiB,CAAC;QAClD,IAAI,CAACH,KAAK,CAACI,IAAI,CAAClB,oBAAoB,EAAEE,QAAQ,EAAE,EAAE,CAAC;MACrD;MAEAhB,QAAQ,GAAGa,cAAc;IAC3B;IAEA,CAACZ,KAAK,IAAIe,QAAQ,KAAK,IAAI,CAACY,KAAK,CAACI,IAAI,CAAChC,QAAQ,EAAEgB,QAAQ,EAAEf,KAAK,CAACD,QAAQ,CAAC,CAAC;EAC7E,CAAC;EACGiC,4BAA4B,GAAG,SAASA,4BAA4BA,CAAChC,KAAK,EAAE;IAC9E,IAAIA,KAAK,CAACiC,SAAS,EAAE;MACnBjC,KAAK,CAACkC,cAAc,CAAC,WAAW,CAAC;MACjClC,KAAK,CAACkC,cAAc,CAAC,OAAO,CAAC;MAC7BlC,KAAK,CAACkC,cAAc,CAAC,QAAQ,CAAC;IAChC;EACF,CAAC;EACGC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzC,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MAClB7B,MAAM,GAAG,IAAI,CAACA,MAAM;MACpBE,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpBS,KAAK,GAAGX,MAAM,CAACM,KAAK;MACpBgC,CAAC;MACDpD,CAAC;IAEL,KAAKoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACpC;MACA,IAAI,CAACT,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,EAAE;QACjBT,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,GAAGpC,KAAK,CAAC2B,KAAK,CAACS,CAAC,CAAC,CAAC,GAAGT,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,GAAGpC,KAAK,CAACkC,cAAc,CAACP,KAAK,CAACS,CAAC,CAAC,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,GAAGX,KAAK,CAACS,CAAC,CAAC,GAAGT,KAAK,CAACS,CAAC,CAAC,CAACG,OAAO,CAACnE,QAAQ,EAAE,KAAK,CAAC,CAACoE,WAAW,CAAC,CAAC,CAAC;MACnK,CAAC,MAAM,IAAIb,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC7B;QACAtC,MAAM,CAAC6B,KAAK,CAACS,CAAC,CAAC,CAAC,CAACT,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC,CAAC;MAChC,CAAC,MAAM;QACL;QACAtC,MAAM,CAAC6B,KAAK,CAACS,CAAC,CAAC,CAAC,GAAGT,KAAK,CAACS,CAAC,GAAG,CAAC,CAAC;MACjC;IACF;IAEA,IAAI,IAAI,CAACnB,GAAG,EAAE;MACZ,KAAKjC,CAAC,IAAI,IAAI,CAACiC,GAAG,EAAE;QAClBR,KAAK,CAACzB,CAAC,CAAC,GAAG,IAAI,CAACiC,GAAG,CAACjC,CAAC,CAAC;MACxB;MAEA,IAAIyB,KAAK,CAACmB,GAAG,EAAE;QACbnB,KAAK,CAACC,eAAe,CAAC,CAAC;QACvBZ,MAAM,CAAC2C,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAACZ,IAAI,IAAI,EAAE,CAAC;MACzD;MAEAO,CAAC,GAAG3E,UAAU,CAAC,CAAC;MAEhB,IAAI,CAAC,CAAC2E,CAAC,IAAI,CAACA,CAAC,CAACM,OAAO,KAAK,CAAC1C,KAAK,CAACY,cAAc,CAAC,EAAE;QAChDoB,4BAA4B,CAAChC,KAAK,CAAC;QAEnC,IAAIS,KAAK,CAACe,OAAO,IAAIxB,KAAK,CAACa,oBAAoB,CAAC,EAAE;UAChDb,KAAK,CAACa,oBAAoB,CAAC,IAAI,GAAG,GAAGJ,KAAK,CAACe,OAAO,GAAG,IAAI,CAAC,CAAC;;UAE3Df,KAAK,CAACe,OAAO,GAAG,CAAC;UACjBf,KAAK,CAACC,eAAe,CAAC,CAAC;QACzB;QAEAD,KAAK,CAACkC,OAAO,GAAG,CAAC,CAAC,CAAC;MACrB;IACF;EACF,CAAC;EACGC,cAAc,GAAG,SAASA,cAAcA,CAAC9C,MAAM,EAAE+C,UAAU,EAAE;IAC/D,IAAIC,KAAK,GAAG;MACVhD,MAAM,EAAEA,MAAM;MACd6B,KAAK,EAAE,EAAE;MACToB,MAAM,EAAEZ,YAAY;MACpBa,IAAI,EAAElC;IACR,CAAC;IACDhB,MAAM,CAACM,KAAK,IAAI7E,IAAI,CAAC0H,IAAI,CAACC,QAAQ,CAACpD,MAAM,CAAC,CAAC,CAAC;;IAE5C+C,UAAU,IAAI/C,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACqD,QAAQ,IAAIN,UAAU,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUpC,CAAC,EAAE;MAC1F,OAAO8D,KAAK,CAACE,IAAI,CAAChE,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC;;IAEJ,OAAO8D,KAAK;EACd,CAAC;EACGM,WAAW;EACXC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACrD,IAAIjE,CAAC,GAAGnC,IAAI,CAACqG,eAAe,GAAGrG,IAAI,CAACqG,eAAe,CAAC,CAACD,EAAE,IAAI,8BAA8B,EAAEhB,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAEe,IAAI,CAAC,GAAGnG,IAAI,CAACsG,aAAa,CAACH,IAAI,CAAC,CAAC,CAAC;;IAExJ,OAAOhE,CAAC,IAAIA,CAAC,CAACU,KAAK,GAAGV,CAAC,GAAGnC,IAAI,CAACsG,aAAa,CAACH,IAAI,CAAC,CAAC,CAAC;EACtD,CAAC;EACGI,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC5D,MAAM,EAAEC,QAAQ,EAAE4D,kBAAkB,EAAE;IAC7F,IAAIC,EAAE,GAAGC,gBAAgB,CAAC/D,MAAM,CAAC;IACjC,OAAO8D,EAAE,CAAC7D,QAAQ,CAAC,IAAI6D,EAAE,CAACE,gBAAgB,CAAC/D,QAAQ,CAACwC,OAAO,CAACnE,QAAQ,EAAE,KAAK,CAAC,CAACoE,WAAW,CAAC,CAAC,CAAC,IAAIoB,EAAE,CAACE,gBAAgB,CAAC/D,QAAQ,CAAC,IAAI,CAAC4D,kBAAkB,IAAID,oBAAoB,CAAC5D,MAAM,EAAEiE,gBAAgB,CAAChE,QAAQ,CAAC,IAAIA,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;EACxO,CAAC;EACGiE,SAAS,GAAG,oBAAoB,CAAC7C,KAAK,CAAC,GAAG,CAAC;EAC3C4C,gBAAgB,GAAG,SAASA,gBAAgBA,CAAChE,QAAQ,EAAEkE,OAAO,EAAEC,YAAY,EAAE;IAChF,IAAI5E,CAAC,GAAG2E,OAAO,IAAI3G,QAAQ;MACvB4B,CAAC,GAAGI,CAAC,CAACU,KAAK;MACXoC,CAAC,GAAG,CAAC;IAET,IAAIrC,QAAQ,IAAIb,CAAC,IAAI,CAACgF,YAAY,EAAE;MAClC,OAAOnE,QAAQ;IACjB;IAEAA,QAAQ,GAAGA,QAAQ,CAACoE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrE,QAAQ,CAACuC,MAAM,CAAC,CAAC,CAAC;IAEhE,OAAOF,CAAC,EAAE,IAAI,EAAE4B,SAAS,CAAC5B,CAAC,CAAC,GAAGrC,QAAQ,IAAIb,CAAC,CAAC,EAAE,CAAC;IAEhD,OAAOkD,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAACA,CAAC,KAAK,CAAC,GAAG,IAAI,GAAGA,CAAC,IAAI,CAAC,GAAG4B,SAAS,CAAC5B,CAAC,CAAC,GAAG,EAAE,IAAIrC,QAAQ;EAChF,CAAC;EACGsE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAI3G,aAAa,CAAC,CAAC,IAAIC,MAAM,CAAC2G,QAAQ,EAAE;MACtCpH,IAAI,GAAGS,MAAM;MACbR,IAAI,GAAGD,IAAI,CAACoH,QAAQ;MACpBlH,WAAW,GAAGD,IAAI,CAACoH,eAAe;MAClCjH,QAAQ,GAAG+F,cAAc,CAAC,KAAK,CAAC,IAAI;QAClCrD,KAAK,EAAE,CAAC;MACV,CAAC;MACDzC,cAAc,GAAG8F,cAAc,CAAC,KAAK,CAAC;MACtCzC,cAAc,GAAGmD,gBAAgB,CAACnD,cAAc,CAAC;MACjDC,oBAAoB,GAAGD,cAAc,GAAG,QAAQ;MAChDtD,QAAQ,CAAC0C,KAAK,CAACwE,OAAO,GAAG,0DAA0D,CAAC,CAAC;;MAErFpB,WAAW,GAAG,CAAC,CAACW,gBAAgB,CAAC,aAAa,CAAC;MAC/CtG,UAAU,GAAGlC,IAAI,CAAC0H,IAAI,CAACwB,SAAS;MAChCpH,cAAc,GAAG,CAAC;IACpB;EACF,CAAC;EACGqH,uBAAuB,GAAG,SAASA,uBAAuBA,CAAC5E,MAAM,EAAE;IACrE;IACA,IAAI6E,KAAK,GAAG7E,MAAM,CAAC8E,eAAe;MAC9BhD,GAAG,GAAGyB,cAAc,CAAC,KAAK,EAAEsB,KAAK,IAAIA,KAAK,CAAC7C,YAAY,CAAC,OAAO,CAAC,IAAI,4BAA4B,CAAC;MACjG+C,KAAK,GAAG/E,MAAM,CAACgF,SAAS,CAAC,IAAI,CAAC;MAC9BC,IAAI;IAERF,KAAK,CAAC7E,KAAK,CAACgF,OAAO,GAAG,OAAO;IAC7BpD,GAAG,CAACqD,WAAW,CAACJ,KAAK,CAAC;IAEtBzH,WAAW,CAAC6H,WAAW,CAACrD,GAAG,CAAC;IAE5B,IAAI;MACFmD,IAAI,GAAGF,KAAK,CAACK,OAAO,CAAC,CAAC;IACxB,CAAC,CAAC,OAAO5F,CAAC,EAAE,CAAC;IAEbsC,GAAG,CAACuD,WAAW,CAACN,KAAK,CAAC;IAEtBzH,WAAW,CAAC+H,WAAW,CAACvD,GAAG,CAAC;IAE5B,OAAOmD,IAAI;EACb,CAAC;EACGK,sBAAsB,GAAG,SAASA,sBAAsBA,CAACtF,MAAM,EAAEuF,eAAe,EAAE;IACpF,IAAIjD,CAAC,GAAGiD,eAAe,CAAChD,MAAM;IAE9B,OAAOD,CAAC,EAAE,EAAE;MACV,IAAItC,MAAM,CAACwF,YAAY,CAACD,eAAe,CAACjD,CAAC,CAAC,CAAC,EAAE;QAC3C,OAAOtC,MAAM,CAACgC,YAAY,CAACuD,eAAe,CAACjD,CAAC,CAAC,CAAC;MAChD;IACF;EACF,CAAC;EACGmD,QAAQ,GAAG,SAASA,QAAQA,CAACzF,MAAM,EAAE;IACvC,IAAI0F,MAAM,EAAEC,MAAM;IAElB,IAAI;MACFD,MAAM,GAAG1F,MAAM,CAACoF,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdF,MAAM,GAAGd,uBAAuB,CAAC5E,MAAM,CAAC;MACxC2F,MAAM,GAAG,CAAC;IACZ;IAEAD,MAAM,KAAKA,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACI,MAAM,CAAC,IAAIH,MAAM,KAAKD,MAAM,GAAGd,uBAAuB,CAAC5E,MAAM,CAAC,CAAC,CAAC,CAAC;;IAEnG,OAAO0F,MAAM,IAAI,CAACA,MAAM,CAACG,KAAK,IAAI,CAACH,MAAM,CAACjE,CAAC,IAAI,CAACiE,MAAM,CAACK,CAAC,GAAG;MACzDtE,CAAC,EAAE,CAAC6D,sBAAsB,CAACtF,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;MAC1D+F,CAAC,EAAE,CAACT,sBAAsB,CAACtF,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;MAC1D6F,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC,GAAGJ,MAAM;EACZ,CAAC;EACGM,MAAM,GAAG,SAASA,MAAMA,CAACxG,CAAC,EAAE;IAC9B,OAAO,CAAC,EAAEA,CAAC,CAACyG,MAAM,KAAK,CAACzG,CAAC,CAAC0G,UAAU,IAAI1G,CAAC,CAACsF,eAAe,CAAC,IAAIW,QAAQ,CAACjG,CAAC,CAAC,CAAC;EAC5E,CAAC;EACG;EACJ2G,eAAe,GAAG,SAASA,eAAeA,CAACnG,MAAM,EAAEC,QAAQ,EAAE;IAC3D,IAAIA,QAAQ,EAAE;MACZ,IAAIC,KAAK,GAAGF,MAAM,CAACE,KAAK;QACpBkG,WAAW;MAEf,IAAInG,QAAQ,IAAInC,eAAe,IAAImC,QAAQ,KAAKc,oBAAoB,EAAE;QACpEd,QAAQ,GAAGa,cAAc;MAC3B;MAEA,IAAIZ,KAAK,CAACkC,cAAc,EAAE;QACxBgE,WAAW,GAAGnG,QAAQ,CAACuC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAEnC,IAAI4D,WAAW,KAAK,IAAI,IAAInG,QAAQ,CAACuC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;UAC9D;UACAvC,QAAQ,GAAG,GAAG,GAAGA,QAAQ;QAC3B;QAEAC,KAAK,CAACkC,cAAc,CAACgE,WAAW,KAAK,IAAI,GAAGnG,QAAQ,GAAGA,QAAQ,CAACwC,OAAO,CAACnE,QAAQ,EAAE,KAAK,CAAC,CAACoE,WAAW,CAAC,CAAC,CAAC;MACzG,CAAC,MAAM;QACL;QACAxC,KAAK,CAACmG,eAAe,CAACpG,QAAQ,CAAC;MACjC;IACF;EACF,CAAC;EACGqG,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,MAAM,EAAEvG,MAAM,EAAEC,QAAQ,EAAEuG,SAAS,EAAEC,GAAG,EAAEC,YAAY,EAAE;IACzG,IAAIC,EAAE,GAAG,IAAIlK,SAAS,CAAC8J,MAAM,CAACK,GAAG,EAAE5G,MAAM,EAAEC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAEyG,YAAY,GAAG5G,gCAAgC,GAAGD,uBAAuB,CAAC;IACrI0G,MAAM,CAACK,GAAG,GAAGD,EAAE;IACfA,EAAE,CAACjH,CAAC,GAAG8G,SAAS;IAChBG,EAAE,CAACnH,CAAC,GAAGiH,GAAG;IAEVF,MAAM,CAACM,MAAM,CAAC5E,IAAI,CAAChC,QAAQ,CAAC;IAE5B,OAAO0G,EAAE;EACX,CAAC;EACGG,oBAAoB,GAAG;IACzBC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE;EACR,CAAC;EACGC,mBAAmB,GAAG;IACxBC,IAAI,EAAE,CAAC;IACPC,IAAI,EAAE;EACR,CAAC;EACG;EACJC,cAAc,GAAG,SAASA,cAAcA,CAACrH,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE0H,IAAI,EAAE;IACtE,IAAIC,QAAQ,GAAGC,UAAU,CAAC5H,KAAK,CAAC,IAAI,CAAC;MACjC6H,OAAO,GAAG,CAAC7H,KAAK,GAAG,EAAE,EAAE8H,IAAI,CAAC,CAAC,CAAClF,MAAM,CAAC,CAAC+E,QAAQ,GAAG,EAAE,EAAEhF,MAAM,CAAC,IAAI,IAAI;MACpE;MACJrC,KAAK,GAAG1C,QAAQ,CAAC0C,KAAK;MAClByH,UAAU,GAAGpJ,cAAc,CAACqJ,IAAI,CAAC3H,QAAQ,CAAC;MAC1C4H,SAAS,GAAG7H,MAAM,CAAC8H,OAAO,CAACpF,WAAW,CAAC,CAAC,KAAK,KAAK;MAClDqF,eAAe,GAAG,CAACF,SAAS,GAAG,QAAQ,GAAG,QAAQ,KAAKF,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;MACvFK,MAAM,GAAG,GAAG;MACZC,QAAQ,GAAGX,IAAI,KAAK,IAAI;MACxBY,SAAS,GAAGZ,IAAI,KAAK,GAAG;MACxBa,EAAE;MACFC,MAAM;MACNzH,KAAK;MACL0H,KAAK;IAET,IAAIf,IAAI,KAAKG,OAAO,IAAI,CAACF,QAAQ,IAAIT,oBAAoB,CAACQ,IAAI,CAAC,IAAIR,oBAAoB,CAACW,OAAO,CAAC,EAAE;MAChG,OAAOF,QAAQ;IACjB;IAEAE,OAAO,KAAK,IAAI,IAAI,CAACQ,QAAQ,KAAKV,QAAQ,GAAGF,cAAc,CAACrH,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE,IAAI,CAAC,CAAC;IAC3FyI,KAAK,GAAGrI,MAAM,CAACiG,MAAM,IAAID,MAAM,CAAChG,MAAM,CAAC;IAEvC,IAAI,CAACkI,SAAS,IAAIT,OAAO,KAAK,GAAG,MAAM3J,eAAe,CAACmC,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACmB,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE;MAC/F+G,EAAE,GAAGE,KAAK,GAAGrI,MAAM,CAACoF,OAAO,CAAC,CAAC,CAACuC,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG3H,MAAM,CAAC+H,eAAe,CAAC;MACxF,OAAOnL,MAAM,CAACsL,SAAS,GAAGX,QAAQ,GAAGY,EAAE,GAAGH,MAAM,GAAGT,QAAQ,GAAG,GAAG,GAAGY,EAAE,CAAC;IACzE;IAEAjI,KAAK,CAACyH,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAGK,MAAM,IAAIC,QAAQ,GAAGR,OAAO,GAAGH,IAAI,CAAC;IAC7Ec,MAAM,GAAGd,IAAI,KAAK,KAAK,IAAI,CAACrH,QAAQ,CAACmB,OAAO,CAAC,OAAO,CAAC,IAAIkG,IAAI,KAAK,IAAI,IAAItH,MAAM,CAACmF,WAAW,IAAI,CAAC0C,SAAS,GAAG7H,MAAM,GAAGA,MAAM,CAACkG,UAAU;IAEvI,IAAImC,KAAK,EAAE;MACTD,MAAM,GAAG,CAACpI,MAAM,CAAC8E,eAAe,IAAI,CAAC,CAAC,EAAEoB,UAAU;IACpD;IAEA,IAAI,CAACkC,MAAM,IAAIA,MAAM,KAAK/K,IAAI,IAAI,CAAC+K,MAAM,CAACjD,WAAW,EAAE;MACrDiD,MAAM,GAAG/K,IAAI,CAACiL,IAAI;IACpB;IAEA3H,KAAK,GAAGyH,MAAM,CAAC9H,KAAK;IAEpB,IAAIK,KAAK,IAAIuH,SAAS,IAAIvH,KAAK,CAACkF,KAAK,IAAI8B,UAAU,IAAIhH,KAAK,CAAC4H,IAAI,KAAK5L,OAAO,CAAC4L,IAAI,IAAI,CAAC5H,KAAK,CAACkC,OAAO,EAAE;MACpG,OAAOjG,MAAM,CAAC2K,QAAQ,GAAG5G,KAAK,CAACkF,KAAK,GAAGmC,MAAM,CAAC;IAChD,CAAC,MAAM;MACL,IAAIE,SAAS,KAAKjI,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,CAAC,EAAE;QAChE;QACA,IAAIuI,CAAC,GAAGxI,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC;QAC9BD,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC,GAAG+H,MAAM,GAAGV,IAAI;QACtCa,EAAE,GAAGnI,MAAM,CAAC+H,eAAe,CAAC;QAC5BS,CAAC,GAAGxI,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC,GAAGuI,CAAC,GAAGrC,eAAe,CAACnG,MAAM,EAAEC,QAAQ,CAAC;MACpE,CAAC,MAAM;QACL,CAACiI,SAAS,IAAIT,OAAO,KAAK,GAAG,KAAK,CAACP,mBAAmB,CAACtD,oBAAoB,CAACwE,MAAM,EAAE,SAAS,CAAC,CAAC,KAAKlI,KAAK,CAACuI,QAAQ,GAAG7E,oBAAoB,CAAC5D,MAAM,EAAE,UAAU,CAAC,CAAC;QAC9JoI,MAAM,KAAKpI,MAAM,KAAKE,KAAK,CAACuI,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC;;QAElDL,MAAM,CAACjD,WAAW,CAAC3H,QAAQ,CAAC;QAC5B2K,EAAE,GAAG3K,QAAQ,CAACuK,eAAe,CAAC;QAC9BK,MAAM,CAAC/C,WAAW,CAAC7H,QAAQ,CAAC;QAC5B0C,KAAK,CAACuI,QAAQ,GAAG,UAAU;MAC7B;MAEA,IAAId,UAAU,IAAIO,SAAS,EAAE;QAC3BvH,KAAK,GAAG5D,SAAS,CAACqL,MAAM,CAAC;QACzBzH,KAAK,CAAC4H,IAAI,GAAG5L,OAAO,CAAC4L,IAAI;QACzB5H,KAAK,CAACkF,KAAK,GAAGuC,MAAM,CAACL,eAAe,CAAC;MACvC;IACF;IAEA,OAAOnL,MAAM,CAACqL,QAAQ,GAAGE,EAAE,GAAGZ,QAAQ,GAAGS,MAAM,GAAGG,EAAE,IAAIZ,QAAQ,GAAGS,MAAM,GAAGG,EAAE,GAAGZ,QAAQ,GAAG,CAAC,CAAC;EAChG,CAAC;EACG/F,IAAI,GAAG,SAASA,IAAIA,CAACxB,MAAM,EAAEC,QAAQ,EAAEqH,IAAI,EAAEzE,OAAO,EAAE;IACxD,IAAIjD,KAAK;IACTrC,cAAc,IAAIgH,SAAS,CAAC,CAAC;IAE7B,IAAItE,QAAQ,IAAIxB,gBAAgB,IAAIwB,QAAQ,KAAK,WAAW,EAAE;MAC5DA,QAAQ,GAAGxB,gBAAgB,CAACwB,QAAQ,CAAC;MAErC,IAAI,CAACA,QAAQ,CAACmB,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1BnB,QAAQ,GAAGA,QAAQ,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC;IACF;IAEA,IAAIvD,eAAe,CAACmC,QAAQ,CAAC,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzDL,KAAK,GAAG8I,eAAe,CAAC1I,MAAM,EAAE6C,OAAO,CAAC;MACxCjD,KAAK,GAAGK,QAAQ,KAAK,iBAAiB,GAAGL,KAAK,CAACK,QAAQ,CAAC,GAAGL,KAAK,CAACkC,GAAG,GAAGlC,KAAK,CAAC+I,MAAM,GAAGC,aAAa,CAAChF,oBAAoB,CAAC5D,MAAM,EAAEe,oBAAoB,CAAC,CAAC,GAAG,GAAG,GAAGnB,KAAK,CAAC8B,OAAO,GAAG,IAAI;IACtL,CAAC,MAAM;MACL9B,KAAK,GAAGI,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC;MAE9B,IAAI,CAACL,KAAK,IAAIA,KAAK,KAAK,MAAM,IAAIiD,OAAO,IAAI,CAAC,CAACjD,KAAK,GAAG,EAAE,EAAEwB,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3ExB,KAAK,GAAGiJ,aAAa,CAAC5I,QAAQ,CAAC,IAAI4I,aAAa,CAAC5I,QAAQ,CAAC,CAACD,MAAM,EAAEC,QAAQ,EAAEqH,IAAI,CAAC,IAAI1D,oBAAoB,CAAC5D,MAAM,EAAEC,QAAQ,CAAC,IAAIvE,YAAY,CAACsE,MAAM,EAAEC,QAAQ,CAAC,KAAKA,QAAQ,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACtM;IACF;IAEA,OAAOqH,IAAI,IAAI,CAAC,CAAC,CAAC1H,KAAK,GAAG,EAAE,EAAE8H,IAAI,CAAC,CAAC,CAACtG,OAAO,CAAC,GAAG,CAAC,GAAGiG,cAAc,CAACrH,MAAM,EAAEC,QAAQ,EAAEL,KAAK,EAAE0H,IAAI,CAAC,GAAGA,IAAI,GAAG1H,KAAK;EAClH,CAAC;EACGkJ,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC9I,MAAM,EAAE+I,IAAI,EAAEC,KAAK,EAAEvC,GAAG,EAAE;IACrF;IACA,IAAI,CAACuC,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;MAC9B;MACA,IAAI9J,CAAC,GAAG+E,gBAAgB,CAAC8E,IAAI,EAAE/I,MAAM,EAAE,CAAC,CAAC;QACrCZ,CAAC,GAAGF,CAAC,IAAI0E,oBAAoB,CAAC5D,MAAM,EAAEd,CAAC,EAAE,CAAC,CAAC;MAE/C,IAAIE,CAAC,IAAIA,CAAC,KAAK4J,KAAK,EAAE;QACpBD,IAAI,GAAG7J,CAAC;QACR8J,KAAK,GAAG5J,CAAC;MACX,CAAC,MAAM,IAAI2J,IAAI,KAAK,aAAa,EAAE;QACjCC,KAAK,GAAGpF,oBAAoB,CAAC5D,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;MAC1D;IACF;IAEA,IAAI2G,EAAE,GAAG,IAAIlK,SAAS,CAAC,IAAI,CAACmK,GAAG,EAAE5G,MAAM,CAACE,KAAK,EAAE6I,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE/M,oBAAoB,CAAC;MAC5EiN,KAAK,GAAG,CAAC;MACTC,UAAU,GAAG,CAAC;MACd3H,CAAC;MACD4H,MAAM;MACNC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLC,UAAU;MACVC,QAAQ;MACRC,MAAM;MACNC,KAAK;MACLC,OAAO;MACPC,SAAS;MACTC,SAAS;IACblD,EAAE,CAACjH,CAAC,GAAGsJ,KAAK;IACZrC,EAAE,CAACnH,CAAC,GAAGiH,GAAG;IACVuC,KAAK,IAAI,EAAE,CAAC,CAAC;;IAEbvC,GAAG,IAAI,EAAE;IAET,IAAIA,GAAG,CAACqD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpCrD,GAAG,GAAG7C,oBAAoB,CAAC5D,MAAM,EAAEyG,GAAG,CAACqD,SAAS,CAAC,CAAC,EAAErD,GAAG,CAACrF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IACxE;IAEA,IAAIqF,GAAG,KAAK,MAAM,EAAE;MAClB8C,UAAU,GAAGvJ,MAAM,CAACE,KAAK,CAAC6I,IAAI,CAAC;MAC/B/I,MAAM,CAACE,KAAK,CAAC6I,IAAI,CAAC,GAAGtC,GAAG;MACxBA,GAAG,GAAG7C,oBAAoB,CAAC5D,MAAM,EAAE+I,IAAI,CAAC,IAAItC,GAAG;MAC/C8C,UAAU,GAAGvJ,MAAM,CAACE,KAAK,CAAC6I,IAAI,CAAC,GAAGQ,UAAU,GAAGpD,eAAe,CAACnG,MAAM,EAAE+I,IAAI,CAAC;IAC9E;IAEAxH,CAAC,GAAG,CAACyH,KAAK,EAAEvC,GAAG,CAAC;IAEhBrK,kBAAkB,CAACmF,CAAC,CAAC,CAAC,CAAC;;IAGvByH,KAAK,GAAGzH,CAAC,CAAC,CAAC,CAAC;IACZkF,GAAG,GAAGlF,CAAC,CAAC,CAAC,CAAC;IACV6H,WAAW,GAAGJ,KAAK,CAACe,KAAK,CAACnO,eAAe,CAAC,IAAI,EAAE;IAChDiO,SAAS,GAAGpD,GAAG,CAACsD,KAAK,CAACnO,eAAe,CAAC,IAAI,EAAE;IAE5C,IAAIiO,SAAS,CAACtH,MAAM,EAAE;MACpB,OAAO4G,MAAM,GAAGvN,eAAe,CAACoO,IAAI,CAACvD,GAAG,CAAC,EAAE;QACzC+C,QAAQ,GAAGL,MAAM,CAAC,CAAC,CAAC;QACpBO,KAAK,GAAGjD,GAAG,CAACqD,SAAS,CAACb,KAAK,EAAEE,MAAM,CAACF,KAAK,CAAC;QAE1C,IAAIK,KAAK,EAAE;UACTA,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,IAAI,CAAC;QACzB,CAAC,MAAM,IAAII,KAAK,CAAClH,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAAIkH,KAAK,CAAClH,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACvE8G,KAAK,GAAG,CAAC;QACX;QAEA,IAAIE,QAAQ,MAAMD,UAAU,GAAGH,WAAW,CAACF,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;UAC/DG,QAAQ,GAAG7B,UAAU,CAAC+B,UAAU,CAAC,IAAI,CAAC;UACtCK,SAAS,GAAGL,UAAU,CAAC/G,MAAM,CAAC,CAAC6G,QAAQ,GAAG,EAAE,EAAE9G,MAAM,CAAC;UACrDiH,QAAQ,CAACnF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAKmF,QAAQ,GAAGvM,cAAc,CAACoM,QAAQ,EAAEG,QAAQ,CAAC,GAAGI,SAAS,CAAC;UACzFH,MAAM,GAAGjC,UAAU,CAACgC,QAAQ,CAAC;UAC7BG,OAAO,GAAGH,QAAQ,CAAChH,MAAM,CAAC,CAACiH,MAAM,GAAG,EAAE,EAAElH,MAAM,CAAC;UAC/C0G,KAAK,GAAGrN,eAAe,CAACqO,SAAS,GAAGN,OAAO,CAACpH,MAAM;UAElD,IAAI,CAACoH,OAAO,EAAE;YACZ;YACAA,OAAO,GAAGA,OAAO,IAAIjN,OAAO,CAACwN,KAAK,CAACnB,IAAI,CAAC,IAAIa,SAAS;YAErD,IAAIX,KAAK,KAAKxC,GAAG,CAAClE,MAAM,EAAE;cACxBkE,GAAG,IAAIkD,OAAO;cACdhD,EAAE,CAACnH,CAAC,IAAImK,OAAO;YACjB;UACF;UAEA,IAAIC,SAAS,KAAKD,OAAO,EAAE;YACzBN,QAAQ,GAAGhC,cAAc,CAACrH,MAAM,EAAE+I,IAAI,EAAEQ,UAAU,EAAEI,OAAO,CAAC,IAAI,CAAC;UACnE,CAAC,CAAC;;UAGFhD,EAAE,CAACC,GAAG,GAAG;YACPuD,KAAK,EAAExD,EAAE,CAACC,GAAG;YACb1H,CAAC,EAAEwK,KAAK,IAAIR,UAAU,KAAK,CAAC,GAAGQ,KAAK,GAAG,GAAG;YAC1C;YACAtK,CAAC,EAAEiK,QAAQ;YACXhK,CAAC,EAAEoK,MAAM,GAAGJ,QAAQ;YACpBe,CAAC,EAAEd,KAAK,IAAIA,KAAK,GAAG,CAAC,IAAIP,IAAI,KAAK,QAAQ,GAAG/K,IAAI,CAACmB,KAAK,GAAG;UAC5D,CAAC;QACH;MACF;MAEAwH,EAAE,CAACtH,CAAC,GAAG4J,KAAK,GAAGxC,GAAG,CAAClE,MAAM,GAAGkE,GAAG,CAACqD,SAAS,CAACb,KAAK,EAAExC,GAAG,CAAClE,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;IACrE,CAAC,MAAM;MACLoE,EAAE,CAAC0D,CAAC,GAAGtB,IAAI,KAAK,SAAS,IAAItC,GAAG,KAAK,MAAM,GAAG3G,gCAAgC,GAAGD,uBAAuB;IAC1G;IAEA5D,OAAO,CAAC2L,IAAI,CAACnB,GAAG,CAAC,KAAKE,EAAE,CAACnH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEjC,IAAI,CAACoH,GAAG,GAAGD,EAAE,CAAC,CAAC;;IAEf,OAAOA,EAAE;EACX,CAAC;EACG2D,iBAAiB,GAAG;IACtBC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC;EACGC,6BAA6B,GAAG,SAASA,6BAA6BA,CAAChL,KAAK,EAAE;IAChF,IAAIyB,KAAK,GAAGzB,KAAK,CAACyB,KAAK,CAAC,GAAG,CAAC;MACxBI,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACZ0E,CAAC,GAAG1E,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK;IAEzB,IAAII,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,QAAQ,IAAIsE,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,OAAO,EAAE;MAClE;MACAnG,KAAK,GAAG6B,CAAC;MACTA,CAAC,GAAGsE,CAAC;MACLA,CAAC,GAAGnG,KAAK;IACX;IAEAyB,KAAK,CAAC,CAAC,CAAC,GAAGiJ,iBAAiB,CAAC7I,CAAC,CAAC,IAAIA,CAAC;IACpCJ,KAAK,CAAC,CAAC,CAAC,GAAGiJ,iBAAiB,CAACvE,CAAC,CAAC,IAAIA,CAAC;IACpC,OAAO1E,KAAK,CAACwJ,IAAI,CAAC,GAAG,CAAC;EACxB,CAAC;EACGC,iBAAiB,GAAG,SAASA,iBAAiBA,CAAChM,KAAK,EAAEC,IAAI,EAAE;IAC9D,IAAIA,IAAI,CAACgM,KAAK,IAAIhM,IAAI,CAACgM,KAAK,CAACC,KAAK,KAAKjM,IAAI,CAACgM,KAAK,CAACE,IAAI,EAAE;MACtD,IAAIjL,MAAM,GAAGjB,IAAI,CAACE,CAAC;QACfiB,KAAK,GAAGF,MAAM,CAACE,KAAK;QACpB2B,KAAK,GAAG9C,IAAI,CAACO,CAAC;QACdqB,KAAK,GAAGX,MAAM,CAACM,KAAK;QACpByI,IAAI;QACJmC,eAAe;QACf5I,CAAC;MAEL,IAAIT,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,EAAE;QACrC3B,KAAK,CAACwE,OAAO,GAAG,EAAE;QAClBwG,eAAe,GAAG,CAAC;MACrB,CAAC,MAAM;QACLrJ,KAAK,GAAGA,KAAK,CAACR,KAAK,CAAC,GAAG,CAAC;QACxBiB,CAAC,GAAGT,KAAK,CAACU,MAAM;QAEhB,OAAO,EAAED,CAAC,GAAG,CAAC,CAAC,EAAE;UACfyG,IAAI,GAAGlH,KAAK,CAACS,CAAC,CAAC;UAEf,IAAIxE,eAAe,CAACiL,IAAI,CAAC,EAAE;YACzBmC,eAAe,GAAG,CAAC;YACnBnC,IAAI,GAAGA,IAAI,KAAK,iBAAiB,GAAGhI,oBAAoB,GAAGD,cAAc;UAC3E;UAEAqF,eAAe,CAACnG,MAAM,EAAE+I,IAAI,CAAC;QAC/B;MACF;MAEA,IAAImC,eAAe,EAAE;QACnB/E,eAAe,CAACnG,MAAM,EAAEc,cAAc,CAAC;QAEvC,IAAIH,KAAK,EAAE;UACTA,KAAK,CAACmB,GAAG,IAAI9B,MAAM,CAACqG,eAAe,CAAC,WAAW,CAAC;UAChDnG,KAAK,CAACvB,KAAK,GAAGuB,KAAK,CAACiL,MAAM,GAAGjL,KAAK,CAACiC,SAAS,GAAG,MAAM;UAErDuG,eAAe,CAAC1I,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;;UAG5BW,KAAK,CAACkC,OAAO,GAAG,CAAC;UAEjBX,4BAA4B,CAAChC,KAAK,CAAC;QACrC;MACF;IACF;EACF,CAAC;EACG;EACJ2I,aAAa,GAAG;IACduC,UAAU,EAAE,SAASA,UAAUA,CAAC7E,MAAM,EAAEvG,MAAM,EAAEC,QAAQ,EAAEuJ,QAAQ,EAAEuB,KAAK,EAAE;MACzE,IAAIA,KAAK,CAAChM,IAAI,KAAK,aAAa,EAAE;QAChC,IAAI4H,EAAE,GAAGJ,MAAM,CAACK,GAAG,GAAG,IAAInK,SAAS,CAAC8J,MAAM,CAACK,GAAG,EAAE5G,MAAM,EAAEC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE6K,iBAAiB,CAAC;QAC1FnE,EAAE,CAACrH,CAAC,GAAGkK,QAAQ;QACf7C,EAAE,CAAC0E,EAAE,GAAG,CAAC,EAAE;QACX1E,EAAE,CAACoE,KAAK,GAAGA,KAAK;QAEhBxE,MAAM,CAACM,MAAM,CAAC5E,IAAI,CAAChC,QAAQ,CAAC;QAE5B,OAAO,CAAC;MACV;IACF;IACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEA,CAAC;EAED;AACA;AACA;AACA;AACA;EACAqL,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClCC,qBAAqB,GAAG,CAAC,CAAC;EAC1BC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC5L,KAAK,EAAE;IACtD,OAAOA,KAAK,KAAK,0BAA0B,IAAIA,KAAK,KAAK,MAAM,IAAI,CAACA,KAAK;EAC3E,CAAC;EACG6L,kCAAkC,GAAG,SAASA,kCAAkCA,CAACzL,MAAM,EAAE;IAC3F,IAAI0L,YAAY,GAAG9H,oBAAoB,CAAC5D,MAAM,EAAEc,cAAc,CAAC;IAE/D,OAAO0K,gBAAgB,CAACE,YAAY,CAAC,GAAGJ,iBAAiB,GAAGI,YAAY,CAAClJ,MAAM,CAAC,CAAC,CAAC,CAACuH,KAAK,CAACpO,OAAO,CAAC,CAACgQ,GAAG,CAAC/O,MAAM,CAAC;EAC/G,CAAC;EACGgP,UAAU,GAAG,SAASA,UAAUA,CAAC5L,MAAM,EAAE6L,OAAO,EAAE;IACpD,IAAIlL,KAAK,GAAGX,MAAM,CAACM,KAAK,IAAIvD,SAAS,CAACiD,MAAM,CAAC;MACzCE,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpB4L,MAAM,GAAGL,kCAAkC,CAACzL,MAAM,CAAC;MACnDoI,MAAM;MACN2D,WAAW;MACXC,IAAI;MACJC,UAAU;IAEd,IAAItL,KAAK,CAACmB,GAAG,IAAI9B,MAAM,CAACgC,YAAY,CAAC,WAAW,CAAC,EAAE;MACjDgK,IAAI,GAAGhM,MAAM,CAAC2B,SAAS,CAACuK,OAAO,CAACC,WAAW,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC;;MAEtDA,MAAM,GAAG,CAACE,IAAI,CAACzK,CAAC,EAAEyK,IAAI,CAACtM,CAAC,EAAEsM,IAAI,CAAC3M,CAAC,EAAE2M,IAAI,CAACI,CAAC,EAAEJ,IAAI,CAACxM,CAAC,EAAEwM,IAAI,CAACK,CAAC,CAAC;MACzD,OAAOP,MAAM,CAACjB,IAAI,CAAC,GAAG,CAAC,KAAK,aAAa,GAAGS,iBAAiB,GAAGQ,MAAM;IACxE,CAAC,MAAM,IAAIA,MAAM,KAAKR,iBAAiB,IAAI,CAACtL,MAAM,CAACsM,YAAY,IAAItM,MAAM,KAAK1C,WAAW,IAAI,CAACqD,KAAK,CAACmB,GAAG,EAAE;MACvG;MACA;MACAkK,IAAI,GAAG9L,KAAK,CAACgF,OAAO;MACpBhF,KAAK,CAACgF,OAAO,GAAG,OAAO;MACvBkD,MAAM,GAAGpI,MAAM,CAACkG,UAAU;MAE1B,IAAI,CAACkC,MAAM,IAAI,CAACpI,MAAM,CAACsM,YAAY,IAAI,CAACtM,MAAM,CAACuM,qBAAqB,CAAC,CAAC,CAAC1G,KAAK,EAAE;QAC5E;QACAoG,UAAU,GAAG,CAAC,CAAC,CAAC;;QAEhBF,WAAW,GAAG/L,MAAM,CAACwM,kBAAkB;QAEvClP,WAAW,CAAC6H,WAAW,CAACnF,MAAM,CAAC,CAAC,CAAC;MAEnC;MAEA8L,MAAM,GAAGL,kCAAkC,CAACzL,MAAM,CAAC;MACnDgM,IAAI,GAAG9L,KAAK,CAACgF,OAAO,GAAG8G,IAAI,GAAG7F,eAAe,CAACnG,MAAM,EAAE,SAAS,CAAC;MAEhE,IAAIiM,UAAU,EAAE;QACdF,WAAW,GAAG3D,MAAM,CAACqE,YAAY,CAACzM,MAAM,EAAE+L,WAAW,CAAC,GAAG3D,MAAM,GAAGA,MAAM,CAACjD,WAAW,CAACnF,MAAM,CAAC,GAAG1C,WAAW,CAAC+H,WAAW,CAACrF,MAAM,CAAC;MAChI;IACF;IAEA,OAAO6L,OAAO,IAAIC,MAAM,CAACvJ,MAAM,GAAG,CAAC,GAAG,CAACuJ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,EAAEA,MAAM,CAAC,EAAE,CAAC,CAAC,GAAGA,MAAM;EACrH,CAAC;EACGY,eAAe,GAAG,SAASA,eAAeA,CAAC1M,MAAM,EAAE2I,MAAM,EAAEgE,gBAAgB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE;IAC7H,IAAInM,KAAK,GAAGX,MAAM,CAACM,KAAK;MACpBwL,MAAM,GAAGe,WAAW,IAAIjB,UAAU,CAAC5L,MAAM,EAAE,IAAI,CAAC;MAChD+M,UAAU,GAAGpM,KAAK,CAACqM,OAAO,IAAI,CAAC;MAC/BC,UAAU,GAAGtM,KAAK,CAACuM,OAAO,IAAI,CAAC;MAC/BC,UAAU,GAAGxM,KAAK,CAACyM,OAAO,IAAI,CAAC;MAC/BC,UAAU,GAAG1M,KAAK,CAAC2M,OAAO,IAAI,CAAC;MAC/B/L,CAAC,GAAGuK,MAAM,CAAC,CAAC,CAAC;MACbpM,CAAC,GAAGoM,MAAM,CAAC,CAAC,CAAC;MACbzM,CAAC,GAAGyM,MAAM,CAAC,CAAC,CAAC;MACbM,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC;MACbyB,EAAE,GAAGzB,MAAM,CAAC,CAAC,CAAC;MACd0B,EAAE,GAAG1B,MAAM,CAAC,CAAC,CAAC;MACd2B,WAAW,GAAG9E,MAAM,CAACtH,KAAK,CAAC,GAAG,CAAC;MAC/B2L,OAAO,GAAGxF,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MACzCP,OAAO,GAAG1F,UAAU,CAACiG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;MACzC/H,MAAM;MACNgI,WAAW;MACXjM,CAAC;MACDsE,CAAC;IAEL,IAAI,CAAC4G,gBAAgB,EAAE;MACrBjH,MAAM,GAAGD,QAAQ,CAACzF,MAAM,CAAC;MACzBgN,OAAO,GAAGtH,MAAM,CAACjE,CAAC,IAAI,CAACgM,WAAW,CAAC,CAAC,CAAC,CAACrM,OAAO,CAAC,GAAG,CAAC,GAAG4L,OAAO,GAAG,GAAG,GAAGtH,MAAM,CAACG,KAAK,GAAGmH,OAAO,CAAC;MAC5FE,OAAO,GAAGxH,MAAM,CAACK,CAAC,IAAI,CAAC,CAAC0H,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAAC,CAAC,CAAC,EAAErM,OAAO,CAAC,GAAG,CAAC,GAAG8L,OAAO,GAAG,GAAG,GAAGxH,MAAM,CAACI,MAAM,GAAGoH,OAAO,CAAC,CAAC,CAAC;MACnH;MACA;MACA;IACF,CAAC,MAAM,IAAIpB,MAAM,KAAKR,iBAAiB,KAAKoC,WAAW,GAAGnM,CAAC,GAAG6K,CAAC,GAAG1M,CAAC,GAAGL,CAAC,CAAC,EAAE;MACxE;MACAoC,CAAC,GAAGuL,OAAO,IAAIZ,CAAC,GAAGsB,WAAW,CAAC,GAAGR,OAAO,IAAI,CAAC7N,CAAC,GAAGqO,WAAW,CAAC,GAAG,CAACrO,CAAC,GAAGmO,EAAE,GAAGpB,CAAC,GAAGmB,EAAE,IAAIG,WAAW;MAChG3H,CAAC,GAAGiH,OAAO,IAAI,CAACtN,CAAC,GAAGgO,WAAW,CAAC,GAAGR,OAAO,IAAI3L,CAAC,GAAGmM,WAAW,CAAC,GAAG,CAACnM,CAAC,GAAGiM,EAAE,GAAG9N,CAAC,GAAG6N,EAAE,IAAIG,WAAW;MAChGV,OAAO,GAAGvL,CAAC;MACXyL,OAAO,GAAGnH,CAAC,CAAC,CAAC;IACf;IAEA,IAAI6G,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIjM,KAAK,CAACiM,MAAM,EAAE;MAC9CW,EAAE,GAAGP,OAAO,GAAGD,UAAU;MACzBS,EAAE,GAAGN,OAAO,GAAGD,UAAU;MACzBtM,KAAK,CAACyM,OAAO,GAAGD,UAAU,IAAII,EAAE,GAAGhM,CAAC,GAAGiM,EAAE,GAAGnO,CAAC,CAAC,GAAGkO,EAAE;MACnD5M,KAAK,CAAC2M,OAAO,GAAGD,UAAU,IAAIE,EAAE,GAAG7N,CAAC,GAAG8N,EAAE,GAAGpB,CAAC,CAAC,GAAGoB,EAAE;IACrD,CAAC,MAAM;MACL7M,KAAK,CAACyM,OAAO,GAAGzM,KAAK,CAAC2M,OAAO,GAAG,CAAC;IACnC;IAEA3M,KAAK,CAACqM,OAAO,GAAGA,OAAO;IACvBrM,KAAK,CAACuM,OAAO,GAAGA,OAAO;IACvBvM,KAAK,CAACiM,MAAM,GAAG,CAAC,CAACA,MAAM;IACvBjM,KAAK,CAACgI,MAAM,GAAGA,MAAM;IACrBhI,KAAK,CAACgM,gBAAgB,GAAG,CAAC,CAACA,gBAAgB;IAC3C3M,MAAM,CAACE,KAAK,CAACa,oBAAoB,CAAC,GAAG,SAAS,CAAC,CAAC;;IAEhD,IAAI+L,uBAAuB,EAAE;MAC3BxG,iBAAiB,CAACwG,uBAAuB,EAAEnM,KAAK,EAAE,SAAS,EAAEoM,UAAU,EAAEC,OAAO,CAAC;MAEjF1G,iBAAiB,CAACwG,uBAAuB,EAAEnM,KAAK,EAAE,SAAS,EAAEsM,UAAU,EAAEC,OAAO,CAAC;MAEjF5G,iBAAiB,CAACwG,uBAAuB,EAAEnM,KAAK,EAAE,SAAS,EAAEwM,UAAU,EAAExM,KAAK,CAACyM,OAAO,CAAC;MAEvF9G,iBAAiB,CAACwG,uBAAuB,EAAEnM,KAAK,EAAE,SAAS,EAAE0M,UAAU,EAAE1M,KAAK,CAAC2M,OAAO,CAAC;IACzF;IAEAtN,MAAM,CAAC2C,YAAY,CAAC,iBAAiB,EAAEqK,OAAO,GAAG,GAAG,GAAGE,OAAO,CAAC;EACjE,CAAC;EACGxE,eAAe,GAAG,SAASA,eAAeA,CAAC1I,MAAM,EAAE6C,OAAO,EAAE;IAC9D,IAAIlC,KAAK,GAAGX,MAAM,CAACM,KAAK,IAAI,IAAI9D,OAAO,CAACwD,MAAM,CAAC;IAE/C,IAAI,GAAG,IAAIW,KAAK,IAAI,CAACkC,OAAO,IAAI,CAAClC,KAAK,CAACkC,OAAO,EAAE;MAC9C,OAAOlC,KAAK;IACd;IAEA,IAAIT,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpByN,cAAc,GAAGhN,KAAK,CAACH,MAAM,GAAG,CAAC;MACjC2H,EAAE,GAAG,IAAI;MACTpB,GAAG,GAAG,KAAK;MACXjD,EAAE,GAAGC,gBAAgB,CAAC/D,MAAM,CAAC;MAC7B2I,MAAM,GAAG/E,oBAAoB,CAAC5D,MAAM,EAAEe,oBAAoB,CAAC,IAAI,GAAG;MAClEU,CAAC;MACDsE,CAAC;MACD6H,CAAC;MACDpN,MAAM;MACNC,MAAM;MACNoN,QAAQ;MACRC,SAAS;MACTC,SAAS;MACTC,KAAK;MACLC,KAAK;MACLC,WAAW;MACXlB,OAAO;MACPE,OAAO;MACPpB,MAAM;MACNqC,KAAK;MACLC,GAAG;MACHC,GAAG;MACH9M,CAAC;MACD7B,CAAC;MACDL,CAAC;MACD+M,CAAC;MACDkC,GAAG;MACHC,GAAG;MACHC,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC,GAAG;MACHC,GAAG;MACHC,GAAG;MACHC,GAAG;MACHC,GAAG;MACHC,GAAG;IACPvN,CAAC,GAAGsE,CAAC,GAAG6H,CAAC,GAAGC,QAAQ,GAAGC,SAAS,GAAGC,SAAS,GAAGC,KAAK,GAAGC,KAAK,GAAGC,WAAW,GAAG,CAAC;IAC9E1N,MAAM,GAAGC,MAAM,GAAG,CAAC;IACnBE,KAAK,CAACmB,GAAG,GAAG,CAAC,EAAE9B,MAAM,CAACiG,MAAM,IAAID,MAAM,CAAChG,MAAM,CAAC,CAAC;IAE/C,IAAI8D,EAAE,CAAC3B,SAAS,EAAE;MAChB;MACA,IAAI2B,EAAE,CAAC3B,SAAS,KAAK,MAAM,IAAI2B,EAAE,CAACnF,KAAK,KAAK,MAAM,IAAImF,EAAE,CAACqH,MAAM,KAAK,MAAM,EAAE;QAC1EjL,KAAK,CAACY,cAAc,CAAC,GAAG,CAACgD,EAAE,CAAC3B,SAAS,KAAK,MAAM,GAAG,cAAc,GAAG,CAAC2B,EAAE,CAAC3B,SAAS,GAAG,MAAM,EAAEd,KAAK,CAAC,GAAG,CAAC,CAAC4N,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,KAAK/G,EAAE,CAACqH,MAAM,KAAK,MAAM,GAAG,SAAS,GAAGrH,EAAE,CAACqH,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,IAAIrH,EAAE,CAACnF,KAAK,KAAK,MAAM,GAAG,QAAQ,GAAGmF,EAAE,CAACnF,KAAK,CAAC0C,KAAK,CAAC,GAAG,CAAC,CAACwJ,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,IAAI/G,EAAE,CAAChD,cAAc,CAAC,KAAK,MAAM,GAAGgD,EAAE,CAAChD,cAAc,CAAC,GAAG,EAAE,CAAC;MACpV;MAEAZ,KAAK,CAACvB,KAAK,GAAGuB,KAAK,CAACiL,MAAM,GAAGjL,KAAK,CAACiC,SAAS,GAAG,MAAM;IACvD;IAEA2J,MAAM,GAAGF,UAAU,CAAC5L,MAAM,EAAEW,KAAK,CAACmB,GAAG,CAAC;IAEtC,IAAInB,KAAK,CAACmB,GAAG,EAAE;MACb,IAAInB,KAAK,CAACkC,OAAO,EAAE;QACjB;QACA4L,EAAE,GAAGzO,MAAM,CAACoF,OAAO,CAAC,CAAC;QACrBuD,MAAM,GAAGhI,KAAK,CAACqM,OAAO,GAAGyB,EAAE,CAAChN,CAAC,GAAG,KAAK,IAAId,KAAK,CAACuM,OAAO,GAAGuB,EAAE,CAAC1I,CAAC,CAAC,GAAG,IAAI;QACrEyI,EAAE,GAAG,EAAE;MACT,CAAC,MAAM;QACLA,EAAE,GAAG,CAAC3L,OAAO,IAAI7C,MAAM,CAACgC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC;MAC3D;MAEA0K,eAAe,CAAC1M,MAAM,EAAEwO,EAAE,IAAI7F,MAAM,EAAE,CAAC,CAAC6F,EAAE,IAAI7N,KAAK,CAACgM,gBAAgB,EAAEhM,KAAK,CAACiM,MAAM,KAAK,KAAK,EAAEd,MAAM,CAAC;IACvG;IAEAkB,OAAO,GAAGrM,KAAK,CAACqM,OAAO,IAAI,CAAC;IAC5BE,OAAO,GAAGvM,KAAK,CAACuM,OAAO,IAAI,CAAC;IAE5B,IAAIpB,MAAM,KAAKR,iBAAiB,EAAE;MAChC/J,CAAC,GAAGuK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEfpM,CAAC,GAAGoM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEfzM,CAAC,GAAGyM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEfM,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEfrK,CAAC,GAAG6M,GAAG,GAAGxC,MAAM,CAAC,CAAC,CAAC;MACnB/F,CAAC,GAAGwI,GAAG,GAAGzC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,IAAIA,MAAM,CAACvJ,MAAM,KAAK,CAAC,EAAE;QACvB/B,MAAM,GAAGxC,IAAI,CAACkR,IAAI,CAAC3N,CAAC,GAAGA,CAAC,GAAG7B,CAAC,GAAGA,CAAC,CAAC;QACjCe,MAAM,GAAGzC,IAAI,CAACkR,IAAI,CAAC9C,CAAC,GAAGA,CAAC,GAAG/M,CAAC,GAAGA,CAAC,CAAC;QACjCwO,QAAQ,GAAGtM,CAAC,IAAI7B,CAAC,GAAGvB,MAAM,CAACuB,CAAC,EAAE6B,CAAC,CAAC,GAAGxD,QAAQ,GAAG,CAAC,CAAC,CAAC;;QAEjDiQ,KAAK,GAAG3O,CAAC,IAAI+M,CAAC,GAAGjO,MAAM,CAACkB,CAAC,EAAE+M,CAAC,CAAC,GAAGrO,QAAQ,GAAG8P,QAAQ,GAAG,CAAC;QACvDG,KAAK,KAAKvN,MAAM,IAAIzC,IAAI,CAACmR,GAAG,CAACnR,IAAI,CAACoQ,GAAG,CAACJ,KAAK,GAAG9P,QAAQ,CAAC,CAAC,CAAC;QAEzD,IAAIyC,KAAK,CAACmB,GAAG,EAAE;UACbL,CAAC,IAAIuL,OAAO,IAAIA,OAAO,GAAGzL,CAAC,GAAG2L,OAAO,GAAG7N,CAAC,CAAC;UAC1C0G,CAAC,IAAImH,OAAO,IAAIF,OAAO,GAAGtN,CAAC,GAAGwN,OAAO,GAAGd,CAAC,CAAC;QAC5C,CAAC,CAAC;MAEJ,CAAC,MAAM;QACL4C,GAAG,GAAGlD,MAAM,CAAC,CAAC,CAAC;QACfgD,GAAG,GAAGhD,MAAM,CAAC,CAAC,CAAC;QACf6C,GAAG,GAAG7C,MAAM,CAAC,CAAC,CAAC;QACf8C,GAAG,GAAG9C,MAAM,CAAC,CAAC,CAAC;QACf+C,GAAG,GAAG/C,MAAM,CAAC,EAAE,CAAC;QAChBiD,GAAG,GAAGjD,MAAM,CAAC,EAAE,CAAC;QAChBrK,CAAC,GAAGqK,MAAM,CAAC,EAAE,CAAC;QACd/F,CAAC,GAAG+F,MAAM,CAAC,EAAE,CAAC;QACd8B,CAAC,GAAG9B,MAAM,CAAC,EAAE,CAAC;QACdqC,KAAK,GAAGhQ,MAAM,CAAC6Q,GAAG,EAAEH,GAAG,CAAC;QACxBf,SAAS,GAAGK,KAAK,GAAGpQ,QAAQ,CAAC,CAAC;;QAE9B,IAAIoQ,KAAK,EAAE;UACTC,GAAG,GAAGpQ,IAAI,CAACoQ,GAAG,CAAC,CAACD,KAAK,CAAC;UACtBE,GAAG,GAAGrQ,IAAI,CAACqQ,GAAG,CAAC,CAACF,KAAK,CAAC;UACtBK,EAAE,GAAGF,GAAG,GAAGF,GAAG,GAAGO,GAAG,GAAGN,GAAG;UAC1BI,EAAE,GAAGF,GAAG,GAAGH,GAAG,GAAGQ,GAAG,GAAGP,GAAG;UAC1BK,EAAE,GAAGM,GAAG,GAAGZ,GAAG,GAAGS,GAAG,GAAGR,GAAG;UAC1BM,GAAG,GAAGL,GAAG,GAAG,CAACD,GAAG,GAAGM,GAAG,GAAGP,GAAG;UAC5BQ,GAAG,GAAGL,GAAG,GAAG,CAACF,GAAG,GAAGO,GAAG,GAAGR,GAAG;UAC5BS,GAAG,GAAGG,GAAG,GAAG,CAACX,GAAG,GAAGQ,GAAG,GAAGT,GAAG;UAC5BW,GAAG,GAAGD,GAAG,GAAG,CAACT,GAAG,GAAGU,GAAG,GAAGX,GAAG;UAC5BE,GAAG,GAAGE,EAAE;UACRD,GAAG,GAAGE,EAAE;UACRO,GAAG,GAAGN,EAAE;QACV,CAAC,CAAC;;QAGFP,KAAK,GAAGhQ,MAAM,CAAC,CAACkB,CAAC,EAAEwP,GAAG,CAAC;QACvBd,SAAS,GAAGI,KAAK,GAAGpQ,QAAQ;QAE5B,IAAIoQ,KAAK,EAAE;UACTC,GAAG,GAAGpQ,IAAI,CAACoQ,GAAG,CAAC,CAACD,KAAK,CAAC;UACtBE,GAAG,GAAGrQ,IAAI,CAACqQ,GAAG,CAAC,CAACF,KAAK,CAAC;UACtBK,EAAE,GAAGjN,CAAC,GAAG6M,GAAG,GAAGO,GAAG,GAAGN,GAAG;UACxBI,EAAE,GAAG/O,CAAC,GAAG0O,GAAG,GAAGQ,GAAG,GAAGP,GAAG;UACxBK,EAAE,GAAGrP,CAAC,GAAG+O,GAAG,GAAGS,GAAG,GAAGR,GAAG;UACxBU,GAAG,GAAG3C,CAAC,GAAGiC,GAAG,GAAGU,GAAG,GAAGX,GAAG;UACzB7M,CAAC,GAAGiN,EAAE;UACN9O,CAAC,GAAG+O,EAAE;UACNpP,CAAC,GAAGqP,EAAE;QACR,CAAC,CAAC;;QAGFP,KAAK,GAAGhQ,MAAM,CAACuB,CAAC,EAAE6B,CAAC,CAAC;QACpBsM,QAAQ,GAAGM,KAAK,GAAGpQ,QAAQ;QAE3B,IAAIoQ,KAAK,EAAE;UACTC,GAAG,GAAGpQ,IAAI,CAACoQ,GAAG,CAACD,KAAK,CAAC;UACrBE,GAAG,GAAGrQ,IAAI,CAACqQ,GAAG,CAACF,KAAK,CAAC;UACrBK,EAAE,GAAGjN,CAAC,GAAG6M,GAAG,GAAG1O,CAAC,GAAG2O,GAAG;UACtBI,EAAE,GAAGH,GAAG,GAAGF,GAAG,GAAGG,GAAG,GAAGF,GAAG;UAC1B3O,CAAC,GAAGA,CAAC,GAAG0O,GAAG,GAAG7M,CAAC,GAAG8M,GAAG;UACrBE,GAAG,GAAGA,GAAG,GAAGH,GAAG,GAAGE,GAAG,GAAGD,GAAG;UAC3B9M,CAAC,GAAGiN,EAAE;UACNF,GAAG,GAAGG,EAAE;QACV;QAEA,IAAIX,SAAS,IAAI9P,IAAI,CAACmR,GAAG,CAACrB,SAAS,CAAC,GAAG9P,IAAI,CAACmR,GAAG,CAACtB,QAAQ,CAAC,GAAG,KAAK,EAAE;UACjE;UACAC,SAAS,GAAGD,QAAQ,GAAG,CAAC;UACxBE,SAAS,GAAG,GAAG,GAAGA,SAAS;QAC7B;QAEAvN,MAAM,GAAG5D,MAAM,CAACoB,IAAI,CAACkR,IAAI,CAAC3N,CAAC,GAAGA,CAAC,GAAG7B,CAAC,GAAGA,CAAC,GAAGL,CAAC,GAAGA,CAAC,CAAC,CAAC;QACjDoB,MAAM,GAAG7D,MAAM,CAACoB,IAAI,CAACkR,IAAI,CAACX,GAAG,GAAGA,GAAG,GAAGS,GAAG,GAAGA,GAAG,CAAC,CAAC;QACjDb,KAAK,GAAGhQ,MAAM,CAACmQ,GAAG,EAAEC,GAAG,CAAC;QACxBP,KAAK,GAAGhQ,IAAI,CAACmR,GAAG,CAAChB,KAAK,CAAC,GAAG,MAAM,GAAGA,KAAK,GAAGpQ,QAAQ,GAAG,CAAC;QACvDmQ,WAAW,GAAGa,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,GAAG,CAACA,GAAG,GAAGA,GAAG,CAAC,GAAG,CAAC;MACpD;MAEA,IAAIpO,KAAK,CAACmB,GAAG,EAAE;QACb;QACA0M,EAAE,GAAGxO,MAAM,CAACgC,YAAY,CAAC,WAAW,CAAC;QACrCrB,KAAK,CAACyO,QAAQ,GAAGpP,MAAM,CAAC2C,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC6I,gBAAgB,CAAC5H,oBAAoB,CAAC5D,MAAM,EAAEc,cAAc,CAAC,CAAC;QACxH0N,EAAE,IAAIxO,MAAM,CAAC2C,YAAY,CAAC,WAAW,EAAE6L,EAAE,CAAC;MAC5C;IACF;IAEA,IAAIxQ,IAAI,CAACmR,GAAG,CAACnB,KAAK,CAAC,GAAG,EAAE,IAAIhQ,IAAI,CAACmR,GAAG,CAACnB,KAAK,CAAC,GAAG,GAAG,EAAE;MACjD,IAAIL,cAAc,EAAE;QAClBnN,MAAM,IAAI,CAAC,CAAC;QACZwN,KAAK,IAAIH,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;QACnCA,QAAQ,IAAIA,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;MACxC,CAAC,MAAM;QACLpN,MAAM,IAAI,CAAC,CAAC;QACZuN,KAAK,IAAIA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;MAClC;IACF;IAEAnL,OAAO,GAAGA,OAAO,IAAIlC,KAAK,CAACkC,OAAO;IAClClC,KAAK,CAACc,CAAC,GAAGA,CAAC,IAAI,CAACd,KAAK,CAAC0O,QAAQ,GAAG5N,CAAC,KAAK,CAACoB,OAAO,IAAIlC,KAAK,CAAC0O,QAAQ,KAAKrR,IAAI,CAACmB,KAAK,CAACa,MAAM,CAACsP,WAAW,GAAG,CAAC,CAAC,KAAKtR,IAAI,CAACmB,KAAK,CAAC,CAACsC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAIzB,MAAM,CAACsP,WAAW,GAAG3O,KAAK,CAAC0O,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGlH,EAAE;IAC9LxH,KAAK,CAACoF,CAAC,GAAGA,CAAC,IAAI,CAACpF,KAAK,CAAC4O,QAAQ,GAAGxJ,CAAC,KAAK,CAAClD,OAAO,IAAIlC,KAAK,CAAC4O,QAAQ,KAAKvR,IAAI,CAACmB,KAAK,CAACa,MAAM,CAACwP,YAAY,GAAG,CAAC,CAAC,KAAKxR,IAAI,CAACmB,KAAK,CAAC,CAAC4G,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI/F,MAAM,CAACwP,YAAY,GAAG7O,KAAK,CAAC4O,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGpH,EAAE;IAChMxH,KAAK,CAACiN,CAAC,GAAGA,CAAC,GAAGzF,EAAE;IAChBxH,KAAK,CAACH,MAAM,GAAG5D,MAAM,CAAC4D,MAAM,CAAC;IAC7BG,KAAK,CAACF,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC;IAC7BE,KAAK,CAACkN,QAAQ,GAAGjR,MAAM,CAACiR,QAAQ,CAAC,GAAG9G,GAAG;IACvCpG,KAAK,CAACmN,SAAS,GAAGlR,MAAM,CAACkR,SAAS,CAAC,GAAG/G,GAAG;IACzCpG,KAAK,CAACoN,SAAS,GAAGnR,MAAM,CAACmR,SAAS,CAAC,GAAGhH,GAAG;IACzCpG,KAAK,CAACqN,KAAK,GAAGA,KAAK,GAAGjH,GAAG;IACzBpG,KAAK,CAACsN,KAAK,GAAGA,KAAK,GAAGlH,GAAG;IACzBpG,KAAK,CAAC8O,oBAAoB,GAAGvB,WAAW,GAAG/F,EAAE;IAE7C,IAAIxH,KAAK,CAACe,OAAO,GAAG8F,UAAU,CAACmB,MAAM,CAACtH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAACwB,OAAO,IAAIlC,KAAK,CAACe,OAAO,IAAI,CAAC,EAAE;MACtFxB,KAAK,CAACa,oBAAoB,CAAC,GAAG6H,aAAa,CAACD,MAAM,CAAC;IACrD;IAEAhI,KAAK,CAACyM,OAAO,GAAGzM,KAAK,CAAC2M,OAAO,GAAG,CAAC;IACjC3M,KAAK,CAAC+O,OAAO,GAAGhT,OAAO,CAACgT,OAAO;IAC/B/O,KAAK,CAACC,eAAe,GAAGD,KAAK,CAACmB,GAAG,GAAG6N,oBAAoB,GAAGrM,WAAW,GAAGsM,oBAAoB,GAAGC,sBAAsB;IACtHlP,KAAK,CAACkC,OAAO,GAAG,CAAC;IACjB,OAAOlC,KAAK;EACd,CAAC;EACGiI,aAAa,GAAG,SAASA,aAAaA,CAAChJ,KAAK,EAAE;IAChD,OAAO,CAACA,KAAK,GAAGA,KAAK,CAACyB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGzB,KAAK,CAAC,CAAC,CAAC;EACvD,CAAC;EACG;EACJkQ,eAAe,GAAG,SAASA,eAAeA,CAAC9P,MAAM,EAAEgJ,KAAK,EAAEpJ,KAAK,EAAE;IAC/D,IAAI0H,IAAI,GAAGzL,OAAO,CAACmN,KAAK,CAAC;IACzB,OAAOpM,MAAM,CAAC4K,UAAU,CAACwB,KAAK,CAAC,GAAGxB,UAAU,CAACH,cAAc,CAACrH,MAAM,EAAE,GAAG,EAAEJ,KAAK,GAAG,IAAI,EAAE0H,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;EACvG,CAAC;EACGuI,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC/Q,KAAK,EAAE6B,KAAK,EAAE;IACzEA,KAAK,CAACiN,CAAC,GAAG,KAAK;IACfjN,KAAK,CAACoN,SAAS,GAAGpN,KAAK,CAACmN,SAAS,GAAG,MAAM;IAC1CnN,KAAK,CAAC+O,OAAO,GAAG,CAAC;IAEjBE,oBAAoB,CAAC9Q,KAAK,EAAE6B,KAAK,CAAC;EACpC,CAAC;EACGoP,QAAQ,GAAG,MAAM;EACjBC,OAAO,GAAG,KAAK;EACfC,eAAe,GAAG,IAAI;EACtBL,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC9Q,KAAK,EAAE6B,KAAK,EAAE;IACrE,IAAIuP,IAAI,GAAGvP,KAAK,IAAI,IAAI;MACpB0O,QAAQ,GAAGa,IAAI,CAACb,QAAQ;MACxBE,QAAQ,GAAGW,IAAI,CAACX,QAAQ;MACxB9N,CAAC,GAAGyO,IAAI,CAACzO,CAAC;MACVsE,CAAC,GAAGmK,IAAI,CAACnK,CAAC;MACV6H,CAAC,GAAGsC,IAAI,CAACtC,CAAC;MACVC,QAAQ,GAAGqC,IAAI,CAACrC,QAAQ;MACxBE,SAAS,GAAGmC,IAAI,CAACnC,SAAS;MAC1BD,SAAS,GAAGoC,IAAI,CAACpC,SAAS;MAC1BE,KAAK,GAAGkC,IAAI,CAAClC,KAAK;MAClBC,KAAK,GAAGiC,IAAI,CAACjC,KAAK;MAClBzN,MAAM,GAAG0P,IAAI,CAAC1P,MAAM;MACpBC,MAAM,GAAGyP,IAAI,CAACzP,MAAM;MACpBgP,oBAAoB,GAAGS,IAAI,CAACT,oBAAoB;MAChDC,OAAO,GAAGQ,IAAI,CAACR,OAAO;MACtB1P,MAAM,GAAGkQ,IAAI,CAAClQ,MAAM;MACpB0B,OAAO,GAAGwO,IAAI,CAACxO,OAAO;MACtByO,UAAU,GAAG,EAAE;MACfC,KAAK,GAAGV,OAAO,KAAK,MAAM,IAAI5Q,KAAK,IAAIA,KAAK,KAAK,CAAC,IAAI4Q,OAAO,KAAK,IAAI,CAAC,CAAC;;IAG5E,IAAIhO,OAAO,KAAKoM,SAAS,KAAKiC,QAAQ,IAAIhC,SAAS,KAAKgC,QAAQ,CAAC,EAAE;MACjE,IAAI5B,KAAK,GAAG3G,UAAU,CAACuG,SAAS,CAAC,GAAG7P,QAAQ;QACxCyQ,GAAG,GAAG3Q,IAAI,CAACqQ,GAAG,CAACF,KAAK,CAAC;QACrBU,GAAG,GAAG7Q,IAAI,CAACoQ,GAAG,CAACD,KAAK,CAAC;QACrBC,GAAG;MAEPD,KAAK,GAAG3G,UAAU,CAACsG,SAAS,CAAC,GAAG5P,QAAQ;MACxCkQ,GAAG,GAAGpQ,IAAI,CAACoQ,GAAG,CAACD,KAAK,CAAC;MACrB1M,CAAC,GAAGqO,eAAe,CAAC9P,MAAM,EAAEyB,CAAC,EAAEkN,GAAG,GAAGP,GAAG,GAAG,CAAC1M,OAAO,CAAC;MACpDqE,CAAC,GAAG+J,eAAe,CAAC9P,MAAM,EAAE+F,CAAC,EAAE,CAAC/H,IAAI,CAACqQ,GAAG,CAACF,KAAK,CAAC,GAAG,CAACzM,OAAO,CAAC;MAC3DkM,CAAC,GAAGkC,eAAe,CAAC9P,MAAM,EAAE4N,CAAC,EAAEiB,GAAG,GAAGT,GAAG,GAAG,CAAC1M,OAAO,GAAGA,OAAO,CAAC;IAChE;IAEA,IAAI+N,oBAAoB,KAAKO,OAAO,EAAE;MACpCG,UAAU,IAAI,cAAc,GAAGV,oBAAoB,GAAGQ,eAAe;IACvE;IAEA,IAAIZ,QAAQ,IAAIE,QAAQ,EAAE;MACxBY,UAAU,IAAI,YAAY,GAAGd,QAAQ,GAAG,KAAK,GAAGE,QAAQ,GAAG,KAAK;IAClE;IAEA,IAAIa,KAAK,IAAI3O,CAAC,KAAKuO,OAAO,IAAIjK,CAAC,KAAKiK,OAAO,IAAIpC,CAAC,KAAKoC,OAAO,EAAE;MAC5DG,UAAU,IAAIvC,CAAC,KAAKoC,OAAO,IAAII,KAAK,GAAG,cAAc,GAAG3O,CAAC,GAAG,IAAI,GAAGsE,CAAC,GAAG,IAAI,GAAG6H,CAAC,GAAG,IAAI,GAAG,YAAY,GAAGnM,CAAC,GAAG,IAAI,GAAGsE,CAAC,GAAGkK,eAAe;IACxI;IAEA,IAAIpC,QAAQ,KAAKkC,QAAQ,EAAE;MACzBI,UAAU,IAAI,SAAS,GAAGtC,QAAQ,GAAGoC,eAAe;IACtD;IAEA,IAAIlC,SAAS,KAAKgC,QAAQ,EAAE;MAC1BI,UAAU,IAAI,UAAU,GAAGpC,SAAS,GAAGkC,eAAe;IACxD;IAEA,IAAInC,SAAS,KAAKiC,QAAQ,EAAE;MAC1BI,UAAU,IAAI,UAAU,GAAGrC,SAAS,GAAGmC,eAAe;IACxD;IAEA,IAAIjC,KAAK,KAAK+B,QAAQ,IAAI9B,KAAK,KAAK8B,QAAQ,EAAE;MAC5CI,UAAU,IAAI,OAAO,GAAGnC,KAAK,GAAG,IAAI,GAAGC,KAAK,GAAGgC,eAAe;IAChE;IAEA,IAAIzP,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE;MAChC0P,UAAU,IAAI,QAAQ,GAAG3P,MAAM,GAAG,IAAI,GAAGC,MAAM,GAAGwP,eAAe;IACnE;IAEAjQ,MAAM,CAACE,KAAK,CAACY,cAAc,CAAC,GAAGqP,UAAU,IAAI,iBAAiB;EAChE,CAAC;EACGR,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC7Q,KAAK,EAAE6B,KAAK,EAAE;IACrE,IAAI0P,KAAK,GAAG1P,KAAK,IAAI,IAAI;MACrB0O,QAAQ,GAAGgB,KAAK,CAAChB,QAAQ;MACzBE,QAAQ,GAAGc,KAAK,CAACd,QAAQ;MACzB9N,CAAC,GAAG4O,KAAK,CAAC5O,CAAC;MACXsE,CAAC,GAAGsK,KAAK,CAACtK,CAAC;MACX8H,QAAQ,GAAGwC,KAAK,CAACxC,QAAQ;MACzBG,KAAK,GAAGqC,KAAK,CAACrC,KAAK;MACnBC,KAAK,GAAGoC,KAAK,CAACpC,KAAK;MACnBzN,MAAM,GAAG6P,KAAK,CAAC7P,MAAM;MACrBC,MAAM,GAAG4P,KAAK,CAAC5P,MAAM;MACrBT,MAAM,GAAGqQ,KAAK,CAACrQ,MAAM;MACrBgN,OAAO,GAAGqD,KAAK,CAACrD,OAAO;MACvBE,OAAO,GAAGmD,KAAK,CAACnD,OAAO;MACvBE,OAAO,GAAGiD,KAAK,CAACjD,OAAO;MACvBE,OAAO,GAAG+C,KAAK,CAAC/C,OAAO;MACvB8B,QAAQ,GAAGiB,KAAK,CAACjB,QAAQ;MACzB7B,EAAE,GAAG/F,UAAU,CAAC/F,CAAC,CAAC;MAClB+L,EAAE,GAAGhG,UAAU,CAACzB,CAAC,CAAC;MAClBuK,GAAG;MACHC,GAAG;MACHjC,GAAG;MACHC,GAAG;MACHvC,IAAI;IAER6B,QAAQ,GAAGrG,UAAU,CAACqG,QAAQ,CAAC;IAC/BG,KAAK,GAAGxG,UAAU,CAACwG,KAAK,CAAC;IACzBC,KAAK,GAAGzG,UAAU,CAACyG,KAAK,CAAC;IAEzB,IAAIA,KAAK,EAAE;MACT;MACAA,KAAK,GAAGzG,UAAU,CAACyG,KAAK,CAAC;MACzBD,KAAK,IAAIC,KAAK;MACdJ,QAAQ,IAAII,KAAK;IACnB;IAEA,IAAIJ,QAAQ,IAAIG,KAAK,EAAE;MACrBH,QAAQ,IAAI3P,QAAQ;MACpB8P,KAAK,IAAI9P,QAAQ;MACjBoS,GAAG,GAAGtS,IAAI,CAACoQ,GAAG,CAACP,QAAQ,CAAC,GAAGrN,MAAM;MACjC+P,GAAG,GAAGvS,IAAI,CAACqQ,GAAG,CAACR,QAAQ,CAAC,GAAGrN,MAAM;MACjC8N,GAAG,GAAGtQ,IAAI,CAACqQ,GAAG,CAACR,QAAQ,GAAGG,KAAK,CAAC,GAAG,CAACvN,MAAM;MAC1C8N,GAAG,GAAGvQ,IAAI,CAACoQ,GAAG,CAACP,QAAQ,GAAGG,KAAK,CAAC,GAAGvN,MAAM;MAEzC,IAAIuN,KAAK,EAAE;QACTC,KAAK,IAAI/P,QAAQ;QACjB8N,IAAI,GAAGhO,IAAI,CAACwS,GAAG,CAACxC,KAAK,GAAGC,KAAK,CAAC;QAC9BjC,IAAI,GAAGhO,IAAI,CAACkR,IAAI,CAAC,CAAC,GAAGlD,IAAI,GAAGA,IAAI,CAAC;QACjCsC,GAAG,IAAItC,IAAI;QACXuC,GAAG,IAAIvC,IAAI;QAEX,IAAIiC,KAAK,EAAE;UACTjC,IAAI,GAAGhO,IAAI,CAACwS,GAAG,CAACvC,KAAK,CAAC;UACtBjC,IAAI,GAAGhO,IAAI,CAACkR,IAAI,CAAC,CAAC,GAAGlD,IAAI,GAAGA,IAAI,CAAC;UACjCsE,GAAG,IAAItE,IAAI;UACXuE,GAAG,IAAIvE,IAAI;QACb;MACF;MAEAsE,GAAG,GAAG1T,MAAM,CAAC0T,GAAG,CAAC;MACjBC,GAAG,GAAG3T,MAAM,CAAC2T,GAAG,CAAC;MACjBjC,GAAG,GAAG1R,MAAM,CAAC0R,GAAG,CAAC;MACjBC,GAAG,GAAG3R,MAAM,CAAC2R,GAAG,CAAC;IACnB,CAAC,MAAM;MACL+B,GAAG,GAAG9P,MAAM;MACZ+N,GAAG,GAAG9N,MAAM;MACZ8P,GAAG,GAAGjC,GAAG,GAAG,CAAC;IACf;IAEA,IAAIf,EAAE,IAAI,CAAC,CAAC,CAAC9L,CAAC,GAAG,EAAE,EAAEL,OAAO,CAAC,IAAI,CAAC,IAAIoM,EAAE,IAAI,CAAC,CAAC,CAACzH,CAAC,GAAG,EAAE,EAAE3E,OAAO,CAAC,IAAI,CAAC,EAAE;MACpEmM,EAAE,GAAGlG,cAAc,CAACrH,MAAM,EAAE,GAAG,EAAEyB,CAAC,EAAE,IAAI,CAAC;MACzC+L,EAAE,GAAGnG,cAAc,CAACrH,MAAM,EAAE,GAAG,EAAE+F,CAAC,EAAE,IAAI,CAAC;IAC3C;IAEA,IAAIiH,OAAO,IAAIE,OAAO,IAAIE,OAAO,IAAIE,OAAO,EAAE;MAC5CC,EAAE,GAAG3Q,MAAM,CAAC2Q,EAAE,GAAGP,OAAO,IAAIA,OAAO,GAAGsD,GAAG,GAAGpD,OAAO,GAAGoB,GAAG,CAAC,GAAGlB,OAAO,CAAC;MACrEI,EAAE,GAAG5Q,MAAM,CAAC4Q,EAAE,GAAGN,OAAO,IAAIF,OAAO,GAAGuD,GAAG,GAAGrD,OAAO,GAAGqB,GAAG,CAAC,GAAGjB,OAAO,CAAC;IACvE;IAEA,IAAI+B,QAAQ,IAAIE,QAAQ,EAAE;MACxB;MACAvD,IAAI,GAAGhM,MAAM,CAACoF,OAAO,CAAC,CAAC;MACvBmI,EAAE,GAAG3Q,MAAM,CAAC2Q,EAAE,GAAG8B,QAAQ,GAAG,GAAG,GAAGrD,IAAI,CAACnG,KAAK,CAAC;MAC7C2H,EAAE,GAAG5Q,MAAM,CAAC4Q,EAAE,GAAG+B,QAAQ,GAAG,GAAG,GAAGvD,IAAI,CAAClG,MAAM,CAAC;IAChD;IAEAkG,IAAI,GAAG,SAAS,GAAGsE,GAAG,GAAG,GAAG,GAAGC,GAAG,GAAG,GAAG,GAAGjC,GAAG,GAAG,GAAG,GAAGC,GAAG,GAAG,GAAG,GAAGhB,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG;IACtFxN,MAAM,CAAC2C,YAAY,CAAC,WAAW,EAAEqJ,IAAI,CAAC;IACtCoD,QAAQ,KAAKpP,MAAM,CAACE,KAAK,CAACY,cAAc,CAAC,GAAGkL,IAAI,CAAC,CAAC,CAAC;EACrD,CAAC;EACGyE,uBAAuB,GAAG,SAASA,uBAAuBA,CAAClK,MAAM,EAAEvG,MAAM,EAAEC,QAAQ,EAAEoJ,QAAQ,EAAEG,QAAQ,EAAE;IAC3G,IAAIkH,GAAG,GAAG,GAAG;MACTC,QAAQ,GAAG7U,SAAS,CAAC0N,QAAQ,CAAC;MAC9BC,MAAM,GAAGjC,UAAU,CAACgC,QAAQ,CAAC,IAAImH,QAAQ,IAAI,CAACnH,QAAQ,CAACpI,OAAO,CAAC,KAAK,CAAC,GAAGrD,QAAQ,GAAG,CAAC,CAAC;MACrF6S,MAAM,GAAGnH,MAAM,GAAGJ,QAAQ;MAC1BwH,UAAU,GAAGxH,QAAQ,GAAGuH,MAAM,GAAG,KAAK;MACtCE,SAAS;MACTnK,EAAE;IAEN,IAAIgK,QAAQ,EAAE;MACZG,SAAS,GAAGtH,QAAQ,CAACnI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAElC,IAAIyP,SAAS,KAAK,OAAO,EAAE;QACzBF,MAAM,IAAIF,GAAG;QAEb,IAAIE,MAAM,KAAKA,MAAM,IAAIF,GAAG,GAAG,CAAC,CAAC,EAAE;UACjCE,MAAM,IAAIA,MAAM,GAAG,CAAC,GAAGF,GAAG,GAAG,CAACA,GAAG;QACnC;MACF;MAEA,IAAII,SAAS,KAAK,IAAI,IAAIF,MAAM,GAAG,CAAC,EAAE;QACpCA,MAAM,GAAG,CAACA,MAAM,GAAGF,GAAG,GAAGrS,OAAO,IAAIqS,GAAG,GAAG,CAAC,EAAEE,MAAM,GAAGF,GAAG,CAAC,GAAGA,GAAG;MAClE,CAAC,MAAM,IAAII,SAAS,KAAK,KAAK,IAAIF,MAAM,GAAG,CAAC,EAAE;QAC5CA,MAAM,GAAG,CAACA,MAAM,GAAGF,GAAG,GAAGrS,OAAO,IAAIqS,GAAG,GAAG,CAAC,EAAEE,MAAM,GAAGF,GAAG,CAAC,GAAGA,GAAG;MAClE;IACF;IAEAnK,MAAM,CAACK,GAAG,GAAGD,EAAE,GAAG,IAAIlK,SAAS,CAAC8J,MAAM,CAACK,GAAG,EAAE5G,MAAM,EAAEC,QAAQ,EAAEoJ,QAAQ,EAAEuH,MAAM,EAAErR,kBAAkB,CAAC;IACnGoH,EAAE,CAACnH,CAAC,GAAGqR,UAAU;IACjBlK,EAAE,CAACrH,CAAC,GAAG,KAAK;IAEZiH,MAAM,CAACM,MAAM,CAAC5E,IAAI,CAAChC,QAAQ,CAAC;IAE5B,OAAO0G,EAAE;EACX,CAAC;EACGoK,OAAO,GAAG,SAASA,OAAOA,CAAC/Q,MAAM,EAAEgR,MAAM,EAAE;IAC7C;IACA,KAAK,IAAI9R,CAAC,IAAI8R,MAAM,EAAE;MACpBhR,MAAM,CAACd,CAAC,CAAC,GAAG8R,MAAM,CAAC9R,CAAC,CAAC;IACvB;IAEA,OAAOc,MAAM;EACf,CAAC;EACGiR,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC1K,MAAM,EAAE4J,UAAU,EAAEnQ,MAAM,EAAE;IACjF;IACA,IAAIkR,UAAU,GAAGH,OAAO,CAAC,CAAC,CAAC,EAAE/Q,MAAM,CAACM,KAAK,CAAC;MACtC6Q,OAAO,GAAG,+CAA+C;MACzDjR,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpBkR,QAAQ;MACRlS,CAAC;MACDqK,UAAU;MACVC,QAAQ;MACRH,QAAQ;MACRI,MAAM;MACNG,SAAS;MACTD,OAAO;IAEX,IAAIuH,UAAU,CAACpP,GAAG,EAAE;MAClByH,UAAU,GAAGvJ,MAAM,CAACgC,YAAY,CAAC,WAAW,CAAC;MAC7ChC,MAAM,CAAC2C,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;MACpCzC,KAAK,CAACY,cAAc,CAAC,GAAGqP,UAAU;MAClCiB,QAAQ,GAAG1I,eAAe,CAAC1I,MAAM,EAAE,CAAC,CAAC;MAErCmG,eAAe,CAACnG,MAAM,EAAEc,cAAc,CAAC;MAEvCd,MAAM,CAAC2C,YAAY,CAAC,WAAW,EAAE4G,UAAU,CAAC;IAC9C,CAAC,MAAM;MACLA,UAAU,GAAGxF,gBAAgB,CAAC/D,MAAM,CAAC,CAACc,cAAc,CAAC;MACrDZ,KAAK,CAACY,cAAc,CAAC,GAAGqP,UAAU;MAClCiB,QAAQ,GAAG1I,eAAe,CAAC1I,MAAM,EAAE,CAAC,CAAC;MACrCE,KAAK,CAACY,cAAc,CAAC,GAAGyI,UAAU;IACpC;IAEA,KAAKrK,CAAC,IAAIpB,eAAe,EAAE;MACzByL,UAAU,GAAG2H,UAAU,CAAChS,CAAC,CAAC;MAC1BsK,QAAQ,GAAG4H,QAAQ,CAAClS,CAAC,CAAC;MAEtB,IAAIqK,UAAU,KAAKC,QAAQ,IAAI2H,OAAO,CAAC/P,OAAO,CAAClC,CAAC,CAAC,GAAG,CAAC,EAAE;QACrD;QACA0K,SAAS,GAAG/N,OAAO,CAAC0N,UAAU,CAAC;QAC/BI,OAAO,GAAG9N,OAAO,CAAC2N,QAAQ,CAAC;QAC3BH,QAAQ,GAAGO,SAAS,KAAKD,OAAO,GAAGtC,cAAc,CAACrH,MAAM,EAAEd,CAAC,EAAEqK,UAAU,EAAEI,OAAO,CAAC,GAAGnC,UAAU,CAAC+B,UAAU,CAAC;QAC1GE,MAAM,GAAGjC,UAAU,CAACgC,QAAQ,CAAC;QAC7BjD,MAAM,CAACK,GAAG,GAAG,IAAInK,SAAS,CAAC8J,MAAM,CAACK,GAAG,EAAEwK,QAAQ,EAAElS,CAAC,EAAEmK,QAAQ,EAAEI,MAAM,GAAGJ,QAAQ,EAAExK,cAAc,CAAC;QAChG0H,MAAM,CAACK,GAAG,CAACtH,CAAC,GAAGqK,OAAO,IAAI,CAAC;QAE3BpD,MAAM,CAACM,MAAM,CAAC5E,IAAI,CAAC/C,CAAC,CAAC;MACvB;IACF;IAEA6R,OAAO,CAACK,QAAQ,EAAEF,UAAU,CAAC;EAC/B,CAAC,CAAC,CAAC;;AAGHhV,YAAY,CAAC,6BAA6B,EAAE,UAAUmV,IAAI,EAAEpI,KAAK,EAAE;EACjE,IAAIhK,CAAC,GAAG,KAAK;IACToL,CAAC,GAAG,OAAO;IACX3K,CAAC,GAAG,QAAQ;IACZ4R,CAAC,GAAG,MAAM;IACVzP,KAAK,GAAG,CAACoH,KAAK,GAAG,CAAC,GAAG,CAAChK,CAAC,EAAEoL,CAAC,EAAE3K,CAAC,EAAE4R,CAAC,CAAC,GAAG,CAACrS,CAAC,GAAGqS,CAAC,EAAErS,CAAC,GAAGoL,CAAC,EAAE3K,CAAC,GAAG2K,CAAC,EAAE3K,CAAC,GAAG4R,CAAC,CAAC,EAAE3F,GAAG,CAAC,UAAU4F,IAAI,EAAE;MACxF,OAAOtI,KAAK,GAAG,CAAC,GAAGoI,IAAI,GAAGE,IAAI,GAAG,QAAQ,GAAGA,IAAI,GAAGF,IAAI;IACzD,CAAC,CAAC;EAEFxI,aAAa,CAACI,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAGoI,IAAI,GAAGA,IAAI,CAAC,GAAG,UAAU9K,MAAM,EAAEvG,MAAM,EAAEC,QAAQ,EAAEuJ,QAAQ,EAAEuB,KAAK,EAAE;IACvG,IAAIxJ,CAAC,EAAEiQ,IAAI;IAEX,IAAIC,SAAS,CAAClP,MAAM,GAAG,CAAC,EAAE;MACxB;MACAhB,CAAC,GAAGM,KAAK,CAAC8J,GAAG,CAAC,UAAU5C,IAAI,EAAE;QAC5B,OAAOvH,IAAI,CAAC+E,MAAM,EAAEwC,IAAI,EAAE9I,QAAQ,CAAC;MACrC,CAAC,CAAC;MACFuR,IAAI,GAAGjQ,CAAC,CAACsJ,IAAI,CAAC,GAAG,CAAC;MAClB,OAAO2G,IAAI,CAACnQ,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgB,MAAM,KAAK,CAAC,GAAGhB,CAAC,CAAC,CAAC,CAAC,GAAGiQ,IAAI;IACpD;IAEAjQ,CAAC,GAAG,CAACiI,QAAQ,GAAG,EAAE,EAAEnI,KAAK,CAAC,GAAG,CAAC;IAC9BmQ,IAAI,GAAG,CAAC,CAAC;IACT3P,KAAK,CAACP,OAAO,CAAC,UAAUyH,IAAI,EAAEzG,CAAC,EAAE;MAC/B,OAAOkP,IAAI,CAACzI,IAAI,CAAC,GAAGxH,CAAC,CAACe,CAAC,CAAC,GAAGf,CAAC,CAACe,CAAC,CAAC,IAAIf,CAAC,CAAC,CAACe,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC,CAAC;IACFiE,MAAM,CAACmL,IAAI,CAAC1R,MAAM,EAAEwR,IAAI,EAAEzG,KAAK,CAAC;EAClC,CAAC;AACH,CAAC,CAAC;AAEF,OAAO,IAAI4G,SAAS,GAAG;EACrBN,IAAI,EAAE,KAAK;EACXO,QAAQ,EAAErN,SAAS;EACnBsN,UAAU,EAAE,SAASA,UAAUA,CAAC7R,MAAM,EAAE;IACtC,OAAOA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACqD,QAAQ;EACxC,CAAC;EACDqO,IAAI,EAAE,SAASA,IAAIA,CAAC1R,MAAM,EAAEwR,IAAI,EAAEzG,KAAK,EAAE9B,KAAK,EAAE6I,OAAO,EAAE;IACvD,IAAIjQ,KAAK,GAAG,IAAI,CAACgF,MAAM;MACnB3G,KAAK,GAAGF,MAAM,CAACE,KAAK;MACpB6R,OAAO,GAAGhH,KAAK,CAACyG,IAAI,CAACO,OAAO;MAC5BxI,UAAU;MACVC,QAAQ;MACRC,MAAM;MACNJ,QAAQ;MACR7F,IAAI;MACJwO,WAAW;MACX9S,CAAC;MACD0K,SAAS;MACTD,OAAO;MACPsI,QAAQ;MACRC,kBAAkB;MAClBC,kBAAkB;MAClBxR,KAAK;MACLiM,MAAM;MACNwF,WAAW;MACXC,WAAW;IACf9U,cAAc,IAAIgH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE/B,IAAI,CAAC+N,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIxP,cAAc,CAAC9C,MAAM,CAAC;IACnDqS,WAAW,GAAG,IAAI,CAACC,MAAM,CAACzQ,KAAK;IAC/B,IAAI,CAACkJ,KAAK,GAAGA,KAAK;IAElB,KAAK7L,CAAC,IAAIsS,IAAI,EAAE;MACd,IAAItS,CAAC,KAAK,WAAW,EAAE;QACrB;MACF;MAEAsK,QAAQ,GAAGgI,IAAI,CAACtS,CAAC,CAAC;MAElB,IAAI3C,QAAQ,CAAC2C,CAAC,CAAC,IAAI7C,YAAY,CAAC6C,CAAC,EAAEsS,IAAI,EAAEzG,KAAK,EAAE9B,KAAK,EAAEjJ,MAAM,EAAE8R,OAAO,CAAC,EAAE;QACvE;QACA;MACF;MAEAtO,IAAI,GAAG,OAAOgG,QAAQ;MACtBwI,WAAW,GAAGnJ,aAAa,CAAC3J,CAAC,CAAC;MAE9B,IAAIsE,IAAI,KAAK,UAAU,EAAE;QACvBgG,QAAQ,GAAGA,QAAQ,CAAC5H,IAAI,CAACmJ,KAAK,EAAE9B,KAAK,EAAEjJ,MAAM,EAAE8R,OAAO,CAAC;QACvDtO,IAAI,GAAG,OAAOgG,QAAQ;MACxB;MAEA,IAAIhG,IAAI,KAAK,QAAQ,IAAI,CAACgG,QAAQ,CAACpI,OAAO,CAAC,SAAS,CAAC,EAAE;QACrDoI,QAAQ,GAAGlN,cAAc,CAACkN,QAAQ,CAAC;MACrC;MAEA,IAAIwI,WAAW,EAAE;QACfA,WAAW,CAAC,IAAI,EAAEhS,MAAM,EAAEd,CAAC,EAAEsK,QAAQ,EAAEuB,KAAK,CAAC,KAAKqH,WAAW,GAAG,CAAC,CAAC;MACpE,CAAC,MAAM,IAAIlT,CAAC,CAACsD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;QAClC;QACA+G,UAAU,GAAG,CAACxF,gBAAgB,CAAC/D,MAAM,CAAC,CAACgE,gBAAgB,CAAC9E,CAAC,CAAC,GAAG,EAAE,EAAEwI,IAAI,CAAC,CAAC;QACvE8B,QAAQ,IAAI,EAAE;QACdxM,SAAS,CAACiN,SAAS,GAAG,CAAC;QAEvB,IAAI,CAACjN,SAAS,CAAC4K,IAAI,CAAC2B,UAAU,CAAC,EAAE;UAC/B;UACAK,SAAS,GAAG/N,OAAO,CAAC0N,UAAU,CAAC;UAC/BI,OAAO,GAAG9N,OAAO,CAAC2N,QAAQ,CAAC;QAC7B;QAEAG,OAAO,GAAGC,SAAS,KAAKD,OAAO,KAAKJ,UAAU,GAAGlC,cAAc,CAACrH,MAAM,EAAEd,CAAC,EAAEqK,UAAU,EAAEI,OAAO,CAAC,GAAGA,OAAO,CAAC,GAAGC,SAAS,KAAKJ,QAAQ,IAAII,SAAS,CAAC;QACjJ,IAAI,CAAC2I,GAAG,CAACrS,KAAK,EAAE,aAAa,EAAEqJ,UAAU,EAAEC,QAAQ,EAAEP,KAAK,EAAE6I,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE5S,CAAC,CAAC;QAC7E2C,KAAK,CAACI,IAAI,CAAC/C,CAAC,CAAC;QACbmT,WAAW,CAACpQ,IAAI,CAAC/C,CAAC,EAAE,CAAC,EAAEgB,KAAK,CAAChB,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM,IAAIsE,IAAI,KAAK,WAAW,EAAE;QAC/B,IAAIuO,OAAO,IAAI7S,CAAC,IAAI6S,OAAO,EAAE;UAC3B;UACAxI,UAAU,GAAG,OAAOwI,OAAO,CAAC7S,CAAC,CAAC,KAAK,UAAU,GAAG6S,OAAO,CAAC7S,CAAC,CAAC,CAAC0C,IAAI,CAACmJ,KAAK,EAAE9B,KAAK,EAAEjJ,MAAM,EAAE8R,OAAO,CAAC,GAAGC,OAAO,CAAC7S,CAAC,CAAC;UAC3GpD,SAAS,CAACyN,UAAU,CAAC,IAAI,CAACA,UAAU,CAACnI,OAAO,CAAC,SAAS,CAAC,KAAKmI,UAAU,GAAGjN,cAAc,CAACiN,UAAU,CAAC,CAAC;UACpG1N,OAAO,CAAC0N,UAAU,GAAG,EAAE,CAAC,IAAIA,UAAU,KAAK,MAAM,KAAKA,UAAU,IAAI7M,OAAO,CAACwN,KAAK,CAAChL,CAAC,CAAC,IAAIrD,OAAO,CAAC2F,IAAI,CAACxB,MAAM,EAAEd,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEzH,CAACqK,UAAU,GAAG,EAAE,EAAElF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAKkF,UAAU,GAAG/H,IAAI,CAACxB,MAAM,EAAEd,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,MAAM;UACLqK,UAAU,GAAG/H,IAAI,CAACxB,MAAM,EAAEd,CAAC,CAAC;QAC9B;QAEAmK,QAAQ,GAAG7B,UAAU,CAAC+B,UAAU,CAAC;QACjC0I,QAAQ,GAAGzO,IAAI,KAAK,QAAQ,IAAIgG,QAAQ,CAACnF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAImF,QAAQ,CAAChH,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACnFyP,QAAQ,KAAKzI,QAAQ,GAAGA,QAAQ,CAAChH,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3CiH,MAAM,GAAGjC,UAAU,CAACgC,QAAQ,CAAC;QAE7B,IAAItK,CAAC,IAAIT,gBAAgB,EAAE;UACzB,IAAIS,CAAC,KAAK,WAAW,EAAE;YACrB;YACA,IAAImK,QAAQ,KAAK,CAAC,IAAI7H,IAAI,CAACxB,MAAM,EAAE,YAAY,CAAC,KAAK,QAAQ,IAAIyJ,MAAM,EAAE;cACvE;cACAJ,QAAQ,GAAG,CAAC;YACd;YAEAgJ,WAAW,CAACpQ,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE/B,KAAK,CAACsS,UAAU,CAAC;YAEnDlM,iBAAiB,CAAC,IAAI,EAAEpG,KAAK,EAAE,YAAY,EAAEmJ,QAAQ,GAAG,SAAS,GAAG,QAAQ,EAAEI,MAAM,GAAG,SAAS,GAAG,QAAQ,EAAE,CAACA,MAAM,CAAC;UACvH;UAEA,IAAIvK,CAAC,KAAK,OAAO,IAAIA,CAAC,KAAK,WAAW,EAAE;YACtCA,CAAC,GAAGT,gBAAgB,CAACS,CAAC,CAAC;YACvB,CAACA,CAAC,CAACkC,OAAO,CAAC,GAAG,CAAC,KAAKlC,CAAC,GAAGA,CAAC,CAACmC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C;QACF;QAEA6Q,kBAAkB,GAAGhT,CAAC,IAAIpB,eAAe,CAAC,CAAC;;QAE3C,IAAIoU,kBAAkB,EAAE;UACtB,IAAI,CAACI,MAAM,CAACpP,IAAI,CAAChE,CAAC,CAAC;UAEnB,IAAIsE,IAAI,KAAK,QAAQ,IAAIgG,QAAQ,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC9DN,QAAQ,GAAG5F,oBAAoB,CAAC5D,MAAM,EAAEwJ,QAAQ,CAACM,SAAS,CAAC,CAAC,EAAEN,QAAQ,CAACpI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;YACrFqI,MAAM,GAAGjC,UAAU,CAACgC,QAAQ,CAAC;UAC/B;UAEA,IAAI,CAAC2I,kBAAkB,EAAE;YACvBxR,KAAK,GAAGX,MAAM,CAACM,KAAK;YACpBK,KAAK,CAACC,eAAe,IAAI,CAAC4Q,IAAI,CAACiB,cAAc,IAAI/J,eAAe,CAAC1I,MAAM,EAAEwR,IAAI,CAACiB,cAAc,CAAC,CAAC,CAAC;;YAE/F7F,MAAM,GAAG4E,IAAI,CAACkB,YAAY,KAAK,KAAK,IAAI/R,KAAK,CAACiM,MAAM;YACpDuF,kBAAkB,GAAG,IAAI,CAACvL,GAAG,GAAG,IAAInK,SAAS,CAAC,IAAI,CAACmK,GAAG,EAAE1G,KAAK,EAAEY,cAAc,EAAE,CAAC,EAAE,CAAC,EAAEH,KAAK,CAACC,eAAe,EAAED,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE3HwR,kBAAkB,CAACQ,GAAG,GAAG,CAAC,CAAC,CAAC;UAC9B;UAEA,IAAIzT,CAAC,KAAK,OAAO,EAAE;YACjB,IAAI,CAAC0H,GAAG,GAAG,IAAInK,SAAS,CAAC,IAAI,CAACmK,GAAG,EAAEjG,KAAK,EAAE,QAAQ,EAAEA,KAAK,CAACF,MAAM,EAAE,CAACwR,QAAQ,GAAGhV,cAAc,CAAC0D,KAAK,CAACF,MAAM,EAAEwR,QAAQ,GAAGxI,MAAM,CAAC,GAAGA,MAAM,IAAI9I,KAAK,CAACF,MAAM,IAAI,CAAC,EAAE5B,cAAc,CAAC;YAC5K,IAAI,CAAC+H,GAAG,CAACtH,CAAC,GAAG,CAAC;YACduC,KAAK,CAACI,IAAI,CAAC,QAAQ,EAAE/C,CAAC,CAAC;YACvBA,CAAC,IAAI,GAAG;UACV,CAAC,MAAM,IAAIA,CAAC,KAAK,iBAAiB,EAAE;YAClCmT,WAAW,CAACpQ,IAAI,CAAClB,oBAAoB,EAAE,CAAC,EAAEb,KAAK,CAACa,oBAAoB,CAAC,CAAC;YACtEyI,QAAQ,GAAGoB,6BAA6B,CAACpB,QAAQ,CAAC,CAAC,CAAC;;YAEpD,IAAI7I,KAAK,CAACmB,GAAG,EAAE;cACb4K,eAAe,CAAC1M,MAAM,EAAEwJ,QAAQ,EAAE,CAAC,EAAEoD,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;YACvD,CAAC,MAAM;cACLjD,OAAO,GAAGnC,UAAU,CAACgC,QAAQ,CAACnI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;cAEnDsI,OAAO,KAAKhJ,KAAK,CAACe,OAAO,IAAI4E,iBAAiB,CAAC,IAAI,EAAE3F,KAAK,EAAE,SAAS,EAAEA,KAAK,CAACe,OAAO,EAAEiI,OAAO,CAAC;cAE9FrD,iBAAiB,CAAC,IAAI,EAAEpG,KAAK,EAAEhB,CAAC,EAAE0J,aAAa,CAACW,UAAU,CAAC,EAAEX,aAAa,CAACY,QAAQ,CAAC,CAAC;YACvF;YAEA;UACF,CAAC,MAAM,IAAItK,CAAC,KAAK,WAAW,EAAE;YAC5BwN,eAAe,CAAC1M,MAAM,EAAEwJ,QAAQ,EAAE,CAAC,EAAEoD,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;YAErD;UACF,CAAC,MAAM,IAAI1N,CAAC,IAAIqM,qBAAqB,EAAE;YACrCkF,uBAAuB,CAAC,IAAI,EAAE9P,KAAK,EAAEzB,CAAC,EAAEmK,QAAQ,EAAE4I,QAAQ,GAAGhV,cAAc,CAACoM,QAAQ,EAAE4I,QAAQ,GAAGzI,QAAQ,CAAC,GAAGA,QAAQ,CAAC;YAEtH;UACF,CAAC,MAAM,IAAItK,CAAC,KAAK,cAAc,EAAE;YAC/BoH,iBAAiB,CAAC,IAAI,EAAE3F,KAAK,EAAE,QAAQ,EAAEA,KAAK,CAACiM,MAAM,EAAEpD,QAAQ,CAAC;YAEhE;UACF,CAAC,MAAM,IAAItK,CAAC,KAAK,SAAS,EAAE;YAC1ByB,KAAK,CAACzB,CAAC,CAAC,GAAGsK,QAAQ;YACnB;UACF,CAAC,MAAM,IAAItK,CAAC,KAAK,WAAW,EAAE;YAC5B+R,mBAAmB,CAAC,IAAI,EAAEzH,QAAQ,EAAExJ,MAAM,CAAC;YAE3C;UACF;QACF,CAAC,MAAM,IAAI,EAAEd,CAAC,IAAIgB,KAAK,CAAC,EAAE;UACxBhB,CAAC,GAAG+E,gBAAgB,CAAC/E,CAAC,CAAC,IAAIA,CAAC;QAC9B;QAEA,IAAIgT,kBAAkB,IAAI,CAACzI,MAAM,IAAIA,MAAM,KAAK,CAAC,MAAMJ,QAAQ,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC7K,WAAW,CAACoJ,IAAI,CAAC4B,QAAQ,CAAC,IAAItK,CAAC,IAAIgB,KAAK,EAAE;UAC/H0J,SAAS,GAAG,CAACL,UAAU,GAAG,EAAE,EAAE/G,MAAM,CAAC,CAAC6G,QAAQ,GAAG,EAAE,EAAE9G,MAAM,CAAC;UAC5DkH,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;UAExBE,OAAO,GAAG9N,OAAO,CAAC2N,QAAQ,CAAC,KAAKtK,CAAC,IAAIxC,OAAO,CAACwN,KAAK,GAAGxN,OAAO,CAACwN,KAAK,CAAChL,CAAC,CAAC,GAAG0K,SAAS,CAAC;UAClFA,SAAS,KAAKD,OAAO,KAAKN,QAAQ,GAAGhC,cAAc,CAACrH,MAAM,EAAEd,CAAC,EAAEqK,UAAU,EAAEI,OAAO,CAAC,CAAC;UACpF,IAAI,CAAC/C,GAAG,GAAG,IAAInK,SAAS,CAAC,IAAI,CAACmK,GAAG,EAAEsL,kBAAkB,GAAGvR,KAAK,GAAGT,KAAK,EAAEhB,CAAC,EAAEmK,QAAQ,EAAE,CAAC4I,QAAQ,GAAGhV,cAAc,CAACoM,QAAQ,EAAE4I,QAAQ,GAAGxI,MAAM,CAAC,GAAGA,MAAM,IAAIJ,QAAQ,EAAE,CAAC6I,kBAAkB,KAAKvI,OAAO,KAAK,IAAI,IAAIzK,CAAC,KAAK,QAAQ,CAAC,IAAIsS,IAAI,CAACoB,SAAS,KAAK,KAAK,GAAGjT,qBAAqB,GAAGd,cAAc,CAAC;UACnS,IAAI,CAAC+H,GAAG,CAACtH,CAAC,GAAGqK,OAAO,IAAI,CAAC;UAEzB,IAAIC,SAAS,KAAKD,OAAO,IAAIA,OAAO,KAAK,GAAG,EAAE;YAC5C;YACA,IAAI,CAAC/C,GAAG,CAAClH,CAAC,GAAG6J,UAAU;YACvB,IAAI,CAAC3C,GAAG,CAACyD,CAAC,GAAG5K,2BAA2B;UAC1C;QACF,CAAC,MAAM,IAAI,EAAEP,CAAC,IAAIgB,KAAK,CAAC,EAAE;UACxB,IAAIhB,CAAC,IAAIc,MAAM,EAAE;YACf;YACA,IAAI,CAACuS,GAAG,CAACvS,MAAM,EAAEd,CAAC,EAAEqK,UAAU,IAAIvJ,MAAM,CAACd,CAAC,CAAC,EAAE+S,QAAQ,GAAGA,QAAQ,GAAGzI,QAAQ,GAAGA,QAAQ,EAAEP,KAAK,EAAE6I,OAAO,CAAC;UACzG,CAAC,MAAM,IAAI5S,CAAC,KAAK,gBAAgB,EAAE;YACjCrC,cAAc,CAACqC,CAAC,EAAEsK,QAAQ,CAAC;YAE3B;UACF;QACF,CAAC,MAAM;UACLV,sBAAsB,CAAClH,IAAI,CAAC,IAAI,EAAE5B,MAAM,EAAEd,CAAC,EAAEqK,UAAU,EAAE0I,QAAQ,GAAGA,QAAQ,GAAGzI,QAAQ,GAAGA,QAAQ,CAAC;QACrG;QAEA0I,kBAAkB,KAAKhT,CAAC,IAAIgB,KAAK,GAAGmS,WAAW,CAACpQ,IAAI,CAAC/C,CAAC,EAAE,CAAC,EAAEgB,KAAK,CAAChB,CAAC,CAAC,CAAC,GAAG,OAAOc,MAAM,CAACd,CAAC,CAAC,KAAK,UAAU,GAAGmT,WAAW,CAACpQ,IAAI,CAAC/C,CAAC,EAAE,CAAC,EAAEc,MAAM,CAACd,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGmT,WAAW,CAACpQ,IAAI,CAAC/C,CAAC,EAAE,CAAC,EAAEqK,UAAU,IAAIvJ,MAAM,CAACd,CAAC,CAAC,CAAC,CAAC;QAC/L2C,KAAK,CAACI,IAAI,CAAC/C,CAAC,CAAC;MACf;IACF;IAEAkT,WAAW,IAAIjW,yBAAyB,CAAC,IAAI,CAAC;EAChD,CAAC;EACD0W,MAAM,EAAE,SAASA,MAAMA,CAAC/T,KAAK,EAAEC,IAAI,EAAE;IACnC,IAAIA,IAAI,CAACgM,KAAK,CAACC,KAAK,IAAI,CAACrN,UAAU,CAAC,CAAC,EAAE;MACrC,IAAIgJ,EAAE,GAAG5H,IAAI,CAAC6H,GAAG;MAEjB,OAAOD,EAAE,EAAE;QACTA,EAAE,CAAC0D,CAAC,CAACvL,KAAK,EAAE6H,EAAE,CAACyF,CAAC,CAAC;QACjBzF,EAAE,GAAGA,EAAE,CAACwD,KAAK;MACf;IACF,CAAC,MAAM;MACLpL,IAAI,CAACuT,MAAM,CAACrP,MAAM,CAAC,CAAC;IACtB;EACF,CAAC;EACD6P,GAAG,EAAEtR,IAAI;EACTuR,OAAO,EAAEtU,gBAAgB;EACzBuU,SAAS,EAAE,SAASA,SAASA,CAAChT,MAAM,EAAEC,QAAQ,EAAEsG,MAAM,EAAE;IACtD;IACA,IAAIrH,CAAC,GAAGT,gBAAgB,CAACwB,QAAQ,CAAC;IAClCf,CAAC,IAAIA,CAAC,CAACkC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,KAAKnB,QAAQ,GAAGf,CAAC,CAAC;IACzC,OAAOe,QAAQ,IAAInC,eAAe,IAAImC,QAAQ,KAAKc,oBAAoB,KAAKf,MAAM,CAACM,KAAK,CAACmB,CAAC,IAAID,IAAI,CAACxB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAGuG,MAAM,IAAI7I,mBAAmB,KAAK6I,MAAM,GAAGtG,QAAQ,KAAK,OAAO,GAAGM,YAAY,GAAGF,gBAAgB,GAAG,CAAC3C,mBAAmB,GAAG6I,MAAM,IAAI,CAAC,CAAC,MAAMtG,QAAQ,KAAK,OAAO,GAAGS,sBAAsB,GAAGG,0BAA0B,CAAC,GAAGb,MAAM,CAACE,KAAK,IAAI,CAACnE,YAAY,CAACiE,MAAM,CAACE,KAAK,CAACD,QAAQ,CAAC,CAAC,GAAGF,eAAe,GAAG,CAACE,QAAQ,CAACmB,OAAO,CAAC,GAAG,CAAC,GAAGjB,cAAc,GAAGrD,UAAU,CAACkD,MAAM,EAAEC,QAAQ,CAAC;EAChe,CAAC;EACDkD,IAAI,EAAE;IACJgD,eAAe,EAAEA,eAAe;IAChCyF,UAAU,EAAEA;EACd;AACF,CAAC;AACDnQ,IAAI,CAACwX,KAAK,CAACC,WAAW,GAAGjP,gBAAgB;AACzCxI,IAAI,CAAC0H,IAAI,CAACgQ,aAAa,GAAGrQ,cAAc;AAExC,CAAC,UAAUsQ,gBAAgB,EAAEvF,QAAQ,EAAEwF,MAAM,EAAEN,OAAO,EAAE;EACtD,IAAIO,GAAG,GAAGpX,YAAY,CAACkX,gBAAgB,GAAG,GAAG,GAAGvF,QAAQ,GAAG,GAAG,GAAGwF,MAAM,EAAE,UAAUhC,IAAI,EAAE;IACvFvT,eAAe,CAACuT,IAAI,CAAC,GAAG,CAAC;EAC3B,CAAC,CAAC;EAEFnV,YAAY,CAAC2R,QAAQ,EAAE,UAAUwD,IAAI,EAAE;IACrC3U,OAAO,CAACwN,KAAK,CAACmH,IAAI,CAAC,GAAG,KAAK;IAC3B9F,qBAAqB,CAAC8F,IAAI,CAAC,GAAG,CAAC;EACjC,CAAC,CAAC;EAEF5S,gBAAgB,CAAC6U,GAAG,CAAC,EAAE,CAAC,CAAC,GAAGF,gBAAgB,GAAG,GAAG,GAAGvF,QAAQ;EAE7D3R,YAAY,CAAC6W,OAAO,EAAE,UAAU1B,IAAI,EAAE;IACpC,IAAIhQ,KAAK,GAAGgQ,IAAI,CAAChQ,KAAK,CAAC,GAAG,CAAC;IAC3B5C,gBAAgB,CAAC4C,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGiS,GAAG,CAACjS,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;AACJ,CAAC,EAAE,6CAA6C,EAAE,0CAA0C,EAAE,+EAA+E,EAAE,4FAA4F,CAAC;AAE5QnF,YAAY,CAAC,8EAA8E,EAAE,UAAUmV,IAAI,EAAE;EAC3G3U,OAAO,CAACwN,KAAK,CAACmH,IAAI,CAAC,GAAG,IAAI;AAC5B,CAAC,CAAC;AAEF5V,IAAI,CAAC8X,cAAc,CAAC5B,SAAS,CAAC;AAC9B,SAASA,SAAS,IAAI6B,OAAO,EAAE/N,QAAQ,EAAElC,cAAc,EAAEU,gBAAgB,IAAIiP,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}