{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './ScaleLetterTransition.css';\nconst ScaleLetterTransition = ({\n  isActive,\n  to,\n  onComplete\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const transitionRef = useRef(null);\n  const animationFrameRef = useRef();\n  const startTimeRef = useRef(0);\n  const duration = 1800; // ms\n\n  useEffect(() => {\n    if (!isActive) return;\n    const scaleTitle = document.querySelector('.hero-section h1');\n    if (!scaleTitle) {\n      navigate(to);\n      onComplete();\n      return;\n    }\n    const rect = scaleTitle.getBoundingClientRect();\n    const letterCenterX = rect.left + rect.width * 0.5;\n    const letterCenterY = rect.top + rect.height * 0.5;\n\n    // Create overlay for transition\n    const overlay = document.createElement('div');\n    overlay.className = 'scale-transition-overlay';\n    document.body.appendChild(overlay);\n\n    // Position the overlay\n    const size = Math.max(window.innerWidth, window.innerHeight) * 2;\n    overlay.style.width = `${size}px`;\n    overlay.style.height = `${size}px`;\n    overlay.style.left = `${letterCenterX - size / 2}px`;\n    overlay.style.top = `${letterCenterY - size / 2}px`;\n\n    // Start the animation\n    startTimeRef.current = performance.now();\n    const animate = currentTime => {\n      if (!startTimeRef.current) return;\n      const elapsed = currentTime - startTimeRef.current;\n      const progress = Math.min(elapsed / duration, 1);\n\n      // Ease-out scaling\n      const scale = 1 - Math.pow(1 - progress, 3);\n      overlay.style.transform = `scale(${scale})`;\n      overlay.style.opacity = `${scale}`;\n      if (progress < 1) {\n        animationFrameRef.current = requestAnimationFrame(animate);\n      } else {\n        // Cleanup and navigate\n        document.body.style.overflow = 'hidden';\n        navigate(to);\n\n        // Allow time for the new page to load\n        setTimeout(() => {\n          document.body.removeChild(overlay);\n          document.body.style.overflow = '';\n          onComplete();\n        }, 100);\n      }\n    };\n    animationFrameRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (document.body.contains(overlay)) {\n        document.body.removeChild(overlay);\n      }\n      document.body.style.overflow = '';\n    };\n  }, [isActive, navigate, onComplete, to]);\n  return null;\n};\n_s(ScaleLetterTransition, \"HrW2bbAqXwVe2g0LgQusI4BBDec=\", false, function () {\n  return [useNavigate];\n});\n_c = ScaleLetterTransition;\nexport default ScaleLetterTransition;\nvar _c;\n$RefreshReg$(_c, \"ScaleLetterTransition\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useNavigate", "ScaleLetterTransition", "isActive", "to", "onComplete", "_s", "navigate", "transitionRef", "animationFrameRef", "startTimeRef", "duration", "scaleTitle", "document", "querySelector", "rect", "getBoundingClientRect", "letterCenterX", "left", "width", "letterCenterY", "top", "height", "overlay", "createElement", "className", "body", "append<PERSON><PERSON><PERSON>", "size", "Math", "max", "window", "innerWidth", "innerHeight", "style", "current", "performance", "now", "animate", "currentTime", "elapsed", "progress", "min", "scale", "pow", "transform", "opacity", "requestAnimationFrame", "overflow", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "cancelAnimationFrame", "contains", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/ScaleLetterTransition.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './ScaleLetterTransition.css';\n\ninterface ScaleLetterTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete: () => void;\n}\n\nconst ScaleLetterTransition: React.FC<ScaleLetterTransitionProps> = ({\n  isActive,\n  to,\n  onComplete\n}) => {\n  const navigate = useNavigate();\n  const transitionRef = useRef<HTMLDivElement>(null);\n  const animationFrameRef = useRef<number>();\n  const startTimeRef = useRef<number>(0);\n  const duration = 1800; // ms\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const scaleTitle = document.querySelector('.hero-section h1');\n    if (!scaleTitle) {\n      navigate(to);\n      onComplete();\n      return;\n    }\n\n    const rect = scaleTitle.getBoundingClientRect();\n    const letterCenterX = rect.left + (rect.width * 0.5);\n    const letterCenterY = rect.top + (rect.height * 0.5);\n\n    // Create overlay for transition\n    const overlay = document.createElement('div');\n    overlay.className = 'scale-transition-overlay';\n    document.body.appendChild(overlay);\n\n    // Position the overlay\n    const size = Math.max(window.innerWidth, window.innerHeight) * 2;\n    overlay.style.width = `${size}px`;\n    overlay.style.height = `${size}px`;\n    overlay.style.left = `${letterCenterX - size/2}px`;\n    overlay.style.top = `${letterCenterY - size/2}px`;\n\n    // Start the animation\n    startTimeRef.current = performance.now();\n    const animate = (currentTime: number) => {\n      if (!startTimeRef.current) return;\n      \n      const elapsed = currentTime - startTimeRef.current;\n      const progress = Math.min(elapsed / duration, 1);\n      \n      // Ease-out scaling\n      const scale = 1 - Math.pow(1 - progress, 3);\n      overlay.style.transform = `scale(${scale})`;\n      overlay.style.opacity = `${scale}`;\n      \n      if (progress < 1) {\n        animationFrameRef.current = requestAnimationFrame(animate);\n      } else {\n        // Cleanup and navigate\n        document.body.style.overflow = 'hidden';\n        navigate(to);\n        \n        // Allow time for the new page to load\n        setTimeout(() => {\n          document.body.removeChild(overlay);\n          document.body.style.overflow = '';\n          onComplete();\n        }, 100);\n      }\n    };\n    \n    animationFrameRef.current = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (document.body.contains(overlay)) {\n        document.body.removeChild(overlay);\n      }\n      document.body.style.overflow = '';\n    };\n  }, [isActive, navigate, onComplete, to]);\n\n  return null;\n};\n\nexport default ScaleLetterTransition;"], "mappings": ";AAAA,SAAgBA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,6BAA6B;AAQpC,MAAMC,qBAA2D,GAAGA,CAAC;EACnEC,QAAQ;EACRC,EAAE;EACFC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,aAAa,GAAGR,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAMS,iBAAiB,GAAGT,MAAM,CAAS,CAAC;EAC1C,MAAMU,YAAY,GAAGV,MAAM,CAAS,CAAC,CAAC;EACtC,MAAMW,QAAQ,GAAG,IAAI,CAAC,CAAC;;EAEvBZ,SAAS,CAAC,MAAM;IACd,IAAI,CAACI,QAAQ,EAAE;IAEf,MAAMS,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;IAC7D,IAAI,CAACF,UAAU,EAAE;MACfL,QAAQ,CAACH,EAAE,CAAC;MACZC,UAAU,CAAC,CAAC;MACZ;IACF;IAEA,MAAMU,IAAI,GAAGH,UAAU,CAACI,qBAAqB,CAAC,CAAC;IAC/C,MAAMC,aAAa,GAAGF,IAAI,CAACG,IAAI,GAAIH,IAAI,CAACI,KAAK,GAAG,GAAI;IACpD,MAAMC,aAAa,GAAGL,IAAI,CAACM,GAAG,GAAIN,IAAI,CAACO,MAAM,GAAG,GAAI;;IAEpD;IACA,MAAMC,OAAO,GAAGV,QAAQ,CAACW,aAAa,CAAC,KAAK,CAAC;IAC7CD,OAAO,CAACE,SAAS,GAAG,0BAA0B;IAC9CZ,QAAQ,CAACa,IAAI,CAACC,WAAW,CAACJ,OAAO,CAAC;;IAElC;IACA,MAAMK,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC,GAAG,CAAC;IAChEV,OAAO,CAACW,KAAK,CAACf,KAAK,GAAG,GAAGS,IAAI,IAAI;IACjCL,OAAO,CAACW,KAAK,CAACZ,MAAM,GAAG,GAAGM,IAAI,IAAI;IAClCL,OAAO,CAACW,KAAK,CAAChB,IAAI,GAAG,GAAGD,aAAa,GAAGW,IAAI,GAAC,CAAC,IAAI;IAClDL,OAAO,CAACW,KAAK,CAACb,GAAG,GAAG,GAAGD,aAAa,GAAGQ,IAAI,GAAC,CAAC,IAAI;;IAEjD;IACAlB,YAAY,CAACyB,OAAO,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IACxC,MAAMC,OAAO,GAAIC,WAAmB,IAAK;MACvC,IAAI,CAAC7B,YAAY,CAACyB,OAAO,EAAE;MAE3B,MAAMK,OAAO,GAAGD,WAAW,GAAG7B,YAAY,CAACyB,OAAO;MAClD,MAAMM,QAAQ,GAAGZ,IAAI,CAACa,GAAG,CAACF,OAAO,GAAG7B,QAAQ,EAAE,CAAC,CAAC;;MAEhD;MACA,MAAMgC,KAAK,GAAG,CAAC,GAAGd,IAAI,CAACe,GAAG,CAAC,CAAC,GAAGH,QAAQ,EAAE,CAAC,CAAC;MAC3ClB,OAAO,CAACW,KAAK,CAACW,SAAS,GAAG,SAASF,KAAK,GAAG;MAC3CpB,OAAO,CAACW,KAAK,CAACY,OAAO,GAAG,GAAGH,KAAK,EAAE;MAElC,IAAIF,QAAQ,GAAG,CAAC,EAAE;QAChBhC,iBAAiB,CAAC0B,OAAO,GAAGY,qBAAqB,CAACT,OAAO,CAAC;MAC5D,CAAC,MAAM;QACL;QACAzB,QAAQ,CAACa,IAAI,CAACQ,KAAK,CAACc,QAAQ,GAAG,QAAQ;QACvCzC,QAAQ,CAACH,EAAE,CAAC;;QAEZ;QACA6C,UAAU,CAAC,MAAM;UACfpC,QAAQ,CAACa,IAAI,CAACwB,WAAW,CAAC3B,OAAO,CAAC;UAClCV,QAAQ,CAACa,IAAI,CAACQ,KAAK,CAACc,QAAQ,GAAG,EAAE;UACjC3C,UAAU,CAAC,CAAC;QACd,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IAEDI,iBAAiB,CAAC0B,OAAO,GAAGY,qBAAqB,CAACT,OAAO,CAAC;IAE1D,OAAO,MAAM;MACX,IAAI7B,iBAAiB,CAAC0B,OAAO,EAAE;QAC7BgB,oBAAoB,CAAC1C,iBAAiB,CAAC0B,OAAO,CAAC;MACjD;MACA,IAAItB,QAAQ,CAACa,IAAI,CAAC0B,QAAQ,CAAC7B,OAAO,CAAC,EAAE;QACnCV,QAAQ,CAACa,IAAI,CAACwB,WAAW,CAAC3B,OAAO,CAAC;MACpC;MACAV,QAAQ,CAACa,IAAI,CAACQ,KAAK,CAACc,QAAQ,GAAG,EAAE;IACnC,CAAC;EACH,CAAC,EAAE,CAAC7C,QAAQ,EAAEI,QAAQ,EAAEF,UAAU,EAAED,EAAE,CAAC,CAAC;EAExC,OAAO,IAAI;AACb,CAAC;AAACE,EAAA,CAhFIJ,qBAA2D;EAAA,QAK9CD,WAAW;AAAA;AAAAoD,EAAA,GALxBnD,qBAA2D;AAkFjE,eAAeA,qBAAqB;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}