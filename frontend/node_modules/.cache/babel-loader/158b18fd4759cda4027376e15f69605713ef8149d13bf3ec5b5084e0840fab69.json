{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport './Gyroscope.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GSAPGyroscope = () => {\n  _s();\n  const gyroscopeRef = useRef(null);\n  const outerRingRef = useRef(null);\n  const middleRingRef = useRef(null);\n  const innerRingRef = useRef(null);\n  const coreRef = useRef(null);\n  const particleContainerRef = useRef(null);\n  const axesRef = useRef(null);\n  const [isDragging, setIsDragging] = useState(false);\n  const animationsRef = useRef([]);\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n\n  // Initialize GSAP animations\n  useEffect(() => {\n    var _particleContainerRef;\n    // Clear any existing animations\n    animationsRef.current.forEach(tween => tween.kill());\n    animationsRef.current = [];\n\n    // Initialize all elements with proper 3D setup\n    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];\n    gsap.set(elements, {\n      transformOrigin: \"center center\",\n      transformStyle: \"preserve-3d\",\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0\n    });\n\n    // Continuous base rotations - store references for cleanup\n    const outerAnim = gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n    const middleAnim = gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n    const innerAnim = gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Core pulsing animation\n    const coreAnim = gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.15,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Axes rotation\n    const axesAnim = gsap.to(axesRef.current, {\n      duration: 30,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Store animation references\n    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim, axesAnim];\n\n    // Initialize particles with GSAP\n    const particles = (_particleContainerRef = particleContainerRef.current) === null || _particleContainerRef === void 0 ? void 0 : _particleContainerRef.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        const element = particle;\n\n        // Set initial state\n        gsap.set(element, {\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          scale: Math.random() * 0.5 + 0.5,\n          opacity: Math.random() * 0.6 + 0.4\n        });\n\n        // Floating animation\n        const particleAnim = gsap.to(element, {\n          duration: 4 + Math.random() * 6,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          scale: Math.random() * 0.8 + 0.6,\n          ease: \"sine.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 3\n        });\n        animationsRef.current.push(particleAnim);\n      });\n    }\n\n    // Cleanup function\n    return () => {\n      animationsRef.current.forEach(tween => tween.kill());\n      animationsRef.current = [];\n    };\n  }, []);\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseMove = e => {\n    if (!isDragging || !gyroscopeRef.current) return;\n    const rect = gyroscopeRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Calculate rotation based on mouse position\n    const rotationX = mouseY / rect.height * 180;\n    const rotationY = mouseX / rect.width * 180;\n    const rotationZ = (mouseX + mouseY) / (rect.width + rect.height) * 90;\n\n    // Apply interactive rotations with GSAP - these will temporarily override base animations\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: rotationX,\n      rotationY: rotationY,\n      rotationZ: rotationZ,\n      ease: \"power2.out\",\n      overwrite: \"auto\" // This allows temporary override of base animation\n    });\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: rotationX * 1.5,\n      rotationY: rotationY * 0.7,\n      rotationZ: rotationZ * -1,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: rotationX * 0.5,\n      rotationY: rotationY * 1.8,\n      rotationZ: rotationZ * 1.5,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseUp = () => {\n    setIsDragging(false);\n\n    // Spring back to base rotations with elastic ease\n    gsap.to(outerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        // Restart base rotation\n        const newAnim = gsap.to(outerRingRef.current, {\n          duration: 20,\n          rotationY: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[0] = newAnim;\n      }\n    });\n    gsap.to(middleRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(middleRingRef.current, {\n          duration: 15,\n          rotationX: -360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[1] = newAnim;\n      }\n    });\n    gsap.to(innerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(innerRingRef.current, {\n          duration: 10,\n          rotationZ: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[2] = newAnim;\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gyroscope-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ambient-effects\",\n      children: [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ambient-orb\",\n        style: {\n          width: `${150 + i * 50}px`,\n          height: `${150 + i * 50}px`,\n          left: `${15 + i * 12}%`,\n          top: `${10 + i * 15}%`,\n          animationDelay: `${i * 0.8}s`,\n          animationDuration: `${4 + i * 0.5}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 10\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"gyroscope-title\",\n        children: \"GSAP Quantum Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: gyroscopeRef,\n        className: \"gyroscope-main\",\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          ref: particleContainerRef,\n          className: \"particles-container\",\n          children: [...Array(25)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"particle\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: outerRingRef,\n          className: \"outer-ring\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: middleRingRef,\n            className: \"middle-ring\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: innerRingRef,\n              className: \"inner-ring\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: coreRef,\n                className: \"gyroscope-core\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"core-center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: axesRef,\n          className: \"rotation-axes\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"axis-horizontal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"axis-vertical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"axis-diagonal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gyroscope-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"info-line info-primary\",\n          children: \"\\u2728 Drag to manipulate quantum fields with GSAP precision\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"info-line info-secondary\",\n          children: \"Enhanced with GreenSock Animation Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"info-line info-tertiary\",\n          children: \"Smooth 60fps 3D transforms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_s(GSAPGyroscope, \"xcu3xQzCDeslOZsGnDHaPT1YvJI=\");\n_c = GSAPGyroscope;\nexport default GSAPGyroscope;\nvar _c;\n$RefreshReg$(_c, \"GSAPGyroscope\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "gsap", "jsxDEV", "_jsxDEV", "GSAPGyroscope", "_s", "gyroscopeRef", "outerRingRef", "middleRingRef", "innerRingRef", "coreRef", "particleContainerRef", "axesRef", "isDragging", "setIsDragging", "animationsRef", "lastMousePos", "x", "y", "_particleContainerRef", "current", "for<PERSON>ach", "tween", "kill", "elements", "set", "transform<PERSON><PERSON>in", "transformStyle", "rotationX", "rotationY", "rotationZ", "outerAnim", "to", "duration", "ease", "repeat", "middleAnim", "innerAnim", "coreAnim", "scale", "yoyo", "axesAnim", "particles", "children", "Array", "from", "particle", "index", "element", "Math", "random", "opacity", "particleAnim", "rotation", "delay", "push", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "mouseX", "mouseY", "overwrite", "handleMouseUp", "onComplete", "newAnim", "className", "map", "_", "i", "style", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "zIndex", "ref", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport './Gyroscope.css';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\nconst GSAPGyroscope = () => {\n  const gyroscopeRef = useRef<HTMLDivElement>(null);\n  const outerRingRef = useRef<HTMLDivElement>(null);\n  const middleRingRef = useRef<HTMLDivElement>(null);\n  const innerRingRef = useRef<HTMLDivElement>(null);\n  const coreRef = useRef<HTMLDivElement>(null);\n  const particleContainerRef = useRef<HTMLDivElement>(null);\n  const axesRef = useRef<HTMLDivElement>(null);\n  \n  const [isDragging, setIsDragging] = useState(false);\n  const animationsRef = useRef<gsap.core.Tween[]>([]);\n  const lastMousePos = useRef({ x: 0, y: 0 });\n\n  // Initialize GSAP animations\n  useEffect(() => {\n    // Clear any existing animations\n    animationsRef.current.forEach(tween => tween.kill());\n    animationsRef.current = [];\n\n    // Initialize all elements with proper 3D setup\n    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];\n    \n    gsap.set(elements, {\n      transformOrigin: \"center center\",\n      transformStyle: \"preserve-3d\",\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0\n    });\n\n    // Continuous base rotations - store references for cleanup\n    const outerAnim = gsap.to(outerRingRef.current, {\n      duration: 20,\n      rotationY: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    const middleAnim = gsap.to(middleRingRef.current, {\n      duration: 15,\n      rotationX: -360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    const innerAnim = gsap.to(innerRingRef.current, {\n      duration: 10,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Core pulsing animation\n    const coreAnim = gsap.to(coreRef.current, {\n      duration: 3,\n      scale: 1.15,\n      ease: \"power2.inOut\",\n      yoyo: true,\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Axes rotation\n    const axesAnim = gsap.to(axesRef.current, {\n      duration: 30,\n      rotationZ: 360,\n      ease: \"none\",\n      repeat: -1,\n      transformOrigin: \"center center\"\n    });\n\n    // Store animation references\n    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim, axesAnim];\n\n    // Initialize particles with GSAP\n    const particles = particleContainerRef.current?.children;\n    if (particles) {\n      Array.from(particles).forEach((particle, index) => {\n        const element = particle as HTMLElement;\n        \n        // Set initial state\n        gsap.set(element, {\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          scale: Math.random() * 0.5 + 0.5,\n          opacity: Math.random() * 0.6 + 0.4\n        });\n        \n        // Floating animation\n        const particleAnim = gsap.to(element, {\n          duration: 4 + Math.random() * 6,\n          x: (Math.random() - 0.5) * 400,\n          y: (Math.random() - 0.5) * 400,\n          rotation: Math.random() * 360,\n          scale: Math.random() * 0.8 + 0.6,\n          ease: \"sine.inOut\",\n          repeat: -1,\n          yoyo: true,\n          delay: Math.random() * 3\n        });\n        \n        animationsRef.current.push(particleAnim);\n      });\n    }\n\n    // Cleanup function\n    return () => {\n      animationsRef.current.forEach(tween => tween.kill());\n      animationsRef.current = [];\n    };\n  }, []);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging || !gyroscopeRef.current) return;\n    \n    const rect = gyroscopeRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Calculate rotation based on mouse position\n    const rotationX = (mouseY / rect.height) * 180;\n    const rotationY = (mouseX / rect.width) * 180;\n    const rotationZ = ((mouseX + mouseY) / (rect.width + rect.height)) * 90;\n\n    // Apply interactive rotations with GSAP - these will temporarily override base animations\n    gsap.to(outerRingRef.current, {\n      duration: 0.3,\n      rotationX: rotationX,\n      rotationY: rotationY,\n      rotationZ: rotationZ,\n      ease: \"power2.out\",\n      overwrite: \"auto\" // This allows temporary override of base animation\n    });\n\n    gsap.to(middleRingRef.current, {\n      duration: 0.4,\n      rotationX: rotationX * 1.5,\n      rotationY: rotationY * 0.7,\n      rotationZ: rotationZ * -1,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n\n    gsap.to(innerRingRef.current, {\n      duration: 0.5,\n      rotationX: rotationX * 0.5,\n      rotationY: rotationY * 1.8,\n      rotationZ: rotationZ * 1.5,\n      ease: \"power2.out\",\n      overwrite: \"auto\"\n    });\n\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n    \n    // Spring back to base rotations with elastic ease\n    gsap.to(outerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        // Restart base rotation\n        const newAnim = gsap.to(outerRingRef.current, {\n          duration: 20,\n          rotationY: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[0] = newAnim;\n      }\n    });\n\n    gsap.to(middleRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(middleRingRef.current, {\n          duration: 15,\n          rotationX: -360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[1] = newAnim;\n      }\n    });\n\n    gsap.to(innerRingRef.current, {\n      duration: 1.5,\n      rotationX: 0,\n      rotationY: 0,\n      rotationZ: 0,\n      ease: \"elastic.out(1, 0.3)\",\n      onComplete: () => {\n        const newAnim = gsap.to(innerRingRef.current, {\n          duration: 10,\n          rotationZ: 360,\n          ease: \"none\",\n          repeat: -1,\n          transformOrigin: \"center center\"\n        });\n        animationsRef.current[2] = newAnim;\n      }\n    });\n  };\n\n  return (\n    <div className=\"gyroscope-container\">\n      {/* Ambient Effects */}\n      <div className=\"ambient-effects\">\n        {[...Array(6)].map((_, i) => (\n          <div\n            key={i}\n            className=\"ambient-orb\"\n            style={{\n              width: `${150 + i * 50}px`,\n              height: `${150 + i * 50}px`,\n              left: `${15 + i * 12}%`,\n              top: `${10 + i * 15}%`,\n              animationDelay: `${i * 0.8}s`,\n              animationDuration: `${4 + i * 0.5}s`\n            }}\n          />\n        ))}\n      </div>\n\n      <div style={{ position: 'relative', zIndex: 10 }}>\n        <h1 className=\"gyroscope-title\">\n          GSAP Quantum Gyroscope\n        </h1>\n\n        <div\n          ref={gyroscopeRef}\n          className=\"gyroscope-main\"\n          onMouseDown={handleMouseDown}\n          onMouseMove={handleMouseMove}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseUp}\n        >\n          {/* Enhanced Particle System */}\n          <div ref={particleContainerRef} className=\"particles-container\">\n            {[...Array(25)].map((_, i) => (\n              <div\n                key={i}\n                className=\"particle\"\n              />\n            ))}\n          </div>\n\n          {/* Outer Ring */}\n          <div ref={outerRingRef} className=\"outer-ring\">\n            {/* Middle Ring */}\n            <div ref={middleRingRef} className=\"middle-ring\">\n              {/* Inner Ring */}\n              <div ref={innerRingRef} className=\"inner-ring\">\n                {/* Core */}\n                <div ref={coreRef} className=\"gyroscope-core\">\n                  <div className=\"core-center\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Rotation Axes */}\n          <div ref={axesRef} className=\"rotation-axes\">\n            <div className=\"axis-horizontal\" />\n            <div className=\"axis-vertical\" />\n            <div className=\"axis-diagonal\" />\n          </div>\n        </div>\n        \n        <div className=\"gyroscope-info\">\n          <p className=\"info-line info-primary\">✨ Drag to manipulate quantum fields with GSAP precision</p>\n          <p className=\"info-line info-secondary\">Enhanced with GreenSock Animation Platform</p>\n          <p className=\"info-line info-tertiary\">Smooth 60fps 3D transforms</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GSAPGyroscope;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,MAAM;AAC3B,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQzB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,YAAY,GAAGP,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMQ,YAAY,GAAGR,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMS,aAAa,GAAGT,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAMU,YAAY,GAAGV,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMW,OAAO,GAAGX,MAAM,CAAiB,IAAI,CAAC;EAC5C,MAAMY,oBAAoB,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EACzD,MAAMa,OAAO,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAE5C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMe,aAAa,GAAGhB,MAAM,CAAoB,EAAE,CAAC;EACnD,MAAMiB,YAAY,GAAGjB,MAAM,CAAC;IAAEkB,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;;EAE3C;EACApB,SAAS,CAAC,MAAM;IAAA,IAAAqB,qBAAA;IACd;IACAJ,aAAa,CAACK,OAAO,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;IACpDR,aAAa,CAACK,OAAO,GAAG,EAAE;;IAE1B;IACA,MAAMI,QAAQ,GAAG,CAACjB,YAAY,CAACa,OAAO,EAAEZ,aAAa,CAACY,OAAO,EAAEX,YAAY,CAACW,OAAO,EAAEV,OAAO,CAACU,OAAO,CAAC;IAErGnB,IAAI,CAACwB,GAAG,CAACD,QAAQ,EAAE;MACjBE,eAAe,EAAE,eAAe;MAChCC,cAAc,EAAE,aAAa;MAC7BC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS,GAAG9B,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC9Ca,QAAQ,EAAE,EAAE;MACZJ,SAAS,EAAE,GAAG;MACdK,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,MAAMU,UAAU,GAAGnC,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAChDa,QAAQ,EAAE,EAAE;MACZL,SAAS,EAAE,CAAC,GAAG;MACfM,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,MAAMW,SAAS,GAAGpC,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC9Ca,QAAQ,EAAE,EAAE;MACZH,SAAS,EAAE,GAAG;MACdI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMY,QAAQ,GAAGrC,IAAI,CAAC+B,EAAE,CAACtB,OAAO,CAACU,OAAO,EAAE;MACxCa,QAAQ,EAAE,CAAC;MACXM,KAAK,EAAE,IAAI;MACXL,IAAI,EAAE,cAAc;MACpBM,IAAI,EAAE,IAAI;MACVL,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACA,MAAMe,QAAQ,GAAGxC,IAAI,CAAC+B,EAAE,CAACpB,OAAO,CAACQ,OAAO,EAAE;MACxCa,QAAQ,EAAE,EAAE;MACZH,SAAS,EAAE,GAAG;MACdI,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,CAAC,CAAC;MACVT,eAAe,EAAE;IACnB,CAAC,CAAC;;IAEF;IACAX,aAAa,CAACK,OAAO,GAAG,CAACW,SAAS,EAAEK,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEG,QAAQ,CAAC;;IAE9E;IACA,MAAMC,SAAS,IAAAvB,qBAAA,GAAGR,oBAAoB,CAACS,OAAO,cAAAD,qBAAA,uBAA5BA,qBAAA,CAA8BwB,QAAQ;IACxD,IAAID,SAAS,EAAE;MACbE,KAAK,CAACC,IAAI,CAACH,SAAS,CAAC,CAACrB,OAAO,CAAC,CAACyB,QAAQ,EAAEC,KAAK,KAAK;QACjD,MAAMC,OAAO,GAAGF,QAAuB;;QAEvC;QACA7C,IAAI,CAACwB,GAAG,CAACuB,OAAO,EAAE;UAChB/B,CAAC,EAAE,CAACgC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BhC,CAAC,EAAE,CAAC+B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BX,KAAK,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCC,OAAO,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACjC,CAAC,CAAC;;QAEF;QACA,MAAME,YAAY,GAAGnD,IAAI,CAAC+B,EAAE,CAACgB,OAAO,EAAE;UACpCf,QAAQ,EAAE,CAAC,GAAGgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BjC,CAAC,EAAE,CAACgC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BhC,CAAC,EAAE,CAAC+B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BG,QAAQ,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC7BX,KAAK,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChChB,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,CAAC,CAAC;UACVK,IAAI,EAAE,IAAI;UACVc,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB,CAAC,CAAC;QAEFnC,aAAa,CAACK,OAAO,CAACmC,IAAI,CAACH,YAAY,CAAC;MAC1C,CAAC,CAAC;IACJ;;IAEA;IACA,OAAO,MAAM;MACXrC,aAAa,CAACK,OAAO,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACpDR,aAAa,CAACK,OAAO,GAAG,EAAE;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoC,eAAe,GAAIC,CAAmB,IAAK;IAC/C3C,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAACI,OAAO,GAAG;MAAEH,CAAC,EAAEwC,CAAC,CAACC,OAAO;MAAExC,CAAC,EAAEuC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAmB,IAAK;IAC/C,IAAI,CAAC5C,UAAU,IAAI,CAACP,YAAY,CAACc,OAAO,EAAE;IAE1C,MAAMyC,IAAI,GAAGvD,YAAY,CAACc,OAAO,CAAC0C,qBAAqB,CAAC,CAAC;IACzD,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;IAC1C,MAAMC,MAAM,GAAGZ,CAAC,CAACC,OAAO,GAAGK,OAAO;IAClC,MAAMO,MAAM,GAAGb,CAAC,CAACE,OAAO,GAAGO,OAAO;;IAElC;IACA,MAAMtC,SAAS,GAAI0C,MAAM,GAAGT,IAAI,CAACO,MAAM,GAAI,GAAG;IAC9C,MAAMvC,SAAS,GAAIwC,MAAM,GAAGR,IAAI,CAACI,KAAK,GAAI,GAAG;IAC7C,MAAMnC,SAAS,GAAI,CAACuC,MAAM,GAAGC,MAAM,KAAKT,IAAI,CAACI,KAAK,GAAGJ,IAAI,CAACO,MAAM,CAAC,GAAI,EAAE;;IAEvE;IACAnE,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE,MAAM,CAAC;IACpB,CAAC,CAAC;IAEFtE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAC7Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,CAAC,CAAC;MACzBI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE;IACb,CAAC,CAAC;IAEFtE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BC,SAAS,EAAEA,SAAS,GAAG,GAAG;MAC1BI,IAAI,EAAE,YAAY;MAClBqC,SAAS,EAAE;IACb,CAAC,CAAC;IAEFvD,YAAY,CAACI,OAAO,GAAG;MAAEH,CAAC,EAAEwC,CAAC,CAACC,OAAO;MAAExC,CAAC,EAAEuC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B1D,aAAa,CAAC,KAAK,CAAC;;IAEpB;IACAb,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB;QACA,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACzB,YAAY,CAACa,OAAO,EAAE;UAC5Ca,QAAQ,EAAE,EAAE;UACZJ,SAAS,EAAE,GAAG;UACdK,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;IAEFzE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;MAC7Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACxB,aAAa,CAACY,OAAO,EAAE;UAC7Ca,QAAQ,EAAE,EAAE;UACZL,SAAS,EAAE,CAAC,GAAG;UACfM,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;IAEFzE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;MAC5Ba,QAAQ,EAAE,GAAG;MACbL,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZI,IAAI,EAAE,qBAAqB;MAC3BuC,UAAU,EAAEA,CAAA,KAAM;QAChB,MAAMC,OAAO,GAAGzE,IAAI,CAAC+B,EAAE,CAACvB,YAAY,CAACW,OAAO,EAAE;UAC5Ca,QAAQ,EAAE,EAAE;UACZH,SAAS,EAAE,GAAG;UACdI,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,CAAC,CAAC;UACVT,eAAe,EAAE;QACnB,CAAC,CAAC;QACFX,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,GAAGsD,OAAO;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACEvE,OAAA;IAAKwE,SAAS,EAAC,qBAAqB;IAAAhC,QAAA,gBAElCxC,OAAA;MAAKwE,SAAS,EAAC,iBAAiB;MAAAhC,QAAA,EAC7B,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB3E,OAAA;QAEEwE,SAAS,EAAC,aAAa;QACvBI,KAAK,EAAE;UACLd,KAAK,EAAE,GAAG,GAAG,GAAGa,CAAC,GAAG,EAAE,IAAI;UAC1BV,MAAM,EAAE,GAAG,GAAG,GAAGU,CAAC,GAAG,EAAE,IAAI;UAC3Bd,IAAI,EAAE,GAAG,EAAE,GAAGc,CAAC,GAAG,EAAE,GAAG;UACvBX,GAAG,EAAE,GAAG,EAAE,GAAGW,CAAC,GAAG,EAAE,GAAG;UACtBE,cAAc,EAAE,GAAGF,CAAC,GAAG,GAAG,GAAG;UAC7BG,iBAAiB,EAAE,GAAG,CAAC,GAAGH,CAAC,GAAG,GAAG;QACnC;MAAE,GATGA,CAAC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENlF,OAAA;MAAK4E,KAAK,EAAE;QAAEO,QAAQ,EAAE,UAAU;QAAEC,MAAM,EAAE;MAAG,CAAE;MAAA5C,QAAA,gBAC/CxC,OAAA;QAAIwE,SAAS,EAAC,iBAAiB;QAAAhC,QAAA,EAAC;MAEhC;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlF,OAAA;QACEqF,GAAG,EAAElF,YAAa;QAClBqE,SAAS,EAAC,gBAAgB;QAC1Bc,WAAW,EAAEjC,eAAgB;QAC7BkC,WAAW,EAAE9B,eAAgB;QAC7B+B,SAAS,EAAEnB,aAAc;QACzBoB,YAAY,EAAEpB,aAAc;QAAA7B,QAAA,gBAG5BxC,OAAA;UAAKqF,GAAG,EAAE7E,oBAAqB;UAACgE,SAAS,EAAC,qBAAqB;UAAAhC,QAAA,EAC5D,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACgC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB3E,OAAA;YAEEwE,SAAS,EAAC;UAAU,GADfG,CAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEP,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlF,OAAA;UAAKqF,GAAG,EAAEjF,YAAa;UAACoE,SAAS,EAAC,YAAY;UAAAhC,QAAA,eAE5CxC,OAAA;YAAKqF,GAAG,EAAEhF,aAAc;YAACmE,SAAS,EAAC,aAAa;YAAAhC,QAAA,eAE9CxC,OAAA;cAAKqF,GAAG,EAAE/E,YAAa;cAACkE,SAAS,EAAC,YAAY;cAAAhC,QAAA,eAE5CxC,OAAA;gBAAKqF,GAAG,EAAE9E,OAAQ;gBAACiE,SAAS,EAAC,gBAAgB;gBAAAhC,QAAA,eAC3CxC,OAAA;kBAAKwE,SAAS,EAAC;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAKqF,GAAG,EAAE5E,OAAQ;UAAC+D,SAAS,EAAC,eAAe;UAAAhC,QAAA,gBAC1CxC,OAAA;YAAKwE,SAAS,EAAC;UAAiB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnClF,OAAA;YAAKwE,SAAS,EAAC;UAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjClF,OAAA;YAAKwE,SAAS,EAAC;UAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA;QAAKwE,SAAS,EAAC,gBAAgB;QAAAhC,QAAA,gBAC7BxC,OAAA;UAAGwE,SAAS,EAAC,wBAAwB;UAAAhC,QAAA,EAAC;QAAuD;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjGlF,OAAA;UAAGwE,SAAS,EAAC,0BAA0B;UAAAhC,QAAA,EAAC;QAA0C;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtFlF,OAAA;UAAGwE,SAAS,EAAC,yBAAyB;UAAAhC,QAAA,EAAC;QAA0B;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CA1SID,aAAa;AAAAyF,EAAA,GAAbzF,aAAa;AA4SnB,eAAeA,aAAa;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}