{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gyroscope = () => {\n  _s();\n  const [rotation, setRotation] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [particles, setParticles] = useState([]);\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n  const animationRef = useRef();\n  const particleIdRef = useRef(0);\n\n  // Initialize particles\n  useEffect(() => {\n    const initParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 20; i++) {\n        newParticles.push({\n          id: particleIdRef.current++,\n          x: Math.random() * 400 - 200,\n          y: Math.random() * 400 - 200,\n          z: Math.random() * 200 - 100,\n          vx: (Math.random() - 0.5) * 2,\n          vy: (Math.random() - 0.5) * 2,\n          vz: (Math.random() - 0.5) * 1,\n          life: Math.random() * 100,\n          maxLife: 100 + Math.random() * 50\n        });\n      }\n      setParticles(newParticles);\n    };\n    initParticles();\n  }, []);\n\n  // Enhanced animation loop with particle physics\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: (prev.x + momentum.x + 0.3) % 360,\n          y: (prev.y + momentum.y + 0.5) % 360,\n          z: (prev.z + momentum.z + 0.2) % 360\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.985,\n          y: prev.y * 0.985,\n          z: prev.z * 0.985\n        }));\n      }\n\n      // Update particles\n      setParticles(prev => {\n        return prev.map(particle => {\n          const newParticle = {\n            ...particle,\n            x: particle.x + particle.vx,\n            y: particle.y + particle.vy,\n            z: particle.z + particle.vz,\n            life: particle.life + 1,\n            vx: particle.vx * 0.99 + (Math.random() - 0.5) * 0.1,\n            vy: particle.vy * 0.99 + (Math.random() - 0.5) * 0.1,\n            vz: particle.vz * 0.99 + (Math.random() - 0.5) * 0.05\n          };\n\n          // Reset particle if it's too old or too far\n          if (newParticle.life > newParticle.maxLife || Math.abs(newParticle.x) > 300 || Math.abs(newParticle.y) > 300) {\n            return {\n              id: particleIdRef.current++,\n              x: Math.random() * 100 - 50,\n              y: Math.random() * 100 - 50,\n              z: Math.random() * 50 - 25,\n              vx: (Math.random() - 0.5) * 3,\n              vy: (Math.random() - 0.5) * 3,\n              vz: (Math.random() - 0.5) * 1.5,\n              life: 0,\n              maxLife: 100 + Math.random() * 100\n            };\n          }\n          return newParticle;\n        });\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n  const handleMouseDown = e => {\n    setIsDragging(true);\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseMove = e => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.8;\n    setRotation(prev => ({\n      x: (prev.x + deltaY * rotationSpeed) % 360,\n      y: (prev.y + deltaX * rotationSpeed) % 360,\n      z: (prev.z + (deltaX + deltaY) * 0.2) % 360\n    }));\n    setMomentum({\n      x: deltaY * 0.15,\n      y: deltaX * 0.15,\n      z: (deltaX + deltaY) * 0.08\n    });\n    lastMousePos.current = {\n      x: e.clientX,\n      y: e.clientY\n    };\n  };\n  const handleMouseUp = () => setIsDragging(false);\n  const getParticleStyle = particle => {\n    const opacity = Math.max(0, 1 - particle.life / particle.maxLife);\n    const size = Math.max(1, 4 - particle.life / particle.maxLife * 3);\n    const perspective = 400;\n    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 200);\n    return {\n      position: 'absolute',\n      left: '50%',\n      top: '50%',\n      width: `${size}px`,\n      height: `${size}px`,\n      transform: `\n        translate(-50%, -50%)\n        translate3d(${particle.x * scale}px, ${particle.y * scale}px, ${particle.z}px)\n        scale(${scale})\n      `,\n      opacity: opacity,\n      background: `radial-gradient(circle,\n        ${particle.z > 0 ? '#ffd700' : '#ff6b6b'} 0%,\n        ${particle.z > 0 ? '#ff8c00' : '#ff1493'} 100%\n      )`,\n      borderRadius: '50%',\n      boxShadow: `0 0 ${size * 3}px ${particle.z > 0 ? '#ffd70088' : '#ff69b488'}`,\n      pointerEvents: 'none',\n      zIndex: Math.round(particle.z + 100)\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-30\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500 rounded-full blur-3xl opacity-20 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full blur-3xl opacity-20 animate-pulse\",\n        style: {\n          animationDelay: '1s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-3/4 left-3/4 w-64 h-64 bg-pink-500 rounded-full blur-3xl opacity-20 animate-pulse\",\n        style: {\n          animationDelay: '2s'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\",\n        children: \"Quantum Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\",\n        style: {\n          perspective: '1000px'\n        },\n        onMouseDown: handleMouseDown,\n        onMouseMove: handleMouseMove,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseUp,\n        children: [particles.map(particle => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getParticleStyle(particle)\n        }, particle.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full border-4 bg-gradient-to-r from-transparent via-blue-500/30 to-transparent\",\n          style: {\n            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,\n            transition: isDragging ? 'none' : 'transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',\n            boxShadow: `\n                0 0 30px #3b82f6,\n                inset 0 0 30px #3b82f6,\n                0 0 60px #3b82f6\n              `,\n            border: '4px solid #3b82f6',\n            borderRadius: '50%'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-purple-500/40 to-transparent\",\n            style: {\n              transform: `rotateY(${rotation.y * 0.7}deg) rotateX(${rotation.x * 1.5}deg)`,\n              boxShadow: `\n                  0 0 40px #8b5cf6,\n                  inset 0 0 40px #8b5cf6\n                `,\n              border: '4px solid #8b5cf6',\n              borderRadius: '50%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-pink-500/50 to-transparent\",\n              style: {\n                transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                boxShadow: `\n                    0 0 50px #ec4899,\n                    inset 0 0 50px #ec4899\n                  `,\n                border: '4px solid #ec4899',\n                borderRadius: '50%'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\",\n                style: {\n                  transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                  boxShadow: `\n                      0 0 60px #ffffffaa,\n                      inset 0 0 20px #3b82f688\n                    `\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse\",\n                  style: {\n                    boxShadow: '0 0 30px #fbbf24aa, 0 0 60px #f97316aa'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -inset-16 pointer-events-none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-0 right-0 h-0.5 opacity-60\",\n            style: {\n              transform: `rotateZ(${rotation.x}deg)`,\n              background: 'linear-gradient(90deg, transparent, #ef4444, transparent)',\n              boxShadow: '0 0 10px #ef4444'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 bottom-0 left-1/2 w-0.5 opacity-60\",\n            style: {\n              transform: `rotateZ(${rotation.y}deg)`,\n              background: 'linear-gradient(180deg, transparent, #22c55e, transparent)',\n              boxShadow: '0 0 10px #22c55e'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-1/2 left-1/2 w-32 h-0.5 -ml-16 opacity-60\",\n            style: {\n              transform: `rotateZ(${rotation.z}deg)`,\n              background: 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\n              boxShadow: '0 0 10px #3b82f6'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute rounded-full border opacity-20 pointer-events-none\",\n          style: {\n            inset: `${-20 - i * 30}px`,\n            borderColor: ['#3b82f6', '#8b5cf6', '#ec4899'][i],\n            transform: `rotateX(${rotation.x * (0.3 + i * 0.1)}deg) rotateY(${rotation.y * (0.2 + i * 0.1)}deg)`,\n            animation: `spin ${10 + i * 5}s linear infinite`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 text-sm text-gray-300 space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-400\",\n          children: \"\\u2728 Click and drag to manipulate quantum fields\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-mono text-xs\",\n          children: [\"Rotation: X: \", Math.round(rotation.x), \"\\xB0 Y: \", Math.round(rotation.y), \"\\xB0 Z: \", Math.round(rotation.z), \"\\xB0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-purple-400 text-xs\",\n          children: [\"Active Particles: \", particles.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          from { transform: rotateZ(0deg); }\n          to { transform: rotateZ(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(Gyroscope, \"cfGHj79Rq7JUQJO2djc7VdnVYsM=\");\n_c = Gyroscope;\nexport default Gyroscope;\nvar _c;\n$RefreshReg$(_c, \"Gyroscope\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Gyroscope", "_s", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "momentum", "setMomentum", "particles", "setParticles", "lastMousePos", "animationRef", "particleIdRef", "initParticles", "newParticles", "i", "push", "id", "current", "Math", "random", "vx", "vy", "vz", "life", "maxLife", "animate", "prev", "map", "particle", "newParticle", "abs", "requestAnimationFrame", "cancelAnimationFrame", "handleMouseDown", "e", "clientX", "clientY", "handleMouseMove", "deltaX", "deltaY", "rotationSpeed", "handleMouseUp", "getParticleStyle", "opacity", "max", "size", "perspective", "scale", "position", "left", "top", "width", "height", "transform", "background", "borderRadius", "boxShadow", "pointerEvents", "zIndex", "round", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "animationDelay", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "transition", "border", "Array", "_", "inset", "borderColor", "animation", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\ninterface ParticleState {\n  id: number;\n  x: number;\n  y: number;\n  z: number;\n  vx: number;\n  vy: number;\n  vz: number;\n  life: number;\n  maxLife: number;\n}\n\nconst Gyroscope = () => {\n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [particles, setParticles] = useState<ParticleState[]>([]);\n  const lastMousePos = useRef({ x: 0, y: 0 });\n  const animationRef = useRef<number>();\n  const particleIdRef = useRef(0);\n\n  // Initialize particles\n  useEffect(() => {\n    const initParticles = () => {\n      const newParticles: ParticleState[] = [];\n      for (let i = 0; i < 20; i++) {\n        newParticles.push({\n          id: particleIdRef.current++,\n          x: Math.random() * 400 - 200,\n          y: Math.random() * 400 - 200,\n          z: Math.random() * 200 - 100,\n          vx: (Math.random() - 0.5) * 2,\n          vy: (Math.random() - 0.5) * 2,\n          vz: (Math.random() - 0.5) * 1,\n          life: Math.random() * 100,\n          maxLife: 100 + Math.random() * 50\n        });\n      }\n      setParticles(newParticles);\n    };\n    initParticles();\n  }, []);\n\n  // Enhanced animation loop with particle physics\n  useEffect(() => {\n    const animate = () => {\n      if (!isDragging) {\n        setRotation(prev => ({\n          x: (prev.x + momentum.x + 0.3) % 360,\n          y: (prev.y + momentum.y + 0.5) % 360,\n          z: (prev.z + momentum.z + 0.2) % 360\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.985,\n          y: prev.y * 0.985,\n          z: prev.z * 0.985\n        }));\n      }\n\n      // Update particles\n      setParticles(prev => {\n        return prev.map(particle => {\n          const newParticle = {\n            ...particle,\n            x: particle.x + particle.vx,\n            y: particle.y + particle.vy,\n            z: particle.z + particle.vz,\n            life: particle.life + 1,\n            vx: particle.vx * 0.99 + (Math.random() - 0.5) * 0.1,\n            vy: particle.vy * 0.99 + (Math.random() - 0.5) * 0.1,\n            vz: particle.vz * 0.99 + (Math.random() - 0.5) * 0.05\n          };\n\n          // Reset particle if it's too old or too far\n          if (newParticle.life > newParticle.maxLife || \n              Math.abs(newParticle.x) > 300 || \n              Math.abs(newParticle.y) > 300) {\n            return {\n              id: particleIdRef.current++,\n              x: Math.random() * 100 - 50,\n              y: Math.random() * 100 - 50,\n              z: Math.random() * 50 - 25,\n              vx: (Math.random() - 0.5) * 3,\n              vy: (Math.random() - 0.5) * 3,\n              vz: (Math.random() - 0.5) * 1.5,\n              life: 0,\n              maxLife: 100 + Math.random() * 100\n            };\n          }\n\n          return newParticle;\n        });\n      });\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum]);\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    setIsDragging(true);\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - lastMousePos.current.x;\n    const deltaY = e.clientY - lastMousePos.current.y;\n    const rotationSpeed = 0.8;\n\n    setRotation(prev => ({\n      x: (prev.x + deltaY * rotationSpeed) % 360,\n      y: (prev.y + deltaX * rotationSpeed) % 360,\n      z: (prev.z + (deltaX + deltaY) * 0.2) % 360\n    }));\n\n    setMomentum({\n      x: deltaY * 0.15,\n      y: deltaX * 0.15,\n      z: (deltaX + deltaY) * 0.08\n    });\n\n    lastMousePos.current = { x: e.clientX, y: e.clientY };\n  };\n\n  const handleMouseUp = () => setIsDragging(false);\n\n  const getParticleStyle = (particle: ParticleState): React.CSSProperties => {\n    const opacity = Math.max(0, 1 - (particle.life / particle.maxLife));\n    const size = Math.max(1, 4 - (particle.life / particle.maxLife) * 3);\n    const perspective = 400;\n    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 200);\n\n    return {\n      position: 'absolute' as const,\n      left: '50%',\n      top: '50%',\n      width: `${size}px`,\n      height: `${size}px`,\n      transform: `\n        translate(-50%, -50%)\n        translate3d(${particle.x * scale}px, ${particle.y * scale}px, ${particle.z}px)\n        scale(${scale})\n      `,\n      opacity: opacity,\n      background: `radial-gradient(circle,\n        ${particle.z > 0 ? '#ffd700' : '#ff6b6b'} 0%,\n        ${particle.z > 0 ? '#ff8c00' : '#ff1493'} 100%\n      )`,\n      borderRadius: '50%',\n      boxShadow: `0 0 ${size * 3}px ${particle.z > 0 ? '#ffd70088' : '#ff69b488'}`,\n      pointerEvents: 'none' as const,\n      zIndex: Math.round(particle.z + 100)\n    };\n  };\n\n  return (\n    <div className=\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden\">\n      {/* Ambient background effects */}\n      <div className=\"absolute inset-0 opacity-30\">\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500 rounded-full blur-3xl opacity-20 animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full blur-3xl opacity-20 animate-pulse\" style={{animationDelay: '1s'}}></div>\n        <div className=\"absolute top-3/4 left-3/4 w-64 h-64 bg-pink-500 rounded-full blur-3xl opacity-20 animate-pulse\" style={{animationDelay: '2s'}}></div>\n      </div>\n\n      <div className=\"text-center relative z-10\">\n        <h1 className=\"text-4xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\">\n          Quantum Gyroscope\n        </h1>\n        \n        <div \n          className=\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\"\n          style={{ perspective: '1000px' }}\n          onMouseDown={handleMouseDown}\n          onMouseMove={handleMouseMove}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseUp}\n        >\n          {/* Particle System */}\n          {particles.map(particle => (\n            <div\n              key={particle.id}\n              style={getParticleStyle(particle)}\n            />\n          ))}\n\n          {/* Outer Ring - Holographic */}\n          <div\n            className=\"absolute inset-0 rounded-full border-4 bg-gradient-to-r from-transparent via-blue-500/30 to-transparent\"\n            style={{\n              transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,\n              transition: isDragging ? 'none' : 'transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',\n              boxShadow: `\n                0 0 30px #3b82f6,\n                inset 0 0 30px #3b82f6,\n                0 0 60px #3b82f6\n              `,\n              border: '4px solid #3b82f6',\n              borderRadius: '50%'\n            }}\n          >\n            {/* Middle Ring - Energy Field */}\n            <div\n              className=\"absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-purple-500/40 to-transparent\"\n              style={{\n                transform: `rotateY(${rotation.y * 0.7}deg) rotateX(${rotation.x * 1.5}deg)`,\n                boxShadow: `\n                  0 0 40px #8b5cf6,\n                  inset 0 0 40px #8b5cf6\n                `,\n                border: '4px solid #8b5cf6',\n                borderRadius: '50%'\n              }}\n            >\n              {/* Inner Ring - Core Energy */}\n              <div\n                className=\"absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-pink-500/50 to-transparent\"\n                style={{\n                  transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                  boxShadow: `\n                    0 0 50px #ec4899,\n                    inset 0 0 50px #ec4899\n                  `,\n                  border: '4px solid #ec4899',\n                  borderRadius: '50%'\n                }}\n              >\n                {/* Quantum Core */}\n                <div \n                  className=\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\"\n                  style={{\n                    transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                    boxShadow: `\n                      0 0 60px #ffffffaa,\n                      inset 0 0 20px #3b82f688\n                    `\n                  }}\n                >\n                  {/* Plasma Center */}\n                  <div \n                    className=\"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse\"\n                    style={{\n                      boxShadow: '0 0 30px #fbbf24aa, 0 0 60px #f97316aa'\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Rotation Axes */}\n          <div className=\"absolute -inset-16 pointer-events-none\">\n            {/* X-Axis */}\n            <div \n              className=\"absolute top-1/2 left-0 right-0 h-0.5 opacity-60\" \n              style={{ \n                transform: `rotateZ(${rotation.x}deg)`,\n                background: 'linear-gradient(90deg, transparent, #ef4444, transparent)',\n                boxShadow: '0 0 10px #ef4444'\n              }} \n            />\n            {/* Y-Axis */}\n            <div \n              className=\"absolute top-0 bottom-0 left-1/2 w-0.5 opacity-60\" \n              style={{ \n                transform: `rotateZ(${rotation.y}deg)`,\n                background: 'linear-gradient(180deg, transparent, #22c55e, transparent)',\n                boxShadow: '0 0 10px #22c55e'\n              }} \n            />\n            {/* Z-Axis */}\n            <div \n              className=\"absolute top-1/2 left-1/2 w-32 h-0.5 -ml-16 opacity-60\" \n              style={{ \n                transform: `rotateZ(${rotation.z}deg)`,\n                background: 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\n                boxShadow: '0 0 10px #3b82f6'\n              }} \n            />\n          </div>\n\n          {/* Orbit rings */}\n          {[...Array(3)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute rounded-full border opacity-20 pointer-events-none\"\n              style={{\n                inset: `${-20 - i * 30}px`,\n                borderColor: ['#3b82f6', '#8b5cf6', '#ec4899'][i],\n                transform: `rotateX(${rotation.x * (0.3 + i * 0.1)}deg) rotateY(${rotation.y * (0.2 + i * 0.1)}deg)`,\n                animation: `spin ${10 + i * 5}s linear infinite`\n              }}\n            />\n          ))}\n        </div>\n        \n        <div className=\"mt-6 text-sm text-gray-300 space-y-1\">\n          <p className=\"text-blue-400\">✨ Click and drag to manipulate quantum fields</p>\n          <p className=\"font-mono text-xs\">\n            Rotation: X: {Math.round(rotation.x)}° Y: {Math.round(rotation.y)}° Z: {Math.round(rotation.z)}°\n          </p>\n          <p className=\"text-purple-400 text-xs\">Active Particles: {particles.length}</p>\n        </div>\n      </div>\n\n      <style>{`\n        @keyframes spin {\n          from { transform: rotateZ(0deg); }\n          to { transform: rotateZ(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Gyroscope;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBpD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAkB,EAAE,CAAC;EAC/D,MAAMkB,YAAY,GAAGhB,MAAM,CAAC;IAAEO,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC3C,MAAMS,YAAY,GAAGjB,MAAM,CAAS,CAAC;EACrC,MAAMkB,aAAa,GAAGlB,MAAM,CAAC,CAAC,CAAC;;EAE/B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMoB,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,YAA6B,GAAG,EAAE;MACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEL,aAAa,CAACM,OAAO,EAAE;UAC3BjB,CAAC,EAAEkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAC5BlB,CAAC,EAAEiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAC5BjB,CAAC,EAAEgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAC5BC,EAAE,EAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BE,EAAE,EAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BG,EAAE,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BI,IAAI,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBK,OAAO,EAAE,GAAG,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACjC,CAAC,CAAC;MACJ;MACAX,YAAY,CAACK,YAAY,CAAC;IAC5B,CAAC;IACDD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApB,SAAS,CAAC,MAAM;IACd,MAAMiC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAACtB,UAAU,EAAE;QACfJ,WAAW,CAAC2B,IAAI,KAAK;UACnB1B,CAAC,EAAE,CAAC0B,IAAI,CAAC1B,CAAC,GAAGK,QAAQ,CAACL,CAAC,GAAG,GAAG,IAAI,GAAG;UACpCC,CAAC,EAAE,CAACyB,IAAI,CAACzB,CAAC,GAAGI,QAAQ,CAACJ,CAAC,GAAG,GAAG,IAAI,GAAG;UACpCC,CAAC,EAAE,CAACwB,IAAI,CAACxB,CAAC,GAAGG,QAAQ,CAACH,CAAC,GAAG,GAAG,IAAI;QACnC,CAAC,CAAC,CAAC;QACHI,WAAW,CAACoB,IAAI,KAAK;UACnB1B,CAAC,EAAE0B,IAAI,CAAC1B,CAAC,GAAG,KAAK;UACjBC,CAAC,EAAEyB,IAAI,CAACzB,CAAC,GAAG,KAAK;UACjBC,CAAC,EAAEwB,IAAI,CAACxB,CAAC,GAAG;QACd,CAAC,CAAC,CAAC;MACL;;MAEA;MACAM,YAAY,CAACkB,IAAI,IAAI;QACnB,OAAOA,IAAI,CAACC,GAAG,CAACC,QAAQ,IAAI;UAC1B,MAAMC,WAAW,GAAG;YAClB,GAAGD,QAAQ;YACX5B,CAAC,EAAE4B,QAAQ,CAAC5B,CAAC,GAAG4B,QAAQ,CAACR,EAAE;YAC3BnB,CAAC,EAAE2B,QAAQ,CAAC3B,CAAC,GAAG2B,QAAQ,CAACP,EAAE;YAC3BnB,CAAC,EAAE0B,QAAQ,CAAC1B,CAAC,GAAG0B,QAAQ,CAACN,EAAE;YAC3BC,IAAI,EAAEK,QAAQ,CAACL,IAAI,GAAG,CAAC;YACvBH,EAAE,EAAEQ,QAAQ,CAACR,EAAE,GAAG,IAAI,GAAG,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;YACpDE,EAAE,EAAEO,QAAQ,CAACP,EAAE,GAAG,IAAI,GAAG,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;YACpDG,EAAE,EAAEM,QAAQ,CAACN,EAAE,GAAG,IAAI,GAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;UACnD,CAAC;;UAED;UACA,IAAIU,WAAW,CAACN,IAAI,GAAGM,WAAW,CAACL,OAAO,IACtCN,IAAI,CAACY,GAAG,CAACD,WAAW,CAAC7B,CAAC,CAAC,GAAG,GAAG,IAC7BkB,IAAI,CAACY,GAAG,CAACD,WAAW,CAAC5B,CAAC,CAAC,GAAG,GAAG,EAAE;YACjC,OAAO;cACLe,EAAE,EAAEL,aAAa,CAACM,OAAO,EAAE;cAC3BjB,CAAC,EAAEkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;cAC3BlB,CAAC,EAAEiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;cAC3BjB,CAAC,EAAEgB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;cAC1BC,EAAE,EAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;cAC7BE,EAAE,EAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;cAC7BG,EAAE,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;cAC/BI,IAAI,EAAE,CAAC;cACPC,OAAO,EAAE,GAAG,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;YACjC,CAAC;UACH;UAEA,OAAOU,WAAW;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFnB,YAAY,CAACO,OAAO,GAAGc,qBAAqB,CAACN,OAAO,CAAC;IACvD,CAAC;IACDf,YAAY,CAACO,OAAO,GAAGc,qBAAqB,CAACN,OAAO,CAAC;IACrD,OAAO,MAAM;MACX,IAAIf,YAAY,CAACO,OAAO,EAAEe,oBAAoB,CAACtB,YAAY,CAACO,OAAO,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAACd,UAAU,EAAEE,QAAQ,CAAC,CAAC;EAE1B,MAAM4B,eAAe,GAAIC,CAAmB,IAAK;IAC/C9B,aAAa,CAAC,IAAI,CAAC;IACnBK,YAAY,CAACQ,OAAO,GAAG;MAAEjB,CAAC,EAAEkC,CAAC,CAACC,OAAO;MAAElC,CAAC,EAAEiC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMC,eAAe,GAAIH,CAAmB,IAAK;IAC/C,IAAI,CAAC/B,UAAU,EAAE;IACjB,MAAMmC,MAAM,GAAGJ,CAAC,CAACC,OAAO,GAAG1B,YAAY,CAACQ,OAAO,CAACjB,CAAC;IACjD,MAAMuC,MAAM,GAAGL,CAAC,CAACE,OAAO,GAAG3B,YAAY,CAACQ,OAAO,CAAChB,CAAC;IACjD,MAAMuC,aAAa,GAAG,GAAG;IAEzBzC,WAAW,CAAC2B,IAAI,KAAK;MACnB1B,CAAC,EAAE,CAAC0B,IAAI,CAAC1B,CAAC,GAAGuC,MAAM,GAAGC,aAAa,IAAI,GAAG;MAC1CvC,CAAC,EAAE,CAACyB,IAAI,CAACzB,CAAC,GAAGqC,MAAM,GAAGE,aAAa,IAAI,GAAG;MAC1CtC,CAAC,EAAE,CAACwB,IAAI,CAACxB,CAAC,GAAG,CAACoC,MAAM,GAAGC,MAAM,IAAI,GAAG,IAAI;IAC1C,CAAC,CAAC,CAAC;IAEHjC,WAAW,CAAC;MACVN,CAAC,EAAEuC,MAAM,GAAG,IAAI;MAChBtC,CAAC,EAAEqC,MAAM,GAAG,IAAI;MAChBpC,CAAC,EAAE,CAACoC,MAAM,GAAGC,MAAM,IAAI;IACzB,CAAC,CAAC;IAEF9B,YAAY,CAACQ,OAAO,GAAG;MAAEjB,CAAC,EAAEkC,CAAC,CAACC,OAAO;MAAElC,CAAC,EAAEiC,CAAC,CAACE;IAAQ,CAAC;EACvD,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAMrC,aAAa,CAAC,KAAK,CAAC;EAEhD,MAAMsC,gBAAgB,GAAId,QAAuB,IAA0B;IACzE,MAAMe,OAAO,GAAGzB,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIhB,QAAQ,CAACL,IAAI,GAAGK,QAAQ,CAACJ,OAAQ,CAAC;IACnE,MAAMqB,IAAI,GAAG3B,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIhB,QAAQ,CAACL,IAAI,GAAGK,QAAQ,CAACJ,OAAO,GAAI,CAAC,CAAC;IACpE,MAAMsB,WAAW,GAAG,GAAG;IACvB,MAAMC,KAAK,GAAG7B,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG1B,IAAI,CAACY,GAAG,CAACF,QAAQ,CAAC1B,CAAC,CAAC,GAAG,GAAG,CAAC;IAE3D,OAAO;MACL8C,QAAQ,EAAE,UAAmB;MAC7BC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,GAAGN,IAAI,IAAI;MAClBO,MAAM,EAAE,GAAGP,IAAI,IAAI;MACnBQ,SAAS,EAAE;AACjB;AACA,sBAAsBzB,QAAQ,CAAC5B,CAAC,GAAG+C,KAAK,OAAOnB,QAAQ,CAAC3B,CAAC,GAAG8C,KAAK,OAAOnB,QAAQ,CAAC1B,CAAC;AAClF,gBAAgB6C,KAAK;AACrB,OAAO;MACDJ,OAAO,EAAEA,OAAO;MAChBW,UAAU,EAAE;AAClB,UAAU1B,QAAQ,CAAC1B,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;AAChD,UAAU0B,QAAQ,CAAC1B,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;AAChD,QAAQ;MACFqD,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,OAAOX,IAAI,GAAG,CAAC,MAAMjB,QAAQ,CAAC1B,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,WAAW,EAAE;MAC5EuD,aAAa,EAAE,MAAe;MAC9BC,MAAM,EAAExC,IAAI,CAACyC,KAAK,CAAC/B,QAAQ,CAAC1B,CAAC,GAAG,GAAG;IACrC,CAAC;EACH,CAAC;EAED,oBACEP,OAAA;IAAKiE,SAAS,EAAC,mIAAmI;IAAAC,QAAA,gBAEhJlE,OAAA;MAAKiE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1ClE,OAAA;QAAKiE,SAAS,EAAC;MAAgG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtHtE,OAAA;QAAKiE,SAAS,EAAC,sGAAsG;QAACM,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3JtE,OAAA;QAAKiE,SAAS,EAAC,gGAAgG;QAACM,KAAK,EAAE;UAACC,cAAc,EAAE;QAAI;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClJ,CAAC,eAENtE,OAAA;MAAKiE,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxClE,OAAA;QAAIiE,SAAS,EAAC,iHAAiH;QAAAC,QAAA,EAAC;MAEhI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtE,OAAA;QACEiE,SAAS,EAAC,2EAA2E;QACrFM,KAAK,EAAE;UAAEpB,WAAW,EAAE;QAAS,CAAE;QACjCsB,WAAW,EAAEnC,eAAgB;QAC7BoC,WAAW,EAAEhC,eAAgB;QAC7BiC,SAAS,EAAE7B,aAAc;QACzB8B,YAAY,EAAE9B,aAAc;QAAAoB,QAAA,GAG3BtD,SAAS,CAACoB,GAAG,CAACC,QAAQ,iBACrBjC,OAAA;UAEEuE,KAAK,EAAExB,gBAAgB,CAACd,QAAQ;QAAE,GAD7BA,QAAQ,CAACZ,EAAE;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEjB,CACF,CAAC,eAGFtE,OAAA;UACEiE,SAAS,EAAC,yGAAyG;UACnHM,KAAK,EAAE;YACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACE,CAAC,gBAAgBF,QAAQ,CAACG,CAAC,MAAM;YAChEuE,UAAU,EAAErE,UAAU,GAAG,MAAM,GAAG,6CAA6C;YAC/EqD,SAAS,EAAE;AACzB;AACA;AACA;AACA,eAAe;YACDiB,MAAM,EAAE,mBAAmB;YAC3BlB,YAAY,EAAE;UAChB,CAAE;UAAAM,QAAA,eAGFlE,OAAA;YACEiE,SAAS,EAAC,2GAA2G;YACrHM,KAAK,EAAE;cACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACE,CAAC,GAAG,GAAG,MAAM;cAC5EwD,SAAS,EAAE;AAC3B;AACA;AACA,iBAAiB;cACDiB,MAAM,EAAE,mBAAmB;cAC3BlB,YAAY,EAAE;YAChB,CAAE;YAAAM,QAAA,eAGFlE,OAAA;cACEiE,SAAS,EAAC,yGAAyG;cACnHM,KAAK,EAAE;gBACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,GAAG,MAAM;gBAC5GsD,SAAS,EAAE;AAC7B;AACA;AACA,mBAAmB;gBACDiB,MAAM,EAAE,mBAAmB;gBAC3BlB,YAAY,EAAE;cAChB,CAAE;cAAAM,QAAA,eAGFlE,OAAA;gBACEiE,SAAS,EAAC,uFAAuF;gBACjGM,KAAK,EAAE;kBACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACE,CAAC,GAAG,CAAC,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,CAAC,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,MAAM;kBAC5GsD,SAAS,EAAE;AAC/B;AACA;AACA;gBACkB,CAAE;gBAAAK,QAAA,eAGFlE,OAAA;kBACEiE,SAAS,EAAC,qIAAqI;kBAC/IM,KAAK,EAAE;oBACLV,SAAS,EAAE;kBACb;gBAAE;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA;UAAKiE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErDlE,OAAA;YACEiE,SAAS,EAAC,kDAAkD;YAC5DM,KAAK,EAAE;cACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACE,CAAC,MAAM;cACtCsD,UAAU,EAAE,2DAA2D;cACvEE,SAAS,EAAE;YACb;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFtE,OAAA;YACEiE,SAAS,EAAC,mDAAmD;YAC7DM,KAAK,EAAE;cACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACG,CAAC,MAAM;cACtCqD,UAAU,EAAE,4DAA4D;cACxEE,SAAS,EAAE;YACb;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFtE,OAAA;YACEiE,SAAS,EAAC,wDAAwD;YAClEM,KAAK,EAAE;cACLb,SAAS,EAAE,WAAWvD,QAAQ,CAACI,CAAC,MAAM;cACtCoD,UAAU,EAAE,2DAA2D;cACvEE,SAAS,EAAE;YACb;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGL,CAAC,GAAGS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC/C,GAAG,CAAC,CAACgD,CAAC,EAAE7D,CAAC,kBACtBnB,OAAA;UAEEiE,SAAS,EAAC,6DAA6D;UACvEM,KAAK,EAAE;YACLU,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG9D,CAAC,GAAG,EAAE,IAAI;YAC1B+D,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC/D,CAAC,CAAC;YACjDuC,SAAS,EAAE,WAAWvD,QAAQ,CAACE,CAAC,IAAI,GAAG,GAAGc,CAAC,GAAG,GAAG,CAAC,gBAAgBhB,QAAQ,CAACG,CAAC,IAAI,GAAG,GAAGa,CAAC,GAAG,GAAG,CAAC,MAAM;YACpGgE,SAAS,EAAE,QAAQ,EAAE,GAAGhE,CAAC,GAAG,CAAC;UAC/B;QAAE,GAPGA,CAAC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDlE,OAAA;UAAGiE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9EtE,OAAA;UAAGiE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAAC,eAClB,EAAC3C,IAAI,CAACyC,KAAK,CAAC7D,QAAQ,CAACE,CAAC,CAAC,EAAC,UAAK,EAACkB,IAAI,CAACyC,KAAK,CAAC7D,QAAQ,CAACG,CAAC,CAAC,EAAC,UAAK,EAACiB,IAAI,CAACyC,KAAK,CAAC7D,QAAQ,CAACI,CAAC,CAAC,EAAC,MACjG;QAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtE,OAAA;UAAGiE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GAAC,oBAAkB,EAACtD,SAAS,CAACwE,MAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtE,OAAA;MAAAkE,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpE,EAAA,CAhTID,SAAS;AAAAoF,EAAA,GAATpF,SAAS;AAkTf,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}