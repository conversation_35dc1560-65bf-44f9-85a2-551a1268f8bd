{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport './SamplesPage.css';\n\n// Simplified interface for shop display\n\n// Group videos by grade\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VerticalCarousel = ({\n  videos,\n  grade,\n  onVideoClick\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const trackRef = useRef(null);\n  const animationRef = useRef(null);\n  const lastUpdateRef = useRef(0);\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isHovered, setIsHovered] = useState(false);\n  const itemHeight = 120;\n  const visibleItems = 3;\n  const maxIndex = Math.max(0, videos.length - 1);\n\n  // Debounced animation with proper cleanup\n  const animate = useCallback(mouseY => {\n    const now = Date.now();\n    if (now - lastUpdateRef.current < 16) return; // ~60fps throttle\n    lastUpdateRef.current = now;\n    if (!containerRef.current || videos.length <= visibleItems) return;\n    const containerRect = containerRef.current.getBoundingClientRect();\n    const containerHeight = containerRect.height;\n    const middleY = containerRect.top + containerHeight / 2;\n    const distanceFromCenter = mouseY - middleY;\n    const threshold = 30;\n    if (Math.abs(distanceFromCenter) > threshold) {\n      const scrollDirection = distanceFromCenter > 0 ? 1 : -1;\n      const scrollSpeed = Math.min(Math.abs(distanceFromCenter) / 2000, 0.1); // Cap speed\n\n      setCurrentIndex(prev => {\n        const newIndex = prev + scrollDirection * scrollSpeed;\n        return Math.max(0, Math.min(newIndex, maxIndex));\n      });\n    }\n  }, [maxIndex, videos.length, visibleItems]);\n\n  // Proper animation frame management\n  useEffect(() => {\n    let currentMouseY = 0;\n    const animateLoop = () => {\n      if (isHovered) {\n        animate(currentMouseY);\n        animationRef.current = requestAnimationFrame(animateLoop);\n      }\n    };\n    const handleMouseMove = e => {\n      currentMouseY = e.clientY;\n    };\n    if (isHovered) {\n      document.addEventListener('mousemove', handleMouseMove);\n      animationRef.current = requestAnimationFrame(animateLoop);\n    }\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = null;\n      }\n    };\n  }, [isHovered, animate]);\n\n  // Smooth track position updates\n  useEffect(() => {\n    if (!trackRef.current) return;\n    const yPosition = -currentIndex * itemHeight;\n    trackRef.current.style.transform = `translateY(${yPosition}px)`;\n  }, [currentIndex, itemHeight]);\n  const handleMouseEnter = () => setIsHovered(true);\n  const handleMouseLeave = () => setIsHovered(false);\n  if (videos.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"carousel-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-container-new\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-videos-placeholder-new\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"No videos for Grade \", grade]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filename-list\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filename-item empty\",\n          children: \"No files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"carousel-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"carousel-container-new\",\n      ref: containerRef,\n      onMouseEnter: handleMouseEnter,\n      onMouseLeave: handleMouseLeave,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"carousel-viewport-new\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"carousel-track-new\",\n          ref: trackRef,\n          children: videos.map((video, index) => {\n            const position = index - Math.floor(currentIndex);\n            const isCenter = position === 1;\n            const isVisible = position >= 0 && position <= 2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `carousel-item-new ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-thumbnail-new\",\n                onClick: () => onVideoClick(video),\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: video.thumbnailUrl,\n                  alt: video.title,\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover',\n                    borderRadius: '6px'\n                  },\n                  onError: e => {\n                    const target = e.target;\n                    // Retry loading the image once\n                    if (!target.dataset.retried) {\n                      target.dataset.retried = \"true\";\n                      setTimeout(() => {\n                        target.src = video.thumbnailUrl + `?retry=${Date.now()}`;\n                      }, 1000);\n                    } else {\n                      // Show fallback if retry failed\n                      target.style.backgroundColor = '#007bff';\n                      target.style.display = 'flex';\n                      target.style.alignItems = 'center';\n                      target.style.justifyContent = 'center';\n                      target.style.color = 'white';\n                      target.style.fontSize = '12px';\n                      target.textContent = 'Thumbnail';\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, video.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filename-list\",\n      children: videos.map((video, index) => {\n        const position = index - Math.floor(currentIndex);\n        const isCenter = position === 1;\n        const isVisible = position >= 0 && position <= 2;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `filename-item ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`,\n          children: video.filename\n        }, `filename-${video.id}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(VerticalCarousel, \"+xirX3IDlprKPz5B4iBQ7EtsfqM=\");\n_c = VerticalCarousel;\nconst SamplesPage = () => {\n  _s2();\n  const [videosByGrade, setVideosByGrade] = useState({});\n  const [selectedVideo, setSelectedVideo] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [gradeLoading, setGradeLoading] = useState({});\n  const [gradeErrors, setGradeErrors] = useState({});\n\n  // Improved API call with retry mechanism\n  const getVideoFilesFromAPI = useCallback(async (grade, retries = 3) => {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout\n\n      const response = await fetch(`/api/videos/grade/${grade}/`, {\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n          'Cache-Control': 'no-cache'\n        }\n      });\n      clearTimeout(timeoutId);\n      if (!response.ok) {\n        if (response.status === 404) return []; // No videos for this grade\n        throw new Error(`HTTP ${response.status}`);\n      }\n      const text = await response.text();\n      if (!text.trim()) return [];\n      const data = JSON.parse(text);\n\n      // Handle different response formats\n      if (Array.isArray(data)) return data;\n      if (data && Array.isArray(data.videos)) return data.videos;\n      if (data && Array.isArray(data.files)) return data.files;\n      return [];\n    } catch (error) {\n      if (retries > 0) {\n        console.warn(`Grade ${grade} - API call failed, retrying... (${retries} retries left)`);\n        // Wait 1 second before retrying\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        return getVideoFilesFromAPI(grade, retries - 1);\n      }\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          console.warn(`Grade ${grade} - Request timeout`);\n        } else {\n          console.error(`Grade ${grade} - API error:`, error);\n        }\n      } else {\n        console.error(`Grade ${grade} - An unknown error occurred:`, error);\n      }\n      return [];\n    }\n  }, []);\n  useEffect(() => {\n    const loadVideoSamples = async () => {\n      setIsLoading(true);\n      setError(null);\n\n      // Initialize loading states\n      const initialLoadingState = {};\n      for (let grade = 1; grade <= 8; grade++) {\n        initialLoadingState[grade] = true;\n      }\n      setGradeLoading(initialLoadingState);\n      const videosByGradeTemp = {};\n      const errorsTemp = {};\n\n      // Initialize empty arrays\n      for (let grade = 1; grade <= 8; grade++) {\n        videosByGradeTemp[grade] = [];\n        errorsTemp[grade] = null;\n      }\n      try {\n        // Load all grades in parallel with proper error isolation\n        const gradePromises = Array.from({\n          length: 8\n        }, (_, i) => {\n          const grade = i + 1;\n          return getVideoFilesFromAPI(grade).then(videoFiles => {\n            if (videoFiles.length > 0) {\n              videosByGradeTemp[grade] = videoFiles.map((filename, index) => ({\n                id: `grade-${grade}-video-${index}`,\n                filename: filename,\n                videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                grade: grade,\n                title: generateVideoTitle(filename, grade)\n              }));\n            }\n            // Update loading state for this grade\n            setGradeLoading(prev => ({\n              ...prev,\n              [grade]: false\n            }));\n          }).catch(error => {\n            console.error(`Failed to load videos for grade ${grade}:`, error);\n            errorsTemp[grade] = `Failed to load videos for grade ${grade}`;\n            setGradeErrors(prev => ({\n              ...prev,\n              [grade]: `Failed to load videos for grade ${grade}`\n            }));\n            // Update loading state for this grade\n            setGradeLoading(prev => ({\n              ...prev,\n              [grade]: false\n            }));\n            // Don't throw - just leave empty array\n          });\n        });\n        await Promise.allSettled(gradePromises);\n        setVideosByGrade(videosByGradeTemp);\n      } catch (error) {\n        console.error('Error loading video samples:', error);\n        setError('Failed to load video samples');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadVideoSamples();\n  }, [getVideoFilesFromAPI]);\n  const generateVideoTitle = (filename, grade) => {\n    const nameWithoutExt = filename.replace(/\\.[^/.]+$/, '');\n    const cleaned = nameWithoutExt.replace(/[_-]/g, ' ');\n    const titleCase = cleaned.replace(/\\b\\w/g, l => l.toUpperCase());\n    return titleCase || `Grade ${grade} Video Sample`;\n  };\n  const handleVideoClick = video => {\n    setSelectedVideo(video);\n  };\n  const handleCloseVideo = () => {\n    setSelectedVideo(null);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"samples-page-new loading\",\n      children: \"Loading video samples...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"samples-page-new error-message\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"samples-page-new\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"carousels-grid-new\",\n      children: [1, 2, 3, 4, 5, 6, 7, 8].map(grade => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grade-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"grade-title-new\",\n          children: [\"Grade \", grade]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), gradeLoading[grade] ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"carousel-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"carousel-container-new loading\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-placeholder-new\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading videos...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 15\n        }, this) : gradeErrors[grade] ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"carousel-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"carousel-container-new error\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"error-placeholder-new\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: gradeErrors[grade]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Reset error and retry loading\n                  setGradeErrors(prev => ({\n                    ...prev,\n                    [grade]: null\n                  }));\n                  setGradeLoading(prev => ({\n                    ...prev,\n                    [grade]: true\n                  }));\n                  getVideoFilesFromAPI(grade).then(videoFiles => {\n                    if (videoFiles.length > 0) {\n                      const updatedVideos = videoFiles.map((filename, index) => ({\n                        id: `grade-${grade}-video-${index}`,\n                        filename: filename,\n                        videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                        thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                        grade: grade,\n                        title: generateVideoTitle(filename, grade)\n                      }));\n                      setVideosByGrade(prev => ({\n                        ...prev,\n                        [grade]: updatedVideos\n                      }));\n                    }\n                    setGradeLoading(prev => ({\n                      ...prev,\n                      [grade]: false\n                    }));\n                  }).catch(error => {\n                    console.error(`Failed to reload videos for grade ${grade}:`, error);\n                    setGradeErrors(prev => ({\n                      ...prev,\n                      [grade]: `Failed to load videos for grade ${grade}`\n                    }));\n                    setGradeLoading(prev => ({\n                      ...prev,\n                      [grade]: false\n                    }));\n                  });\n                },\n                children: \"Retry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(VerticalCarousel, {\n          videos: videosByGrade[grade] || [],\n          grade: grade,\n          onVideoClick: handleVideoClick\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this)]\n      }, grade, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), selectedVideo && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selected-video-section\",\n      onClick: handleCloseVideo,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleCloseVideo,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-player\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [selectedVideo.title, \" - Grade \", selectedVideo.grade]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"video\", {\n            controls: true,\n            width: \"100%\",\n            style: {\n              maxWidth: '600px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"source\", {\n              src: selectedVideo.videoUrl,\n              type: \"video/mp4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), \"Your browser does not support the video tag.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"purchase-button\",\n              children: \"Add to Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"info-button\",\n              children: \"More Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 347,\n    columnNumber: 5\n  }, this);\n};\n_s2(SamplesPage, \"QPUzCbklF14kVhLFN+9afogqEEo=\");\n_c2 = SamplesPage;\nexport default SamplesPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"VerticalCarousel\");\n$RefreshReg$(_c2, \"SamplesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "jsxDEV", "_jsxDEV", "VerticalCarousel", "videos", "grade", "onVideoClick", "_s", "containerRef", "trackRef", "animationRef", "lastUpdateRef", "currentIndex", "setCurrentIndex", "isHovered", "setIsHovered", "itemHeight", "visibleItems", "maxIndex", "Math", "max", "length", "animate", "mouseY", "now", "Date", "current", "containerRect", "getBoundingClientRect", "containerHeight", "height", "middleY", "top", "distanceFromCenter", "threshold", "abs", "scrollDirection", "scrollSpeed", "min", "prev", "newIndex", "currentMouseY", "animateLoop", "requestAnimationFrame", "handleMouseMove", "e", "clientY", "document", "addEventListener", "removeEventListener", "cancelAnimationFrame", "yPosition", "style", "transform", "handleMouseEnter", "handleMouseLeave", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onMouseEnter", "onMouseLeave", "map", "video", "index", "position", "floor", "isCenter", "isVisible", "onClick", "src", "thumbnailUrl", "alt", "title", "width", "objectFit", "borderRadius", "onError", "target", "dataset", "retried", "setTimeout", "backgroundColor", "display", "alignItems", "justifyContent", "color", "fontSize", "textContent", "id", "filename", "_c", "SamplesPage", "_s2", "videosByGrade", "setVideosByGrade", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "isLoading", "setIsLoading", "error", "setError", "gradeLoading", "setGradeLoading", "gradeErrors", "setGradeErrors", "getVideoFilesFromAPI", "retries", "controller", "AbortController", "timeoutId", "abort", "response", "fetch", "signal", "headers", "clearTimeout", "ok", "status", "Error", "text", "trim", "data", "JSON", "parse", "Array", "isArray", "files", "console", "warn", "Promise", "resolve", "name", "loadVideoSamples", "initialLoadingState", "videosByGradeTemp", "errorsTemp", "gradePromises", "from", "_", "i", "then", "videoFiles", "videoUrl", "replace", "generateVideoTitle", "catch", "allSettled", "nameWithoutExt", "cleaned", "titleCase", "l", "toUpperCase", "handleVideoClick", "handleCloseVideo", "updatedVideos", "stopPropagation", "controls", "max<PERSON><PERSON><PERSON>", "type", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport './SamplesPage.css';\n\n// Simplified interface for shop display\ninterface VideoSample {\n  id: string;\n  filename: string;\n  videoUrl: string;\n  thumbnailUrl: string;\n  grade: number;\n  title: string;\n}\n\n// Group videos by grade\ninterface VideosByGrade {\n  [grade: number]: VideoSample[];\n}\n\ninterface VerticalCarouselProps {\n  videos: VideoSample[];\n  grade: number;\n  onVideoClick: (video: VideoSample) => void;\n}\n\nconst VerticalCarousel: React.FC<VerticalCarouselProps> = ({ videos, grade, onVideoClick }) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const trackRef = useRef<HTMLDivElement>(null);\n  const animationRef = useRef<number | null>(null);\n  const lastUpdateRef = useRef<number>(0);\n  \n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isHovered, setIsHovered] = useState(false);\n  \n  const itemHeight = 120;\n  const visibleItems = 3;\n  const maxIndex = Math.max(0, videos.length - 1);\n\n  // Debounced animation with proper cleanup\n  const animate = useCallback((mouseY: number) => {\n    const now = Date.now();\n    if (now - lastUpdateRef.current < 16) return; // ~60fps throttle\n    lastUpdateRef.current = now;\n\n    if (!containerRef.current || videos.length <= visibleItems) return;\n\n    const containerRect = containerRef.current.getBoundingClientRect();\n    const containerHeight = containerRect.height;\n    const middleY = containerRect.top + containerHeight / 2;\n    \n    const distanceFromCenter = mouseY - middleY;\n    const threshold = 30;\n    \n    if (Math.abs(distanceFromCenter) > threshold) {\n      const scrollDirection = distanceFromCenter > 0 ? 1 : -1;\n      const scrollSpeed = Math.min(Math.abs(distanceFromCenter) / 2000, 0.1); // Cap speed\n      \n      setCurrentIndex(prev => {\n        const newIndex = prev + (scrollDirection * scrollSpeed);\n        return Math.max(0, Math.min(newIndex, maxIndex));\n      });\n    }\n  }, [maxIndex, videos.length, visibleItems]);\n\n  // Proper animation frame management\n  useEffect(() => {\n    let currentMouseY = 0;\n    \n    const animateLoop = () => {\n      if (isHovered) {\n        animate(currentMouseY);\n        animationRef.current = requestAnimationFrame(animateLoop);\n      }\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      currentMouseY = e.clientY;\n    };\n\n    if (isHovered) {\n      document.addEventListener('mousemove', handleMouseMove);\n      animationRef.current = requestAnimationFrame(animateLoop);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = null;\n      }\n    };\n  }, [isHovered, animate]);\n\n  // Smooth track position updates\n  useEffect(() => {\n    if (!trackRef.current) return;\n    \n    const yPosition = -currentIndex * itemHeight;\n    trackRef.current.style.transform = `translateY(${yPosition}px)`;\n  }, [currentIndex, itemHeight]);\n\n  const handleMouseEnter = () => setIsHovered(true);\n  const handleMouseLeave = () => setIsHovered(false);\n\n\n  if (videos.length === 0) {\n    return (\n      <div className=\"carousel-wrapper\">\n        <div className=\"carousel-container-new\">\n          <div className=\"no-videos-placeholder-new\">\n            <p>No videos for Grade {grade}</p>\n          </div>\n        </div>\n        <div className=\"filename-list\">\n          <div className=\"filename-item empty\">No files</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"carousel-wrapper\">\n      <div \n        className=\"carousel-container-new\"\n        ref={containerRef}\n        onMouseEnter={handleMouseEnter}\n        onMouseLeave={handleMouseLeave}\n      >\n        <div className=\"carousel-viewport-new\">\n          <div className=\"carousel-track-new\" ref={trackRef}>\n            {videos.map((video, index) => {\n              const position = index - Math.floor(currentIndex);\n              const isCenter = position === 1;\n              const isVisible = position >= 0 && position <= 2;\n              \n              return (\n                <div \n                  key={video.id} \n                  className={`carousel-item-new ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`}\n                >\n                  <div className=\"video-thumbnail-new\" onClick={() => onVideoClick(video)}>\n                    <img\n                      src={video.thumbnailUrl}\n                      alt={video.title}\n                      style={{\n                        width: '100%',\n                        height: '100%',\n                        objectFit: 'cover',\n                        borderRadius: '6px'\n                      }}\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement;\n                        // Retry loading the image once\n                        if (!target.dataset.retried) {\n                          target.dataset.retried = \"true\";\n                          setTimeout(() => {\n                            target.src = video.thumbnailUrl + `?retry=${Date.now()}`;\n                          }, 1000);\n                        } else {\n                          // Show fallback if retry failed\n                          target.style.backgroundColor = '#007bff';\n                          target.style.display = 'flex';\n                          target.style.alignItems = 'center';\n                          target.style.justifyContent = 'center';\n                          target.style.color = 'white';\n                          target.style.fontSize = '12px';\n                          target.textContent = 'Thumbnail';\n                        }\n                      }}\n                    />\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"filename-list\">\n        {videos.map((video, index) => {\n          const position = index - Math.floor(currentIndex);\n          const isCenter = position === 1;\n          const isVisible = position >= 0 && position <= 2;\n          \n          return (\n            <div \n              key={`filename-${video.id}`}\n              className={`filename-item ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`}\n            >\n              {video.filename}\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nconst SamplesPage: React.FC = () => {\n  const [videosByGrade, setVideosByGrade] = useState<VideosByGrade>({});\n  const [selectedVideo, setSelectedVideo] = useState<VideoSample | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [gradeLoading, setGradeLoading] = useState<{[key: number]: boolean}>({});\n  const [gradeErrors, setGradeErrors] = useState<{[key: number]: string | null}>({});\n\n  // Improved API call with retry mechanism\n  const getVideoFilesFromAPI = useCallback(async (grade: number, retries = 3): Promise<string[]> => {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout\n      \n      const response = await fetch(`/api/videos/grade/${grade}/`, {\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n          'Cache-Control': 'no-cache'\n        }\n      });\n      \n      clearTimeout(timeoutId);\n      \n      if (!response.ok) {\n        if (response.status === 404) return []; // No videos for this grade\n        throw new Error(`HTTP ${response.status}`);\n      }\n      \n      const text = await response.text();\n      if (!text.trim()) return [];\n      \n      const data = JSON.parse(text);\n      \n      // Handle different response formats\n      if (Array.isArray(data)) return data;\n      if (data && Array.isArray(data.videos)) return data.videos;\n      if (data && Array.isArray(data.files)) return data.files;\n      \n      return [];\n    } catch (error: unknown) {\n      if (retries > 0) {\n        console.warn(`Grade ${grade} - API call failed, retrying... (${retries} retries left)`);\n        // Wait 1 second before retrying\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        return getVideoFilesFromAPI(grade, retries - 1);\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          console.warn(`Grade ${grade} - Request timeout`);\n        } else {\n          console.error(`Grade ${grade} - API error:`, error);\n        }\n      } else {\n        console.error(`Grade ${grade} - An unknown error occurred:`, error);\n      }\n      return [];\n    }\n  }, []);\n\n  useEffect(() => {\n    const loadVideoSamples = async () => {\n      setIsLoading(true);\n      setError(null);\n      \n      // Initialize loading states\n      const initialLoadingState: {[key: number]: boolean} = {};\n      for (let grade = 1; grade <= 8; grade++) {\n        initialLoadingState[grade] = true;\n      }\n      setGradeLoading(initialLoadingState);\n      \n      const videosByGradeTemp: VideosByGrade = {};\n      const errorsTemp: {[key: number]: string | null} = {};\n      \n      // Initialize empty arrays\n      for (let grade = 1; grade <= 8; grade++) {\n        videosByGradeTemp[grade] = [];\n        errorsTemp[grade] = null;\n      }\n\n      try {\n        // Load all grades in parallel with proper error isolation\n        const gradePromises = Array.from({ length: 8 }, (_, i) => {\n          const grade = i + 1;\n          return getVideoFilesFromAPI(grade)\n            .then(videoFiles => {\n              if (videoFiles.length > 0) {\n                videosByGradeTemp[grade] = videoFiles.map((filename, index) => ({\n                  id: `grade-${grade}-video-${index}`,\n                  filename: filename,\n                  videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                  thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                  grade: grade,\n                  title: generateVideoTitle(filename, grade)\n                }));\n              }\n              // Update loading state for this grade\n              setGradeLoading(prev => ({...prev, [grade]: false}));\n            })\n            .catch(error => {\n              console.error(`Failed to load videos for grade ${grade}:`, error);\n              errorsTemp[grade] = `Failed to load videos for grade ${grade}`;\n              setGradeErrors(prev => ({...prev, [grade]: `Failed to load videos for grade ${grade}`}));\n              // Update loading state for this grade\n              setGradeLoading(prev => ({...prev, [grade]: false}));\n              // Don't throw - just leave empty array\n            });\n        });\n\n        await Promise.allSettled(gradePromises);\n        setVideosByGrade(videosByGradeTemp);\n        \n      } catch (error) {\n        console.error('Error loading video samples:', error);\n        setError('Failed to load video samples');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadVideoSamples();\n  }, [getVideoFilesFromAPI]);\n\n  const generateVideoTitle = (filename: string, grade: number): string => {\n    const nameWithoutExt = filename.replace(/\\.[^/.]+$/, '');\n    const cleaned = nameWithoutExt.replace(/[_-]/g, ' ');\n    const titleCase = cleaned.replace(/\\b\\w/g, l => l.toUpperCase());\n    return titleCase || `Grade ${grade} Video Sample`;\n  };\n\n  const handleVideoClick = (video: VideoSample) => {\n    setSelectedVideo(video);\n  };\n\n  const handleCloseVideo = () => {\n    setSelectedVideo(null);\n  };\n\n  if (isLoading) {\n    return <div className=\"samples-page-new loading\">Loading video samples...</div>;\n  }\n\n  if (error) {\n    return <div className=\"samples-page-new error-message\">Error: {error}</div>;\n  }\n\n  return (\n    <div className=\"samples-page-new\">\n      <div className=\"carousels-grid-new\">\n        {[1, 2, 3, 4, 5, 6, 7, 8].map(grade => (\n          <div key={grade} className=\"grade-section\">\n            <h2 className=\"grade-title-new\">Grade {grade}</h2>\n            {gradeLoading[grade] ? (\n              <div className=\"carousel-wrapper\">\n                <div className=\"carousel-container-new loading\">\n                  <div className=\"loading-placeholder-new\">\n                    <p>Loading videos...</p>\n                  </div>\n                </div>\n              </div>\n            ) : gradeErrors[grade] ? (\n              <div className=\"carousel-wrapper\">\n                <div className=\"carousel-container-new error\">\n                  <div className=\"error-placeholder-new\">\n                    <p>{gradeErrors[grade]}</p>\n                    <button\n                      onClick={() => {\n                        // Reset error and retry loading\n                        setGradeErrors(prev => ({...prev, [grade]: null}));\n                        setGradeLoading(prev => ({...prev, [grade]: true}));\n                        getVideoFilesFromAPI(grade).then(videoFiles => {\n                          if (videoFiles.length > 0) {\n                            const updatedVideos = videoFiles.map((filename, index) => ({\n                              id: `grade-${grade}-video-${index}`,\n                              filename: filename,\n                              videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                              thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                              grade: grade,\n                              title: generateVideoTitle(filename, grade)\n                            }));\n                            setVideosByGrade(prev => ({...prev, [grade]: updatedVideos}));\n                          }\n                          setGradeLoading(prev => ({...prev, [grade]: false}));\n                        }).catch(error => {\n                          console.error(`Failed to reload videos for grade ${grade}:`, error);\n                          setGradeErrors(prev => ({...prev, [grade]: `Failed to load videos for grade ${grade}`}));\n                          setGradeLoading(prev => ({...prev, [grade]: false}));\n                        });\n                      }}\n                    >\n                      Retry\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <VerticalCarousel\n                videos={videosByGrade[grade] || []}\n                grade={grade}\n                onVideoClick={handleVideoClick}\n              />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {selectedVideo && (\n        <div className=\"selected-video-section\" onClick={handleCloseVideo}>\n          <div className=\"video-content\" onClick={(e) => e.stopPropagation()}>\n            <button className=\"close-button\" onClick={handleCloseVideo}>×</button>\n            <div className=\"video-player\">\n              <h2>{selectedVideo.title} - Grade {selectedVideo.grade}</h2>\n              <video controls width=\"100%\" style={{ maxWidth: '600px' }}>\n                <source src={selectedVideo.videoUrl} type=\"video/mp4\" />\n                Your browser does not support the video tag.\n              </video>\n              <div className=\"video-actions\">\n                <button className=\"purchase-button\">Add to Cart</button>\n                <button className=\"info-button\">More Info</button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SamplesPage;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,mBAAmB;;AAE1B;;AAUA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC7F,MAAMC,YAAY,GAAGT,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAMU,QAAQ,GAAGV,MAAM,CAAiB,IAAI,CAAC;EAC7C,MAAMW,YAAY,GAAGX,MAAM,CAAgB,IAAI,CAAC;EAChD,MAAMY,aAAa,GAAGZ,MAAM,CAAS,CAAC,CAAC;EAEvC,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmB,UAAU,GAAG,GAAG;EACtB,MAAMC,YAAY,GAAG,CAAC;EACtB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhB,MAAM,CAACiB,MAAM,GAAG,CAAC,CAAC;;EAE/C;EACA,MAAMC,OAAO,GAAGtB,WAAW,CAAEuB,MAAc,IAAK;IAC9C,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGb,aAAa,CAACe,OAAO,GAAG,EAAE,EAAE,OAAO,CAAC;IAC9Cf,aAAa,CAACe,OAAO,GAAGF,GAAG;IAE3B,IAAI,CAAChB,YAAY,CAACkB,OAAO,IAAItB,MAAM,CAACiB,MAAM,IAAIJ,YAAY,EAAE;IAE5D,MAAMU,aAAa,GAAGnB,YAAY,CAACkB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAClE,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM;IAC5C,MAAMC,OAAO,GAAGJ,aAAa,CAACK,GAAG,GAAGH,eAAe,GAAG,CAAC;IAEvD,MAAMI,kBAAkB,GAAGV,MAAM,GAAGQ,OAAO;IAC3C,MAAMG,SAAS,GAAG,EAAE;IAEpB,IAAIf,IAAI,CAACgB,GAAG,CAACF,kBAAkB,CAAC,GAAGC,SAAS,EAAE;MAC5C,MAAME,eAAe,GAAGH,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACvD,MAAMI,WAAW,GAAGlB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACgB,GAAG,CAACF,kBAAkB,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;;MAExEpB,eAAe,CAAC0B,IAAI,IAAI;QACtB,MAAMC,QAAQ,GAAGD,IAAI,GAAIH,eAAe,GAAGC,WAAY;QACvD,OAAOlB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACmB,GAAG,CAACE,QAAQ,EAAEtB,QAAQ,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEd,MAAM,CAACiB,MAAM,EAAEJ,YAAY,CAAC,CAAC;;EAE3C;EACAnB,SAAS,CAAC,MAAM;IACd,IAAI2C,aAAa,GAAG,CAAC;IAErB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAI5B,SAAS,EAAE;QACbQ,OAAO,CAACmB,aAAa,CAAC;QACtB/B,YAAY,CAACgB,OAAO,GAAGiB,qBAAqB,CAACD,WAAW,CAAC;MAC3D;IACF,CAAC;IAED,MAAME,eAAe,GAAIC,CAAa,IAAK;MACzCJ,aAAa,GAAGI,CAAC,CAACC,OAAO;IAC3B,CAAC;IAED,IAAIhC,SAAS,EAAE;MACbiC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,eAAe,CAAC;MACvDlC,YAAY,CAACgB,OAAO,GAAGiB,qBAAqB,CAACD,WAAW,CAAC;IAC3D;IAEA,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEL,eAAe,CAAC;MAC1D,IAAIlC,YAAY,CAACgB,OAAO,EAAE;QACxBwB,oBAAoB,CAACxC,YAAY,CAACgB,OAAO,CAAC;QAC1ChB,YAAY,CAACgB,OAAO,GAAG,IAAI;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACZ,SAAS,EAAEQ,OAAO,CAAC,CAAC;;EAExB;EACAxB,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,QAAQ,CAACiB,OAAO,EAAE;IAEvB,MAAMyB,SAAS,GAAG,CAACvC,YAAY,GAAGI,UAAU;IAC5CP,QAAQ,CAACiB,OAAO,CAAC0B,KAAK,CAACC,SAAS,GAAG,cAAcF,SAAS,KAAK;EACjE,CAAC,EAAE,CAACvC,YAAY,EAAEI,UAAU,CAAC,CAAC;EAE9B,MAAMsC,gBAAgB,GAAGA,CAAA,KAAMvC,YAAY,CAAC,IAAI,CAAC;EACjD,MAAMwC,gBAAgB,GAAGA,CAAA,KAAMxC,YAAY,CAAC,KAAK,CAAC;EAGlD,IAAIX,MAAM,CAACiB,MAAM,KAAK,CAAC,EAAE;IACvB,oBACEnB,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvD,OAAA;QAAKsD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCvD,OAAA;UAAKsD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCvD,OAAA;YAAAuD,QAAA,GAAG,sBAAoB,EAACpD,KAAK;UAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3D,OAAA;QAAKsD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BvD,OAAA;UAAKsD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3D,OAAA;IAAKsD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BvD,OAAA;MACEsD,SAAS,EAAC,wBAAwB;MAClCM,GAAG,EAAEtD,YAAa;MAClBuD,YAAY,EAAET,gBAAiB;MAC/BU,YAAY,EAAET,gBAAiB;MAAAE,QAAA,eAE/BvD,OAAA;QAAKsD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCvD,OAAA;UAAKsD,SAAS,EAAC,oBAAoB;UAACM,GAAG,EAAErD,QAAS;UAAAgD,QAAA,EAC/CrD,MAAM,CAAC6D,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;YAC5B,MAAMC,QAAQ,GAAGD,KAAK,GAAGhD,IAAI,CAACkD,KAAK,CAACzD,YAAY,CAAC;YACjD,MAAM0D,QAAQ,GAAGF,QAAQ,KAAK,CAAC;YAC/B,MAAMG,SAAS,GAAGH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC;YAEhD,oBACElE,OAAA;cAEEsD,SAAS,EAAE,qBAAqBc,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAIC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;cAAAd,QAAA,eAEzFvD,OAAA;gBAAKsD,SAAS,EAAC,qBAAqB;gBAACgB,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAAC4D,KAAK,CAAE;gBAAAT,QAAA,eACtEvD,OAAA;kBACEuE,GAAG,EAAEP,KAAK,CAACQ,YAAa;kBACxBC,GAAG,EAAET,KAAK,CAACU,KAAM;kBACjBxB,KAAK,EAAE;oBACLyB,KAAK,EAAE,MAAM;oBACb/C,MAAM,EAAE,MAAM;oBACdgD,SAAS,EAAE,OAAO;oBAClBC,YAAY,EAAE;kBAChB,CAAE;kBACFC,OAAO,EAAGnC,CAAC,IAAK;oBACd,MAAMoC,MAAM,GAAGpC,CAAC,CAACoC,MAA0B;oBAC3C;oBACA,IAAI,CAACA,MAAM,CAACC,OAAO,CAACC,OAAO,EAAE;sBAC3BF,MAAM,CAACC,OAAO,CAACC,OAAO,GAAG,MAAM;sBAC/BC,UAAU,CAAC,MAAM;wBACfH,MAAM,CAACR,GAAG,GAAGP,KAAK,CAACQ,YAAY,GAAG,UAAUjD,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;sBAC1D,CAAC,EAAE,IAAI,CAAC;oBACV,CAAC,MAAM;sBACL;sBACAyD,MAAM,CAAC7B,KAAK,CAACiC,eAAe,GAAG,SAAS;sBACxCJ,MAAM,CAAC7B,KAAK,CAACkC,OAAO,GAAG,MAAM;sBAC7BL,MAAM,CAAC7B,KAAK,CAACmC,UAAU,GAAG,QAAQ;sBAClCN,MAAM,CAAC7B,KAAK,CAACoC,cAAc,GAAG,QAAQ;sBACtCP,MAAM,CAAC7B,KAAK,CAACqC,KAAK,GAAG,OAAO;sBAC5BR,MAAM,CAAC7B,KAAK,CAACsC,QAAQ,GAAG,MAAM;sBAC9BT,MAAM,CAACU,WAAW,GAAG,WAAW;oBAClC;kBACF;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC,GAjCDK,KAAK,CAAC0B,EAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3D,OAAA;MAAKsD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BrD,MAAM,CAAC6D,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC5B,MAAMC,QAAQ,GAAGD,KAAK,GAAGhD,IAAI,CAACkD,KAAK,CAACzD,YAAY,CAAC;QACjD,MAAM0D,QAAQ,GAAGF,QAAQ,KAAK,CAAC;QAC/B,MAAMG,SAAS,GAAGH,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC;QAEhD,oBACElE,OAAA;UAEEsD,SAAS,EAAE,iBAAiBc,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAIC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UAAAd,QAAA,EAEpFS,KAAK,CAAC2B;QAAQ,GAHV,YAAY3B,KAAK,CAAC0B,EAAE,EAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIxB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA3KIJ,gBAAiD;AAAA2F,EAAA,GAAjD3F,gBAAiD;AA6KvD,MAAM4F,WAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrG,QAAQ,CAAgB,CAAC,CAAC,CAAC;EACrE,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAqB,IAAI,CAAC;EAC5E,MAAM,CAACwG,SAAS,EAAEC,YAAY,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0G,KAAK,EAAEC,QAAQ,CAAC,GAAG3G,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4G,YAAY,EAAEC,eAAe,CAAC,GAAG7G,QAAQ,CAA2B,CAAC,CAAC,CAAC;EAC9E,MAAM,CAAC8G,WAAW,EAAEC,cAAc,CAAC,GAAG/G,QAAQ,CAAiC,CAAC,CAAC,CAAC;;EAElF;EACA,MAAMgH,oBAAoB,GAAG7G,WAAW,CAAC,OAAOK,KAAa,EAAEyG,OAAO,GAAG,CAAC,KAAwB;IAChG,IAAI;MACF,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAG7B,UAAU,CAAC,MAAM2B,UAAU,CAACG,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAE/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/G,KAAK,GAAG,EAAE;QAC1DgH,MAAM,EAAEN,UAAU,CAACM,MAAM;QACzBC,OAAO,EAAE;UACP,QAAQ,EAAE,kBAAkB;UAC5B,eAAe,EAAE;QACnB;MACF,CAAC,CAAC;MAEFC,YAAY,CAACN,SAAS,CAAC;MAEvB,IAAI,CAACE,QAAQ,CAACK,EAAE,EAAE;QAChB,IAAIL,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;QACxC,MAAM,IAAIC,KAAK,CAAC,QAAQP,QAAQ,CAACM,MAAM,EAAE,CAAC;MAC5C;MAEA,MAAME,IAAI,GAAG,MAAMR,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClC,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;;MAE7B;MACA,IAAIK,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE,OAAOA,IAAI;MACpC,IAAIA,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACzH,MAAM,CAAC,EAAE,OAAOyH,IAAI,CAACzH,MAAM;MAC1D,IAAIyH,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,KAAK,CAAC,EAAE,OAAOL,IAAI,CAACK,KAAK;MAExD,OAAO,EAAE;IACX,CAAC,CAAC,OAAO3B,KAAc,EAAE;MACvB,IAAIO,OAAO,GAAG,CAAC,EAAE;QACfqB,OAAO,CAACC,IAAI,CAAC,SAAS/H,KAAK,oCAAoCyG,OAAO,gBAAgB,CAAC;QACvF;QACA,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAIlD,UAAU,CAACkD,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,OAAOzB,oBAAoB,CAACxG,KAAK,EAAEyG,OAAO,GAAG,CAAC,CAAC;MACjD;MAEA,IAAIP,KAAK,YAAYmB,KAAK,EAAE;QAC1B,IAAInB,KAAK,CAACgC,IAAI,KAAK,YAAY,EAAE;UAC/BJ,OAAO,CAACC,IAAI,CAAC,SAAS/H,KAAK,oBAAoB,CAAC;QAClD,CAAC,MAAM;UACL8H,OAAO,CAAC5B,KAAK,CAAC,SAASlG,KAAK,eAAe,EAAEkG,KAAK,CAAC;QACrD;MACF,CAAC,MAAM;QACL4B,OAAO,CAAC5B,KAAK,CAAC,SAASlG,KAAK,+BAA+B,EAAEkG,KAAK,CAAC;MACrE;MACA,OAAO,EAAE;IACX;EACF,CAAC,EAAE,EAAE,CAAC;EAENzG,SAAS,CAAC,MAAM;IACd,MAAM0I,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnClC,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMiC,mBAA6C,GAAG,CAAC,CAAC;MACxD,KAAK,IAAIpI,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;QACvCoI,mBAAmB,CAACpI,KAAK,CAAC,GAAG,IAAI;MACnC;MACAqG,eAAe,CAAC+B,mBAAmB,CAAC;MAEpC,MAAMC,iBAAgC,GAAG,CAAC,CAAC;MAC3C,MAAMC,UAA0C,GAAG,CAAC,CAAC;;MAErD;MACA,KAAK,IAAItI,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;QACvCqI,iBAAiB,CAACrI,KAAK,CAAC,GAAG,EAAE;QAC7BsI,UAAU,CAACtI,KAAK,CAAC,GAAG,IAAI;MAC1B;MAEA,IAAI;QACF;QACA,MAAMuI,aAAa,GAAGZ,KAAK,CAACa,IAAI,CAAC;UAAExH,MAAM,EAAE;QAAE,CAAC,EAAE,CAACyH,CAAC,EAAEC,CAAC,KAAK;UACxD,MAAM1I,KAAK,GAAG0I,CAAC,GAAG,CAAC;UACnB,OAAOlC,oBAAoB,CAACxG,KAAK,CAAC,CAC/B2I,IAAI,CAACC,UAAU,IAAI;YAClB,IAAIA,UAAU,CAAC5H,MAAM,GAAG,CAAC,EAAE;cACzBqH,iBAAiB,CAACrI,KAAK,CAAC,GAAG4I,UAAU,CAAChF,GAAG,CAAC,CAAC4B,QAAQ,EAAE1B,KAAK,MAAM;gBAC9DyB,EAAE,EAAE,SAASvF,KAAK,UAAU8D,KAAK,EAAE;gBACnC0B,QAAQ,EAAEA,QAAQ;gBAClBqD,QAAQ,EAAE,uBAAuB7I,KAAK,IAAIwF,QAAQ,EAAE;gBACpDnB,YAAY,EAAE,uBAAuBrE,KAAK,IAAIwF,QAAQ,CAACsD,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;gBACrF9I,KAAK,EAAEA,KAAK;gBACZuE,KAAK,EAAEwE,kBAAkB,CAACvD,QAAQ,EAAExF,KAAK;cAC3C,CAAC,CAAC,CAAC;YACL;YACA;YACAqG,eAAe,CAACnE,IAAI,KAAK;cAAC,GAAGA,IAAI;cAAE,CAAClC,KAAK,GAAG;YAAK,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CACDgJ,KAAK,CAAC9C,KAAK,IAAI;YACd4B,OAAO,CAAC5B,KAAK,CAAC,mCAAmClG,KAAK,GAAG,EAAEkG,KAAK,CAAC;YACjEoC,UAAU,CAACtI,KAAK,CAAC,GAAG,mCAAmCA,KAAK,EAAE;YAC9DuG,cAAc,CAACrE,IAAI,KAAK;cAAC,GAAGA,IAAI;cAAE,CAAClC,KAAK,GAAG,mCAAmCA,KAAK;YAAE,CAAC,CAAC,CAAC;YACxF;YACAqG,eAAe,CAACnE,IAAI,KAAK;cAAC,GAAGA,IAAI;cAAE,CAAClC,KAAK,GAAG;YAAK,CAAC,CAAC,CAAC;YACpD;UACF,CAAC,CAAC;QACN,CAAC,CAAC;QAEF,MAAMgI,OAAO,CAACiB,UAAU,CAACV,aAAa,CAAC;QACvC1C,gBAAgB,CAACwC,iBAAiB,CAAC;MAErC,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACd4B,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDC,QAAQ,CAAC,8BAA8B,CAAC;MAC1C,CAAC,SAAS;QACRF,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDkC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC3B,oBAAoB,CAAC,CAAC;EAE1B,MAAMuC,kBAAkB,GAAGA,CAACvD,QAAgB,EAAExF,KAAa,KAAa;IACtE,MAAMkJ,cAAc,GAAG1D,QAAQ,CAACsD,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACxD,MAAMK,OAAO,GAAGD,cAAc,CAACJ,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IACpD,MAAMM,SAAS,GAAGD,OAAO,CAACL,OAAO,CAAC,OAAO,EAAEO,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IAChE,OAAOF,SAAS,IAAI,SAASpJ,KAAK,eAAe;EACnD,CAAC;EAED,MAAMuJ,gBAAgB,GAAI1F,KAAkB,IAAK;IAC/CkC,gBAAgB,CAAClC,KAAK,CAAC;EACzB,CAAC;EAED,MAAM2F,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,IAAIC,SAAS,EAAE;IACb,oBAAOnG,OAAA;MAAKsD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjF;EAEA,IAAI0C,KAAK,EAAE;IACT,oBAAOrG,OAAA;MAAKsD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,GAAC,SAAO,EAAC8C,KAAK;IAAA;MAAA7C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC7E;EAEA,oBACE3D,OAAA;IAAKsD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BvD,OAAA;MAAKsD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACQ,GAAG,CAAC5D,KAAK,iBACjCH,OAAA;QAAiBsD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACxCvD,OAAA;UAAIsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,QAAM,EAACpD,KAAK;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACjD4C,YAAY,CAACpG,KAAK,CAAC,gBAClBH,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvD,OAAA;YAAKsD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,eAC7CvD,OAAA;cAAKsD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACtCvD,OAAA;gBAAAuD,QAAA,EAAG;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ8C,WAAW,CAACtG,KAAK,CAAC,gBACpBH,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BvD,OAAA;YAAKsD,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CvD,OAAA;cAAKsD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpCvD,OAAA;gBAAAuD,QAAA,EAAIkD,WAAW,CAACtG,KAAK;cAAC;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3B3D,OAAA;gBACEsE,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAoC,cAAc,CAACrE,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE,CAAClC,KAAK,GAAG;kBAAI,CAAC,CAAC,CAAC;kBAClDqG,eAAe,CAACnE,IAAI,KAAK;oBAAC,GAAGA,IAAI;oBAAE,CAAClC,KAAK,GAAG;kBAAI,CAAC,CAAC,CAAC;kBACnDwG,oBAAoB,CAACxG,KAAK,CAAC,CAAC2I,IAAI,CAACC,UAAU,IAAI;oBAC7C,IAAIA,UAAU,CAAC5H,MAAM,GAAG,CAAC,EAAE;sBACzB,MAAMyI,aAAa,GAAGb,UAAU,CAAChF,GAAG,CAAC,CAAC4B,QAAQ,EAAE1B,KAAK,MAAM;wBACzDyB,EAAE,EAAE,SAASvF,KAAK,UAAU8D,KAAK,EAAE;wBACnC0B,QAAQ,EAAEA,QAAQ;wBAClBqD,QAAQ,EAAE,uBAAuB7I,KAAK,IAAIwF,QAAQ,EAAE;wBACpDnB,YAAY,EAAE,uBAAuBrE,KAAK,IAAIwF,QAAQ,CAACsD,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;wBACrF9I,KAAK,EAAEA,KAAK;wBACZuE,KAAK,EAAEwE,kBAAkB,CAACvD,QAAQ,EAAExF,KAAK;sBAC3C,CAAC,CAAC,CAAC;sBACH6F,gBAAgB,CAAC3D,IAAI,KAAK;wBAAC,GAAGA,IAAI;wBAAE,CAAClC,KAAK,GAAGyJ;sBAAa,CAAC,CAAC,CAAC;oBAC/D;oBACApD,eAAe,CAACnE,IAAI,KAAK;sBAAC,GAAGA,IAAI;sBAAE,CAAClC,KAAK,GAAG;oBAAK,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC,CAACgJ,KAAK,CAAC9C,KAAK,IAAI;oBAChB4B,OAAO,CAAC5B,KAAK,CAAC,qCAAqClG,KAAK,GAAG,EAAEkG,KAAK,CAAC;oBACnEK,cAAc,CAACrE,IAAI,KAAK;sBAAC,GAAGA,IAAI;sBAAE,CAAClC,KAAK,GAAG,mCAAmCA,KAAK;oBAAE,CAAC,CAAC,CAAC;oBACxFqG,eAAe,CAACnE,IAAI,KAAK;sBAAC,GAAGA,IAAI;sBAAE,CAAClC,KAAK,GAAG;oBAAK,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC;gBACJ,CAAE;gBAAAoD,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN3D,OAAA,CAACC,gBAAgB;UACfC,MAAM,EAAE6F,aAAa,CAAC5F,KAAK,CAAC,IAAI,EAAG;UACnCA,KAAK,EAAEA,KAAM;UACbC,YAAY,EAAEsJ;QAAiB;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACF;MAAA,GAnDOxD,KAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoDV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELsC,aAAa,iBACZjG,OAAA;MAAKsD,SAAS,EAAC,wBAAwB;MAACgB,OAAO,EAAEqF,gBAAiB;MAAApG,QAAA,eAChEvD,OAAA;QAAKsD,SAAS,EAAC,eAAe;QAACgB,OAAO,EAAG3B,CAAC,IAAKA,CAAC,CAACkH,eAAe,CAAC,CAAE;QAAAtG,QAAA,gBACjEvD,OAAA;UAAQsD,SAAS,EAAC,cAAc;UAACgB,OAAO,EAAEqF,gBAAiB;UAAApG,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtE3D,OAAA;UAAKsD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvD,OAAA;YAAAuD,QAAA,GAAK0C,aAAa,CAACvB,KAAK,EAAC,WAAS,EAACuB,aAAa,CAAC9F,KAAK;UAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5D3D,OAAA;YAAO8J,QAAQ;YAACnF,KAAK,EAAC,MAAM;YAACzB,KAAK,EAAE;cAAE6G,QAAQ,EAAE;YAAQ,CAAE;YAAAxG,QAAA,gBACxDvD,OAAA;cAAQuE,GAAG,EAAE0B,aAAa,CAAC+C,QAAS;cAACgB,IAAI,EAAC;YAAW;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gDAE1D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR3D,OAAA;YAAKsD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvD,OAAA;cAAQsD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxD3D,OAAA;cAAQsD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACmC,GAAA,CApOID,WAAqB;AAAAoE,GAAA,GAArBpE,WAAqB;AAsO3B,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAqE,GAAA;AAAAC,YAAA,CAAAtE,EAAA;AAAAsE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}