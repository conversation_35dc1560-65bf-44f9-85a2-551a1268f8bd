{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams,useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const VideoPreview=()=>{const{videoId}=useParams();const navigate=useNavigate();const[videoUrl,setVideoUrl]=useState('');useEffect(()=>{// Fetch watermarked video URL\nfetch(\"/api/videos/\".concat(videoId,\"/preview\")).then(res=>res.json()).then(data=>setVideoUrl(data.url));},[videoId]);return/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-4xl mx-auto py-8 px-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"aspect-w-16 aspect-h-9 mb-6\",children:/*#__PURE__*/_jsx(\"video\",{src:videoUrl,controls:true,className:\"rounded-lg shadow-lg\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>navigate(\"/checkout/\".concat(videoId)),className:\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\",children:\"Remove Watermark and Download Video\"})})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "VideoPreview", "videoId", "navigate", "videoUrl", "setVideoUrl", "fetch", "concat", "then", "res", "json", "data", "url", "className", "children", "src", "controls", "onClick"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nexport const VideoPreview: React.FC = () => {\n  const { videoId } = useParams();\n  const navigate = useNavigate();\n  const [videoUrl, setVideoUrl] = useState('');\n  \n  useEffect(() => {\n    // Fetch watermarked video URL\n    fetch(`/api/videos/${videoId}/preview`)\n      .then(res => res.json())\n      .then(data => setVideoUrl(data.url));\n  }, [videoId]);\n  \n  return (\n    <div className=\"max-w-4xl mx-auto py-8 px-4\">\n      <div className=\"aspect-w-16 aspect-h-9 mb-6\">\n        <video src={videoUrl} controls className=\"rounded-lg shadow-lg\" />\n      </div>\n      \n      <div className=\"text-center\">\n        <button\n          onClick={() => navigate(`/checkout/${videoId}`)}\n          className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\"\n        >\n          Remove Watermark and Download Video\n        </button>\n      </div>\n    </div>\n  );\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1D,MAAO,MAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAEC,OAAQ,CAAC,CAAGP,SAAS,CAAC,CAAC,CAC/B,KAAM,CAAAQ,QAAQ,CAAGP,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACQ,QAAQ,CAAEC,WAAW,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd;AACAY,KAAK,gBAAAC,MAAA,CAAgBL,OAAO,YAAU,CAAC,CACpCM,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,EAAIN,WAAW,CAACM,IAAI,CAACC,GAAG,CAAC,CAAC,CACxC,CAAC,CAAE,CAACV,OAAO,CAAC,CAAC,CAEb,mBACEF,KAAA,QAAKa,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ChB,IAAA,QAAKe,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ChB,IAAA,UAAOiB,GAAG,CAAEX,QAAS,CAACY,QAAQ,MAACH,SAAS,CAAC,sBAAsB,CAAE,CAAC,CAC/D,CAAC,cAENf,IAAA,QAAKe,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BhB,IAAA,WACEmB,OAAO,CAAEA,CAAA,GAAMd,QAAQ,cAAAI,MAAA,CAAcL,OAAO,CAAE,CAAE,CAChDW,SAAS,CAAC,iEAAiE,CAAAC,QAAA,CAC5E,qCAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}