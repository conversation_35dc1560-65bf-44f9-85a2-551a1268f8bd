{"ast": null, "code": "// The primary entry point assumes we're working with standard ReactDOM/RN, but\n// older versions that do not include `useSyncExternalStore` (React 16.9 - 17.x).\n// Because of that, the useSyncExternalStore compat shim is needed.\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch';\nimport { initializeUseSelector } from './hooks/useSelector';\nimport { initializeConnect } from './components/connect';\ninitializeUseSelector(useSyncExternalStoreWithSelector);\ninitializeConnect(useSyncExternalStore); // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };\nexport * from './exports';", "map": {"version": 3, "names": ["useSyncExternalStore", "useSyncExternalStoreWithSelector", "unstable_batchedUpdates", "batch", "setBatch", "initializeUseSelector", "initializeConnect"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/index.js"], "sourcesContent": ["// The primary entry point assumes we're working with standard ReactDOM/RN, but\n// older versions that do not include `useSyncExternalStore` (React 16.9 - 17.x).\n// Because of that, the useSyncExternalStore compat shim is needed.\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch';\nimport { initializeUseSelector } from './hooks/useSelector';\nimport { initializeConnect } from './components/connect';\ninitializeUseSelector(useSyncExternalStoreWithSelector);\ninitializeConnect(useSyncExternalStore); // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };\nexport * from './exports';"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,gCAAgC,QAAQ,4CAA4C;AAC7F,SAASC,uBAAuB,IAAIC,KAAK,QAAQ,6BAA6B;AAC9E,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,iBAAiB,QAAQ,sBAAsB;AACxDD,qBAAqB,CAACJ,gCAAgC,CAAC;AACvDK,iBAAiB,CAACN,oBAAoB,CAAC,CAAC,CAAC;AACzC;;AAEAI,QAAQ,CAACD,KAAK,CAAC;AACf,SAASA,KAAK;AACd,cAAc,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}