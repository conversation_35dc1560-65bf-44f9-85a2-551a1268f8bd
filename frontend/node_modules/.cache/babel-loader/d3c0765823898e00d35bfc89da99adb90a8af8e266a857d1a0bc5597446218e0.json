{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  lessons: {},\n  currentLesson: null,\n  isLoading: false,\n  error: null\n};\nexport const lessonSlice = createSlice({\n  name: 'lessons',\n  initialState,\n  reducers: {\n    clearLessons: state => {\n      state.lessons = {};\n    },\n    setLesson: (state, action) => {\n      state.lessons[action.payload.id] = action.payload;\n    },\n    setCurrentLesson: (state, action) => {\n      state.currentLesson = action.payload;\n    },\n    markLessonComplete: (state, action) => {\n      if (state.lessons[action.payload]) {\n        state.lessons[action.payload].completed = true;\n      }\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    }\n  }\n});\nexport const lessonActions = lessonSlice.actions;", "map": {"version": 3, "names": ["createSlice", "initialState", "lessons", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "error", "lessonSlice", "name", "reducers", "clear<PERSON><PERSON><PERSON>", "state", "<PERSON><PERSON><PERSON><PERSON>", "action", "payload", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markLessonComplete", "completed", "setLoading", "setError", "lessonActions", "actions"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { Lesson, LessonState } from '../types/lesson';\n\nconst initialState: LessonState = {\n  lessons: {},\n  currentLesson: null,\n  isLoading: false,\n  error: null\n};\n\nexport const lessonSlice = createSlice({\n  name: 'lessons',\n  initialState,\n  reducers: {\n    clearLessons: (state) => {\n      state.lessons = {};\n    },\n    setLesson: (state, action: PayloadAction<Lesson>) => {\n      state.lessons[action.payload.id] = action.payload;\n    },\n    setCurrentLesson: (state, action: PayloadAction<string>) => {\n      state.currentLesson = action.payload;\n    },\n    markLessonComplete: (state, action: PayloadAction<string>) => {\n      if (state.lessons[action.payload]) {\n        state.lessons[action.payload].completed = true;\n      }\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    }\n  }\n});\n\nexport const lessonActions = lessonSlice.actions; "], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAG7D,MAAMC,YAAyB,GAAG;EAChCC,OAAO,EAAE,CAAC,CAAC;EACXC,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGN,WAAW,CAAC;EACrCO,IAAI,EAAE,SAAS;EACfN,YAAY;EACZO,QAAQ,EAAE;IACRC,YAAY,EAAGC,KAAK,IAAK;MACvBA,KAAK,CAACR,OAAO,GAAG,CAAC,CAAC;IACpB,CAAC;IACDS,SAAS,EAAEA,CAACD,KAAK,EAAEE,MAA6B,KAAK;MACnDF,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,GAAGF,MAAM,CAACC,OAAO;IACnD,CAAC;IACDE,gBAAgB,EAAEA,CAACL,KAAK,EAAEE,MAA6B,KAAK;MAC1DF,KAAK,CAACP,aAAa,GAAGS,MAAM,CAACC,OAAO;IACtC,CAAC;IACDG,kBAAkB,EAAEA,CAACN,KAAK,EAAEE,MAA6B,KAAK;MAC5D,IAAIF,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAAC,EAAE;QACjCH,KAAK,CAACR,OAAO,CAACU,MAAM,CAACC,OAAO,CAAC,CAACI,SAAS,GAAG,IAAI;MAChD;IACF,CAAC;IACDC,UAAU,EAAEA,CAACR,KAAK,EAAEE,MAA8B,KAAK;MACrDF,KAAK,CAACN,SAAS,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACDM,QAAQ,EAAEA,CAACT,KAAK,EAAEE,MAAoC,KAAK;MACzDF,KAAK,CAACL,KAAK,GAAGO,MAAM,CAACC,OAAO;IAC9B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAMO,aAAa,GAAGd,WAAW,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}