{"ast": null, "code": "import { gsap, Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, <PERSON><PERSON><PERSON>, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ, TweenLite, TimelineLite, TimelineMax } from \"./gsap-core.js\";\nimport { CSSPlugin } from \"./CSSPlugin.js\";\nvar gsapWithCSS = gsap.registerPlugin(CSSPlugin) || gsap,\n  // to protect from tree shaking\n  TweenMaxWithCSS = gsapWithCSS.core.Tween;\nexport { gsapWithCSS as gsap, gsapWithCSS as default, CSSPlugin, TweenMaxWithCSS as TweenMax, TweenLite, TimelineMax, TimelineLite, Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, Quint, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ };", "map": {"version": 3, "names": ["gsap", "Power0", "Power1", "Power2", "Power3", "Power4", "Linear", "Quad", "Cubic", "Quart", "<PERSON><PERSON><PERSON>", "Strong", "Elastic", "Back", "SteppedEase", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Expo", "Circ", "TweenLite", "TimelineLite", "TimelineMax", "CSSPlugin", "gsapWithCSS", "registerPlugin", "TweenMaxWithCSS", "core", "Tween", "default", "TweenMax"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/gsap/index.js"], "sourcesContent": ["import { gsap, Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, <PERSON><PERSON><PERSON>, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ, TweenLite, TimelineLite, TimelineMax } from \"./gsap-core.js\";\nimport { CSSPlugin } from \"./CSSPlugin.js\";\nvar gsapWithCSS = gsap.registerPlugin(CSSPlugin) || gsap,\n    // to protect from tree shaking\nTweenMaxWithCSS = gsapWithCSS.core.Tween;\nexport { gsapWithCSS as gsap, gsapWithCSS as default, CSSPlugin, TweenMaxWithCSS as TweenMax, TweenLite, TimelineMax, TimelineLite, Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, Quint, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ };"], "mappings": "AAAA,SAASA,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,QAAQ,gBAAgB;AACpN,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,IAAIC,WAAW,GAAGvB,IAAI,CAACwB,cAAc,CAACF,SAAS,CAAC,IAAItB,IAAI;EACpD;EACJyB,eAAe,GAAGF,WAAW,CAACG,IAAI,CAACC,KAAK;AACxC,SAASJ,WAAW,IAAIvB,IAAI,EAAEuB,WAAW,IAAIK,OAAO,EAAEN,SAAS,EAAEG,eAAe,IAAII,QAAQ,EAAEV,SAAS,EAAEE,WAAW,EAAED,YAAY,EAAEnB,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}