{"ast": null, "code": "/*!\n * MotionPathPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { getRawPath, cacheRawPathMeasurements, getPositionOnPath, pointsToSegment, flatPointsToSegment, sliceRawPath, stringToRawPath, rawPathToString, transformRawPath, convertToPath as _convertToPath } from \"./utils/paths.js\";\nimport { getGlobalMatrix } from \"./utils/matrix.js\";\nvar _xProps = \"x,translateX,left,marginLeft,xPercent\".split(\",\"),\n  _yProps = \"y,translateY,top,marginTop,yPercent\".split(\",\"),\n  _DEG2RAD = Math.PI / 180,\n  gsap,\n  PropTween,\n  _getUnit,\n  _toArray,\n  _getStyleSaver,\n  _reverting,\n  _getGSAP = function _getGSAP() {\n    return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n  },\n  _populateSegmentFromArray = function _populateSegmentFromArray(segment, values, property, mode) {\n    //mode: 0 = x but don't fill y yet, 1 = y, 2 = x and fill y with 0.\n    var l = values.length,\n      si = mode === 2 ? 0 : mode,\n      i = 0,\n      v;\n    for (; i < l; i++) {\n      segment[si] = v = parseFloat(values[i][property]);\n      mode === 2 && (segment[si + 1] = 0);\n      si += 2;\n    }\n    return segment;\n  },\n  _getPropNum = function _getPropNum(target, prop, unit) {\n    return parseFloat(target._gsap.get(target, prop, unit || \"px\")) || 0;\n  },\n  _relativize = function _relativize(segment) {\n    var x = segment[0],\n      y = segment[1],\n      i;\n    for (i = 2; i < segment.length; i += 2) {\n      x = segment[i] += x;\n      y = segment[i + 1] += y;\n    }\n  },\n  // feed in an array of quadratic bezier points like [{x: 0, y: 0}, ...] and it'll convert it to cubic bezier\n  // _quadToCubic = points => {\n  // \tlet cubic = [],\n  // \t\tl = points.length - 1,\n  // \t\ti = 1,\n  // \t\ta, b, c;\n  // \tfor (; i < l; i+=2) {\n  // \t\ta = points[i-1];\n  // \t\tb = points[i];\n  // \t\tc = points[i+1];\n  // \t\tcubic.push(a, {x: (2 * b.x + a.x) / 3, y: (2 * b.y + a.y) / 3}, {x: (2 * b.x + c.x) / 3, y: (2 * b.y + c.y) / 3});\n  // \t}\n  // \tcubic.push(points[l]);\n  // \treturn cubic;\n  // },\n  _segmentToRawPath = function _segmentToRawPath(plugin, segment, target, x, y, slicer, vars, unitX, unitY) {\n    if (vars.type === \"cubic\") {\n      segment = [segment];\n    } else {\n      vars.fromCurrent !== false && segment.unshift(_getPropNum(target, x, unitX), y ? _getPropNum(target, y, unitY) : 0);\n      vars.relative && _relativize(segment);\n      var pointFunc = y ? pointsToSegment : flatPointsToSegment;\n      segment = [pointFunc(segment, vars.curviness)];\n    }\n    segment = slicer(_align(segment, target, vars));\n    _addDimensionalPropTween(plugin, target, x, segment, \"x\", unitX);\n    y && _addDimensionalPropTween(plugin, target, y, segment, \"y\", unitY);\n    return cacheRawPathMeasurements(segment, vars.resolution || (vars.curviness === 0 ? 20 : 12)); //when curviness is 0, it creates control points right on top of the anchors which makes it more sensitive to resolution, thus we change the default accordingly.\n  },\n  _emptyFunc = function _emptyFunc(v) {\n    return v;\n  },\n  _numExp = /[-+\\.]*\\d+\\.?(?:e-|e\\+)?\\d*/g,\n  _originToPoint = function _originToPoint(element, origin, parentMatrix) {\n    // origin is an array of normalized values (0-1) in relation to the width/height, so [0.5, 0.5] would be the center. It can also be \"auto\" in which case it will be the top left unless it's a <path>, when it will start at the beginning of the path itself.\n    var m = getGlobalMatrix(element),\n      x = 0,\n      y = 0,\n      svg;\n    if ((element.tagName + \"\").toLowerCase() === \"svg\") {\n      svg = element.viewBox.baseVal;\n      svg.width || (svg = {\n        width: +element.getAttribute(\"width\"),\n        height: +element.getAttribute(\"height\")\n      });\n    } else {\n      svg = origin && element.getBBox && element.getBBox();\n    }\n    if (origin && origin !== \"auto\") {\n      x = origin.push ? origin[0] * (svg ? svg.width : element.offsetWidth || 0) : origin.x;\n      y = origin.push ? origin[1] * (svg ? svg.height : element.offsetHeight || 0) : origin.y;\n    }\n    return parentMatrix.apply(x || y ? m.apply({\n      x: x,\n      y: y\n    }) : {\n      x: m.e,\n      y: m.f\n    });\n  },\n  _getAlignMatrix = function _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin) {\n    var parentMatrix = getGlobalMatrix(fromElement.parentNode, true, true),\n      m = parentMatrix.clone().multiply(getGlobalMatrix(toElement)),\n      fromPoint = _originToPoint(fromElement, fromOrigin, parentMatrix),\n      _originToPoint2 = _originToPoint(toElement, toOrigin, parentMatrix),\n      x = _originToPoint2.x,\n      y = _originToPoint2.y,\n      p;\n    m.e = m.f = 0;\n    if (toOrigin === \"auto\" && toElement.getTotalLength && toElement.tagName.toLowerCase() === \"path\") {\n      p = toElement.getAttribute(\"d\").match(_numExp) || [];\n      p = m.apply({\n        x: +p[0],\n        y: +p[1]\n      });\n      x += p.x;\n      y += p.y;\n    } //if (p || (toElement.getBBox && fromElement.getBBox && toElement.ownerSVGElement === fromElement.ownerSVGElement)) {\n\n    if (p) {\n      p = m.apply(toElement.getBBox());\n      x -= p.x;\n      y -= p.y;\n    }\n    m.e = x - fromPoint.x;\n    m.f = y - fromPoint.y;\n    return m;\n  },\n  _align = function _align(rawPath, target, _ref) {\n    var align = _ref.align,\n      matrix = _ref.matrix,\n      offsetX = _ref.offsetX,\n      offsetY = _ref.offsetY,\n      alignOrigin = _ref.alignOrigin;\n    var x = rawPath[0][0],\n      y = rawPath[0][1],\n      curX = _getPropNum(target, \"x\"),\n      curY = _getPropNum(target, \"y\"),\n      alignTarget,\n      m,\n      p;\n    if (!rawPath || !rawPath.length) {\n      return getRawPath(\"M0,0L0,0\");\n    }\n    if (align) {\n      if (align === \"self\" || (alignTarget = _toArray(align)[0] || target) === target) {\n        transformRawPath(rawPath, 1, 0, 0, 1, curX - x, curY - y);\n      } else {\n        if (alignOrigin && alignOrigin[2] !== false) {\n          gsap.set(target, {\n            transformOrigin: alignOrigin[0] * 100 + \"% \" + alignOrigin[1] * 100 + \"%\"\n          });\n        } else {\n          alignOrigin = [_getPropNum(target, \"xPercent\") / -100, _getPropNum(target, \"yPercent\") / -100];\n        }\n        m = _getAlignMatrix(target, alignTarget, alignOrigin, \"auto\");\n        p = m.apply({\n          x: x,\n          y: y\n        });\n        transformRawPath(rawPath, m.a, m.b, m.c, m.d, curX + m.e - (p.x - m.e), curY + m.f - (p.y - m.f));\n      }\n    }\n    if (matrix) {\n      transformRawPath(rawPath, matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f);\n    } else if (offsetX || offsetY) {\n      transformRawPath(rawPath, 1, 0, 0, 1, offsetX || 0, offsetY || 0);\n    }\n    return rawPath;\n  },\n  _addDimensionalPropTween = function _addDimensionalPropTween(plugin, target, property, rawPath, pathProperty, forceUnit) {\n    var cache = target._gsap,\n      harness = cache.harness,\n      alias = harness && harness.aliases && harness.aliases[property],\n      prop = alias && alias.indexOf(\",\") < 0 ? alias : property,\n      pt = plugin._pt = new PropTween(plugin._pt, target, prop, 0, 0, _emptyFunc, 0, cache.set(target, prop, plugin));\n    pt.u = _getUnit(cache.get(target, prop, forceUnit)) || 0;\n    pt.path = rawPath;\n    pt.pp = pathProperty;\n    plugin._props.push(prop);\n  },\n  _sliceModifier = function _sliceModifier(start, end) {\n    return function (rawPath) {\n      return start || end !== 1 ? sliceRawPath(rawPath, start, end) : rawPath;\n    };\n  };\nexport var MotionPathPlugin = {\n  version: \"3.13.0\",\n  name: \"motionPath\",\n  register: function register(core, Plugin, propTween) {\n    gsap = core;\n    _getUnit = gsap.utils.getUnit;\n    _toArray = gsap.utils.toArray;\n    _getStyleSaver = gsap.core.getStyleSaver;\n    _reverting = gsap.core.reverting || function () {};\n    PropTween = propTween;\n  },\n  init: function init(target, vars, tween) {\n    if (!gsap) {\n      console.warn(\"Please gsap.registerPlugin(MotionPathPlugin)\");\n      return false;\n    }\n    if (!(typeof vars === \"object\" && !vars.style) || !vars.path) {\n      vars = {\n        path: vars\n      };\n    }\n    var rawPaths = [],\n      _vars = vars,\n      path = _vars.path,\n      autoRotate = _vars.autoRotate,\n      unitX = _vars.unitX,\n      unitY = _vars.unitY,\n      x = _vars.x,\n      y = _vars.y,\n      firstObj = path[0],\n      slicer = _sliceModifier(vars.start, \"end\" in vars ? vars.end : 1),\n      rawPath,\n      p;\n    this.rawPaths = rawPaths;\n    this.target = target;\n    this.tween = tween;\n    this.styles = _getStyleSaver && _getStyleSaver(target, \"transform\");\n    if (this.rotate = autoRotate || autoRotate === 0) {\n      //get the rotational data FIRST so that the setTransform() method is called in the correct order in the render() loop - rotation gets set last.\n      this.rOffset = parseFloat(autoRotate) || 0;\n      this.radians = !!vars.useRadians;\n      this.rProp = vars.rotation || \"rotation\"; // rotation property\n\n      this.rSet = target._gsap.set(target, this.rProp, this); // rotation setter\n\n      this.ru = _getUnit(target._gsap.get(target, this.rProp)) || 0; // rotation units\n    }\n    if (Array.isArray(path) && !(\"closed\" in path) && typeof firstObj !== \"number\") {\n      for (p in firstObj) {\n        if (!x && ~_xProps.indexOf(p)) {\n          x = p;\n        } else if (!y && ~_yProps.indexOf(p)) {\n          y = p;\n        }\n      }\n      if (x && y) {\n        //correlated values\n        rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray(_populateSegmentFromArray([], path, x, 0), path, y, 1), target, x, y, slicer, vars, unitX || _getUnit(path[0][x]), unitY || _getUnit(path[0][y])));\n      } else {\n        x = y = 0;\n      }\n      for (p in firstObj) {\n        p !== x && p !== y && rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray([], path, p, 2), target, p, 0, slicer, vars, _getUnit(path[0][p])));\n      }\n    } else {\n      rawPath = slicer(_align(getRawPath(vars.path), target, vars));\n      cacheRawPathMeasurements(rawPath, vars.resolution);\n      rawPaths.push(rawPath);\n      _addDimensionalPropTween(this, target, vars.x || \"x\", rawPath, \"x\", vars.unitX || \"px\");\n      _addDimensionalPropTween(this, target, vars.y || \"y\", rawPath, \"y\", vars.unitY || \"px\");\n    }\n    tween.vars.immediateRender && this.render(tween.progress(), this);\n  },\n  render: function render(ratio, data) {\n    var rawPaths = data.rawPaths,\n      i = rawPaths.length,\n      pt = data._pt;\n    if (data.tween._time || !_reverting()) {\n      if (ratio > 1) {\n        ratio = 1;\n      } else if (ratio < 0) {\n        ratio = 0;\n      }\n      while (i--) {\n        getPositionOnPath(rawPaths[i], ratio, !i && data.rotate, rawPaths[i]);\n      }\n      while (pt) {\n        pt.set(pt.t, pt.p, pt.path[pt.pp] + pt.u, pt.d, ratio);\n        pt = pt._next;\n      }\n      data.rotate && data.rSet(data.target, data.rProp, rawPaths[0].angle * (data.radians ? _DEG2RAD : 1) + data.rOffset + data.ru, data, ratio);\n    } else {\n      data.styles.revert();\n    }\n  },\n  getLength: function getLength(path) {\n    return cacheRawPathMeasurements(getRawPath(path)).totalLength;\n  },\n  sliceRawPath: sliceRawPath,\n  getRawPath: getRawPath,\n  pointsToSegment: pointsToSegment,\n  stringToRawPath: stringToRawPath,\n  rawPathToString: rawPathToString,\n  transformRawPath: transformRawPath,\n  getGlobalMatrix: getGlobalMatrix,\n  getPositionOnPath: getPositionOnPath,\n  cacheRawPathMeasurements: cacheRawPathMeasurements,\n  convertToPath: function convertToPath(targets, swap) {\n    return _toArray(targets).map(function (target) {\n      return _convertToPath(target, swap !== false);\n    });\n  },\n  convertCoordinates: function convertCoordinates(fromElement, toElement, point) {\n    var m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n    return point ? m.apply(point) : m;\n  },\n  getAlignMatrix: _getAlignMatrix,\n  getRelativePosition: function getRelativePosition(fromElement, toElement, fromOrigin, toOrigin) {\n    var m = _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin);\n    return {\n      x: m.e,\n      y: m.f\n    };\n  },\n  arrayToRawPath: function arrayToRawPath(value, vars) {\n    vars = vars || {};\n    var segment = _populateSegmentFromArray(_populateSegmentFromArray([], value, vars.x || \"x\", 0), value, vars.y || \"y\", 1);\n    vars.relative && _relativize(segment);\n    return [vars.type === \"cubic\" ? segment : pointsToSegment(segment, vars.curviness)];\n  }\n};\n_getGSAP() && gsap.registerPlugin(MotionPathPlugin);\nexport { MotionPathPlugin as default };", "map": {"version": 3, "names": ["getRawPath", "cacheRawPathMeasurements", "getPositionOnPath", "pointsToSegment", "flatPointsToSegment", "sliceRawPath", "stringToRawPath", "rawPathToString", "transformRawPath", "convertToPath", "_convertToPath", "getGlobalMatrix", "_xProps", "split", "_yProps", "_DEG2RAD", "Math", "PI", "gsap", "PropTween", "_getUnit", "_toArray", "_getStyleSaver", "_reverting", "_getGSAP", "window", "registerPlugin", "_populateSegmentFromArray", "segment", "values", "property", "mode", "l", "length", "si", "i", "v", "parseFloat", "_getPropNum", "target", "prop", "unit", "_gsap", "get", "_relativize", "x", "y", "_segmentToRawPath", "plugin", "slicer", "vars", "unitX", "unitY", "type", "fromCurrent", "unshift", "relative", "pointFunc", "curviness", "_align", "_addDimensionalPropTween", "resolution", "_emptyFunc", "_numExp", "_originToPoint", "element", "origin", "parentMatrix", "m", "svg", "tagName", "toLowerCase", "viewBox", "baseVal", "width", "getAttribute", "height", "getBBox", "push", "offsetWidth", "offsetHeight", "apply", "e", "f", "_getAlignMatrix", "fromElement", "toElement", "fromOrigin", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "clone", "multiply", "fromPoint", "_originToPoint2", "p", "getTotalLength", "match", "rawPath", "_ref", "align", "matrix", "offsetX", "offsetY", "align<PERSON><PERSON>in", "curX", "curY", "alignTarget", "set", "transform<PERSON><PERSON>in", "a", "b", "c", "d", "pathProperty", "forceUnit", "cache", "harness", "alias", "aliases", "indexOf", "pt", "_pt", "u", "path", "pp", "_props", "_sliceModifier", "start", "end", "MotionPathPlugin", "version", "name", "register", "core", "Plugin", "propTween", "utils", "getUnit", "toArray", "getStyleSaver", "reverting", "init", "tween", "console", "warn", "style", "rawPaths", "_vars", "autoRotate", "firstObj", "styles", "rotate", "rOffset", "radians", "useRadians", "rProp", "rotation", "rSet", "ru", "Array", "isArray", "immediateRender", "render", "progress", "ratio", "data", "_time", "t", "_next", "angle", "revert", "<PERSON><PERSON><PERSON><PERSON>", "totalLength", "targets", "swap", "map", "convertCoordinates", "point", "getAlignMatrix", "getRelativePosition", "arrayToRawPath", "value", "default"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/gsap/MotionPathPlugin.js"], "sourcesContent": ["/*!\n * MotionPathPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { getRawPath, cacheRawPathMeasurements, getPositionOnPath, pointsToSegment, flatPointsToSegment, sliceRawPath, stringToRawPath, rawPathToString, transformRawPath, convertToPath as _convertToPath } from \"./utils/paths.js\";\nimport { getGlobalMatrix } from \"./utils/matrix.js\";\n\nvar _xProps = \"x,translateX,left,marginLeft,xPercent\".split(\",\"),\n    _yProps = \"y,translateY,top,marginTop,yPercent\".split(\",\"),\n    _DEG2RAD = Math.PI / 180,\n    gsap,\n    PropTween,\n    _getUnit,\n    _toArray,\n    _getStyleSaver,\n    _reverting,\n    _getGSAP = function _getGSAP() {\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _populateSegmentFromArray = function _populateSegmentFromArray(segment, values, property, mode) {\n  //mode: 0 = x but don't fill y yet, 1 = y, 2 = x and fill y with 0.\n  var l = values.length,\n      si = mode === 2 ? 0 : mode,\n      i = 0,\n      v;\n\n  for (; i < l; i++) {\n    segment[si] = v = parseFloat(values[i][property]);\n    mode === 2 && (segment[si + 1] = 0);\n    si += 2;\n  }\n\n  return segment;\n},\n    _getPropNum = function _getPropNum(target, prop, unit) {\n  return parseFloat(target._gsap.get(target, prop, unit || \"px\")) || 0;\n},\n    _relativize = function _relativize(segment) {\n  var x = segment[0],\n      y = segment[1],\n      i;\n\n  for (i = 2; i < segment.length; i += 2) {\n    x = segment[i] += x;\n    y = segment[i + 1] += y;\n  }\n},\n    // feed in an array of quadratic bezier points like [{x: 0, y: 0}, ...] and it'll convert it to cubic bezier\n// _quadToCubic = points => {\n// \tlet cubic = [],\n// \t\tl = points.length - 1,\n// \t\ti = 1,\n// \t\ta, b, c;\n// \tfor (; i < l; i+=2) {\n// \t\ta = points[i-1];\n// \t\tb = points[i];\n// \t\tc = points[i+1];\n// \t\tcubic.push(a, {x: (2 * b.x + a.x) / 3, y: (2 * b.y + a.y) / 3}, {x: (2 * b.x + c.x) / 3, y: (2 * b.y + c.y) / 3});\n// \t}\n// \tcubic.push(points[l]);\n// \treturn cubic;\n// },\n_segmentToRawPath = function _segmentToRawPath(plugin, segment, target, x, y, slicer, vars, unitX, unitY) {\n  if (vars.type === \"cubic\") {\n    segment = [segment];\n  } else {\n    vars.fromCurrent !== false && segment.unshift(_getPropNum(target, x, unitX), y ? _getPropNum(target, y, unitY) : 0);\n    vars.relative && _relativize(segment);\n    var pointFunc = y ? pointsToSegment : flatPointsToSegment;\n    segment = [pointFunc(segment, vars.curviness)];\n  }\n\n  segment = slicer(_align(segment, target, vars));\n\n  _addDimensionalPropTween(plugin, target, x, segment, \"x\", unitX);\n\n  y && _addDimensionalPropTween(plugin, target, y, segment, \"y\", unitY);\n  return cacheRawPathMeasurements(segment, vars.resolution || (vars.curviness === 0 ? 20 : 12)); //when curviness is 0, it creates control points right on top of the anchors which makes it more sensitive to resolution, thus we change the default accordingly.\n},\n    _emptyFunc = function _emptyFunc(v) {\n  return v;\n},\n    _numExp = /[-+\\.]*\\d+\\.?(?:e-|e\\+)?\\d*/g,\n    _originToPoint = function _originToPoint(element, origin, parentMatrix) {\n  // origin is an array of normalized values (0-1) in relation to the width/height, so [0.5, 0.5] would be the center. It can also be \"auto\" in which case it will be the top left unless it's a <path>, when it will start at the beginning of the path itself.\n  var m = getGlobalMatrix(element),\n      x = 0,\n      y = 0,\n      svg;\n\n  if ((element.tagName + \"\").toLowerCase() === \"svg\") {\n    svg = element.viewBox.baseVal;\n    svg.width || (svg = {\n      width: +element.getAttribute(\"width\"),\n      height: +element.getAttribute(\"height\")\n    });\n  } else {\n    svg = origin && element.getBBox && element.getBBox();\n  }\n\n  if (origin && origin !== \"auto\") {\n    x = origin.push ? origin[0] * (svg ? svg.width : element.offsetWidth || 0) : origin.x;\n    y = origin.push ? origin[1] * (svg ? svg.height : element.offsetHeight || 0) : origin.y;\n  }\n\n  return parentMatrix.apply(x || y ? m.apply({\n    x: x,\n    y: y\n  }) : {\n    x: m.e,\n    y: m.f\n  });\n},\n    _getAlignMatrix = function _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin) {\n  var parentMatrix = getGlobalMatrix(fromElement.parentNode, true, true),\n      m = parentMatrix.clone().multiply(getGlobalMatrix(toElement)),\n      fromPoint = _originToPoint(fromElement, fromOrigin, parentMatrix),\n      _originToPoint2 = _originToPoint(toElement, toOrigin, parentMatrix),\n      x = _originToPoint2.x,\n      y = _originToPoint2.y,\n      p;\n\n  m.e = m.f = 0;\n\n  if (toOrigin === \"auto\" && toElement.getTotalLength && toElement.tagName.toLowerCase() === \"path\") {\n    p = toElement.getAttribute(\"d\").match(_numExp) || [];\n    p = m.apply({\n      x: +p[0],\n      y: +p[1]\n    });\n    x += p.x;\n    y += p.y;\n  } //if (p || (toElement.getBBox && fromElement.getBBox && toElement.ownerSVGElement === fromElement.ownerSVGElement)) {\n\n\n  if (p) {\n    p = m.apply(toElement.getBBox());\n    x -= p.x;\n    y -= p.y;\n  }\n\n  m.e = x - fromPoint.x;\n  m.f = y - fromPoint.y;\n  return m;\n},\n    _align = function _align(rawPath, target, _ref) {\n  var align = _ref.align,\n      matrix = _ref.matrix,\n      offsetX = _ref.offsetX,\n      offsetY = _ref.offsetY,\n      alignOrigin = _ref.alignOrigin;\n\n  var x = rawPath[0][0],\n      y = rawPath[0][1],\n      curX = _getPropNum(target, \"x\"),\n      curY = _getPropNum(target, \"y\"),\n      alignTarget,\n      m,\n      p;\n\n  if (!rawPath || !rawPath.length) {\n    return getRawPath(\"M0,0L0,0\");\n  }\n\n  if (align) {\n    if (align === \"self\" || (alignTarget = _toArray(align)[0] || target) === target) {\n      transformRawPath(rawPath, 1, 0, 0, 1, curX - x, curY - y);\n    } else {\n      if (alignOrigin && alignOrigin[2] !== false) {\n        gsap.set(target, {\n          transformOrigin: alignOrigin[0] * 100 + \"% \" + alignOrigin[1] * 100 + \"%\"\n        });\n      } else {\n        alignOrigin = [_getPropNum(target, \"xPercent\") / -100, _getPropNum(target, \"yPercent\") / -100];\n      }\n\n      m = _getAlignMatrix(target, alignTarget, alignOrigin, \"auto\");\n      p = m.apply({\n        x: x,\n        y: y\n      });\n      transformRawPath(rawPath, m.a, m.b, m.c, m.d, curX + m.e - (p.x - m.e), curY + m.f - (p.y - m.f));\n    }\n  }\n\n  if (matrix) {\n    transformRawPath(rawPath, matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f);\n  } else if (offsetX || offsetY) {\n    transformRawPath(rawPath, 1, 0, 0, 1, offsetX || 0, offsetY || 0);\n  }\n\n  return rawPath;\n},\n    _addDimensionalPropTween = function _addDimensionalPropTween(plugin, target, property, rawPath, pathProperty, forceUnit) {\n  var cache = target._gsap,\n      harness = cache.harness,\n      alias = harness && harness.aliases && harness.aliases[property],\n      prop = alias && alias.indexOf(\",\") < 0 ? alias : property,\n      pt = plugin._pt = new PropTween(plugin._pt, target, prop, 0, 0, _emptyFunc, 0, cache.set(target, prop, plugin));\n  pt.u = _getUnit(cache.get(target, prop, forceUnit)) || 0;\n  pt.path = rawPath;\n  pt.pp = pathProperty;\n\n  plugin._props.push(prop);\n},\n    _sliceModifier = function _sliceModifier(start, end) {\n  return function (rawPath) {\n    return start || end !== 1 ? sliceRawPath(rawPath, start, end) : rawPath;\n  };\n};\n\nexport var MotionPathPlugin = {\n  version: \"3.13.0\",\n  name: \"motionPath\",\n  register: function register(core, Plugin, propTween) {\n    gsap = core;\n    _getUnit = gsap.utils.getUnit;\n    _toArray = gsap.utils.toArray;\n    _getStyleSaver = gsap.core.getStyleSaver;\n\n    _reverting = gsap.core.reverting || function () {};\n\n    PropTween = propTween;\n  },\n  init: function init(target, vars, tween) {\n    if (!gsap) {\n      console.warn(\"Please gsap.registerPlugin(MotionPathPlugin)\");\n      return false;\n    }\n\n    if (!(typeof vars === \"object\" && !vars.style) || !vars.path) {\n      vars = {\n        path: vars\n      };\n    }\n\n    var rawPaths = [],\n        _vars = vars,\n        path = _vars.path,\n        autoRotate = _vars.autoRotate,\n        unitX = _vars.unitX,\n        unitY = _vars.unitY,\n        x = _vars.x,\n        y = _vars.y,\n        firstObj = path[0],\n        slicer = _sliceModifier(vars.start, \"end\" in vars ? vars.end : 1),\n        rawPath,\n        p;\n\n    this.rawPaths = rawPaths;\n    this.target = target;\n    this.tween = tween;\n    this.styles = _getStyleSaver && _getStyleSaver(target, \"transform\");\n\n    if (this.rotate = autoRotate || autoRotate === 0) {\n      //get the rotational data FIRST so that the setTransform() method is called in the correct order in the render() loop - rotation gets set last.\n      this.rOffset = parseFloat(autoRotate) || 0;\n      this.radians = !!vars.useRadians;\n      this.rProp = vars.rotation || \"rotation\"; // rotation property\n\n      this.rSet = target._gsap.set(target, this.rProp, this); // rotation setter\n\n      this.ru = _getUnit(target._gsap.get(target, this.rProp)) || 0; // rotation units\n    }\n\n    if (Array.isArray(path) && !(\"closed\" in path) && typeof firstObj !== \"number\") {\n      for (p in firstObj) {\n        if (!x && ~_xProps.indexOf(p)) {\n          x = p;\n        } else if (!y && ~_yProps.indexOf(p)) {\n          y = p;\n        }\n      }\n\n      if (x && y) {\n        //correlated values\n        rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray(_populateSegmentFromArray([], path, x, 0), path, y, 1), target, x, y, slicer, vars, unitX || _getUnit(path[0][x]), unitY || _getUnit(path[0][y])));\n      } else {\n        x = y = 0;\n      }\n\n      for (p in firstObj) {\n        p !== x && p !== y && rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray([], path, p, 2), target, p, 0, slicer, vars, _getUnit(path[0][p])));\n      }\n    } else {\n      rawPath = slicer(_align(getRawPath(vars.path), target, vars));\n      cacheRawPathMeasurements(rawPath, vars.resolution);\n      rawPaths.push(rawPath);\n\n      _addDimensionalPropTween(this, target, vars.x || \"x\", rawPath, \"x\", vars.unitX || \"px\");\n\n      _addDimensionalPropTween(this, target, vars.y || \"y\", rawPath, \"y\", vars.unitY || \"px\");\n    }\n\n    tween.vars.immediateRender && this.render(tween.progress(), this);\n  },\n  render: function render(ratio, data) {\n    var rawPaths = data.rawPaths,\n        i = rawPaths.length,\n        pt = data._pt;\n\n    if (data.tween._time || !_reverting()) {\n      if (ratio > 1) {\n        ratio = 1;\n      } else if (ratio < 0) {\n        ratio = 0;\n      }\n\n      while (i--) {\n        getPositionOnPath(rawPaths[i], ratio, !i && data.rotate, rawPaths[i]);\n      }\n\n      while (pt) {\n        pt.set(pt.t, pt.p, pt.path[pt.pp] + pt.u, pt.d, ratio);\n        pt = pt._next;\n      }\n\n      data.rotate && data.rSet(data.target, data.rProp, rawPaths[0].angle * (data.radians ? _DEG2RAD : 1) + data.rOffset + data.ru, data, ratio);\n    } else {\n      data.styles.revert();\n    }\n  },\n  getLength: function getLength(path) {\n    return cacheRawPathMeasurements(getRawPath(path)).totalLength;\n  },\n  sliceRawPath: sliceRawPath,\n  getRawPath: getRawPath,\n  pointsToSegment: pointsToSegment,\n  stringToRawPath: stringToRawPath,\n  rawPathToString: rawPathToString,\n  transformRawPath: transformRawPath,\n  getGlobalMatrix: getGlobalMatrix,\n  getPositionOnPath: getPositionOnPath,\n  cacheRawPathMeasurements: cacheRawPathMeasurements,\n  convertToPath: function convertToPath(targets, swap) {\n    return _toArray(targets).map(function (target) {\n      return _convertToPath(target, swap !== false);\n    });\n  },\n  convertCoordinates: function convertCoordinates(fromElement, toElement, point) {\n    var m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n    return point ? m.apply(point) : m;\n  },\n  getAlignMatrix: _getAlignMatrix,\n  getRelativePosition: function getRelativePosition(fromElement, toElement, fromOrigin, toOrigin) {\n    var m = _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin);\n\n    return {\n      x: m.e,\n      y: m.f\n    };\n  },\n  arrayToRawPath: function arrayToRawPath(value, vars) {\n    vars = vars || {};\n\n    var segment = _populateSegmentFromArray(_populateSegmentFromArray([], value, vars.x || \"x\", 0), value, vars.y || \"y\", 1);\n\n    vars.relative && _relativize(segment);\n    return [vars.type === \"cubic\" ? segment : pointsToSegment(segment, vars.curviness)];\n  }\n};\n_getGSAP() && gsap.registerPlugin(MotionPathPlugin);\nexport { MotionPathPlugin as default };"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,UAAU,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,IAAIC,cAAc,QAAQ,kBAAkB;AACnO,SAASC,eAAe,QAAQ,mBAAmB;AAEnD,IAAIC,OAAO,GAAG,uCAAuC,CAACC,KAAK,CAAC,GAAG,CAAC;EAC5DC,OAAO,GAAG,qCAAqC,CAACD,KAAK,CAAC,GAAG,CAAC;EAC1DE,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;EACxBC,IAAI;EACJC,SAAS;EACTC,QAAQ;EACRC,QAAQ;EACRC,cAAc;EACdC,UAAU;EACVC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjC,OAAON,IAAI,IAAI,OAAOO,MAAM,KAAK,WAAW,KAAKP,IAAI,GAAGO,MAAM,CAACP,IAAI,CAAC,IAAIA,IAAI,CAACQ,cAAc,IAAIR,IAAI;EACrG,CAAC;EACGS,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE;IAClG;IACA,IAAIC,CAAC,GAAGH,MAAM,CAACI,MAAM;MACjBC,EAAE,GAAGH,IAAI,KAAK,CAAC,GAAG,CAAC,GAAGA,IAAI;MAC1BI,CAAC,GAAG,CAAC;MACLC,CAAC;IAEL,OAAOD,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACjBP,OAAO,CAACM,EAAE,CAAC,GAAGE,CAAC,GAAGC,UAAU,CAACR,MAAM,CAACM,CAAC,CAAC,CAACL,QAAQ,CAAC,CAAC;MACjDC,IAAI,KAAK,CAAC,KAAKH,OAAO,CAACM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnCA,EAAE,IAAI,CAAC;IACT;IAEA,OAAON,OAAO;EAChB,CAAC;EACGU,WAAW,GAAG,SAASA,WAAWA,CAACC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACzD,OAAOJ,UAAU,CAACE,MAAM,CAACG,KAAK,CAACC,GAAG,CAACJ,MAAM,EAAEC,IAAI,EAAEC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC;EACtE,CAAC;EACGG,WAAW,GAAG,SAASA,WAAWA,CAAChB,OAAO,EAAE;IAC9C,IAAIiB,CAAC,GAAGjB,OAAO,CAAC,CAAC,CAAC;MACdkB,CAAC,GAAGlB,OAAO,CAAC,CAAC,CAAC;MACdO,CAAC;IAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACK,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MACtCU,CAAC,GAAGjB,OAAO,CAACO,CAAC,CAAC,IAAIU,CAAC;MACnBC,CAAC,GAAGlB,OAAO,CAACO,CAAC,GAAG,CAAC,CAAC,IAAIW,CAAC;IACzB;EACF,CAAC;EACG;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,MAAM,EAAEpB,OAAO,EAAEW,MAAM,EAAEM,CAAC,EAAEC,CAAC,EAAEG,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACxG,IAAIF,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MACzBzB,OAAO,GAAG,CAACA,OAAO,CAAC;IACrB,CAAC,MAAM;MACLsB,IAAI,CAACI,WAAW,KAAK,KAAK,IAAI1B,OAAO,CAAC2B,OAAO,CAACjB,WAAW,CAACC,MAAM,EAAEM,CAAC,EAAEM,KAAK,CAAC,EAAEL,CAAC,GAAGR,WAAW,CAACC,MAAM,EAAEO,CAAC,EAAEM,KAAK,CAAC,GAAG,CAAC,CAAC;MACnHF,IAAI,CAACM,QAAQ,IAAIZ,WAAW,CAAChB,OAAO,CAAC;MACrC,IAAI6B,SAAS,GAAGX,CAAC,GAAG3C,eAAe,GAAGC,mBAAmB;MACzDwB,OAAO,GAAG,CAAC6B,SAAS,CAAC7B,OAAO,EAAEsB,IAAI,CAACQ,SAAS,CAAC,CAAC;IAChD;IAEA9B,OAAO,GAAGqB,MAAM,CAACU,MAAM,CAAC/B,OAAO,EAAEW,MAAM,EAAEW,IAAI,CAAC,CAAC;IAE/CU,wBAAwB,CAACZ,MAAM,EAAET,MAAM,EAAEM,CAAC,EAAEjB,OAAO,EAAE,GAAG,EAAEuB,KAAK,CAAC;IAEhEL,CAAC,IAAIc,wBAAwB,CAACZ,MAAM,EAAET,MAAM,EAAEO,CAAC,EAAElB,OAAO,EAAE,GAAG,EAAEwB,KAAK,CAAC;IACrE,OAAOnD,wBAAwB,CAAC2B,OAAO,EAAEsB,IAAI,CAACW,UAAU,KAAKX,IAAI,CAACQ,SAAS,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EACjG,CAAC;EACGI,UAAU,GAAG,SAASA,UAAUA,CAAC1B,CAAC,EAAE;IACtC,OAAOA,CAAC;EACV,CAAC;EACG2B,OAAO,GAAG,8BAA8B;EACxCC,cAAc,GAAG,SAASA,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAE;IAC1E;IACA,IAAIC,CAAC,GAAGzD,eAAe,CAACsD,OAAO,CAAC;MAC5BpB,CAAC,GAAG,CAAC;MACLC,CAAC,GAAG,CAAC;MACLuB,GAAG;IAEP,IAAI,CAACJ,OAAO,CAACK,OAAO,GAAG,EAAE,EAAEC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;MAClDF,GAAG,GAAGJ,OAAO,CAACO,OAAO,CAACC,OAAO;MAC7BJ,GAAG,CAACK,KAAK,KAAKL,GAAG,GAAG;QAClBK,KAAK,EAAE,CAACT,OAAO,CAACU,YAAY,CAAC,OAAO,CAAC;QACrCC,MAAM,EAAE,CAACX,OAAO,CAACU,YAAY,CAAC,QAAQ;MACxC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLN,GAAG,GAAGH,MAAM,IAAID,OAAO,CAACY,OAAO,IAAIZ,OAAO,CAACY,OAAO,CAAC,CAAC;IACtD;IAEA,IAAIX,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE;MAC/BrB,CAAC,GAAGqB,MAAM,CAACY,IAAI,GAAGZ,MAAM,CAAC,CAAC,CAAC,IAAIG,GAAG,GAAGA,GAAG,CAACK,KAAK,GAAGT,OAAO,CAACc,WAAW,IAAI,CAAC,CAAC,GAAGb,MAAM,CAACrB,CAAC;MACrFC,CAAC,GAAGoB,MAAM,CAACY,IAAI,GAAGZ,MAAM,CAAC,CAAC,CAAC,IAAIG,GAAG,GAAGA,GAAG,CAACO,MAAM,GAAGX,OAAO,CAACe,YAAY,IAAI,CAAC,CAAC,GAAGd,MAAM,CAACpB,CAAC;IACzF;IAEA,OAAOqB,YAAY,CAACc,KAAK,CAACpC,CAAC,IAAIC,CAAC,GAAGsB,CAAC,CAACa,KAAK,CAAC;MACzCpC,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA;IACL,CAAC,CAAC,GAAG;MACHD,CAAC,EAAEuB,CAAC,CAACc,CAAC;MACNpC,CAAC,EAAEsB,CAAC,CAACe;IACP,CAAC,CAAC;EACJ,CAAC;EACGC,eAAe,GAAG,SAASA,eAAeA,CAACC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC3F,IAAIrB,YAAY,GAAGxD,eAAe,CAAC0E,WAAW,CAACI,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;MAClErB,CAAC,GAAGD,YAAY,CAACuB,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAChF,eAAe,CAAC2E,SAAS,CAAC,CAAC;MAC7DM,SAAS,GAAG5B,cAAc,CAACqB,WAAW,EAAEE,UAAU,EAAEpB,YAAY,CAAC;MACjE0B,eAAe,GAAG7B,cAAc,CAACsB,SAAS,EAAEE,QAAQ,EAAErB,YAAY,CAAC;MACnEtB,CAAC,GAAGgD,eAAe,CAAChD,CAAC;MACrBC,CAAC,GAAG+C,eAAe,CAAC/C,CAAC;MACrBgD,CAAC;IAEL1B,CAAC,CAACc,CAAC,GAAGd,CAAC,CAACe,CAAC,GAAG,CAAC;IAEb,IAAIK,QAAQ,KAAK,MAAM,IAAIF,SAAS,CAACS,cAAc,IAAIT,SAAS,CAAChB,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,MAAM,EAAE;MACjGuB,CAAC,GAAGR,SAAS,CAACX,YAAY,CAAC,GAAG,CAAC,CAACqB,KAAK,CAACjC,OAAO,CAAC,IAAI,EAAE;MACpD+B,CAAC,GAAG1B,CAAC,CAACa,KAAK,CAAC;QACVpC,CAAC,EAAE,CAACiD,CAAC,CAAC,CAAC,CAAC;QACRhD,CAAC,EAAE,CAACgD,CAAC,CAAC,CAAC;MACT,CAAC,CAAC;MACFjD,CAAC,IAAIiD,CAAC,CAACjD,CAAC;MACRC,CAAC,IAAIgD,CAAC,CAAChD,CAAC;IACV,CAAC,CAAC;;IAGF,IAAIgD,CAAC,EAAE;MACLA,CAAC,GAAG1B,CAAC,CAACa,KAAK,CAACK,SAAS,CAACT,OAAO,CAAC,CAAC,CAAC;MAChChC,CAAC,IAAIiD,CAAC,CAACjD,CAAC;MACRC,CAAC,IAAIgD,CAAC,CAAChD,CAAC;IACV;IAEAsB,CAAC,CAACc,CAAC,GAAGrC,CAAC,GAAG+C,SAAS,CAAC/C,CAAC;IACrBuB,CAAC,CAACe,CAAC,GAAGrC,CAAC,GAAG8C,SAAS,CAAC9C,CAAC;IACrB,OAAOsB,CAAC;EACV,CAAC;EACGT,MAAM,GAAG,SAASA,MAAMA,CAACsC,OAAO,EAAE1D,MAAM,EAAE2D,IAAI,EAAE;IAClD,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MAClBC,MAAM,GAAGF,IAAI,CAACE,MAAM;MACpBC,OAAO,GAAGH,IAAI,CAACG,OAAO;MACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;MACtBC,WAAW,GAAGL,IAAI,CAACK,WAAW;IAElC,IAAI1D,CAAC,GAAGoD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjBnD,CAAC,GAAGmD,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjBO,IAAI,GAAGlE,WAAW,CAACC,MAAM,EAAE,GAAG,CAAC;MAC/BkE,IAAI,GAAGnE,WAAW,CAACC,MAAM,EAAE,GAAG,CAAC;MAC/BmE,WAAW;MACXtC,CAAC;MACD0B,CAAC;IAEL,IAAI,CAACG,OAAO,IAAI,CAACA,OAAO,CAAChE,MAAM,EAAE;MAC/B,OAAOjC,UAAU,CAAC,UAAU,CAAC;IAC/B;IAEA,IAAImG,KAAK,EAAE;MACT,IAAIA,KAAK,KAAK,MAAM,IAAI,CAACO,WAAW,GAAGrF,QAAQ,CAAC8E,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI5D,MAAM,MAAMA,MAAM,EAAE;QAC/E/B,gBAAgB,CAACyF,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEO,IAAI,GAAG3D,CAAC,EAAE4D,IAAI,GAAG3D,CAAC,CAAC;MAC3D,CAAC,MAAM;QACL,IAAIyD,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;UAC3CrF,IAAI,CAACyF,GAAG,CAACpE,MAAM,EAAE;YACfqE,eAAe,EAAEL,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG;UACxE,CAAC,CAAC;QACJ,CAAC,MAAM;UACLA,WAAW,GAAG,CAACjE,WAAW,CAACC,MAAM,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,EAAED,WAAW,CAACC,MAAM,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;QAChG;QAEA6B,CAAC,GAAGgB,eAAe,CAAC7C,MAAM,EAAEmE,WAAW,EAAEH,WAAW,EAAE,MAAM,CAAC;QAC7DT,CAAC,GAAG1B,CAAC,CAACa,KAAK,CAAC;UACVpC,CAAC,EAAEA,CAAC;UACJC,CAAC,EAAEA;QACL,CAAC,CAAC;QACFtC,gBAAgB,CAACyF,OAAO,EAAE7B,CAAC,CAACyC,CAAC,EAAEzC,CAAC,CAAC0C,CAAC,EAAE1C,CAAC,CAAC2C,CAAC,EAAE3C,CAAC,CAAC4C,CAAC,EAAER,IAAI,GAAGpC,CAAC,CAACc,CAAC,IAAIY,CAAC,CAACjD,CAAC,GAAGuB,CAAC,CAACc,CAAC,CAAC,EAAEuB,IAAI,GAAGrC,CAAC,CAACe,CAAC,IAAIW,CAAC,CAAChD,CAAC,GAAGsB,CAAC,CAACe,CAAC,CAAC,CAAC;MACnG;IACF;IAEA,IAAIiB,MAAM,EAAE;MACV5F,gBAAgB,CAACyF,OAAO,EAAEG,MAAM,CAACS,CAAC,EAAET,MAAM,CAACU,CAAC,EAAEV,MAAM,CAACW,CAAC,EAAEX,MAAM,CAACY,CAAC,EAAEZ,MAAM,CAAClB,CAAC,EAAEkB,MAAM,CAACjB,CAAC,CAAC;IACvF,CAAC,MAAM,IAAIkB,OAAO,IAAIC,OAAO,EAAE;MAC7B9F,gBAAgB,CAACyF,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEI,OAAO,IAAI,CAAC,EAAEC,OAAO,IAAI,CAAC,CAAC;IACnE;IAEA,OAAOL,OAAO;EAChB,CAAC;EACGrC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACZ,MAAM,EAAET,MAAM,EAAET,QAAQ,EAAEmE,OAAO,EAAEgB,YAAY,EAAEC,SAAS,EAAE;IAC3H,IAAIC,KAAK,GAAG5E,MAAM,CAACG,KAAK;MACpB0E,OAAO,GAAGD,KAAK,CAACC,OAAO;MACvBC,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIF,OAAO,CAACE,OAAO,CAACxF,QAAQ,CAAC;MAC/DU,IAAI,GAAG6E,KAAK,IAAIA,KAAK,CAACE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGF,KAAK,GAAGvF,QAAQ;MACzD0F,EAAE,GAAGxE,MAAM,CAACyE,GAAG,GAAG,IAAItG,SAAS,CAAC6B,MAAM,CAACyE,GAAG,EAAElF,MAAM,EAAEC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAEsB,UAAU,EAAE,CAAC,EAAEqD,KAAK,CAACR,GAAG,CAACpE,MAAM,EAAEC,IAAI,EAAEQ,MAAM,CAAC,CAAC;IACnHwE,EAAE,CAACE,CAAC,GAAGtG,QAAQ,CAAC+F,KAAK,CAACxE,GAAG,CAACJ,MAAM,EAAEC,IAAI,EAAE0E,SAAS,CAAC,CAAC,IAAI,CAAC;IACxDM,EAAE,CAACG,IAAI,GAAG1B,OAAO;IACjBuB,EAAE,CAACI,EAAE,GAAGX,YAAY;IAEpBjE,MAAM,CAAC6E,MAAM,CAAC/C,IAAI,CAACtC,IAAI,CAAC;EAC1B,CAAC;EACGsF,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;IACvD,OAAO,UAAU/B,OAAO,EAAE;MACxB,OAAO8B,KAAK,IAAIC,GAAG,KAAK,CAAC,GAAG3H,YAAY,CAAC4F,OAAO,EAAE8B,KAAK,EAAEC,GAAG,CAAC,GAAG/B,OAAO;IACzE,CAAC;EACH,CAAC;AAED,OAAO,IAAIgC,gBAAgB,GAAG;EAC5BC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,YAAY;EAClBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAE;IACnDrH,IAAI,GAAGmH,IAAI;IACXjH,QAAQ,GAAGF,IAAI,CAACsH,KAAK,CAACC,OAAO;IAC7BpH,QAAQ,GAAGH,IAAI,CAACsH,KAAK,CAACE,OAAO;IAC7BpH,cAAc,GAAGJ,IAAI,CAACmH,IAAI,CAACM,aAAa;IAExCpH,UAAU,GAAGL,IAAI,CAACmH,IAAI,CAACO,SAAS,IAAI,YAAY,CAAC,CAAC;IAElDzH,SAAS,GAAGoH,SAAS;EACvB,CAAC;EACDM,IAAI,EAAE,SAASA,IAAIA,CAACtG,MAAM,EAAEW,IAAI,EAAE4F,KAAK,EAAE;IACvC,IAAI,CAAC5H,IAAI,EAAE;MACT6H,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;MAC5D,OAAO,KAAK;IACd;IAEA,IAAI,EAAE,OAAO9F,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,CAAC+F,KAAK,CAAC,IAAI,CAAC/F,IAAI,CAACyE,IAAI,EAAE;MAC5DzE,IAAI,GAAG;QACLyE,IAAI,EAAEzE;MACR,CAAC;IACH;IAEA,IAAIgG,QAAQ,GAAG,EAAE;MACbC,KAAK,GAAGjG,IAAI;MACZyE,IAAI,GAAGwB,KAAK,CAACxB,IAAI;MACjByB,UAAU,GAAGD,KAAK,CAACC,UAAU;MAC7BjG,KAAK,GAAGgG,KAAK,CAAChG,KAAK;MACnBC,KAAK,GAAG+F,KAAK,CAAC/F,KAAK;MACnBP,CAAC,GAAGsG,KAAK,CAACtG,CAAC;MACXC,CAAC,GAAGqG,KAAK,CAACrG,CAAC;MACXuG,QAAQ,GAAG1B,IAAI,CAAC,CAAC,CAAC;MAClB1E,MAAM,GAAG6E,cAAc,CAAC5E,IAAI,CAAC6E,KAAK,EAAE,KAAK,IAAI7E,IAAI,GAAGA,IAAI,CAAC8E,GAAG,GAAG,CAAC,CAAC;MACjE/B,OAAO;MACPH,CAAC;IAEL,IAAI,CAACoD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3G,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACQ,MAAM,GAAGhI,cAAc,IAAIA,cAAc,CAACiB,MAAM,EAAE,WAAW,CAAC;IAEnE,IAAI,IAAI,CAACgH,MAAM,GAAGH,UAAU,IAAIA,UAAU,KAAK,CAAC,EAAE;MAChD;MACA,IAAI,CAACI,OAAO,GAAGnH,UAAU,CAAC+G,UAAU,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACK,OAAO,GAAG,CAAC,CAACvG,IAAI,CAACwG,UAAU;MAChC,IAAI,CAACC,KAAK,GAAGzG,IAAI,CAAC0G,QAAQ,IAAI,UAAU,CAAC,CAAC;;MAE1C,IAAI,CAACC,IAAI,GAAGtH,MAAM,CAACG,KAAK,CAACiE,GAAG,CAACpE,MAAM,EAAE,IAAI,CAACoH,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;;MAExD,IAAI,CAACG,EAAE,GAAG1I,QAAQ,CAACmB,MAAM,CAACG,KAAK,CAACC,GAAG,CAACJ,MAAM,EAAE,IAAI,CAACoH,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE;IAEA,IAAII,KAAK,CAACC,OAAO,CAACrC,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAIA,IAAI,CAAC,IAAI,OAAO0B,QAAQ,KAAK,QAAQ,EAAE;MAC9E,KAAKvD,CAAC,IAAIuD,QAAQ,EAAE;QAClB,IAAI,CAACxG,CAAC,IAAI,CAACjC,OAAO,CAAC2G,OAAO,CAACzB,CAAC,CAAC,EAAE;UAC7BjD,CAAC,GAAGiD,CAAC;QACP,CAAC,MAAM,IAAI,CAAChD,CAAC,IAAI,CAAChC,OAAO,CAACyG,OAAO,CAACzB,CAAC,CAAC,EAAE;UACpChD,CAAC,GAAGgD,CAAC;QACP;MACF;MAEA,IAAIjD,CAAC,IAAIC,CAAC,EAAE;QACV;QACAoG,QAAQ,CAACpE,IAAI,CAAC/B,iBAAiB,CAAC,IAAI,EAAEpB,yBAAyB,CAACA,yBAAyB,CAAC,EAAE,EAAEgG,IAAI,EAAE9E,CAAC,EAAE,CAAC,CAAC,EAAE8E,IAAI,EAAE7E,CAAC,EAAE,CAAC,CAAC,EAAEP,MAAM,EAAEM,CAAC,EAAEC,CAAC,EAAEG,MAAM,EAAEC,IAAI,EAAEC,KAAK,IAAI/B,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC9E,CAAC,CAAC,CAAC,EAAEO,KAAK,IAAIhC,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC7E,CAAC,CAAC,CAAC,CAAC,CAAC;MACpN,CAAC,MAAM;QACLD,CAAC,GAAGC,CAAC,GAAG,CAAC;MACX;MAEA,KAAKgD,CAAC,IAAIuD,QAAQ,EAAE;QAClBvD,CAAC,KAAKjD,CAAC,IAAIiD,CAAC,KAAKhD,CAAC,IAAIoG,QAAQ,CAACpE,IAAI,CAAC/B,iBAAiB,CAAC,IAAI,EAAEpB,yBAAyB,CAAC,EAAE,EAAEgG,IAAI,EAAE7B,CAAC,EAAE,CAAC,CAAC,EAAEvD,MAAM,EAAEuD,CAAC,EAAE,CAAC,EAAE7C,MAAM,EAAEC,IAAI,EAAE9B,QAAQ,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3J;IACF,CAAC,MAAM;MACLG,OAAO,GAAGhD,MAAM,CAACU,MAAM,CAAC3D,UAAU,CAACkD,IAAI,CAACyE,IAAI,CAAC,EAAEpF,MAAM,EAAEW,IAAI,CAAC,CAAC;MAC7DjD,wBAAwB,CAACgG,OAAO,EAAE/C,IAAI,CAACW,UAAU,CAAC;MAClDqF,QAAQ,CAACpE,IAAI,CAACmB,OAAO,CAAC;MAEtBrC,wBAAwB,CAAC,IAAI,EAAErB,MAAM,EAAEW,IAAI,CAACL,CAAC,IAAI,GAAG,EAAEoD,OAAO,EAAE,GAAG,EAAE/C,IAAI,CAACC,KAAK,IAAI,IAAI,CAAC;MAEvFS,wBAAwB,CAAC,IAAI,EAAErB,MAAM,EAAEW,IAAI,CAACJ,CAAC,IAAI,GAAG,EAAEmD,OAAO,EAAE,GAAG,EAAE/C,IAAI,CAACE,KAAK,IAAI,IAAI,CAAC;IACzF;IAEA0F,KAAK,CAAC5F,IAAI,CAAC+G,eAAe,IAAI,IAAI,CAACC,MAAM,CAACpB,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;EACnE,CAAC;EACDD,MAAM,EAAE,SAASA,MAAMA,CAACE,KAAK,EAAEC,IAAI,EAAE;IACnC,IAAInB,QAAQ,GAAGmB,IAAI,CAACnB,QAAQ;MACxB/G,CAAC,GAAG+G,QAAQ,CAACjH,MAAM;MACnBuF,EAAE,GAAG6C,IAAI,CAAC5C,GAAG;IAEjB,IAAI4C,IAAI,CAACvB,KAAK,CAACwB,KAAK,IAAI,CAAC/I,UAAU,CAAC,CAAC,EAAE;MACrC,IAAI6I,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,GAAG,CAAC;MACX,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;QACpBA,KAAK,GAAG,CAAC;MACX;MAEA,OAAOjI,CAAC,EAAE,EAAE;QACVjC,iBAAiB,CAACgJ,QAAQ,CAAC/G,CAAC,CAAC,EAAEiI,KAAK,EAAE,CAACjI,CAAC,IAAIkI,IAAI,CAACd,MAAM,EAAEL,QAAQ,CAAC/G,CAAC,CAAC,CAAC;MACvE;MAEA,OAAOqF,EAAE,EAAE;QACTA,EAAE,CAACb,GAAG,CAACa,EAAE,CAAC+C,CAAC,EAAE/C,EAAE,CAAC1B,CAAC,EAAE0B,EAAE,CAACG,IAAI,CAACH,EAAE,CAACI,EAAE,CAAC,GAAGJ,EAAE,CAACE,CAAC,EAAEF,EAAE,CAACR,CAAC,EAAEoD,KAAK,CAAC;QACtD5C,EAAE,GAAGA,EAAE,CAACgD,KAAK;MACf;MAEAH,IAAI,CAACd,MAAM,IAAIc,IAAI,CAACR,IAAI,CAACQ,IAAI,CAAC9H,MAAM,EAAE8H,IAAI,CAACV,KAAK,EAAET,QAAQ,CAAC,CAAC,CAAC,CAACuB,KAAK,IAAIJ,IAAI,CAACZ,OAAO,GAAG1I,QAAQ,GAAG,CAAC,CAAC,GAAGsJ,IAAI,CAACb,OAAO,GAAGa,IAAI,CAACP,EAAE,EAAEO,IAAI,EAAED,KAAK,CAAC;IAC5I,CAAC,MAAM;MACLC,IAAI,CAACf,MAAM,CAACoB,MAAM,CAAC,CAAC;IACtB;EACF,CAAC;EACDC,SAAS,EAAE,SAASA,SAASA,CAAChD,IAAI,EAAE;IAClC,OAAO1H,wBAAwB,CAACD,UAAU,CAAC2H,IAAI,CAAC,CAAC,CAACiD,WAAW;EAC/D,CAAC;EACDvK,YAAY,EAAEA,YAAY;EAC1BL,UAAU,EAAEA,UAAU;EACtBG,eAAe,EAAEA,eAAe;EAChCG,eAAe,EAAEA,eAAe;EAChCC,eAAe,EAAEA,eAAe;EAChCC,gBAAgB,EAAEA,gBAAgB;EAClCG,eAAe,EAAEA,eAAe;EAChCT,iBAAiB,EAAEA,iBAAiB;EACpCD,wBAAwB,EAAEA,wBAAwB;EAClDQ,aAAa,EAAE,SAASA,aAAaA,CAACoK,OAAO,EAAEC,IAAI,EAAE;IACnD,OAAOzJ,QAAQ,CAACwJ,OAAO,CAAC,CAACE,GAAG,CAAC,UAAUxI,MAAM,EAAE;MAC7C,OAAO7B,cAAc,CAAC6B,MAAM,EAAEuI,IAAI,KAAK,KAAK,CAAC;IAC/C,CAAC,CAAC;EACJ,CAAC;EACDE,kBAAkB,EAAE,SAASA,kBAAkBA,CAAC3F,WAAW,EAAEC,SAAS,EAAE2F,KAAK,EAAE;IAC7E,IAAI7G,CAAC,GAAGzD,eAAe,CAAC2E,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAACK,QAAQ,CAAChF,eAAe,CAAC0E,WAAW,CAAC,CAAC;IACrF,OAAO4F,KAAK,GAAG7G,CAAC,CAACa,KAAK,CAACgG,KAAK,CAAC,GAAG7G,CAAC;EACnC,CAAC;EACD8G,cAAc,EAAE9F,eAAe;EAC/B+F,mBAAmB,EAAE,SAASA,mBAAmBA,CAAC9F,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC9F,IAAIpB,CAAC,GAAGgB,eAAe,CAACC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,CAAC;IAErE,OAAO;MACL3C,CAAC,EAAEuB,CAAC,CAACc,CAAC;MACNpC,CAAC,EAAEsB,CAAC,CAACe;IACP,CAAC;EACH,CAAC;EACDiG,cAAc,EAAE,SAASA,cAAcA,CAACC,KAAK,EAAEnI,IAAI,EAAE;IACnDA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IAEjB,IAAItB,OAAO,GAAGD,yBAAyB,CAACA,yBAAyB,CAAC,EAAE,EAAE0J,KAAK,EAAEnI,IAAI,CAACL,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,EAAEwI,KAAK,EAAEnI,IAAI,CAACJ,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IAExHI,IAAI,CAACM,QAAQ,IAAIZ,WAAW,CAAChB,OAAO,CAAC;IACrC,OAAO,CAACsB,IAAI,CAACG,IAAI,KAAK,OAAO,GAAGzB,OAAO,GAAGzB,eAAe,CAACyB,OAAO,EAAEsB,IAAI,CAACQ,SAAS,CAAC,CAAC;EACrF;AACF,CAAC;AACDlC,QAAQ,CAAC,CAAC,IAAIN,IAAI,CAACQ,cAAc,CAACuG,gBAAgB,CAAC;AACnD,SAASA,gBAAgB,IAAIqD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}