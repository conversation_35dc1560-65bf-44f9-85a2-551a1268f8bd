{"ast": null, "code": "import{useEffect}from'react';import{useNavigate}from'react-router-dom';const ScaleLetterTransition=_ref=>{let{isActive,to,onComplete}=_ref;const navigate=useNavigate();useEffect(()=>{if(isActive){startTransition();}},[isActive]);const startTransition=async()=>{await document.fonts.ready;// Find the 'a' in SCaLE\nconst scaleTitle=document.querySelector('.hero-section h1');if(scaleTitle){const rect=scaleTitle.getBoundingClientRect();// Calculate position of the 'a' in \"SCaLE\" \n// S-C-a-L-E: 'a' is 3rd out of 5 letters, so about 51% across\nconst letterCenterX=rect.left+rect.width*0.507;const letterCenterY=rect.top+rect.height*0.5;// Apply zoom directly to document.body\ndocument.body.style.transition='transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';document.body.style.transformOrigin=\"\".concat(letterCenterX,\"px \").concat(letterCenterY,\"px\");document.body.style.transform=\"scale(350)\";// Navigate after zoom completes\nsetTimeout(()=>{// Reset body transform before navigating so back button works\ndocument.body.style.transform='';document.body.style.transition='';document.body.style.transformOrigin='';navigate(to);onComplete();},3200);}else{setTimeout(()=>{navigate(to);onComplete();},100);}};return null;};export default ScaleLetterTransition;", "map": {"version": 3, "names": ["useEffect", "useNavigate", "ScaleLetterTransition", "_ref", "isActive", "to", "onComplete", "navigate", "startTransition", "document", "fonts", "ready", "scaleTitle", "querySelector", "rect", "getBoundingClientRect", "letterCenterX", "left", "width", "letterCenterY", "top", "height", "body", "style", "transition", "transform<PERSON><PERSON>in", "concat", "transform", "setTimeout"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/ScaleLetterTransition.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\ninterface ScaleLetterTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete: () => void;\n}\n\nconst ScaleLetterTransition: React.FC<ScaleLetterTransitionProps> = ({\n  isActive,\n  to,\n  onComplete\n}) => {\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (isActive) {\n      startTransition();\n    }\n  }, [isActive]);\n\n  const startTransition = async () => {\n    await document.fonts.ready;\n    // Find the 'a' in SCaLE\n    const scaleTitle = document.querySelector('.hero-section h1');\n    \n    if (scaleTitle) {\n      const rect = scaleTitle.getBoundingClientRect();\n      \n      // Calculate position of the 'a' in \"SCaLE\" \n      // S-C-a-L-E: 'a' is 3rd out of 5 letters, so about 51% across\n      const letterCenterX = rect.left + (rect.width * 0.507);\n      const letterCenterY = rect.top + (rect.height * 0.5);\n\n      // Apply zoom directly to document.body\n      document.body.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';\n      document.body.style.transformOrigin = `${letterCenterX}px ${letterCenterY}px`;\n      document.body.style.transform = `scale(350)`;\n\n      // Navigate after zoom completes\n      setTimeout(() => {\n        // Reset body transform before navigating so back button works\n        document.body.style.transform = '';\n        document.body.style.transition = '';\n        document.body.style.transformOrigin = '';\n        \n        navigate(to);\n        onComplete();\n      }, 3200);\n    } else {\n      setTimeout(() => {\n        navigate(to);\n        onComplete();\n      }, 100);\n    }\n  };\n\n  return null;\n};\n\nexport default ScaleLetterTransition;"], "mappings": "AAAA,OAAgBA,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,KAAQ,kBAAkB,CAQ9C,KAAM,CAAAC,qBAA2D,CAAGC,IAAA,EAI9D,IAJ+D,CACnEC,QAAQ,CACRC,EAAE,CACFC,UACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAE9BD,SAAS,CAAC,IAAM,CACd,GAAII,QAAQ,CAAE,CACZI,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACJ,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAI,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,KAAM,CAAAC,QAAQ,CAACC,KAAK,CAACC,KAAK,CAC1B;AACA,KAAM,CAAAC,UAAU,CAAGH,QAAQ,CAACI,aAAa,CAAC,kBAAkB,CAAC,CAE7D,GAAID,UAAU,CAAE,CACd,KAAM,CAAAE,IAAI,CAAGF,UAAU,CAACG,qBAAqB,CAAC,CAAC,CAE/C;AACA;AACA,KAAM,CAAAC,aAAa,CAAGF,IAAI,CAACG,IAAI,CAAIH,IAAI,CAACI,KAAK,CAAG,KAAM,CACtD,KAAM,CAAAC,aAAa,CAAGL,IAAI,CAACM,GAAG,CAAIN,IAAI,CAACO,MAAM,CAAG,GAAI,CAEpD;AACAZ,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACC,UAAU,CAAG,mDAAmD,CACpFf,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACE,eAAe,IAAAC,MAAA,CAAMV,aAAa,QAAAU,MAAA,CAAMP,aAAa,MAAI,CAC7EV,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACI,SAAS,aAAe,CAE5C;AACAC,UAAU,CAAC,IAAM,CACf;AACAnB,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACI,SAAS,CAAG,EAAE,CAClClB,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACC,UAAU,CAAG,EAAE,CACnCf,QAAQ,CAACa,IAAI,CAACC,KAAK,CAACE,eAAe,CAAG,EAAE,CAExClB,QAAQ,CAACF,EAAE,CAAC,CACZC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLsB,UAAU,CAAC,IAAM,CACfrB,QAAQ,CAACF,EAAE,CAAC,CACZC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CAAC,CAED,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAAJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}