{"ast": null, "code": "import React,{useState,useRef}from'react';import{Link}from'react-router-dom';import'./HomePage.css';import HyperOrangeTransition from'../Xtra/HyperOrangeTransition';import ScaleLetterTransition from'../Xtra/ScaleLetterTransition';// Add this import\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Homepage=()=>{const[isTransitioning,setIsTransitioning]=useState(false);const[isScaleTransitioning,setIsScaleTransitioning]=useState(false);// Add this state\nconst[buttonRect,setButtonRect]=useState(null);const shopButtonRef=useRef(null);const createButtonRef=useRef(null);// Add this ref\nconst handleSamplesClick=e=>{e.preventDefault();if(shopButtonRef.current){const rect=shopButtonRef.current.getBoundingClientRect();setButtonRect(rect);setIsTransitioning(true);}};// Add this handler for Create Custom button\nconst handleCreateCustomClick=e=>{e.preventDefault();setIsScaleTransitioning(true);};const handleTransitionComplete=()=>{setIsTransitioning(false);};// Add this handler for scale transition complete\nconst handleScaleTransitionComplete=()=>{setIsScaleTransitioning(false);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"background-container\",children:/*#__PURE__*/_jsx(\"div\",{className:\"background-image\",id:\"background-image\"})}),isTransitioning&&buttonRect&&/*#__PURE__*/_jsx(HyperOrangeTransition,{isActive:isTransitioning,to:\"/samples\",onComplete:handleTransitionComplete,buttonRect:buttonRect}),isScaleTransitioning&&/*#__PURE__*/_jsx(ScaleLetterTransition,{isActive:isScaleTransitioning,to:\"/create-video\",onComplete:handleScaleTransitionComplete}),/*#__PURE__*/_jsx(\"header\",{children:/*#__PURE__*/_jsx(\"nav\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"logo\",children:\"Tailored Tutoring presents...\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"compliance-banner\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"compliance-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Compliance & Integration\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2713 LTI Compliant\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2713 SCORM Compliant\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2713 xAPI Compliant\"})]}),/*#__PURE__*/_jsx(\"h4\",{children:\"Seamlessly Integrated with:\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"lms-list\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDFE2 Canvas\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDFE2 Moodle\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDFE2 Google Classroom\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDFE2 Blackboard\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\uD83D\\uDFE1 Schoology (Beta)\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"more-text\",children:\"...and more!\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"scale-banner\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"scale-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"SCaLE Features\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"- Science videos and quizzes for elementary\"}),/*#__PURE__*/_jsx(\"li\",{children:\"- Create video from lesson name or buy pre-made\"}),/*#__PURE__*/_jsx(\"li\",{children:\"- Use yours and your child's voice (TOTALLY OPTIONAL)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"- Louisiana Education Board approved\"})]})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"hero-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hero-content\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"SCaLE\"}),/*#__PURE__*/_jsx(\"p\",{children:\"The Science Class Lesson Engine\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"hero-buttons\",children:[/*#__PURE__*/_jsx(\"a\",{ref:createButtonRef,href:\"/create-video\",className:\"cta-button\",onClick:handleCreateCustomClick,children:\"Create Custom\"}),/*#__PURE__*/_jsx(\"a\",{ref:shopButtonRef,href:\"/samples\",className:\"cta-button shop-button\",onClick:handleSamplesClick,children:\"Shop Pre-made\"})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:\"features-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"What Tailored Tutoring Offers\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"features-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:\"\\uD83C\\uDFA5\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Auto-generated educational video\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:\"\\uD83E\\uDDD1\\u200D\\uD83C\\uDFEB\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Teacher-student dialogue overlay\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:\"\\uD83D\\uDCDD\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Personalized quiz\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"feature-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon\",children:\"\\uD83C\\uDF10\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Private webpage for student access\"})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'50px',textAlign:'center'},children:/*#__PURE__*/_jsx(Link,{to:\"/manual-create-video\",style:{color:'#007bff',textDecoration:'underline'},children:\"Manual Video Creation (Fallback)\"})})]}),/*#__PURE__*/_jsx(\"footer\",{})]});};export default Homepage;", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "HyperOrangeTransition", "ScaleLetterTransition", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Homepage", "isTransitioning", "setIsTransitioning", "isScaleTransitioning", "setIsScaleTransitioning", "buttonRect", "setButtonRect", "shopButtonRef", "createButtonRef", "handleSamplesClick", "e", "preventDefault", "current", "rect", "getBoundingClientRect", "handleCreateCustomClick", "handleTransitionComplete", "handleScaleTransitionComplete", "children", "className", "id", "isActive", "to", "onComplete", "ref", "href", "onClick", "style", "marginTop", "textAlign", "color", "textDecoration"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport './HomePage.css';\nimport HyperOrangeTransition from '../Xtra/HyperOrangeTransition';\nimport ScaleLetterTransition from '../Xtra/ScaleLetterTransition'; // Add this import\n\nconst Homepage: React.FC = () => {\n    const [isTransitioning, setIsTransitioning] = useState(false);\n    const [isScaleTransitioning, setIsScaleTransitioning] = useState(false); // Add this state\n    const [buttonRect, setButtonRect] = useState<DOMRect | null>(null);\n    const shopButtonRef = useRef<HTMLAnchorElement>(null);\n    const createButtonRef = useRef<HTMLAnchorElement>(null); // Add this ref\n\n    const handleSamplesClick = (e: React.MouseEvent) => {\n        e.preventDefault();\n        if (shopButtonRef.current) {\n            const rect = shopButtonRef.current.getBoundingClientRect();\n            setButtonRect(rect);\n            setIsTransitioning(true);\n        }\n    };\n\n    // Add this handler for Create Custom button\n    const handleCreateCustomClick = (e: React.MouseEvent) => {\n        e.preventDefault();\n        setIsScaleTransitioning(true);\n    };\n\n    const handleTransitionComplete = () => {\n        setIsTransitioning(false);\n    };\n\n    // Add this handler for scale transition complete\n    const handleScaleTransitionComplete = () => {\n        setIsScaleTransitioning(false);\n    };\n\n    return (\n        <>\n            {/* Background container - this replaces the body background */}\n            <div className=\"background-container\">\n                <div className=\"background-image\" id=\"background-image\"></div>\n            </div>\n\n            {isTransitioning && buttonRect && (\n                <HyperOrangeTransition\n                    isActive={isTransitioning}\n                    to=\"/samples\"\n                    onComplete={handleTransitionComplete}\n                    buttonRect={buttonRect}\n                />\n            )}\n\n            {/* Add the scale letter transition */}\n            {isScaleTransitioning && (\n                <ScaleLetterTransition\n                    isActive={isScaleTransitioning}\n                    to=\"/create-video\"\n                    onComplete={handleScaleTransitionComplete}\n                />\n            )}\n            \n            <header>\n                <nav>\n                    <div className=\"logo\">Tailored Tutoring presents...</div>\n                </nav>\n            </header>\n\n            <div className=\"compliance-banner\">\n                <div className=\"compliance-content\">\n                    <h3>Compliance & Integration</h3>\n                    <ul>\n                        <li>✓ LTI Compliant</li>\n                        <li>✓ SCORM Compliant</li>\n                        <li>✓ xAPI Compliant</li>\n                    </ul>\n                    <h4>Seamlessly Integrated with:</h4>\n                    <ul className=\"lms-list\">\n                        <li>🟢 Canvas</li>\n                        <li>🟢 Moodle</li>\n                        <li>🟢 Google Classroom</li>\n                        <li>🟢 Blackboard</li>\n                        <li>🟡 Schoology (Beta)</li>\n                    </ul>\n                    <p className=\"more-text\">...and more!</p>\n                </div>\n            </div>\n            <div className=\"scale-banner\">\n                <div className=\"scale-content\">\n                    <h3>SCaLE Features</h3>\n                    <ul>\n                        <li>- Science videos and quizzes for elementary</li>\n                        <li>- Create video from lesson name or buy pre-made</li>\n                        <li>- Use yours and your child's voice (TOTALLY OPTIONAL)</li>\n                        <li>- Louisiana Education Board approved</li>\n                    </ul>\n                </div>\n            </div>\n\n            <section className=\"hero-section\">\n                <div className=\"hero-content\">\n                    <h1>SCaLE</h1>\n                    <p>The Science Class Lesson Engine</p>\n                    <div className=\"hero-buttons\">\n                        {/* Update the Create Custom button */}\n                        <a \n                            ref={createButtonRef}\n                            href=\"/create-video\" \n                            className=\"cta-button\"\n                            onClick={handleCreateCustomClick}\n                        >\n                            Create Custom\n                        </a>\n                        <a \n                            ref={shopButtonRef}\n                            href=\"/samples\" \n                            className=\"cta-button shop-button\"\n                            onClick={handleSamplesClick}\n                        >\n                            Shop Pre-made\n                        </a>\n                    </div>\n                </div>\n            </section>\n\n            <section className=\"features-section\">\n                <h2>What Tailored Tutoring Offers</h2>\n                <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🎥</div>\n                        <p>Auto-generated educational video</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🧑‍🏫</div>\n                        <p>Teacher-student dialogue overlay</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">📝</div>\n                        <p>Personalized quiz</p>\n                    </div>\n                    <div className=\"feature-item\">\n                        <div className=\"icon\">🌐</div>\n                        <p>Private webpage for student access</p>\n                    </div>\n                </div>            \n                <div style={{ marginTop: '50px', textAlign: 'center' }}>\n                    <Link to=\"/manual-create-video\" style={{ color: '#007bff', textDecoration: 'underline' }}>\n                        Manual Video Creation (Fallback)\n                    </Link>\n                </div>\n            </section>\n\n            <footer>\n                {/* Optional: Add footer content here */}\n            </footer>\n        </>\n    );\n};\n\nexport default Homepage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAeC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,gBAAgB,CACvB,MAAO,CAAAC,qBAAqB,KAAM,+BAA+B,CACjE,MAAO,CAAAC,qBAAqB,KAAM,+BAA+B,CAAE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnE,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACc,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAAE;AACzE,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAiB,IAAI,CAAC,CAClE,KAAM,CAAAkB,aAAa,CAAGjB,MAAM,CAAoB,IAAI,CAAC,CACrD,KAAM,CAAAkB,eAAe,CAAGlB,MAAM,CAAoB,IAAI,CAAC,CAAE;AAEzD,KAAM,CAAAmB,kBAAkB,CAAIC,CAAmB,EAAK,CAChDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAIJ,aAAa,CAACK,OAAO,CAAE,CACvB,KAAM,CAAAC,IAAI,CAAGN,aAAa,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC1DR,aAAa,CAACO,IAAI,CAAC,CACnBX,kBAAkB,CAAC,IAAI,CAAC,CAC5B,CACJ,CAAC,CAED;AACA,KAAM,CAAAa,uBAAuB,CAAIL,CAAmB,EAAK,CACrDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBP,uBAAuB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED,KAAM,CAAAY,wBAAwB,CAAGA,CAAA,GAAM,CACnCd,kBAAkB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAe,6BAA6B,CAAGA,CAAA,GAAM,CACxCb,uBAAuB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED,mBACIP,KAAA,CAAAE,SAAA,EAAAmB,QAAA,eAEIvB,IAAA,QAAKwB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACjCvB,IAAA,QAAKwB,SAAS,CAAC,kBAAkB,CAACC,EAAE,CAAC,kBAAkB,CAAM,CAAC,CAC7D,CAAC,CAELnB,eAAe,EAAII,UAAU,eAC1BV,IAAA,CAACH,qBAAqB,EAClB6B,QAAQ,CAAEpB,eAAgB,CAC1BqB,EAAE,CAAC,UAAU,CACbC,UAAU,CAAEP,wBAAyB,CACrCX,UAAU,CAAEA,UAAW,CAC1B,CACJ,CAGAF,oBAAoB,eACjBR,IAAA,CAACF,qBAAqB,EAClB4B,QAAQ,CAAElB,oBAAqB,CAC/BmB,EAAE,CAAC,eAAe,CAClBC,UAAU,CAAEN,6BAA8B,CAC7C,CACJ,cAEDtB,IAAA,WAAAuB,QAAA,cACIvB,IAAA,QAAAuB,QAAA,cACIvB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,+BAA6B,CAAK,CAAC,CACxD,CAAC,CACF,CAAC,cAETvB,IAAA,QAAKwB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAC9BrB,KAAA,QAAKsB,SAAS,CAAC,oBAAoB,CAAAD,QAAA,eAC/BvB,IAAA,OAAAuB,QAAA,CAAI,0BAAwB,CAAI,CAAC,cACjCrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAI,sBAAe,CAAI,CAAC,cACxBvB,IAAA,OAAAuB,QAAA,CAAI,wBAAiB,CAAI,CAAC,cAC1BvB,IAAA,OAAAuB,QAAA,CAAI,uBAAgB,CAAI,CAAC,EACzB,CAAC,cACLvB,IAAA,OAAAuB,QAAA,CAAI,6BAA2B,CAAI,CAAC,cACpCrB,KAAA,OAAIsB,SAAS,CAAC,UAAU,CAAAD,QAAA,eACpBvB,IAAA,OAAAuB,QAAA,CAAI,qBAAS,CAAI,CAAC,cAClBvB,IAAA,OAAAuB,QAAA,CAAI,qBAAS,CAAI,CAAC,cAClBvB,IAAA,OAAAuB,QAAA,CAAI,+BAAmB,CAAI,CAAC,cAC5BvB,IAAA,OAAAuB,QAAA,CAAI,yBAAa,CAAI,CAAC,cACtBvB,IAAA,OAAAuB,QAAA,CAAI,+BAAmB,CAAI,CAAC,EAC5B,CAAC,cACLvB,IAAA,MAAGwB,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,cAAY,CAAG,CAAC,EACxC,CAAC,CACL,CAAC,cACNvB,IAAA,QAAKwB,SAAS,CAAC,cAAc,CAAAD,QAAA,cACzBrB,KAAA,QAAKsB,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC1BvB,IAAA,OAAAuB,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAI,6CAA2C,CAAI,CAAC,cACpDvB,IAAA,OAAAuB,QAAA,CAAI,iDAA+C,CAAI,CAAC,cACxDvB,IAAA,OAAAuB,QAAA,CAAI,uDAAqD,CAAI,CAAC,cAC9DvB,IAAA,OAAAuB,QAAA,CAAI,sCAAoC,CAAI,CAAC,EAC7C,CAAC,EACJ,CAAC,CACL,CAAC,cAENvB,IAAA,YAASwB,SAAS,CAAC,cAAc,CAAAD,QAAA,cAC7BrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBvB,IAAA,OAAAuB,QAAA,CAAI,OAAK,CAAI,CAAC,cACdvB,IAAA,MAAAuB,QAAA,CAAG,iCAA+B,CAAG,CAAC,cACtCrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eAEzBvB,IAAA,MACI6B,GAAG,CAAEhB,eAAgB,CACrBiB,IAAI,CAAC,eAAe,CACpBN,SAAS,CAAC,YAAY,CACtBO,OAAO,CAAEX,uBAAwB,CAAAG,QAAA,CACpC,eAED,CAAG,CAAC,cACJvB,IAAA,MACI6B,GAAG,CAAEjB,aAAc,CACnBkB,IAAI,CAAC,UAAU,CACfN,SAAS,CAAC,wBAAwB,CAClCO,OAAO,CAAEjB,kBAAmB,CAAAS,QAAA,CAC/B,eAED,CAAG,CAAC,EACH,CAAC,EACL,CAAC,CACD,CAAC,cAEVrB,KAAA,YAASsB,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eACjCvB,IAAA,OAAAuB,QAAA,CAAI,+BAA6B,CAAI,CAAC,cACtCrB,KAAA,QAAKsB,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC1BrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBvB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,cAAE,CAAK,CAAC,cAC9BvB,IAAA,MAAAuB,QAAA,CAAG,kCAAgC,CAAG,CAAC,EACtC,CAAC,cACNrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBvB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,gCAAK,CAAK,CAAC,cACjCvB,IAAA,MAAAuB,QAAA,CAAG,kCAAgC,CAAG,CAAC,EACtC,CAAC,cACNrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBvB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,cAAE,CAAK,CAAC,cAC9BvB,IAAA,MAAAuB,QAAA,CAAG,mBAAiB,CAAG,CAAC,EACvB,CAAC,cACNrB,KAAA,QAAKsB,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBvB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,cAAE,CAAK,CAAC,cAC9BvB,IAAA,MAAAuB,QAAA,CAAG,oCAAkC,CAAG,CAAC,EACxC,CAAC,EACL,CAAC,cACNvB,IAAA,QAAKgC,KAAK,CAAE,CAAEC,SAAS,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAX,QAAA,cACnDvB,IAAA,CAACJ,IAAI,EAAC+B,EAAE,CAAC,sBAAsB,CAACK,KAAK,CAAE,CAAEG,KAAK,CAAE,SAAS,CAAEC,cAAc,CAAE,WAAY,CAAE,CAAAb,QAAA,CAAC,kCAE1F,CAAM,CAAC,CACN,CAAC,EACD,CAAC,cAEVvB,IAAA,YAEQ,CAAC,EACX,CAAC,CAEX,CAAC,CAED,cAAe,CAAAK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}