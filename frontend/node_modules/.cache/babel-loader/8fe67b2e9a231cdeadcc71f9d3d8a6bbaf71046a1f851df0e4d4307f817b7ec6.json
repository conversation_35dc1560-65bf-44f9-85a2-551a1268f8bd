{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuantumGyroscope = () => {\n  _s();\n  const [rotation, setRotation] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState({\n    x: 0,\n    y: 0,\n    z: 0\n  });\n  const [particles, setParticles] = useState([]);\n  const [energyLevel, setEnergyLevel] = useState(0.5);\n  const lastMousePos = useRef({\n    x: 0,\n    y: 0\n  });\n  const animationRef = useRef();\n  const particleIdRef = useRef(0);\n  const gyroscopeRef = useRef(null);\n\n  // Initialize enhanced particle system\n  useEffect(() => {\n    const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n    const initParticles = () => {\n      const newParticles = [];\n      for (let i = 0; i < 40; i++) {\n        newParticles.push({\n          id: particleIdRef.current++,\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          z: (Math.random() - 0.5) * 150,\n          vx: (Math.random() - 0.5) * 4,\n          vy: (Math.random() - 0.5) * 4,\n          vz: (Math.random() - 0.5) * 2,\n          life: Math.random() * 120,\n          maxLife: 120 + Math.random() * 80,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          size: 2 + Math.random() * 4\n        });\n      }\n      setParticles(newParticles);\n    };\n    initParticles();\n  }, []);\n\n  // Enhanced animation loop\n  useEffect(() => {\n    const animate = () => {\n      // Gyroscope rotation with dynamic energy\n      if (!isDragging) {\n        const baseSpeed = energyLevel * 2;\n        setRotation(prev => ({\n          x: prev.x + momentum.x + baseSpeed * 0.4,\n          y: prev.y + momentum.y + baseSpeed * 0.6,\n          z: prev.z + momentum.z + baseSpeed * 0.3\n        }));\n        setMomentum(prev => ({\n          x: prev.x * 0.99,\n          y: prev.y * 0.99,\n          z: prev.z * 0.99\n        }));\n\n        // Dynamic energy fluctuation\n        setEnergyLevel(prev => {\n          const fluctuation = Math.sin(Date.now() * 0.001) * 0.1;\n          return Math.max(0.2, Math.min(1, prev + fluctuation));\n        });\n      }\n\n      // Advanced particle physics\n      setParticles(prev => {\n        return prev.map(particle => {\n          // Gravitational pull towards center\n          const centerForce = 0.02;\n          const dx = -particle.x * centerForce;\n          const dy = -particle.y * centerForce;\n          const dz = -particle.z * centerForce;\n\n          // Orbital motion\n          const orbitalForce = 0.01;\n          const orbitalVx = -particle.y * orbitalForce;\n          const orbitalVy = particle.x * orbitalForce;\n\n          // Turbulence\n          const turbulence = energyLevel * 0.2;\n          const turbVx = (Math.random() - 0.5) * turbulence;\n          const turbVy = (Math.random() - 0.5) * turbulence;\n          const turbVz = (Math.random() - 0.5) * turbulence * 0.5;\n          const newParticle = {\n            ...particle,\n            vx: particle.vx * 0.98 + dx + orbitalVx + turbVx,\n            vy: particle.vy * 0.98 + dy + orbitalVy + turbVy,\n            vz: particle.vz * 0.95 + dz + turbVz,\n            x: particle.x + particle.vx,\n            y: particle.y + particle.vy,\n            z: particle.z + particle.vz,\n            life: particle.life + 1\n          };\n\n          // Respawn particles\n          if (newParticle.life > newParticle.maxLife || Math.sqrt(newParticle.x ** 2 + newParticle.y ** 2 + newParticle.z ** 2) > 400) {\n            const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n            const angle = Math.random() * Math.PI * 2;\n            const radius = 50 + Math.random() * 100;\n            return {\n              id: particleIdRef.current++,\n              x: Math.cos(angle) * radius,\n              y: Math.sin(angle) * radius,\n              z: (Math.random() - 0.5) * 50,\n              vx: (Math.random() - 0.5) * 6,\n              vy: (Math.random() - 0.5) * 6,\n              vz: (Math.random() - 0.5) * 3,\n              life: 0,\n              maxLife: 120 + Math.random() * 80,\n              color: colors[Math.floor(Math.random() * colors.length)],\n              size: 2 + Math.random() * 4\n            };\n          }\n          return newParticle;\n        });\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum, energyLevel]);\n  const handleInteraction = (e, isStart = false) => {\n    var _gyroscopeRef$current;\n    const rect = (_gyroscopeRef$current = gyroscopeRef.current) === null || _gyroscopeRef$current === void 0 ? void 0 : _gyroscopeRef$current.getBoundingClientRect();\n    if (!rect) return;\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n    if (isStart) {\n      setIsDragging(true);\n      lastMousePos.current = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    } else if (isDragging) {\n      const deltaX = e.clientX - lastMousePos.current.x;\n      const deltaY = e.clientY - lastMousePos.current.y;\n      const intensity = Math.sqrt(deltaX ** 2 + deltaY ** 2) / 100;\n      setEnergyLevel(prev => Math.min(1, prev + intensity * 0.1));\n      const rotationSpeed = 1.2;\n      setRotation(prev => ({\n        x: prev.x + deltaY * rotationSpeed,\n        y: prev.y + deltaX * rotationSpeed,\n        z: prev.z + (deltaX - deltaY) * 0.3\n      }));\n      setMomentum({\n        x: deltaY * 0.2,\n        y: deltaX * 0.2,\n        z: (deltaX - deltaY) * 0.1\n      });\n      lastMousePos.current = {\n        x: e.clientX,\n        y: e.clientY\n      };\n    }\n  };\n  const getParticleStyle = particle => {\n    const lifeRatio = particle.life / particle.maxLife;\n    const opacity = Math.max(0.1, (1 - lifeRatio) * energyLevel);\n    const distance = Math.sqrt(particle.x ** 2 + particle.y ** 2 + particle.z ** 2);\n    const perspective = 600;\n    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 300);\n    return {\n      position: 'absolute',\n      left: '50%',\n      top: '50%',\n      width: `${particle.size}px`,\n      height: `${particle.size}px`,\n      transform: `\n        translate(-50%, -50%)\n        translate3d(${particle.x * scale}px, ${particle.y * scale}px, 0px)\n        scale(${scale})\n      `,\n      opacity: opacity,\n      background: `radial-gradient(circle, ${particle.color} 0%, transparent 70%)`,\n      borderRadius: '50%',\n      boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,\n      pointerEvents: 'none',\n      zIndex: Math.round(particle.z + 100),\n      filter: `brightness(${energyLevel + 0.5})`\n    };\n  };\n  const ringIntensity = energyLevel * 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-40\",\n      children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute rounded-full blur-3xl animate-pulse\",\n        style: {\n          width: `${200 + i * 100}px`,\n          height: `${200 + i * 100}px`,\n          left: `${20 + i * 15}%`,\n          top: `${10 + i * 20}%`,\n          background: `radial-gradient(circle, ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i]}33, transparent)`,\n          animationDelay: `${i * 0.5}s`,\n          animationDuration: `${3 + i}s`,\n          transform: `rotate(${rotation.z * (i + 1)}deg) scale(${energyLevel})`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-5xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-pulse\",\n        style: {\n          filter: `brightness(${1 + energyLevel})`,\n          textShadow: `0 0 20px rgba(59, 130, 246, ${energyLevel})`\n        },\n        children: \"Quantum Gyroscope\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: gyroscopeRef,\n        className: \"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\",\n        style: {\n          perspective: '1200px'\n        },\n        onMouseDown: e => handleInteraction(e, true),\n        onMouseMove: e => handleInteraction(e),\n        onMouseUp: () => setIsDragging(false),\n        onMouseLeave: () => setIsDragging(false),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 pointer-events-none\",\n          children: particles.map(particle => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: getParticleStyle(particle)\n          }, particle.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 rounded-full border-2\",\n          style: {\n            transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n            transition: isDragging ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            borderColor: '#3b82f6',\n            boxShadow: `\n                0 0 ${20 + ringIntensity}px #3b82f6,\n                inset 0 0 ${20 + ringIntensity}px #3b82f6aa,\n                0 0 ${40 + ringIntensity * 2}px #3b82f6aa\n              `,\n            filter: `brightness(${1 + energyLevel * 0.5})`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-8 rounded-full border-2\",\n            style: {\n              transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n              borderColor: '#8b5cf6',\n              boxShadow: `\n                  0 0 ${30 + ringIntensity}px #8b5cf6,\n                  inset 0 0 ${30 + ringIntensity}px #8b5cf6aa\n                `\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-8 rounded-full border-2\",\n              style: {\n                transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                borderColor: '#ec4899',\n                boxShadow: `\n                    0 0 ${40 + ringIntensity}px #ec4899,\n                    inset 0 0 ${40 + ringIntensity}px #ec4899aa\n                  `\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\",\n                style: {\n                  transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                  boxShadow: `\n                      0 0 ${60 + ringIntensity * 2}px #ffffff,\n                      inset 0 0 ${20 + ringIntensity}px #3b82f688\n                    `,\n                  filter: `brightness(${1 + energyLevel})`\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500\",\n                  style: {\n                    boxShadow: `0 0 ${30 + ringIntensity}px #fbbf24, 0 0 ${60 + ringIntensity * 2}px #f97316`,\n                    transform: `scale(${1 + energyLevel * 0.3})`,\n                    filter: `brightness(${1 + energyLevel})`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), [...Array(4)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute rounded-full border opacity-30 pointer-events-none\",\n          style: {\n            inset: `${-30 - i * 40}px`,\n            borderColor: ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i],\n            borderWidth: '1px',\n            transform: `\n                  rotateX(${rotation.x * (0.2 + i * 0.1)}deg) \n                  rotateY(${rotation.y * (0.3 + i * 0.1)}deg)\n                  rotateZ(${rotation.z * (0.1 + i * 0.05)}deg)\n                `,\n            boxShadow: `0 0 10px ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i]}`,\n            opacity: energyLevel * 0.6,\n            animation: `spin ${15 + i * 10}s linear infinite reverse`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6 text-sm text-gray-300 space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-400\",\n            children: \"\\u2728 Drag to manipulate quantum fields\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-purple-400\",\n            children: [\"Energy: \", Math.round(energyLevel * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-mono text-xs opacity-70\",\n          children: [\"X: \", Math.round(rotation.x), \"\\xB0 Y: \", Math.round(rotation.y), \"\\xB0 Z: \", Math.round(rotation.z), \"\\xB0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-pink-400 text-xs\",\n          children: [\"Active Particles: \", particles.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          from { transform: rotateZ(0deg); }\n          to { transform: rotateZ(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(QuantumGyroscope, \"kynW33Dc/kR0xUTtQnjII2qvQXA=\");\n_c = QuantumGyroscope;\nexport default QuantumGyroscope;\nvar _c;\n$RefreshReg$(_c, \"QuantumGyroscope\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "QuantumGyroscope", "_s", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "momentum", "setMomentum", "particles", "setParticles", "energyLevel", "setEnergyLevel", "lastMousePos", "animationRef", "particleIdRef", "gyroscopeRef", "colors", "initParticles", "newParticles", "i", "push", "id", "current", "Math", "random", "vx", "vy", "vz", "life", "maxLife", "color", "floor", "length", "size", "animate", "baseSpeed", "prev", "fluctuation", "sin", "Date", "now", "max", "min", "map", "particle", "centerForce", "dx", "dy", "dz", "orbitalForce", "orbitalVx", "orbitalVy", "turbulence", "turbVx", "turbVy", "turbVz", "newParticle", "sqrt", "angle", "PI", "radius", "cos", "requestAnimationFrame", "cancelAnimationFrame", "handleInteraction", "e", "isStart", "_gyroscopeRef$current", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "mouseX", "clientX", "mouseY", "clientY", "deltaX", "deltaY", "intensity", "rotationSpeed", "getParticleStyle", "lifeRatio", "opacity", "distance", "perspective", "scale", "abs", "position", "transform", "background", "borderRadius", "boxShadow", "pointerEvents", "zIndex", "round", "filter", "ringIntensity", "className", "children", "Array", "_", "style", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textShadow", "ref", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "transition", "borderColor", "inset", "borderWidth", "animation", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\ninterface ParticleState {\n  id: number;\n  x: number;\n  y: number;\n  z: number;\n  vx: number;\n  vy: number;\n  vz: number;\n  life: number;\n  maxLife: number;\n  color: string;\n  size: number;\n}\n\nconst QuantumGyroscope = () => {\n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [particles, setParticles] = useState<ParticleState[]>([]);\n  const [energyLevel, setEnergyLevel] = useState(0.5);\n  \n  const lastMousePos = useRef({ x: 0, y: 0 });\n  const animationRef = useRef<number>();\n  const particleIdRef = useRef(0);\n  const gyroscopeRef = useRef<HTMLDivElement>(null);\n\n  // Initialize enhanced particle system\n  useEffect(() => {\n    const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n    const initParticles = () => {\n      const newParticles: ParticleState[] = [];\n      for (let i = 0; i < 40; i++) {\n        newParticles.push({\n          id: particleIdRef.current++,\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          z: (Math.random() - 0.5) * 150,\n          vx: (Math.random() - 0.5) * 4,\n          vy: (Math.random() - 0.5) * 4,\n          vz: (Math.random() - 0.5) * 2,\n          life: Math.random() * 120,\n          maxLife: 120 + Math.random() * 80,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          size: 2 + Math.random() * 4\n        });\n      }\n      setParticles(newParticles);\n    };\n    initParticles();\n  }, []);\n\n  // Enhanced animation loop\n  useEffect(() => {\n    const animate = () => {\n      // Gyroscope rotation with dynamic energy\n      if (!isDragging) {\n        const baseSpeed = energyLevel * 2;\n        setRotation(prev => ({\n          x: prev.x + momentum.x + baseSpeed * 0.4,\n          y: prev.y + momentum.y + baseSpeed * 0.6,\n          z: prev.z + momentum.z + baseSpeed * 0.3\n        }));\n        \n        setMomentum(prev => ({\n          x: prev.x * 0.99,\n          y: prev.y * 0.99,\n          z: prev.z * 0.99\n        }));\n\n        // Dynamic energy fluctuation\n        setEnergyLevel(prev => {\n          const fluctuation = Math.sin(Date.now() * 0.001) * 0.1;\n          return Math.max(0.2, Math.min(1, prev + fluctuation));\n        });\n      }\n\n      // Advanced particle physics\n      setParticles(prev => {\n        return prev.map(particle => {\n          // Gravitational pull towards center\n          const centerForce = 0.02;\n          const dx = -particle.x * centerForce;\n          const dy = -particle.y * centerForce;\n          const dz = -particle.z * centerForce;\n\n          // Orbital motion\n          const orbitalForce = 0.01;\n          const orbitalVx = -particle.y * orbitalForce;\n          const orbitalVy = particle.x * orbitalForce;\n\n          // Turbulence\n          const turbulence = energyLevel * 0.2;\n          const turbVx = (Math.random() - 0.5) * turbulence;\n          const turbVy = (Math.random() - 0.5) * turbulence;\n          const turbVz = (Math.random() - 0.5) * turbulence * 0.5;\n\n          const newParticle = {\n            ...particle,\n            vx: particle.vx * 0.98 + dx + orbitalVx + turbVx,\n            vy: particle.vy * 0.98 + dy + orbitalVy + turbVy,\n            vz: particle.vz * 0.95 + dz + turbVz,\n            x: particle.x + particle.vx,\n            y: particle.y + particle.vy,\n            z: particle.z + particle.vz,\n            life: particle.life + 1\n          };\n\n          // Respawn particles\n          if (newParticle.life > newParticle.maxLife || \n              Math.sqrt(newParticle.x**2 + newParticle.y**2 + newParticle.z**2) > 400) {\n            const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n            const angle = Math.random() * Math.PI * 2;\n            const radius = 50 + Math.random() * 100;\n            return {\n              id: particleIdRef.current++,\n              x: Math.cos(angle) * radius,\n              y: Math.sin(angle) * radius,\n              z: (Math.random() - 0.5) * 50,\n              vx: (Math.random() - 0.5) * 6,\n              vy: (Math.random() - 0.5) * 6,\n              vz: (Math.random() - 0.5) * 3,\n              life: 0,\n              maxLife: 120 + Math.random() * 80,\n              color: colors[Math.floor(Math.random() * colors.length)],\n              size: 2 + Math.random() * 4\n            };\n          }\n\n          return newParticle;\n        });\n      });\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    \n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum, energyLevel]);\n\n  const handleInteraction = (e: React.MouseEvent, isStart: boolean = false) => {\n    const rect = gyroscopeRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    if (isStart) {\n      setIsDragging(true);\n      lastMousePos.current = { x: e.clientX, y: e.clientY };\n    } else if (isDragging) {\n      const deltaX = e.clientX - lastMousePos.current.x;\n      const deltaY = e.clientY - lastMousePos.current.y;\n      const intensity = Math.sqrt(deltaX**2 + deltaY**2) / 100;\n      \n      setEnergyLevel(prev => Math.min(1, prev + intensity * 0.1));\n      \n      const rotationSpeed = 1.2;\n      setRotation(prev => ({\n        x: prev.x + deltaY * rotationSpeed,\n        y: prev.y + deltaX * rotationSpeed,\n        z: prev.z + (deltaX - deltaY) * 0.3\n      }));\n      \n      setMomentum({\n        x: deltaY * 0.2,\n        y: deltaX * 0.2,\n        z: (deltaX - deltaY) * 0.1\n      });\n      \n      lastMousePos.current = { x: e.clientX, y: e.clientY };\n    }\n  };\n\n  const getParticleStyle = (particle: ParticleState) => {\n    const lifeRatio = particle.life / particle.maxLife;\n    const opacity = Math.max(0.1, (1 - lifeRatio) * energyLevel);\n    const distance = Math.sqrt(particle.x**2 + particle.y**2 + particle.z**2);\n    const perspective = 600;\n    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 300);\n\n    return {\n      position: 'absolute' as const,\n      left: '50%',\n      top: '50%',\n      width: `${particle.size}px`,\n      height: `${particle.size}px`,\n      transform: `\n        translate(-50%, -50%)\n        translate3d(${particle.x * scale}px, ${particle.y * scale}px, 0px)\n        scale(${scale})\n      `,\n      opacity: opacity,\n      background: `radial-gradient(circle, ${particle.color} 0%, transparent 70%)`,\n      borderRadius: '50%',\n      boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,\n      pointerEvents: 'none' as const,\n      zIndex: Math.round(particle.z + 100),\n      filter: `brightness(${energyLevel + 0.5})`\n    };\n  };\n\n  const ringIntensity = energyLevel * 100;\n\n  return (\n    <div className=\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden relative\">\n      {/* Dynamic Background Energy Fields */}\n      <div className=\"absolute inset-0 opacity-40\">\n        {[...Array(5)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute rounded-full blur-3xl animate-pulse\"\n            style={{\n              width: `${200 + i * 100}px`,\n              height: `${200 + i * 100}px`,\n              left: `${20 + i * 15}%`,\n              top: `${10 + i * 20}%`,\n              background: `radial-gradient(circle, ${ \n                ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i] \n              }33, transparent)`,\n              animationDelay: `${i * 0.5}s`,\n              animationDuration: `${3 + i}s`,\n              transform: `rotate(${rotation.z * (i + 1)}deg) scale(${energyLevel})`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"text-center relative z-10\">\n        <h1 \n          className=\"text-5xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-pulse\"\n          style={{ \n            filter: `brightness(${1 + energyLevel})`,\n            textShadow: `0 0 20px rgba(59, 130, 246, ${energyLevel})`\n          }}\n        >\n          Quantum Gyroscope\n        </h1>\n        \n        <div \n          ref={gyroscopeRef}\n          className=\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\"\n          style={{ perspective: '1200px' }}\n          onMouseDown={(e) => handleInteraction(e, true)}\n          onMouseMove={(e) => handleInteraction(e)}\n          onMouseUp={() => setIsDragging(false)}\n          onMouseLeave={() => setIsDragging(false)}\n        >\n          {/* Enhanced Particle System */}\n          <div className=\"absolute inset-0 pointer-events-none\">\n            {particles.map(particle => (\n              <div key={particle.id} style={getParticleStyle(particle)} />\n            ))}\n          </div>\n\n          {/* Quantum Energy Rings */}\n          {/* Outer Ring */}\n          <div \n            className=\"absolute inset-0 rounded-full border-2\"\n            style={{\n              transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n              transition: isDragging ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              borderColor: '#3b82f6',\n              boxShadow: `\n                0 0 ${20 + ringIntensity}px #3b82f6,\n                inset 0 0 ${20 + ringIntensity}px #3b82f6aa,\n                0 0 ${40 + ringIntensity * 2}px #3b82f6aa\n              `,\n              filter: `brightness(${1 + energyLevel * 0.5})`\n            }}\n          >\n            {/* Middle Ring */}\n            <div \n              className=\"absolute inset-8 rounded-full border-2\"\n              style={{\n                transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n                borderColor: '#8b5cf6',\n                boxShadow: `\n                  0 0 ${30 + ringIntensity}px #8b5cf6,\n                  inset 0 0 ${30 + ringIntensity}px #8b5cf6aa\n                `\n              }}\n            >\n              {/* Inner Ring */}\n              <div \n                className=\"absolute inset-8 rounded-full border-2\"\n                style={{\n                  transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                  borderColor: '#ec4899',\n                  boxShadow: `\n                    0 0 ${40 + ringIntensity}px #ec4899,\n                    inset 0 0 ${40 + ringIntensity}px #ec4899aa\n                  `\n                }}\n              >\n                {/* Quantum Core */}\n                <div \n                  className=\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\"\n                  style={{\n                    transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                    boxShadow: `\n                      0 0 ${60 + ringIntensity * 2}px #ffffff,\n                      inset 0 0 ${20 + ringIntensity}px #3b82f688\n                    `,\n                    filter: `brightness(${1 + energyLevel})`\n                  }}\n                >\n                  {/* Energy Center */}\n                  <div \n                    className=\"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500\"\n                    style={{\n                      boxShadow: `0 0 ${30 + ringIntensity}px #fbbf24, 0 0 ${60 + ringIntensity * 2}px #f97316`,\n                      transform: `scale(${1 + energyLevel * 0.3})`,\n                      filter: `brightness(${1 + energyLevel})`\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Dynamic Orbital Rings */}\n          {[...Array(4)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute rounded-full border opacity-30 pointer-events-none\"\n              style={{\n                inset: `${-30 - i * 40}px`,\n                borderColor: ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i],\n                borderWidth: '1px',\n                transform: `\n                  rotateX(${rotation.x * (0.2 + i * 0.1)}deg) \n                  rotateY(${rotation.y * (0.3 + i * 0.1)}deg)\n                  rotateZ(${rotation.z * (0.1 + i * 0.05)}deg)\n                `,\n                boxShadow: `0 0 10px ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i]}`,\n                opacity: energyLevel * 0.6,\n                animation: `spin ${15 + i * 10}s linear infinite reverse`\n              }}\n            />\n          ))}\n        </div>\n        \n        <div className=\"mt-6 text-sm text-gray-300 space-y-2\">\n          <div className=\"flex justify-center items-center space-x-4\">\n            <span className=\"text-blue-400\">✨ Drag to manipulate quantum fields</span>\n            <span className=\"text-purple-400\">Energy: {Math.round(energyLevel * 100)}%</span>\n          </div>\n          <p className=\"font-mono text-xs opacity-70\">\n            X: {Math.round(rotation.x)}° Y: {Math.round(rotation.y)}° Z: {Math.round(rotation.z)}°\n          </p>\n          <p className=\"text-pink-400 text-xs\">Active Particles: {particles.length}</p>\n        </div>\n      </div>\n\n      <style>{`\n        @keyframes spin {\n          from { transform: rotateZ(0deg); }\n          to { transform: rotateZ(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default QuantumGyroscope;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBpD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAgB;IAAES,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC7E,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAkB,EAAE,CAAC;EAC/D,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,GAAG,CAAC;EAEnD,MAAMoB,YAAY,GAAGlB,MAAM,CAAC;IAAEO,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC3C,MAAMW,YAAY,GAAGnB,MAAM,CAAS,CAAC;EACrC,MAAMoB,aAAa,GAAGpB,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAMqB,YAAY,GAAGrB,MAAM,CAAiB,IAAI,CAAC;;EAEjD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMuB,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IACjF,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,YAA6B,GAAG,EAAE;MACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,YAAY,CAACE,IAAI,CAAC;UAChBC,EAAE,EAAEP,aAAa,CAACQ,OAAO,EAAE;UAC3BrB,CAAC,EAAE,CAACsB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BtB,CAAC,EAAE,CAACqB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BrB,CAAC,EAAE,CAACoB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAC9BC,EAAE,EAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BE,EAAE,EAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BG,EAAE,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;UAC7BI,IAAI,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBK,OAAO,EAAE,GAAG,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACjCM,KAAK,EAAEd,MAAM,CAACO,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGR,MAAM,CAACgB,MAAM,CAAC,CAAC;UACxDC,IAAI,EAAE,CAAC,GAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACAf,YAAY,CAACS,YAAY,CAAC;IAC5B,CAAC;IACDD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMyC,OAAO,GAAGA,CAAA,KAAM;MACpB;MACA,IAAI,CAAC9B,UAAU,EAAE;QACf,MAAM+B,SAAS,GAAGzB,WAAW,GAAG,CAAC;QACjCV,WAAW,CAACoC,IAAI,KAAK;UACnBnC,CAAC,EAAEmC,IAAI,CAACnC,CAAC,GAAGK,QAAQ,CAACL,CAAC,GAAGkC,SAAS,GAAG,GAAG;UACxCjC,CAAC,EAAEkC,IAAI,CAAClC,CAAC,GAAGI,QAAQ,CAACJ,CAAC,GAAGiC,SAAS,GAAG,GAAG;UACxChC,CAAC,EAAEiC,IAAI,CAACjC,CAAC,GAAGG,QAAQ,CAACH,CAAC,GAAGgC,SAAS,GAAG;QACvC,CAAC,CAAC,CAAC;QAEH5B,WAAW,CAAC6B,IAAI,KAAK;UACnBnC,CAAC,EAAEmC,IAAI,CAACnC,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAEkC,IAAI,CAAClC,CAAC,GAAG,IAAI;UAChBC,CAAC,EAAEiC,IAAI,CAACjC,CAAC,GAAG;QACd,CAAC,CAAC,CAAC;;QAEH;QACAQ,cAAc,CAACyB,IAAI,IAAI;UACrB,MAAMC,WAAW,GAAGd,IAAI,CAACe,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG;UACtD,OAAOjB,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAElB,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEN,IAAI,GAAGC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC;MACJ;;MAEA;MACA5B,YAAY,CAAC2B,IAAI,IAAI;QACnB,OAAOA,IAAI,CAACO,GAAG,CAACC,QAAQ,IAAI;UAC1B;UACA,MAAMC,WAAW,GAAG,IAAI;UACxB,MAAMC,EAAE,GAAG,CAACF,QAAQ,CAAC3C,CAAC,GAAG4C,WAAW;UACpC,MAAME,EAAE,GAAG,CAACH,QAAQ,CAAC1C,CAAC,GAAG2C,WAAW;UACpC,MAAMG,EAAE,GAAG,CAACJ,QAAQ,CAACzC,CAAC,GAAG0C,WAAW;;UAEpC;UACA,MAAMI,YAAY,GAAG,IAAI;UACzB,MAAMC,SAAS,GAAG,CAACN,QAAQ,CAAC1C,CAAC,GAAG+C,YAAY;UAC5C,MAAME,SAAS,GAAGP,QAAQ,CAAC3C,CAAC,GAAGgD,YAAY;;UAE3C;UACA,MAAMG,UAAU,GAAG1C,WAAW,GAAG,GAAG;UACpC,MAAM2C,MAAM,GAAG,CAAC9B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI4B,UAAU;UACjD,MAAME,MAAM,GAAG,CAAC/B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI4B,UAAU;UACjD,MAAMG,MAAM,GAAG,CAAChC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI4B,UAAU,GAAG,GAAG;UAEvD,MAAMI,WAAW,GAAG;YAClB,GAAGZ,QAAQ;YACXnB,EAAE,EAAEmB,QAAQ,CAACnB,EAAE,GAAG,IAAI,GAAGqB,EAAE,GAAGI,SAAS,GAAGG,MAAM;YAChD3B,EAAE,EAAEkB,QAAQ,CAAClB,EAAE,GAAG,IAAI,GAAGqB,EAAE,GAAGI,SAAS,GAAGG,MAAM;YAChD3B,EAAE,EAAEiB,QAAQ,CAACjB,EAAE,GAAG,IAAI,GAAGqB,EAAE,GAAGO,MAAM;YACpCtD,CAAC,EAAE2C,QAAQ,CAAC3C,CAAC,GAAG2C,QAAQ,CAACnB,EAAE;YAC3BvB,CAAC,EAAE0C,QAAQ,CAAC1C,CAAC,GAAG0C,QAAQ,CAAClB,EAAE;YAC3BvB,CAAC,EAAEyC,QAAQ,CAACzC,CAAC,GAAGyC,QAAQ,CAACjB,EAAE;YAC3BC,IAAI,EAAEgB,QAAQ,CAAChB,IAAI,GAAG;UACxB,CAAC;;UAED;UACA,IAAI4B,WAAW,CAAC5B,IAAI,GAAG4B,WAAW,CAAC3B,OAAO,IACtCN,IAAI,CAACkC,IAAI,CAACD,WAAW,CAACvD,CAAC,IAAE,CAAC,GAAGuD,WAAW,CAACtD,CAAC,IAAE,CAAC,GAAGsD,WAAW,CAACrD,CAAC,IAAE,CAAC,CAAC,GAAG,GAAG,EAAE;YAC3E,MAAMa,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACjF,MAAM0C,KAAK,GAAGnC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGD,IAAI,CAACoC,EAAE,GAAG,CAAC;YACzC,MAAMC,MAAM,GAAG,EAAE,GAAGrC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;YACvC,OAAO;cACLH,EAAE,EAAEP,aAAa,CAACQ,OAAO,EAAE;cAC3BrB,CAAC,EAAEsB,IAAI,CAACsC,GAAG,CAACH,KAAK,CAAC,GAAGE,MAAM;cAC3B1D,CAAC,EAAEqB,IAAI,CAACe,GAAG,CAACoB,KAAK,CAAC,GAAGE,MAAM;cAC3BzD,CAAC,EAAE,CAACoB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;cAC7BC,EAAE,EAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;cAC7BE,EAAE,EAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;cAC7BG,EAAE,EAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;cAC7BI,IAAI,EAAE,CAAC;cACPC,OAAO,EAAE,GAAG,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;cACjCM,KAAK,EAAEd,MAAM,CAACO,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGR,MAAM,CAACgB,MAAM,CAAC,CAAC;cACxDC,IAAI,EAAE,CAAC,GAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;YAC5B,CAAC;UACH;UAEA,OAAOgC,WAAW;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF3C,YAAY,CAACS,OAAO,GAAGwC,qBAAqB,CAAC5B,OAAO,CAAC;IACvD,CAAC;IAEDrB,YAAY,CAACS,OAAO,GAAGwC,qBAAqB,CAAC5B,OAAO,CAAC;IACrD,OAAO,MAAM;MACX,IAAIrB,YAAY,CAACS,OAAO,EAAEyC,oBAAoB,CAAClD,YAAY,CAACS,OAAO,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAAClB,UAAU,EAAEE,QAAQ,EAAEI,WAAW,CAAC,CAAC;EAEvC,MAAMsD,iBAAiB,GAAGA,CAACC,CAAmB,EAAEC,OAAgB,GAAG,KAAK,KAAK;IAAA,IAAAC,qBAAA;IAC3E,MAAMC,IAAI,IAAAD,qBAAA,GAAGpD,YAAY,CAACO,OAAO,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBE,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACD,IAAI,EAAE;IAEX,MAAME,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;IAC1C,MAAMC,MAAM,GAAGX,CAAC,CAACY,OAAO,GAAGP,OAAO;IAClC,MAAMQ,MAAM,GAAGb,CAAC,CAACc,OAAO,GAAGN,OAAO;IAElC,IAAIP,OAAO,EAAE;MACX7D,aAAa,CAAC,IAAI,CAAC;MACnBO,YAAY,CAACU,OAAO,GAAG;QAAErB,CAAC,EAAEgE,CAAC,CAACY,OAAO;QAAE3E,CAAC,EAAE+D,CAAC,CAACc;MAAQ,CAAC;IACvD,CAAC,MAAM,IAAI3E,UAAU,EAAE;MACrB,MAAM4E,MAAM,GAAGf,CAAC,CAACY,OAAO,GAAGjE,YAAY,CAACU,OAAO,CAACrB,CAAC;MACjD,MAAMgF,MAAM,GAAGhB,CAAC,CAACc,OAAO,GAAGnE,YAAY,CAACU,OAAO,CAACpB,CAAC;MACjD,MAAMgF,SAAS,GAAG3D,IAAI,CAACkC,IAAI,CAACuB,MAAM,IAAE,CAAC,GAAGC,MAAM,IAAE,CAAC,CAAC,GAAG,GAAG;MAExDtE,cAAc,CAACyB,IAAI,IAAIb,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEN,IAAI,GAAG8C,SAAS,GAAG,GAAG,CAAC,CAAC;MAE3D,MAAMC,aAAa,GAAG,GAAG;MACzBnF,WAAW,CAACoC,IAAI,KAAK;QACnBnC,CAAC,EAAEmC,IAAI,CAACnC,CAAC,GAAGgF,MAAM,GAAGE,aAAa;QAClCjF,CAAC,EAAEkC,IAAI,CAAClC,CAAC,GAAG8E,MAAM,GAAGG,aAAa;QAClChF,CAAC,EAAEiC,IAAI,CAACjC,CAAC,GAAG,CAAC6E,MAAM,GAAGC,MAAM,IAAI;MAClC,CAAC,CAAC,CAAC;MAEH1E,WAAW,CAAC;QACVN,CAAC,EAAEgF,MAAM,GAAG,GAAG;QACf/E,CAAC,EAAE8E,MAAM,GAAG,GAAG;QACf7E,CAAC,EAAE,CAAC6E,MAAM,GAAGC,MAAM,IAAI;MACzB,CAAC,CAAC;MAEFrE,YAAY,CAACU,OAAO,GAAG;QAAErB,CAAC,EAAEgE,CAAC,CAACY,OAAO;QAAE3E,CAAC,EAAE+D,CAAC,CAACc;MAAQ,CAAC;IACvD;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAIxC,QAAuB,IAAK;IACpD,MAAMyC,SAAS,GAAGzC,QAAQ,CAAChB,IAAI,GAAGgB,QAAQ,CAACf,OAAO;IAClD,MAAMyD,OAAO,GAAG/D,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG4C,SAAS,IAAI3E,WAAW,CAAC;IAC5D,MAAM6E,QAAQ,GAAGhE,IAAI,CAACkC,IAAI,CAACb,QAAQ,CAAC3C,CAAC,IAAE,CAAC,GAAG2C,QAAQ,CAAC1C,CAAC,IAAE,CAAC,GAAG0C,QAAQ,CAACzC,CAAC,IAAE,CAAC,CAAC;IACzE,MAAMqF,WAAW,GAAG,GAAG;IACvB,MAAMC,KAAK,GAAGlE,IAAI,CAACkB,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGlB,IAAI,CAACmE,GAAG,CAAC9C,QAAQ,CAACzC,CAAC,CAAC,GAAG,GAAG,CAAC;IAE3D,OAAO;MACLwF,QAAQ,EAAE,UAAmB;MAC7BpB,IAAI,EAAE,KAAK;MACXG,GAAG,EAAE,KAAK;MACVF,KAAK,EAAE,GAAG5B,QAAQ,CAACX,IAAI,IAAI;MAC3B0C,MAAM,EAAE,GAAG/B,QAAQ,CAACX,IAAI,IAAI;MAC5B2D,SAAS,EAAE;AACjB;AACA,sBAAsBhD,QAAQ,CAAC3C,CAAC,GAAGwF,KAAK,OAAO7C,QAAQ,CAAC1C,CAAC,GAAGuF,KAAK;AACjE,gBAAgBA,KAAK;AACrB,OAAO;MACDH,OAAO,EAAEA,OAAO;MAChBO,UAAU,EAAE,2BAA2BjD,QAAQ,CAACd,KAAK,uBAAuB;MAC5EgE,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,OAAOnD,QAAQ,CAACX,IAAI,GAAG,CAAC,MAAMW,QAAQ,CAACd,KAAK,EAAE;MACzDkE,aAAa,EAAE,MAAe;MAC9BC,MAAM,EAAE1E,IAAI,CAAC2E,KAAK,CAACtD,QAAQ,CAACzC,CAAC,GAAG,GAAG,CAAC;MACpCgG,MAAM,EAAE,cAAczF,WAAW,GAAG,GAAG;IACzC,CAAC;EACH,CAAC;EAED,MAAM0F,aAAa,GAAG1F,WAAW,GAAG,GAAG;EAEvC,oBACEd,OAAA;IAAKyG,SAAS,EAAC,4IAA4I;IAAAC,QAAA,gBAEzJ1G,OAAA;MAAKyG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,EACzC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAAC6D,CAAC,EAAErF,CAAC,kBACtBvB,OAAA;QAEEyG,SAAS,EAAC,8CAA8C;QACxDI,KAAK,EAAE;UACLjC,KAAK,EAAE,GAAG,GAAG,GAAGrD,CAAC,GAAG,GAAG,IAAI;UAC3BwD,MAAM,EAAE,GAAG,GAAG,GAAGxD,CAAC,GAAG,GAAG,IAAI;UAC5BoD,IAAI,EAAE,GAAG,EAAE,GAAGpD,CAAC,GAAG,EAAE,GAAG;UACvBuD,GAAG,EAAE,GAAG,EAAE,GAAGvD,CAAC,GAAG,EAAE,GAAG;UACtB0E,UAAU,EAAE,2BACV,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC1E,CAAC,CAAC,kBAC1C;UAClBuF,cAAc,EAAE,GAAGvF,CAAC,GAAG,GAAG,GAAG;UAC7BwF,iBAAiB,EAAE,GAAG,CAAC,GAAGxF,CAAC,GAAG;UAC9ByE,SAAS,EAAE,UAAU7F,QAAQ,CAACI,CAAC,IAAIgB,CAAC,GAAG,CAAC,CAAC,cAAcT,WAAW;QACpE;MAAE,GAbGS,CAAC;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnH,OAAA;MAAKyG,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC1G,OAAA;QACEyG,SAAS,EAAC,+HAA+H;QACzII,KAAK,EAAE;UACLN,MAAM,EAAE,cAAc,CAAC,GAAGzF,WAAW,GAAG;UACxCsG,UAAU,EAAE,+BAA+BtG,WAAW;QACxD,CAAE;QAAA4F,QAAA,EACH;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnH,OAAA;QACEqH,GAAG,EAAElG,YAAa;QAClBsF,SAAS,EAAC,2EAA2E;QACrFI,KAAK,EAAE;UAAEjB,WAAW,EAAE;QAAS,CAAE;QACjC0B,WAAW,EAAGjD,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE,IAAI,CAAE;QAC/CkD,WAAW,EAAGlD,CAAC,IAAKD,iBAAiB,CAACC,CAAC,CAAE;QACzCmD,SAAS,EAAEA,CAAA,KAAM/G,aAAa,CAAC,KAAK,CAAE;QACtCgH,YAAY,EAAEA,CAAA,KAAMhH,aAAa,CAAC,KAAK,CAAE;QAAAiG,QAAA,gBAGzC1G,OAAA;UAAKyG,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAClD9F,SAAS,CAACmC,GAAG,CAACC,QAAQ,iBACrBhD,OAAA;YAAuB6G,KAAK,EAAErB,gBAAgB,CAACxC,QAAQ;UAAE,GAA/CA,QAAQ,CAACvB,EAAE;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAsC,CAC5D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAINnH,OAAA;UACEyG,SAAS,EAAC,wCAAwC;UAClDI,KAAK,EAAE;YACLb,SAAS,EAAE,WAAW7F,QAAQ,CAACE,CAAC,gBAAgBF,QAAQ,CAACG,CAAC,gBAAgBH,QAAQ,CAACI,CAAC,MAAM;YAC1FmH,UAAU,EAAElH,UAAU,GAAG,MAAM,GAAG,6CAA6C;YAC/EmH,WAAW,EAAE,SAAS;YACtBxB,SAAS,EAAE;AACzB,sBAAsB,EAAE,GAAGK,aAAa;AACxC,4BAA4B,EAAE,GAAGA,aAAa;AAC9C,sBAAsB,EAAE,GAAGA,aAAa,GAAG,CAAC;AAC5C,eAAe;YACDD,MAAM,EAAE,cAAc,CAAC,GAAGzF,WAAW,GAAG,GAAG;UAC7C,CAAE;UAAA4F,QAAA,eAGF1G,OAAA;YACEyG,SAAS,EAAC,wCAAwC;YAClDI,KAAK,EAAE;cACLb,SAAS,EAAE,WAAW7F,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,CAAC,MAAM;cAC3GoH,WAAW,EAAE,SAAS;cACtBxB,SAAS,EAAE;AAC3B,wBAAwB,EAAE,GAAGK,aAAa;AAC1C,8BAA8B,EAAE,GAAGA,aAAa;AAChD;YACc,CAAE;YAAAE,QAAA,eAGF1G,OAAA;cACEyG,SAAS,EAAC,wCAAwC;cAClDI,KAAK,EAAE;gBACLb,SAAS,EAAE,WAAW7F,QAAQ,CAACE,CAAC,GAAG,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,GAAG,MAAM;gBAC5GoH,WAAW,EAAE,SAAS;gBACtBxB,SAAS,EAAE;AAC7B,0BAA0B,EAAE,GAAGK,aAAa;AAC5C,gCAAgC,EAAE,GAAGA,aAAa;AAClD;cACgB,CAAE;cAAAE,QAAA,eAGF1G,OAAA;gBACEyG,SAAS,EAAC,uFAAuF;gBACjGI,KAAK,EAAE;kBACLb,SAAS,EAAE,WAAW7F,QAAQ,CAACE,CAAC,GAAG,CAAC,GAAG,gBAAgBF,QAAQ,CAACG,CAAC,GAAG,CAAC,GAAG,gBAAgBH,QAAQ,CAACI,CAAC,GAAG,CAAC,MAAM;kBAC5G4F,SAAS,EAAE;AAC/B,4BAA4B,EAAE,GAAGK,aAAa,GAAG,CAAC;AAClD,kCAAkC,EAAE,GAAGA,aAAa;AACpD,qBAAqB;kBACDD,MAAM,EAAE,cAAc,CAAC,GAAGzF,WAAW;gBACvC,CAAE;gBAAA4F,QAAA,eAGF1G,OAAA;kBACEyG,SAAS,EAAC,uHAAuH;kBACjII,KAAK,EAAE;oBACLV,SAAS,EAAE,OAAO,EAAE,GAAGK,aAAa,mBAAmB,EAAE,GAAGA,aAAa,GAAG,CAAC,YAAY;oBACzFR,SAAS,EAAE,SAAS,CAAC,GAAGlF,WAAW,GAAG,GAAG,GAAG;oBAC5CyF,MAAM,EAAE,cAAc,CAAC,GAAGzF,WAAW;kBACvC;gBAAE;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC,GAAGR,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC5D,GAAG,CAAC,CAAC6D,CAAC,EAAErF,CAAC,kBACtBvB,OAAA;UAEEyG,SAAS,EAAC,6DAA6D;UACvEI,KAAK,EAAE;YACLe,KAAK,EAAE,GAAG,CAAC,EAAE,GAAGrG,CAAC,GAAG,EAAE,IAAI;YAC1BoG,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACpG,CAAC,CAAC;YAC5DsG,WAAW,EAAE,KAAK;YAClB7B,SAAS,EAAE;AAC3B,4BAA4B7F,QAAQ,CAACE,CAAC,IAAI,GAAG,GAAGkB,CAAC,GAAG,GAAG,CAAC;AACxD,4BAA4BpB,QAAQ,CAACG,CAAC,IAAI,GAAG,GAAGiB,CAAC,GAAG,GAAG,CAAC;AACxD,4BAA4BpB,QAAQ,CAACI,CAAC,IAAI,GAAG,GAAGgB,CAAC,GAAG,IAAI,CAAC;AACzD,iBAAiB;YACD4E,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC5E,CAAC,CAAC,EAAE;YACxEmE,OAAO,EAAE5E,WAAW,GAAG,GAAG;YAC1BgH,SAAS,EAAE,QAAQ,EAAE,GAAGvG,CAAC,GAAG,EAAE;UAChC;QAAE,GAdGA,CAAC;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeP,CACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnH,OAAA;QAAKyG,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnD1G,OAAA;UAAKyG,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzD1G,OAAA;YAAMyG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAmC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1EnH,OAAA;YAAMyG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,GAAC,UAAQ,EAAC/E,IAAI,CAAC2E,KAAK,CAACxF,WAAW,GAAG,GAAG,CAAC,EAAC,GAAC;UAAA;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNnH,OAAA;UAAGyG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,GAAC,KACvC,EAAC/E,IAAI,CAAC2E,KAAK,CAACnG,QAAQ,CAACE,CAAC,CAAC,EAAC,UAAK,EAACsB,IAAI,CAAC2E,KAAK,CAACnG,QAAQ,CAACG,CAAC,CAAC,EAAC,UAAK,EAACqB,IAAI,CAAC2E,KAAK,CAACnG,QAAQ,CAACI,CAAC,CAAC,EAAC,MACvF;QAAA;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJnH,OAAA;UAAGyG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAC,oBAAkB,EAAC9F,SAAS,CAACwB,MAAM;QAAA;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnH,OAAA;MAAA0G,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACjH,EAAA,CAhWID,gBAAgB;AAAA8H,EAAA,GAAhB9H,gBAAgB;AAkWtB,eAAeA,gBAAgB;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}