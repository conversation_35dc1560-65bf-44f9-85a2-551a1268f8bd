{"ast": null, "code": "import React from'react';import Gyroscope from'../Xtra/Gyroscope';import'./GyroscopePage.css';import{jsx as _jsx}from\"react/jsx-runtime\";const GyroscopePage=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"gyroscope-page\",children:/*#__PURE__*/_jsx(Gyroscope,{})});};export default GyroscopePage;", "map": {"version": 3, "names": ["React", "Gyroscope", "jsx", "_jsx", "GyroscopePage", "className", "children"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/GyroscopePage.tsx"], "sourcesContent": ["import React from 'react';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport './GyroscopePage.css';\n\nconst GyroscopePage = () => {\n  return (\n    <div className=\"gyroscope-page\">\n      <Gyroscope />\n    </div>\n  );\n};\n\nexport default GyroscopePage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE7B,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,mBACED,IAAA,QAAKE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BH,IAAA,CAACF,SAAS,GAAE,CAAC,CACV,CAAC,CAEV,CAAC,CAED,cAAe,CAAAG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}