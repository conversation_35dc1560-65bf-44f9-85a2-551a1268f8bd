{"ast": null, "code": "// Cache implementation based on <PERSON>'s `lru-memoize`:\n// https://github.com/erikras/lru-memoize\nvar NOT_FOUND = 'NOT_FOUND';\nfunction createSingletonCache(equals) {\n  var entry;\n  return {\n    get: function get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put: function put(key, value) {\n      entry = {\n        key: key,\n        value: value\n      };\n    },\n    getEntries: function getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear: function clear() {\n      entry = undefined;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  var entries = [];\n  function get(key) {\n    var cacheIndex = entries.findIndex(function (entry) {\n      return equals(key, entry.key);\n    }); // We found a cached entry\n\n    if (cacheIndex > -1) {\n      var entry = entries[cacheIndex]; // Cached entry not at top of cache, move it to the top\n\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    } // No entry found in cache, return sentinel\n\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      // TODO Is unshift slow?\n      entries.unshift({\n        key: key,\n        value: value\n      });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return {\n    get: get,\n    put: put,\n    getEntries: getEntries,\n    clear: clear\n  };\n}\nexport var defaultEqualityCheck = function defaultEqualityCheck(a, b) {\n  return a === b;\n};\nexport function createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    } // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\n\n    var length = prev.length;\n    for (var i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\n// defaultMemoize now supports a configurable cache size with LRU behavior,\n// and optional comparison of the result value with existing values\nexport function defaultMemoize(func, equalityCheckOrOptions) {\n  var providedOptions = typeof equalityCheckOrOptions === 'object' ? equalityCheckOrOptions : {\n    equalityCheck: equalityCheckOrOptions\n  };\n  var _providedOptions$equa = providedOptions.equalityCheck,\n    equalityCheck = _providedOptions$equa === void 0 ? defaultEqualityCheck : _providedOptions$equa,\n    _providedOptions$maxS = providedOptions.maxSize,\n    maxSize = _providedOptions$maxS === void 0 ? 1 : _providedOptions$maxS,\n    resultEqualityCheck = providedOptions.resultEqualityCheck;\n  var comparator = createCacheKeyComparator(equalityCheck);\n  var cache = maxSize === 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator); // we reference arguments instead of spreading them for performance reasons\n\n  function memoized() {\n    var value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      // @ts-ignore\n      value = func.apply(null, arguments);\n      if (resultEqualityCheck) {\n        var entries = cache.getEntries();\n        var matchingEntry = entries.find(function (entry) {\n          return resultEqualityCheck(entry.value, value);\n        });\n        if (matchingEntry) {\n          value = matchingEntry.value;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = function () {\n    return cache.clear();\n  };\n  return memoized;\n}", "map": {"version": 3, "names": ["NOT_FOUND", "createSingletonCache", "equals", "entry", "get", "key", "value", "put", "getEntries", "clear", "undefined", "createLruCache", "maxSize", "entries", "cacheIndex", "findIndex", "splice", "unshift", "length", "pop", "defaultEqualityCheck", "a", "b", "createCacheKeyComparator", "equalityCheck", "areArgumentsShallowlyEqual", "prev", "next", "i", "defaultMemoize", "func", "equalityCheckOrOptions", "providedOptions", "_providedOptions$equa", "_providedOptions$maxS", "resultEqualityCheck", "comparator", "cache", "memoized", "arguments", "apply", "matchingEntry", "find", "clearCache"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/reselect/es/defaultMemoize.js"], "sourcesContent": ["// Cache implementation based on <PERSON>'s `lru-memoize`:\n// https://github.com/erikras/lru-memoize\nvar NOT_FOUND = 'NOT_FOUND';\n\nfunction createSingletonCache(equals) {\n  var entry;\n  return {\n    get: function get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n\n      return NOT_FOUND;\n    },\n    put: function put(key, value) {\n      entry = {\n        key: key,\n        value: value\n      };\n    },\n    getEntries: function getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear: function clear() {\n      entry = undefined;\n    }\n  };\n}\n\nfunction createLruCache(maxSize, equals) {\n  var entries = [];\n\n  function get(key) {\n    var cacheIndex = entries.findIndex(function (entry) {\n      return equals(key, entry.key);\n    }); // We found a cached entry\n\n    if (cacheIndex > -1) {\n      var entry = entries[cacheIndex]; // Cached entry not at top of cache, move it to the top\n\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n\n      return entry.value;\n    } // No entry found in cache, return sentinel\n\n\n    return NOT_FOUND;\n  }\n\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      // TODO Is unshift slow?\n      entries.unshift({\n        key: key,\n        value: value\n      });\n\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n\n  function getEntries() {\n    return entries;\n  }\n\n  function clear() {\n    entries = [];\n  }\n\n  return {\n    get: get,\n    put: put,\n    getEntries: getEntries,\n    clear: clear\n  };\n}\n\nexport var defaultEqualityCheck = function defaultEqualityCheck(a, b) {\n  return a === b;\n};\nexport function createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    } // Do this in a for loop (and not a `forEach` or an `every`) so we can determine equality as fast as possible.\n\n\n    var length = prev.length;\n\n    for (var i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n\n    return true;\n  };\n}\n// defaultMemoize now supports a configurable cache size with LRU behavior,\n// and optional comparison of the result value with existing values\nexport function defaultMemoize(func, equalityCheckOrOptions) {\n  var providedOptions = typeof equalityCheckOrOptions === 'object' ? equalityCheckOrOptions : {\n    equalityCheck: equalityCheckOrOptions\n  };\n  var _providedOptions$equa = providedOptions.equalityCheck,\n      equalityCheck = _providedOptions$equa === void 0 ? defaultEqualityCheck : _providedOptions$equa,\n      _providedOptions$maxS = providedOptions.maxSize,\n      maxSize = _providedOptions$maxS === void 0 ? 1 : _providedOptions$maxS,\n      resultEqualityCheck = providedOptions.resultEqualityCheck;\n  var comparator = createCacheKeyComparator(equalityCheck);\n  var cache = maxSize === 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator); // we reference arguments instead of spreading them for performance reasons\n\n  function memoized() {\n    var value = cache.get(arguments);\n\n    if (value === NOT_FOUND) {\n      // @ts-ignore\n      value = func.apply(null, arguments);\n\n      if (resultEqualityCheck) {\n        var entries = cache.getEntries();\n        var matchingEntry = entries.find(function (entry) {\n          return resultEqualityCheck(entry.value, value);\n        });\n\n        if (matchingEntry) {\n          value = matchingEntry.value;\n        }\n      }\n\n      cache.put(arguments, value);\n    }\n\n    return value;\n  }\n\n  memoized.clearCache = function () {\n    return cache.clear();\n  };\n\n  return memoized;\n}"], "mappings": "AAAA;AACA;AACA,IAAIA,SAAS,GAAG,WAAW;AAE3B,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EACpC,IAAIC,KAAK;EACT,OAAO;IACLC,GAAG,EAAE,SAASA,GAAGA,CAACC,GAAG,EAAE;MACrB,IAAIF,KAAK,IAAID,MAAM,CAACC,KAAK,CAACE,GAAG,EAAEA,GAAG,CAAC,EAAE;QACnC,OAAOF,KAAK,CAACG,KAAK;MACpB;MAEA,OAAON,SAAS;IAClB,CAAC;IACDO,GAAG,EAAE,SAASA,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;MAC5BH,KAAK,GAAG;QACNE,GAAG,EAAEA,GAAG;QACRC,KAAK,EAAEA;MACT,CAAC;IACH,CAAC;IACDE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChC,OAAOL,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,EAAE;IAC7B,CAAC;IACDM,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;MACtBN,KAAK,GAAGO,SAAS;IACnB;EACF,CAAC;AACH;AAEA,SAASC,cAAcA,CAACC,OAAO,EAAEV,MAAM,EAAE;EACvC,IAAIW,OAAO,GAAG,EAAE;EAEhB,SAAST,GAAGA,CAACC,GAAG,EAAE;IAChB,IAAIS,UAAU,GAAGD,OAAO,CAACE,SAAS,CAAC,UAAUZ,KAAK,EAAE;MAClD,OAAOD,MAAM,CAACG,GAAG,EAAEF,KAAK,CAACE,GAAG,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIS,UAAU,GAAG,CAAC,CAAC,EAAE;MACnB,IAAIX,KAAK,GAAGU,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC;;MAEjC,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClBD,OAAO,CAACG,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC;QAC7BD,OAAO,CAACI,OAAO,CAACd,KAAK,CAAC;MACxB;MAEA,OAAOA,KAAK,CAACG,KAAK;IACpB,CAAC,CAAC;;IAGF,OAAON,SAAS;EAClB;EAEA,SAASO,GAAGA,CAACF,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAIF,GAAG,CAACC,GAAG,CAAC,KAAKL,SAAS,EAAE;MAC1B;MACAa,OAAO,CAACI,OAAO,CAAC;QACdZ,GAAG,EAAEA,GAAG;QACRC,KAAK,EAAEA;MACT,CAAC,CAAC;MAEF,IAAIO,OAAO,CAACK,MAAM,GAAGN,OAAO,EAAE;QAC5BC,OAAO,CAACM,GAAG,CAAC,CAAC;MACf;IACF;EACF;EAEA,SAASX,UAAUA,CAAA,EAAG;IACpB,OAAOK,OAAO;EAChB;EAEA,SAASJ,KAAKA,CAAA,EAAG;IACfI,OAAO,GAAG,EAAE;EACd;EAEA,OAAO;IACLT,GAAG,EAAEA,GAAG;IACRG,GAAG,EAAEA,GAAG;IACRC,UAAU,EAAEA,UAAU;IACtBC,KAAK,EAAEA;EACT,CAAC;AACH;AAEA,OAAO,IAAIW,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpE,OAAOD,CAAC,KAAKC,CAAC;AAChB,CAAC;AACD,OAAO,SAASC,wBAAwBA,CAACC,aAAa,EAAE;EACtD,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACrD,IAAID,IAAI,KAAK,IAAI,IAAIC,IAAI,KAAK,IAAI,IAAID,IAAI,CAACR,MAAM,KAAKS,IAAI,CAACT,MAAM,EAAE;MACjE,OAAO,KAAK;IACd,CAAC,CAAC;;IAGF,IAAIA,MAAM,GAAGQ,IAAI,CAACR,MAAM;IAExB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC/B,IAAI,CAACJ,aAAa,CAACE,IAAI,CAACE,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,CAAC,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;IACF;IAEA,OAAO,IAAI;EACb,CAAC;AACH;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,sBAAsB,EAAE;EAC3D,IAAIC,eAAe,GAAG,OAAOD,sBAAsB,KAAK,QAAQ,GAAGA,sBAAsB,GAAG;IAC1FP,aAAa,EAAEO;EACjB,CAAC;EACD,IAAIE,qBAAqB,GAAGD,eAAe,CAACR,aAAa;IACrDA,aAAa,GAAGS,qBAAqB,KAAK,KAAK,CAAC,GAAGb,oBAAoB,GAAGa,qBAAqB;IAC/FC,qBAAqB,GAAGF,eAAe,CAACpB,OAAO;IAC/CA,OAAO,GAAGsB,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IACtEC,mBAAmB,GAAGH,eAAe,CAACG,mBAAmB;EAC7D,IAAIC,UAAU,GAAGb,wBAAwB,CAACC,aAAa,CAAC;EACxD,IAAIa,KAAK,GAAGzB,OAAO,KAAK,CAAC,GAAGX,oBAAoB,CAACmC,UAAU,CAAC,GAAGzB,cAAc,CAACC,OAAO,EAAEwB,UAAU,CAAC,CAAC,CAAC;;EAEpG,SAASE,QAAQA,CAAA,EAAG;IAClB,IAAIhC,KAAK,GAAG+B,KAAK,CAACjC,GAAG,CAACmC,SAAS,CAAC;IAEhC,IAAIjC,KAAK,KAAKN,SAAS,EAAE;MACvB;MACAM,KAAK,GAAGwB,IAAI,CAACU,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;MAEnC,IAAIJ,mBAAmB,EAAE;QACvB,IAAItB,OAAO,GAAGwB,KAAK,CAAC7B,UAAU,CAAC,CAAC;QAChC,IAAIiC,aAAa,GAAG5B,OAAO,CAAC6B,IAAI,CAAC,UAAUvC,KAAK,EAAE;UAChD,OAAOgC,mBAAmB,CAAChC,KAAK,CAACG,KAAK,EAAEA,KAAK,CAAC;QAChD,CAAC,CAAC;QAEF,IAAImC,aAAa,EAAE;UACjBnC,KAAK,GAAGmC,aAAa,CAACnC,KAAK;QAC7B;MACF;MAEA+B,KAAK,CAAC9B,GAAG,CAACgC,SAAS,EAAEjC,KAAK,CAAC;IAC7B;IAEA,OAAOA,KAAK;EACd;EAEAgC,QAAQ,CAACK,UAAU,GAAG,YAAY;IAChC,OAAON,KAAK,CAAC5B,KAAK,CAAC,CAAC;EACtB,CAAC;EAED,OAAO6B,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}