{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport GyroscopeButton from '../Xtra/GyroscopeButton';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreateVideoPage = () => {\n  _s();\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n  const [formData, setFormData] = useState({\n    lessonTitle: '',\n    studentName: '',\n    studentAge: '',\n    teacherVoice: '',\n    studentVoice: ''\n  });\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n  const formFields = [{\n    label: \"lesson title\",\n    type: \"text\",\n    placeholder: \"enter your video lesson title...\",\n    key: \"lessonTitle\"\n  }, {\n    label: \"student's first name\",\n    type: \"text\",\n    placeholder: \"All Of This Is Optional!!\",\n    key: \"studentName\"\n  }, {\n    label: \"student's age\",\n    type: \"select\",\n    options: [\"6\", \"7\", \"8\", \"9\"],\n    key: \"studentAge\"\n  }, {\n    label: \"teacher's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"],\n    key: \"teacherVoice\"\n  }, {\n    label: \"student's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"],\n    key: \"studentVoice\"\n  }];\n  const handleInputChange = (key, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    if (key === 'lessonTitle') {\n      setLessonTitle(value);\n    }\n  };\n  const handleGenerate = () => {\n    console.log('Generating video with data:', formData);\n    // Add your video generation logic here\n    // You could navigate to a different page, show loading state, etc.\n  };\n  const isFormValid = () => {\n    return formData.lessonTitle.trim().length >= 3;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"westworld-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"particles-container\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 3}s`,\n          animationDuration: `${2 + Math.random() * 2}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"milk-waves\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"100%\",\n        height: \"100%\",\n        viewBox: \"0 0 1000 1000\",\n        preserveAspectRatio: \"none\",\n        children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n          children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"milkGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"100%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#ffffff\",\n              stopOpacity: \"0.8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"50%\",\n              stopColor: \"#f8fafc\",\n              stopOpacity: \"0.6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#f1f5f9\",\n              stopOpacity: \"0.4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\",\n          fill: \"url(#milkGradient)\",\n          className: \"milk-surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `form-container ${formVisible ? 'visible' : 'hidden'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"main-title\",\n            children: \"CREATE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: [/*#__PURE__*/_jsxDEV(_Fragment, {\n            children: formFields.map((field, index) => {\n              var _field$options2;\n              // Special handling for student's first name and age on same line\n              if (index === 1) {\n                var _formFields$2$options;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `field-wrapper combined-fields ${childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n                  style: {\n                    transitionDelay: `${index * 200}ms`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"field-label\",\n                      children: field.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      placeholder: field.placeholder,\n                      className: \"field-input\",\n                      value: formData.studentName,\n                      onChange: e => handleInputChange('studentName', e.target.value)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"field-label\",\n                      children: formFields[2].label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      className: \"field-select\",\n                      value: formData.studentAge,\n                      onChange: e => handleInputChange('studentAge', e.target.value),\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: [\"Select \", formFields[2].label.toLowerCase(), \"...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 151,\n                        columnNumber: 27\n                      }, this), (_formFields$2$options = formFields[2].options) === null || _formFields$2$options === void 0 ? void 0 : _formFields$2$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: option,\n                        children: option\n                      }, optIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)]\n                }, `combined-${index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this);\n              }\n\n              // Skip the age field since it's included in the combined container\n              if (index === 2) return null;\n\n              // Special handling for teacher's voice and student's voice on same line\n              if (index === 3) {\n                var _field$options, _formFields$4$options;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `field-wrapper combined-fields ${childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n                  style: {\n                    transitionDelay: `${index * 200}ms`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"field-label\",\n                      children: field.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      className: \"field-select\",\n                      value: formData.teacherVoice,\n                      onChange: e => handleInputChange('teacherVoice', e.target.value),\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: [\"Select \", field.label.toLowerCase(), \"...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 27\n                      }, this), (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: option,\n                        children: option\n                      }, optIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"inline-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"field-label\",\n                      children: formFields[4].label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      className: \"field-select\",\n                      value: formData.studentVoice,\n                      onChange: e => handleInputChange('studentVoice', e.target.value),\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: [\"Select \", formFields[4].label.toLowerCase(), \"...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 199,\n                        columnNumber: 27\n                      }, this), (_formFields$4$options = formFields[4].options) === null || _formFields$4$options === void 0 ? void 0 : _formFields$4$options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: option,\n                        children: option\n                      }, optIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 29\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 23\n                  }, this)]\n                }, `combined-voices-${index}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this);\n              }\n\n              // Skip the student's voice field since it's included in the combined container\n              if (index === 4) return null;\n\n              // Regular field rendering\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `field-wrapper ${index === 0 ? 'emerged' : childFieldsVisible ? currentField > index ? 'emerged' : 'emerging' : 'hidden'}`,\n                style: {\n                  transitionDelay: `${index * 200}ms`\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"field-label\",\n                  children: field.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this), field.type === 'text' && /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: field.placeholder,\n                  className: \"field-input\",\n                  value: formData[field.key],\n                  onChange: e => handleInputChange(field.key, e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), field.type === 'select' && /*#__PURE__*/_jsxDEV(\"select\", {\n                  className: \"field-select\",\n                  value: formData[field.key],\n                  onChange: e => handleInputChange(field.key, e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: [\"Select \", field.label.toLowerCase(), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this), (_field$options2 = field.options) === null || _field$options2 === void 0 ? void 0 : _field$options2.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, optIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), field.type === 'textarea' && /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  placeholder: field.placeholder,\n                  rows: 4,\n                  className: \"field-textarea\",\n                  value: formData[field.key],\n                  onChange: e => handleInputChange(field.key, e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `gyroscope-wrapper ${childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(GyroscopeButton, {\n              onClick: handleGenerate,\n              disabled: !isFormValid(),\n              size: 140\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid-pattern\",\n        children: [...Array(400)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid-cell\"\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 42\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ambient-light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateVideoPage, \"TBrn8W/jdqY7zzTt+CMHwvYQSxo=\");\n_c = CreateVideoPage;\nexport default CreateVideoPage;\nvar _c;\n$RefreshReg$(_c, \"CreateVideoPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "GyroscopeButton", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateVideoPage", "_s", "formVisible", "setFormVisible", "current<PERSON><PERSON>", "setCurrentField", "lessonTitle", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childFieldsVisible", "<PERSON><PERSON><PERSON><PERSON>FieldsVisible", "formData", "setFormData", "studentName", "studentAge", "teacher<PERSON><PERSON><PERSON>", "studentVoice", "setTimeout", "length", "timer", "setInterval", "prev", "formFields", "clearInterval", "label", "type", "placeholder", "key", "options", "handleInputChange", "value", "handleGenerate", "console", "log", "isFormValid", "trim", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "preserveAspectRatio", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "d", "fill", "field", "index", "_field$options2", "_formFields$2$options", "transitionDelay", "onChange", "e", "target", "toLowerCase", "option", "optIndex", "_field$options", "_formFields$4$options", "rows", "onClick", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport GyroscopeButton from '../Xtra/GyroscopeButton';\n\nconst CreateVideoPage = () => {\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n  const [formData, setFormData] = useState({\n    lessonTitle: '',\n    studentName: '',\n    studentAge: '',\n    teacherVoice: '',\n    studentVoice: ''\n  });\n\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n\n  const formFields = [\n    { label: \"lesson title\", type: \"text\", placeholder: \"enter your video lesson title...\", key: \"lessonTitle\" },\n    { label: \"student's first name\", type: \"text\", placeholder: \"All Of This Is Optional!!\", key: \"studentName\" },\n    { label: \"student's age\", type: \"select\", options: [\"6\", \"7\", \"8\", \"9\"], key: \"studentAge\" },\n    { label: \"teacher's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"], key: \"teacherVoice\" },\n    { label: \"student's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"], key: \"studentVoice\" }\n  ];\n\n  const handleInputChange = (key: string, value: string) => {\n    setFormData(prev => ({ ...prev, [key]: value }));\n    if (key === 'lessonTitle') {\n      setLessonTitle(value);\n    }\n  };\n\n  const handleGenerate = () => {\n    console.log('Generating video with data:', formData);\n    // Add your video generation logic here\n    // You could navigate to a different page, show loading state, etc.\n  };\n\n  const isFormValid = () => {\n    return formData.lessonTitle.trim().length >= 3;\n  };\n\n  return (\n    <div className=\"westworld-container\">\n      {/* Floating particles for depth */}\n      <div className=\"particles-container\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"floating-particle\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${2 + Math.random() * 2}s`\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Milk-like surface waves */}\n      <div className=\"milk-waves\">\n        <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1000 1000\" preserveAspectRatio=\"none\">\n          <defs>\n            <linearGradient id=\"milkGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#ffffff\" stopOpacity=\"0.8\" />\n              <stop offset=\"50%\" stopColor=\"#f8fafc\" stopOpacity=\"0.6\" />\n              <stop offset=\"100%\" stopColor=\"#f1f5f9\" stopOpacity=\"0.4\" />\n            </linearGradient>\n          </defs>\n          <path\n            d=\"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\"\n            fill=\"url(#milkGradient)\"\n            className=\"milk-surface\"\n          />\n        </svg>\n      </div>\n\n      {/* Main content */}\n      <div className=\"content-wrapper\">\n        <div className={`form-container ${formVisible ? 'visible' : 'hidden'}`}>\n          {/* Title */}\n          <div className=\"title-section\">\n            <h1 className=\"main-title\">CREATE</h1>\n            <div className=\"title-line\"></div>\n          </div>\n\n          {/* Form */}\n          <div className=\"form-fields\">\n            <>\n              {formFields.map((field, index) => {\n                // Special handling for student's first name and age on same line\n                if (index === 1) {\n                  return (\n                    <div\n                      key={`combined-${index}`}\n                      className={`field-wrapper combined-fields ${\n                        childFieldsVisible\n                          ? currentField > index ? 'emerged' : 'emerging'\n                          : 'hidden'\n                      }`}\n                      style={{ transitionDelay: `${index * 200}ms` }}\n                    >\n                      {/* First Name Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{field.label}</label>\n                        <input\n                          type=\"text\"\n                          placeholder={field.placeholder}\n                          className=\"field-input\"\n                          value={formData.studentName}\n                          onChange={(e) => handleInputChange('studentName', e.target.value)}\n                        />\n                      </div>\n\n                      {/* Age Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{formFields[2].label}</label>\n                        <select \n                          className=\"field-select\"\n                          value={formData.studentAge}\n                          onChange={(e) => handleInputChange('studentAge', e.target.value)}\n                        >\n                          <option value=\"\">Select {formFields[2].label.toLowerCase()}...</option>\n                          {formFields[2].options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n                    </div>\n                  );\n                }\n\n                // Skip the age field since it's included in the combined container\n                if (index === 2) return null;\n\n                // Special handling for teacher's voice and student's voice on same line\n                if (index === 3) {\n                  return (\n                    <div\n                      key={`combined-voices-${index}`}\n                      className={`field-wrapper combined-fields ${\n                        childFieldsVisible\n                          ? currentField > index ? 'emerged' : 'emerging'\n                          : 'hidden'\n                      }`}\n                      style={{ transitionDelay: `${index * 200}ms` }}\n                    >\n                      {/* Teacher's Voice Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{field.label}</label>\n                        <select \n                          className=\"field-select\"\n                          value={formData.teacherVoice}\n                          onChange={(e) => handleInputChange('teacherVoice', e.target.value)}\n                        >\n                          <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                          {field.options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n\n                      {/* Student's Voice Field */}\n                      <div className=\"inline-field\">\n                        <label className=\"field-label\">{formFields[4].label}</label>\n                        <select \n                          className=\"field-select\"\n                          value={formData.studentVoice}\n                          onChange={(e) => handleInputChange('studentVoice', e.target.value)}\n                        >\n                          <option value=\"\">Select {formFields[4].label.toLowerCase()}...</option>\n                          {formFields[4].options?.map((option, optIndex) => (\n                            <option key={optIndex} value={option}>{option}</option>\n                          ))}\n                        </select>\n                      </div>\n                    </div>\n                  );\n                }\n\n                // Skip the student's voice field since it's included in the combined container\n                if (index === 4) return null;\n\n                // Regular field rendering\n                return (\n                  <div\n                    key={index}\n                    className={`field-wrapper ${\n                      index === 0\n                        ? 'emerged'\n                        : childFieldsVisible\n                        ? currentField > index ? 'emerged' : 'emerging'\n                        : 'hidden'\n                    }`}\n                    style={{ transitionDelay: `${index * 200}ms` }}\n                  >\n                    <label className=\"field-label\">{field.label}</label>\n                    {field.type === 'text' && (\n                      <input\n                        type=\"text\"\n                        placeholder={field.placeholder}\n                        className=\"field-input\"\n                        value={formData[field.key as keyof typeof formData]}\n                        onChange={(e) => handleInputChange(field.key, e.target.value)}\n                      />\n                    )}\n                    {field.type === 'select' && (\n                      <select \n                        className=\"field-select\"\n                        value={formData[field.key as keyof typeof formData]}\n                        onChange={(e) => handleInputChange(field.key, e.target.value)}\n                      >\n                        <option value=\"\">Select {field.label.toLowerCase()}...</option>\n                        {field.options?.map((option, optIndex) => (\n                          <option key={optIndex} value={option}>{option}</option>\n                        ))}\n                      </select>\n                    )}\n                    {field.type === 'textarea' && (\n                      <textarea\n                        placeholder={field.placeholder}\n                        rows={4}\n                        className=\"field-textarea\"\n                        value={formData[field.key as keyof typeof formData]}\n                        onChange={(e) => handleInputChange(field.key, e.target.value)}\n                      />\n                    )}\n                  </div>\n                );\n              })}\n            </>\n\n            {/* Gyroscope Generate Button */}\n            <div className={`gyroscope-wrapper ${\n              childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'\n            }`}>\n              <GyroscopeButton\n                onClick={handleGenerate}\n                disabled={!isFormValid()}\n                size={140}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Grid overlay and ambient light */}\n      <div className=\"grid-overlay\">\n        <div className=\"grid-pattern\">\n          {[...Array(400)].map((_, i) => <div key={i} className=\"grid-cell\"></div>)}\n        </div>\n      </div>\n      <div className=\"ambient-light\"></div>\n    </div>\n  );\n};\n\nexport default CreateVideoPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,uBAAuB;AAC9B,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCa,WAAW,EAAE,EAAE;IACfM,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFrB,SAAS,CAAC,MAAM;IACd;IACAsB,UAAU,CAAC,MAAM;MACfb,cAAc,CAAC,IAAI,CAAC;MACpBE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EAENX,SAAS,CAAC,MAAM;IACd,IAAIY,WAAW,CAACW,MAAM,IAAI,CAAC,EAAE;MAC3BR,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,MAAM;MACLA,qBAAqB,CAAC,KAAK,CAAC;MAC5BJ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EAEjBZ,SAAS,CAAC,MAAM;IACd,IAAIQ,WAAW,IAAIM,kBAAkB,EAAE;MACrC,MAAMU,KAAK,GAAGC,WAAW,CAAC,MAAM;QAC9Bd,eAAe,CAACe,IAAI,IAAIA,IAAI,GAAGC,UAAU,CAACJ,MAAM,GAAGG,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC;MACrE,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAME,aAAa,CAACJ,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAAChB,WAAW,EAAEM,kBAAkB,CAAC,CAAC;EAErC,MAAMa,UAAU,GAAG,CACjB;IAAEE,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE,kCAAkC;IAAEC,GAAG,EAAE;EAAc,CAAC,EAC5G;IAAEH,KAAK,EAAE,sBAAsB;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE,2BAA2B;IAAEC,GAAG,EAAE;EAAc,CAAC,EAC7G;IAAEH,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,QAAQ;IAAEG,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAAED,GAAG,EAAE;EAAa,CAAC,EAC5F;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEG,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IAAED,GAAG,EAAE;EAAe,CAAC,EAChH;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEG,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IAAED,GAAG,EAAE;EAAe,CAAC,CACjH;EAED,MAAME,iBAAiB,GAAGA,CAACF,GAAW,EAAEG,KAAa,KAAK;IACxDlB,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACM,GAAG,GAAGG;IAAM,CAAC,CAAC,CAAC;IAChD,IAAIH,GAAG,KAAK,aAAa,EAAE;MACzBnB,cAAc,CAACsB,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEtB,QAAQ,CAAC;IACpD;IACA;EACF,CAAC;EAED,MAAMuB,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAOvB,QAAQ,CAACJ,WAAW,CAAC4B,IAAI,CAAC,CAAC,CAACjB,MAAM,IAAI,CAAC;EAChD,CAAC;EAED,oBACEpB,OAAA;IAAKsC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCvC,OAAA;MAAKsC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EACjC,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB3C,OAAA;QAEEsC,SAAS,EAAC,mBAAmB;QAC7BM,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GAPGJ,CAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtD,OAAA;MAAKsC,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBvC,OAAA;QAAKuD,KAAK,EAAC,MAAM;QAACC,MAAM,EAAC,MAAM;QAACC,OAAO,EAAC,eAAe;QAACC,mBAAmB,EAAC,MAAM;QAAAnB,QAAA,gBAChFvC,OAAA;UAAAuC,QAAA,eACEvC,OAAA;YAAgB2D,EAAE,EAAC,cAAc;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAACC,EAAE,EAAC,MAAM;YAAAxB,QAAA,gBACnEvC,OAAA;cAAMgE,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DtD,OAAA;cAAMgE,MAAM,EAAC,KAAK;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtD,OAAA;cAAMgE,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACPtD,OAAA;UACEmE,CAAC,EAAC,wDAAwD;UAC1DC,IAAI,EAAC,oBAAoB;UACzB9B,SAAS,EAAC;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKsC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BvC,OAAA;QAAKsC,SAAS,EAAE,kBAAkBjC,WAAW,GAAG,SAAS,GAAG,QAAQ,EAAG;QAAAkC,QAAA,gBAErEvC,OAAA;UAAKsC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvC,OAAA;YAAIsC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCtD,OAAA;YAAKsC,SAAS,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGNtD,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvC,OAAA,CAAAE,SAAA;YAAAqC,QAAA,EACGf,UAAU,CAACiB,GAAG,CAAC,CAAC4B,KAAK,EAAEC,KAAK,KAAK;cAAA,IAAAC,eAAA;cAChC;cACA,IAAID,KAAK,KAAK,CAAC,EAAE;gBAAA,IAAAE,qBAAA;gBACf,oBACExE,OAAA;kBAEEsC,SAAS,EAAE,iCACT3B,kBAAkB,GACdJ,YAAY,GAAG+D,KAAK,GAAG,SAAS,GAAG,UAAU,GAC7C,QAAQ,EACX;kBACH1B,KAAK,EAAE;oBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;kBAAK,CAAE;kBAAA/B,QAAA,gBAG/CvC,OAAA;oBAAKsC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvC,OAAA;sBAAOsC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE8B,KAAK,CAAC3C;oBAAK;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpDtD,OAAA;sBACE2B,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAEyC,KAAK,CAACzC,WAAY;sBAC/BU,SAAS,EAAC,aAAa;sBACvBN,KAAK,EAAEnB,QAAQ,CAACE,WAAY;sBAC5B2D,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAAC,aAAa,EAAE4C,CAAC,CAACC,MAAM,CAAC5C,KAAK;oBAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAGNtD,OAAA;oBAAKsC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvC,OAAA;sBAAOsC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEf,UAAU,CAAC,CAAC,CAAC,CAACE;oBAAK;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5DtD,OAAA;sBACEsC,SAAS,EAAC,cAAc;sBACxBN,KAAK,EAAEnB,QAAQ,CAACG,UAAW;sBAC3B0D,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAAC,YAAY,EAAE4C,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;sBAAAO,QAAA,gBAEjEvC,OAAA;wBAAQgC,KAAK,EAAC,EAAE;wBAAAO,QAAA,GAAC,SAAO,EAACf,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACmD,WAAW,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,GAAAkB,qBAAA,GACtEhD,UAAU,CAAC,CAAC,CAAC,CAACM,OAAO,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuB/B,GAAG,CAAC,CAACqC,MAAM,EAAEC,QAAQ,kBAC3C/E,OAAA;wBAAuBgC,KAAK,EAAE8C,MAAO;wBAAAvC,QAAA,EAAEuC;sBAAM,GAAhCC,QAAQ;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiC,CACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAjCD,YAAYgB,KAAK,EAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkCrB,CAAC;cAEV;;cAEA;cACA,IAAIgB,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;;cAE5B;cACA,IAAIA,KAAK,KAAK,CAAC,EAAE;gBAAA,IAAAU,cAAA,EAAAC,qBAAA;gBACf,oBACEjF,OAAA;kBAEEsC,SAAS,EAAE,iCACT3B,kBAAkB,GACdJ,YAAY,GAAG+D,KAAK,GAAG,SAAS,GAAG,UAAU,GAC7C,QAAQ,EACX;kBACH1B,KAAK,EAAE;oBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;kBAAK,CAAE;kBAAA/B,QAAA,gBAG/CvC,OAAA;oBAAKsC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvC,OAAA;sBAAOsC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAE8B,KAAK,CAAC3C;oBAAK;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpDtD,OAAA;sBACEsC,SAAS,EAAC,cAAc;sBACxBN,KAAK,EAAEnB,QAAQ,CAACI,YAAa;sBAC7ByD,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAAC,cAAc,EAAE4C,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;sBAAAO,QAAA,gBAEnEvC,OAAA;wBAAQgC,KAAK,EAAC,EAAE;wBAAAO,QAAA,GAAC,SAAO,EAAC8B,KAAK,CAAC3C,KAAK,CAACmD,WAAW,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,GAAA0B,cAAA,GAC9DX,KAAK,CAACvC,OAAO,cAAAkD,cAAA,uBAAbA,cAAA,CAAevC,GAAG,CAAC,CAACqC,MAAM,EAAEC,QAAQ,kBACnC/E,OAAA;wBAAuBgC,KAAK,EAAE8C,MAAO;wBAAAvC,QAAA,EAAEuC;sBAAM,GAAhCC,QAAQ;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiC,CACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGNtD,OAAA;oBAAKsC,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BvC,OAAA;sBAAOsC,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAEf,UAAU,CAAC,CAAC,CAAC,CAACE;oBAAK;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5DtD,OAAA;sBACEsC,SAAS,EAAC,cAAc;sBACxBN,KAAK,EAAEnB,QAAQ,CAACK,YAAa;sBAC7BwD,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAAC,cAAc,EAAE4C,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;sBAAAO,QAAA,gBAEnEvC,OAAA;wBAAQgC,KAAK,EAAC,EAAE;wBAAAO,QAAA,GAAC,SAAO,EAACf,UAAU,CAAC,CAAC,CAAC,CAACE,KAAK,CAACmD,WAAW,CAAC,CAAC,EAAC,KAAG;sBAAA;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,GAAA2B,qBAAA,GACtEzD,UAAU,CAAC,CAAC,CAAC,CAACM,OAAO,cAAAmD,qBAAA,uBAArBA,qBAAA,CAAuBxC,GAAG,CAAC,CAACqC,MAAM,EAAEC,QAAQ,kBAC3C/E,OAAA;wBAAuBgC,KAAK,EAAE8C,MAAO;wBAAAvC,QAAA,EAAEuC;sBAAM,GAAhCC,QAAQ;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiC,CACvD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GApCD,mBAAmBgB,KAAK,EAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqC5B,CAAC;cAEV;;cAEA;cACA,IAAIgB,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;;cAE5B;cACA,oBACEtE,OAAA;gBAEEsC,SAAS,EAAE,iBACTgC,KAAK,KAAK,CAAC,GACP,SAAS,GACT3D,kBAAkB,GAClBJ,YAAY,GAAG+D,KAAK,GAAG,SAAS,GAAG,UAAU,GAC7C,QAAQ,EACX;gBACH1B,KAAK,EAAE;kBAAE6B,eAAe,EAAE,GAAGH,KAAK,GAAG,GAAG;gBAAK,CAAE;gBAAA/B,QAAA,gBAE/CvC,OAAA;kBAAOsC,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE8B,KAAK,CAAC3C;gBAAK;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACnDe,KAAK,CAAC1C,IAAI,KAAK,MAAM,iBACpB3B,OAAA;kBACE2B,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAEyC,KAAK,CAACzC,WAAY;kBAC/BU,SAAS,EAAC,aAAa;kBACvBN,KAAK,EAAEnB,QAAQ,CAACwD,KAAK,CAACxC,GAAG,CAA2B;kBACpD6C,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAACsC,KAAK,CAACxC,GAAG,EAAE8C,CAAC,CAACC,MAAM,CAAC5C,KAAK;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CACF,EACAe,KAAK,CAAC1C,IAAI,KAAK,QAAQ,iBACtB3B,OAAA;kBACEsC,SAAS,EAAC,cAAc;kBACxBN,KAAK,EAAEnB,QAAQ,CAACwD,KAAK,CAACxC,GAAG,CAA2B;kBACpD6C,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAACsC,KAAK,CAACxC,GAAG,EAAE8C,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAE;kBAAAO,QAAA,gBAE9DvC,OAAA;oBAAQgC,KAAK,EAAC,EAAE;oBAAAO,QAAA,GAAC,SAAO,EAAC8B,KAAK,CAAC3C,KAAK,CAACmD,WAAW,CAAC,CAAC,EAAC,KAAG;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,GAAAiB,eAAA,GAC9DF,KAAK,CAACvC,OAAO,cAAAyC,eAAA,uBAAbA,eAAA,CAAe9B,GAAG,CAAC,CAACqC,MAAM,EAAEC,QAAQ,kBACnC/E,OAAA;oBAAuBgC,KAAK,EAAE8C,MAAO;oBAAAvC,QAAA,EAAEuC;kBAAM,GAAhCC,QAAQ;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACT,EACAe,KAAK,CAAC1C,IAAI,KAAK,UAAU,iBACxB3B,OAAA;kBACE4B,WAAW,EAAEyC,KAAK,CAACzC,WAAY;kBAC/BsD,IAAI,EAAE,CAAE;kBACR5C,SAAS,EAAC,gBAAgB;kBAC1BN,KAAK,EAAEnB,QAAQ,CAACwD,KAAK,CAACxC,GAAG,CAA2B;kBACpD6C,QAAQ,EAAGC,CAAC,IAAK5C,iBAAiB,CAACsC,KAAK,CAACxC,GAAG,EAAE8C,CAAC,CAACC,MAAM,CAAC5C,KAAK;gBAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CACF;cAAA,GAxCIgB,KAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCP,CAAC;YAEV,CAAC;UAAC,gBACF,CAAC,eAGHtD,OAAA;YAAKsC,SAAS,EAAE,qBACd3B,kBAAkB,IAAIJ,YAAY,GAAGiB,UAAU,CAACJ,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ,EAChF;YAAAmB,QAAA,eACDvC,OAAA,CAACF,eAAe;cACdqF,OAAO,EAAElD,cAAe;cACxBmD,QAAQ,EAAE,CAAChD,WAAW,CAAC,CAAE;cACzBiD,IAAI,EAAE;YAAI;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKsC,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BvC,OAAA;QAAKsC,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B,CAAC,GAAGC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAAK3C,OAAA;UAAasC,SAAS,EAAC;QAAW,GAAxBK,CAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtD,OAAA;MAAKsC,SAAS,EAAC;IAAe;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAEV,CAAC;AAAClD,EAAA,CAvRID,eAAe;AAAAmF,EAAA,GAAfnF,eAAe;AAyRrB,eAAeA,eAAe;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}