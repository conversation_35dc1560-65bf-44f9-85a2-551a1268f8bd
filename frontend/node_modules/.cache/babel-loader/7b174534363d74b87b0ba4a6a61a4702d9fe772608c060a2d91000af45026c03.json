{"ast": null, "code": "import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function mapDispatchToPropsFactory(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(dispatch =>\n  // @ts-ignore\n  bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant(dispatch => ({\n    dispatch\n  })) : typeof mapDispatchToProps === 'function' ?\n  // @ts-ignore\n  wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : createInvalidArgFactory(mapDispatchToProps, 'mapDispatchToProps');\n}", "map": {"version": 3, "names": ["bindActionCreators", "wrapMapToPropsConstant", "wrapMapToPropsFunc", "createInvalidArgFactory", "mapDispatchToPropsFactory", "mapDispatchToProps", "dispatch"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/react-redux/es/connect/mapDispatchToProps.js"], "sourcesContent": ["import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function mapDispatchToPropsFactory(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(dispatch => // @ts-ignore\n  bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant(dispatch => ({\n    dispatch\n  })) : typeof mapDispatchToProps === 'function' ? // @ts-ignore\n  wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : createInvalidArgFactory(mapDispatchToProps, 'mapDispatchToProps');\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,6BAA6B;AAC5D,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC7E,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,OAAO,SAASC,yBAAyBA,CAACC,kBAAkB,EAAE;EAC5D,OAAOA,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,GAAGJ,sBAAsB,CAACK,QAAQ;EAAI;EACzGN,kBAAkB,CAACK,kBAAkB,EAAEC,QAAQ,CAAC,CAAC,GAAG,CAACD,kBAAkB,GAAGJ,sBAAsB,CAACK,QAAQ,KAAK;IAC5GA;EACF,CAAC,CAAC,CAAC,GAAG,OAAOD,kBAAkB,KAAK,UAAU;EAAG;EACjDH,kBAAkB,CAACG,kBAAkB,EAAE,oBAAoB,CAAC,GAAGF,uBAAuB,CAACE,kBAAkB,EAAE,oBAAoB,CAAC;AAClI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}