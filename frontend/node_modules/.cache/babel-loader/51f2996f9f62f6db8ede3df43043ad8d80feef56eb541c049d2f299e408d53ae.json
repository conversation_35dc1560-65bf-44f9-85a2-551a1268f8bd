{"ast": null, "code": "import React from'react';import{useParams}from'react-router-dom';import{jsxs as _jsxs}from\"react/jsx-runtime\";export const CheckoutPage=()=>{const{videoId}=useParams();return/*#__PURE__*/_jsxs(\"div\",{children:[\"Checkout Page for video \",videoId]});};", "map": {"version": 3, "names": ["React", "useParams", "jsxs", "_jsxs", "CheckoutPage", "videoId", "children"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\n\nexport const CheckoutPage: React.FC = () => {\n  const { videoId } = useParams();\n  return <div>Checkout Page for video {videoId}</div>;\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,KAAQ,kBAAkB,CAAC,OAAAC,IAAA,IAAAC,KAAA,yBAE7C,MAAO,MAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAEC,OAAQ,CAAC,CAAGJ,SAAS,CAAC,CAAC,CAC/B,mBAAOE,KAAA,QAAAG,QAAA,EAAK,0BAAwB,CAACD,OAAO,EAAM,CAAC,CACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}