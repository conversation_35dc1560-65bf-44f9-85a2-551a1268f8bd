{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/code_base/lms_project/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useRef}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Gyroscope=()=>{const[rotation,setRotation]=useState({x:0,y:0,z:0});const[isDragging,setIsDragging]=useState(false);const[momentum,setMomentum]=useState({x:0,y:0,z:0});const[particles,setParticles]=useState([]);const[energyLevel,setEnergyLevel]=useState(0.5);const lastMousePos=useRef({x:0,y:0});const animationRef=useRef();const particleIdRef=useRef(0);const gyroscopeRef=useRef(null);// Initialize enhanced particle system\nuseEffect(()=>{const colors=['#3b82f6','#8b5cf6','#ec4899','#f59e0b','#10b981','#ef4444'];const initParticles=()=>{const newParticles=[];for(let i=0;i<40;i++){newParticles.push({id:particleIdRef.current++,x:(Math.random()-0.5)*300,y:(Math.random()-0.5)*300,z:(Math.random()-0.5)*150,vx:(Math.random()-0.5)*4,vy:(Math.random()-0.5)*4,vz:(Math.random()-0.5)*2,life:Math.random()*120,maxLife:120+Math.random()*80,color:colors[Math.floor(Math.random()*colors.length)],size:2+Math.random()*4});}setParticles(newParticles);};initParticles();},[]);// Enhanced animation loop\nuseEffect(()=>{const animate=()=>{// Gyroscope rotation with dynamic energy\nif(!isDragging){const baseSpeed=energyLevel*2;setRotation(prev=>({x:prev.x+momentum.x+baseSpeed*0.4,y:prev.y+momentum.y+baseSpeed*0.6,z:prev.z+momentum.z+baseSpeed*0.3}));setMomentum(prev=>({x:prev.x*0.99,y:prev.y*0.99,z:prev.z*0.99}));// Dynamic energy fluctuation\nsetEnergyLevel(prev=>{const fluctuation=Math.sin(Date.now()*0.001)*0.1;return Math.max(0.2,Math.min(1,prev+fluctuation));});}// Advanced particle physics\nsetParticles(prev=>{return prev.map(particle=>{// Gravitational pull towards center\nconst centerForce=0.02;const dx=-particle.x*centerForce;const dy=-particle.y*centerForce;const dz=-particle.z*centerForce;// Orbital motion\nconst orbitalForce=0.01;const orbitalVx=-particle.y*orbitalForce;const orbitalVy=particle.x*orbitalForce;// Turbulence\nconst turbulence=energyLevel*0.2;const turbVx=(Math.random()-0.5)*turbulence;const turbVy=(Math.random()-0.5)*turbulence;const turbVz=(Math.random()-0.5)*turbulence*0.5;const newParticle=_objectSpread(_objectSpread({},particle),{},{vx:particle.vx*0.98+dx+orbitalVx+turbVx,vy:particle.vy*0.98+dy+orbitalVy+turbVy,vz:particle.vz*0.95+dz+turbVz,x:particle.x+particle.vx,y:particle.y+particle.vy,z:particle.z+particle.vz,life:particle.life+1});// Respawn particles\nif(newParticle.life>newParticle.maxLife||Math.sqrt(newParticle.x**2+newParticle.y**2+newParticle.z**2)>400){const colors=['#3b82f6','#8b5cf6','#ec4899','#f59e0b','#10b981','#ef4444'];const angle=Math.random()*Math.PI*2;const radius=50+Math.random()*100;return{id:particleIdRef.current++,x:Math.cos(angle)*radius,y:Math.sin(angle)*radius,z:(Math.random()-0.5)*50,vx:(Math.random()-0.5)*6,vy:(Math.random()-0.5)*6,vz:(Math.random()-0.5)*3,life:0,maxLife:120+Math.random()*80,color:colors[Math.floor(Math.random()*colors.length)],size:2+Math.random()*4};}return newParticle;});});animationRef.current=requestAnimationFrame(animate);};animationRef.current=requestAnimationFrame(animate);return()=>{if(animationRef.current)cancelAnimationFrame(animationRef.current);};},[isDragging,momentum,energyLevel]);const handleInteraction=function(e){var _gyroscopeRef$current;let isStart=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;const rect=(_gyroscopeRef$current=gyroscopeRef.current)===null||_gyroscopeRef$current===void 0?void 0:_gyroscopeRef$current.getBoundingClientRect();if(!rect)return;const centerX=rect.left+rect.width/2;const centerY=rect.top+rect.height/2;const mouseX=e.clientX-centerX;const mouseY=e.clientY-centerY;if(isStart){setIsDragging(true);lastMousePos.current={x:e.clientX,y:e.clientY};}else if(isDragging){const deltaX=e.clientX-lastMousePos.current.x;const deltaY=e.clientY-lastMousePos.current.y;const intensity=Math.sqrt(deltaX**2+deltaY**2)/100;setEnergyLevel(prev=>Math.min(1,prev+intensity*0.1));const rotationSpeed=1.2;setRotation(prev=>({x:prev.x+deltaY*rotationSpeed,y:prev.y+deltaX*rotationSpeed,z:prev.z+(deltaX-deltaY)*0.3}));setMomentum({x:deltaY*0.2,y:deltaX*0.2,z:(deltaX-deltaY)*0.1});lastMousePos.current={x:e.clientX,y:e.clientY};}};const getParticleStyle=particle=>{const lifeRatio=particle.life/particle.maxLife;const opacity=Math.max(0.1,(1-lifeRatio)*energyLevel);const distance=Math.sqrt(particle.x**2+particle.y**2+particle.z**2);const perspective=600;const scale=Math.max(0.1,1-Math.abs(particle.z)/300);return{position:'absolute',left:'50%',top:'50%',width:\"\".concat(particle.size,\"px\"),height:\"\".concat(particle.size,\"px\"),transform:\"\\n        translate(-50%, -50%)\\n        translate3d(\".concat(particle.x*scale,\"px, \").concat(particle.y*scale,\"px, 0px)\\n        scale(\").concat(scale,\")\\n      \"),opacity:opacity,background:\"radial-gradient(circle, \".concat(particle.color,\" 0%, transparent 70%)\"),borderRadius:'50%',boxShadow:\"0 0 \".concat(particle.size*2,\"px \").concat(particle.color),pointerEvents:'none',zIndex:Math.round(particle.z+100),filter:\"brightness(\".concat(energyLevel+0.5,\")\")};};const ringIntensity=energyLevel*100;return/*#__PURE__*/_jsxs(\"div\",{className:\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 opacity-40\",children:[...Array(5)].map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"absolute rounded-full blur-3xl animate-pulse\",style:{width:\"\".concat(200+i*100,\"px\"),height:\"\".concat(200+i*100,\"px\"),left:\"\".concat(20+i*15,\"%\"),top:\"\".concat(10+i*20,\"%\"),background:\"radial-gradient(circle, \".concat(['#3b82f6','#8b5cf6','#ec4899','#f59e0b','#10b981'][i],\"33, transparent)\"),animationDelay:\"\".concat(i*0.5,\"s\"),animationDuration:\"\".concat(3+i,\"s\"),transform:\"rotate(\".concat(rotation.z*(i+1),\"deg) scale(\").concat(energyLevel,\")\")}},i))}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center relative z-10\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-5xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-pulse\",style:{filter:\"brightness(\".concat(1+energyLevel,\")\"),textShadow:\"0 0 20px rgba(59, 130, 246, \".concat(energyLevel,\")\")},children:\"Quantum Gyroscope\"}),/*#__PURE__*/_jsxs(\"div\",{ref:gyroscopeRef,className:\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\",style:{perspective:'1200px'},onMouseDown:e=>handleInteraction(e,true),onMouseMove:e=>handleInteraction(e),onMouseUp:()=>setIsDragging(false),onMouseLeave:()=>setIsDragging(false),children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 pointer-events-none\",children:particles.map(particle=>/*#__PURE__*/_jsx(\"div\",{style:getParticleStyle(particle)},particle.id))}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 rounded-full border-2\",style:{transform:\"rotateX(\".concat(rotation.x,\"deg) rotateY(\").concat(rotation.y,\"deg) rotateZ(\").concat(rotation.z,\"deg)\"),transition:isDragging?'none':'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',borderColor:'#3b82f6',boxShadow:\"\\n                0 0 \".concat(20+ringIntensity,\"px #3b82f6,\\n                inset 0 0 \").concat(20+ringIntensity,\"px #3b82f6aa,\\n                0 0 \").concat(40+ringIntensity*2,\"px #3b82f6aa\\n              \"),filter:\"brightness(\".concat(1+energyLevel*0.5,\")\")},children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-8 rounded-full border-2\",style:{transform:\"rotateX(\".concat(rotation.x*1.5,\"deg) rotateY(\").concat(rotation.y*0.7,\"deg) rotateZ(\").concat(rotation.z*-1,\"deg)\"),borderColor:'#8b5cf6',boxShadow:\"\\n                  0 0 \".concat(30+ringIntensity,\"px #8b5cf6,\\n                  inset 0 0 \").concat(30+ringIntensity,\"px #8b5cf6aa\\n                \")},children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-8 rounded-full border-2\",style:{transform:\"rotateX(\".concat(rotation.x*0.5,\"deg) rotateY(\").concat(rotation.y*1.8,\"deg) rotateZ(\").concat(rotation.z*1.5,\"deg)\"),borderColor:'#ec4899',boxShadow:\"\\n                    0 0 \".concat(40+ringIntensity,\"px #ec4899,\\n                    inset 0 0 \").concat(40+ringIntensity,\"px #ec4899aa\\n                  \")},children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\",style:{transform:\"rotateX(\".concat(rotation.x*-0.3,\"deg) rotateY(\").concat(rotation.y*-0.5,\"deg) rotateZ(\").concat(rotation.z*2,\"deg)\"),boxShadow:\"\\n                      0 0 \".concat(60+ringIntensity*2,\"px #ffffff,\\n                      inset 0 0 \").concat(20+ringIntensity,\"px #3b82f688\\n                    \"),filter:\"brightness(\".concat(1+energyLevel,\")\")},children:/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500\",style:{boxShadow:\"0 0 \".concat(30+ringIntensity,\"px #fbbf24, 0 0 \").concat(60+ringIntensity*2,\"px #f97316\"),transform:\"scale(\".concat(1+energyLevel*0.3,\")\"),filter:\"brightness(\".concat(1+energyLevel,\")\")}})})})})}),[...Array(4)].map((_,i)=>/*#__PURE__*/_jsx(\"div\",{className:\"absolute rounded-full border opacity-30 pointer-events-none\",style:{inset:\"\".concat(-30-i*40,\"px\"),borderColor:['#3b82f6','#8b5cf6','#ec4899','#f59e0b'][i],borderWidth:'1px',transform:\"\\n                  rotateX(\".concat(rotation.x*(0.2+i*0.1),\"deg) \\n                  rotateY(\").concat(rotation.y*(0.3+i*0.1),\"deg)\\n                  rotateZ(\").concat(rotation.z*(0.1+i*0.05),\"deg)\\n                \"),boxShadow:\"0 0 10px \".concat(['#3b82f6','#8b5cf6','#ec4899','#f59e0b'][i]),opacity:energyLevel*0.6,animation:\"spin \".concat(15+i*10,\"s linear infinite reverse\")}},i))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 text-sm text-gray-300 space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-400\",children:\"\\u2728 Drag to manipulate quantum fields\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-purple-400\",children:[\"Energy: \",Math.round(energyLevel*100),\"%\"]})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"font-mono text-xs opacity-70\",children:[\"X: \",Math.round(rotation.x),\"\\xB0 Y: \",Math.round(rotation.y),\"\\xB0 Z: \",Math.round(rotation.z),\"\\xB0\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-pink-400 text-xs\",children:[\"Active Particles: \",particles.length]})]})]}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        @keyframes spin {\\n          from { transform: rotateZ(0deg); }\\n          to { transform: rotateZ(360deg); }\\n        }\\n      \"})]});};export default Gyroscope;", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsx", "_jsx", "jsxs", "_jsxs", "Gyroscope", "rotation", "setRotation", "x", "y", "z", "isDragging", "setIsDragging", "momentum", "setMomentum", "particles", "setParticles", "energyLevel", "setEnergyLevel", "lastMousePos", "animationRef", "particleIdRef", "gyroscopeRef", "colors", "initParticles", "newParticles", "i", "push", "id", "current", "Math", "random", "vx", "vy", "vz", "life", "maxLife", "color", "floor", "length", "size", "animate", "baseSpeed", "prev", "fluctuation", "sin", "Date", "now", "max", "min", "map", "particle", "centerForce", "dx", "dy", "dz", "orbitalForce", "orbitalVx", "orbitalVy", "turbulence", "turbVx", "turbVy", "turbVz", "newParticle", "_objectSpread", "sqrt", "angle", "PI", "radius", "cos", "requestAnimationFrame", "cancelAnimationFrame", "handleInteraction", "e", "_gyroscopeRef$current", "isStart", "arguments", "undefined", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "mouseX", "clientX", "mouseY", "clientY", "deltaX", "deltaY", "intensity", "rotationSpeed", "getParticleStyle", "lifeRatio", "opacity", "distance", "perspective", "scale", "abs", "position", "concat", "transform", "background", "borderRadius", "boxShadow", "pointerEvents", "zIndex", "round", "filter", "ringIntensity", "className", "children", "Array", "_", "style", "animationDelay", "animationDuration", "textShadow", "ref", "onMouseDown", "onMouseMove", "onMouseUp", "onMouseLeave", "transition", "borderColor", "inset", "borderWidth", "animation"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface RotationState {\n  x: number;\n  y: number;\n  z: number;\n}\n\ninterface ParticleState {\n  id: number;\n  x: number;\n  y: number;\n  z: number;\n  vx: number;\n  vy: number;\n  vz: number;\n  life: number;\n  maxLife: number;\n  color: string;\n  size: number;\n}\n\nconst Gyroscope = () => {\n  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });\n  const [particles, setParticles] = useState<ParticleState[]>([]);\n  const [energyLevel, setEnergyLevel] = useState(0.5);\n  \n  const lastMousePos = useRef({ x: 0, y: 0 });\n  const animationRef = useRef<number>();\n  const particleIdRef = useRef(0);\n  const gyroscopeRef = useRef<HTMLDivElement>(null);\n\n  // Initialize enhanced particle system\n  useEffect(() => {\n    const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n    const initParticles = () => {\n      const newParticles: ParticleState[] = [];\n      for (let i = 0; i < 40; i++) {\n        newParticles.push({\n          id: particleIdRef.current++,\n          x: (Math.random() - 0.5) * 300,\n          y: (Math.random() - 0.5) * 300,\n          z: (Math.random() - 0.5) * 150,\n          vx: (Math.random() - 0.5) * 4,\n          vy: (Math.random() - 0.5) * 4,\n          vz: (Math.random() - 0.5) * 2,\n          life: Math.random() * 120,\n          maxLife: 120 + Math.random() * 80,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          size: 2 + Math.random() * 4\n        });\n      }\n      setParticles(newParticles);\n    };\n    initParticles();\n  }, []);\n\n  // Enhanced animation loop\n  useEffect(() => {\n    const animate = () => {\n      // Gyroscope rotation with dynamic energy\n      if (!isDragging) {\n        const baseSpeed = energyLevel * 2;\n        setRotation(prev => ({\n          x: prev.x + momentum.x + baseSpeed * 0.4,\n          y: prev.y + momentum.y + baseSpeed * 0.6,\n          z: prev.z + momentum.z + baseSpeed * 0.3\n        }));\n        \n        setMomentum(prev => ({\n          x: prev.x * 0.99,\n          y: prev.y * 0.99,\n          z: prev.z * 0.99\n        }));\n\n        // Dynamic energy fluctuation\n        setEnergyLevel(prev => {\n          const fluctuation = Math.sin(Date.now() * 0.001) * 0.1;\n          return Math.max(0.2, Math.min(1, prev + fluctuation));\n        });\n      }\n\n      // Advanced particle physics\n      setParticles(prev => {\n        return prev.map(particle => {\n          // Gravitational pull towards center\n          const centerForce = 0.02;\n          const dx = -particle.x * centerForce;\n          const dy = -particle.y * centerForce;\n          const dz = -particle.z * centerForce;\n\n          // Orbital motion\n          const orbitalForce = 0.01;\n          const orbitalVx = -particle.y * orbitalForce;\n          const orbitalVy = particle.x * orbitalForce;\n\n          // Turbulence\n          const turbulence = energyLevel * 0.2;\n          const turbVx = (Math.random() - 0.5) * turbulence;\n          const turbVy = (Math.random() - 0.5) * turbulence;\n          const turbVz = (Math.random() - 0.5) * turbulence * 0.5;\n\n          const newParticle = {\n            ...particle,\n            vx: particle.vx * 0.98 + dx + orbitalVx + turbVx,\n            vy: particle.vy * 0.98 + dy + orbitalVy + turbVy,\n            vz: particle.vz * 0.95 + dz + turbVz,\n            x: particle.x + particle.vx,\n            y: particle.y + particle.vy,\n            z: particle.z + particle.vz,\n            life: particle.life + 1\n          };\n\n          // Respawn particles\n          if (newParticle.life > newParticle.maxLife || \n              Math.sqrt(newParticle.x**2 + newParticle.y**2 + newParticle.z**2) > 400) {\n            const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#ef4444'];\n            const angle = Math.random() * Math.PI * 2;\n            const radius = 50 + Math.random() * 100;\n            return {\n              id: particleIdRef.current++,\n              x: Math.cos(angle) * radius,\n              y: Math.sin(angle) * radius,\n              z: (Math.random() - 0.5) * 50,\n              vx: (Math.random() - 0.5) * 6,\n              vy: (Math.random() - 0.5) * 6,\n              vz: (Math.random() - 0.5) * 3,\n              life: 0,\n              maxLife: 120 + Math.random() * 80,\n              color: colors[Math.floor(Math.random() * colors.length)],\n              size: 2 + Math.random() * 4\n            };\n          }\n\n          return newParticle;\n        });\n      });\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    \n    animationRef.current = requestAnimationFrame(animate);\n    return () => {\n      if (animationRef.current) cancelAnimationFrame(animationRef.current);\n    };\n  }, [isDragging, momentum, energyLevel]);\n\n  const handleInteraction = (e: React.MouseEvent, isStart: boolean = false) => {\n    const rect = gyroscopeRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    if (isStart) {\n      setIsDragging(true);\n      lastMousePos.current = { x: e.clientX, y: e.clientY };\n    } else if (isDragging) {\n      const deltaX = e.clientX - lastMousePos.current.x;\n      const deltaY = e.clientY - lastMousePos.current.y;\n      const intensity = Math.sqrt(deltaX**2 + deltaY**2) / 100;\n      \n      setEnergyLevel(prev => Math.min(1, prev + intensity * 0.1));\n      \n      const rotationSpeed = 1.2;\n      setRotation(prev => ({\n        x: prev.x + deltaY * rotationSpeed,\n        y: prev.y + deltaX * rotationSpeed,\n        z: prev.z + (deltaX - deltaY) * 0.3\n      }));\n      \n      setMomentum({\n        x: deltaY * 0.2,\n        y: deltaX * 0.2,\n        z: (deltaX - deltaY) * 0.1\n      });\n      \n      lastMousePos.current = { x: e.clientX, y: e.clientY };\n    }\n  };\n\n  const getParticleStyle = (particle: ParticleState) => {\n    const lifeRatio = particle.life / particle.maxLife;\n    const opacity = Math.max(0.1, (1 - lifeRatio) * energyLevel);\n    const distance = Math.sqrt(particle.x**2 + particle.y**2 + particle.z**2);\n    const perspective = 600;\n    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 300);\n\n    return {\n      position: 'absolute' as const,\n      left: '50%',\n      top: '50%',\n      width: `${particle.size}px`,\n      height: `${particle.size}px`,\n      transform: `\n        translate(-50%, -50%)\n        translate3d(${particle.x * scale}px, ${particle.y * scale}px, 0px)\n        scale(${scale})\n      `,\n      opacity: opacity,\n      background: `radial-gradient(circle, ${particle.color} 0%, transparent 70%)`,\n      borderRadius: '50%',\n      boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,\n      pointerEvents: 'none' as const,\n      zIndex: Math.round(particle.z + 100),\n      filter: `brightness(${energyLevel + 0.5})`\n    };\n  };\n\n  const ringIntensity = energyLevel * 100;\n\n  return (\n    <div className=\"w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden relative\">\n      {/* Dynamic Background Energy Fields */}\n      <div className=\"absolute inset-0 opacity-40\">\n        {[...Array(5)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute rounded-full blur-3xl animate-pulse\"\n            style={{\n              width: `${200 + i * 100}px`,\n              height: `${200 + i * 100}px`,\n              left: `${20 + i * 15}%`,\n              top: `${10 + i * 20}%`,\n              background: `radial-gradient(circle, ${ \n                ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'][i] \n              }33, transparent)`,\n              animationDelay: `${i * 0.5}s`,\n              animationDuration: `${3 + i}s`,\n              transform: `rotate(${rotation.z * (i + 1)}deg) scale(${energyLevel})`\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"text-center relative z-10\">\n        <h1 \n          className=\"text-5xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-pulse\"\n          style={{ \n            filter: `brightness(${1 + energyLevel})`,\n            textShadow: `0 0 20px rgba(59, 130, 246, ${energyLevel})`\n          }}\n        >\n          Quantum Gyroscope\n        </h1>\n        \n        <div \n          ref={gyroscopeRef}\n          className=\"relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none\"\n          style={{ perspective: '1200px' }}\n          onMouseDown={(e) => handleInteraction(e, true)}\n          onMouseMove={(e) => handleInteraction(e)}\n          onMouseUp={() => setIsDragging(false)}\n          onMouseLeave={() => setIsDragging(false)}\n        >\n          {/* Enhanced Particle System */}\n          <div className=\"absolute inset-0 pointer-events-none\">\n            {particles.map(particle => (\n              <div key={particle.id} style={getParticleStyle(particle)} />\n            ))}\n          </div>\n\n          {/* Quantum Energy Rings */}\n          {/* Outer Ring */}\n          <div \n            className=\"absolute inset-0 rounded-full border-2\"\n            style={{\n              transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,\n              transition: isDragging ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              borderColor: '#3b82f6',\n              boxShadow: `\n                0 0 ${20 + ringIntensity}px #3b82f6,\n                inset 0 0 ${20 + ringIntensity}px #3b82f6aa,\n                0 0 ${40 + ringIntensity * 2}px #3b82f6aa\n              `,\n              filter: `brightness(${1 + energyLevel * 0.5})`\n            }}\n          >\n            {/* Middle Ring */}\n            <div \n              className=\"absolute inset-8 rounded-full border-2\"\n              style={{\n                transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,\n                borderColor: '#8b5cf6',\n                boxShadow: `\n                  0 0 ${30 + ringIntensity}px #8b5cf6,\n                  inset 0 0 ${30 + ringIntensity}px #8b5cf6aa\n                `\n              }}\n            >\n              {/* Inner Ring */}\n              <div \n                className=\"absolute inset-8 rounded-full border-2\"\n                style={{\n                  transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,\n                  borderColor: '#ec4899',\n                  boxShadow: `\n                    0 0 ${40 + ringIntensity}px #ec4899,\n                    inset 0 0 ${40 + ringIntensity}px #ec4899aa\n                  `\n                }}\n              >\n                {/* Quantum Core */}\n                <div \n                  className=\"absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300\"\n                  style={{\n                    transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,\n                    boxShadow: `\n                      0 0 ${60 + ringIntensity * 2}px #ffffff,\n                      inset 0 0 ${20 + ringIntensity}px #3b82f688\n                    `,\n                    filter: `brightness(${1 + energyLevel})`\n                  }}\n                >\n                  {/* Energy Center */}\n                  <div \n                    className=\"absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500\"\n                    style={{\n                      boxShadow: `0 0 ${30 + ringIntensity}px #fbbf24, 0 0 ${60 + ringIntensity * 2}px #f97316`,\n                      transform: `scale(${1 + energyLevel * 0.3})`,\n                      filter: `brightness(${1 + energyLevel})`\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Dynamic Orbital Rings */}\n          {[...Array(4)].map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute rounded-full border opacity-30 pointer-events-none\"\n              style={{\n                inset: `${-30 - i * 40}px`,\n                borderColor: ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i],\n                borderWidth: '1px',\n                transform: `\n                  rotateX(${rotation.x * (0.2 + i * 0.1)}deg) \n                  rotateY(${rotation.y * (0.3 + i * 0.1)}deg)\n                  rotateZ(${rotation.z * (0.1 + i * 0.05)}deg)\n                `,\n                boxShadow: `0 0 10px ${['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'][i]}`,\n                opacity: energyLevel * 0.6,\n                animation: `spin ${15 + i * 10}s linear infinite reverse`\n              }}\n            />\n          ))}\n        </div>\n        \n        <div className=\"mt-6 text-sm text-gray-300 space-y-2\">\n          <div className=\"flex justify-center items-center space-x-4\">\n            <span className=\"text-blue-400\">✨ Drag to manipulate quantum fields</span>\n            <span className=\"text-purple-400\">Energy: {Math.round(energyLevel * 100)}%</span>\n          </div>\n          <p className=\"font-mono text-xs opacity-70\">\n            X: {Math.round(rotation.x)}° Y: {Math.round(rotation.y)}° Z: {Math.round(rotation.z)}°\n          </p>\n          <p className=\"text-pink-400 text-xs\">Active Particles: {particles.length}</p>\n        </div>\n      </div>\n\n      <style>{`\n        @keyframes spin {\n          from { transform: rotateZ(0deg); }\n          to { transform: rotateZ(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Gyroscope;"], "mappings": "oIAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAsBpD,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGT,QAAQ,CAAgB,CAAEU,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC7E,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACe,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAAgB,CAAEU,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC7E,KAAM,CAACK,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAkB,EAAE,CAAC,CAC/D,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,GAAG,CAAC,CAEnD,KAAM,CAAAqB,YAAY,CAAGnB,MAAM,CAAC,CAAEQ,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC3C,KAAM,CAAAW,YAAY,CAAGpB,MAAM,CAAS,CAAC,CACrC,KAAM,CAAAqB,aAAa,CAAGrB,MAAM,CAAC,CAAC,CAAC,CAC/B,KAAM,CAAAsB,YAAY,CAAGtB,MAAM,CAAiB,IAAI,CAAC,CAEjD;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwB,MAAM,CAAG,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CACjF,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,YAA6B,CAAG,EAAE,CACxC,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC3BD,YAAY,CAACE,IAAI,CAAC,CAChBC,EAAE,CAAEP,aAAa,CAACQ,OAAO,EAAE,CAC3BrB,CAAC,CAAE,CAACsB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAG,CAC9BtB,CAAC,CAAE,CAACqB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAG,CAC9BrB,CAAC,CAAE,CAACoB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAG,CAC9BC,EAAE,CAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BE,EAAE,CAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BG,EAAE,CAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BI,IAAI,CAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CACzBK,OAAO,CAAE,GAAG,CAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CACjCM,KAAK,CAAEd,MAAM,CAACO,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGR,MAAM,CAACgB,MAAM,CAAC,CAAC,CACxDC,IAAI,CAAE,CAAC,CAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAC5B,CAAC,CAAC,CACJ,CACAf,YAAY,CAACS,YAAY,CAAC,CAC5B,CAAC,CACDD,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA0C,OAAO,CAAGA,CAAA,GAAM,CACpB;AACA,GAAI,CAAC9B,UAAU,CAAE,CACf,KAAM,CAAA+B,SAAS,CAAGzB,WAAW,CAAG,CAAC,CACjCV,WAAW,CAACoC,IAAI,GAAK,CACnBnC,CAAC,CAAEmC,IAAI,CAACnC,CAAC,CAAGK,QAAQ,CAACL,CAAC,CAAGkC,SAAS,CAAG,GAAG,CACxCjC,CAAC,CAAEkC,IAAI,CAAClC,CAAC,CAAGI,QAAQ,CAACJ,CAAC,CAAGiC,SAAS,CAAG,GAAG,CACxChC,CAAC,CAAEiC,IAAI,CAACjC,CAAC,CAAGG,QAAQ,CAACH,CAAC,CAAGgC,SAAS,CAAG,GACvC,CAAC,CAAC,CAAC,CAEH5B,WAAW,CAAC6B,IAAI,GAAK,CACnBnC,CAAC,CAAEmC,IAAI,CAACnC,CAAC,CAAG,IAAI,CAChBC,CAAC,CAAEkC,IAAI,CAAClC,CAAC,CAAG,IAAI,CAChBC,CAAC,CAAEiC,IAAI,CAACjC,CAAC,CAAG,IACd,CAAC,CAAC,CAAC,CAEH;AACAQ,cAAc,CAACyB,IAAI,EAAI,CACrB,KAAM,CAAAC,WAAW,CAAGd,IAAI,CAACe,GAAG,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,KAAK,CAAC,CAAG,GAAG,CACtD,MAAO,CAAAjB,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAElB,IAAI,CAACmB,GAAG,CAAC,CAAC,CAAEN,IAAI,CAAGC,WAAW,CAAC,CAAC,CACvD,CAAC,CAAC,CACJ,CAEA;AACA5B,YAAY,CAAC2B,IAAI,EAAI,CACnB,MAAO,CAAAA,IAAI,CAACO,GAAG,CAACC,QAAQ,EAAI,CAC1B;AACA,KAAM,CAAAC,WAAW,CAAG,IAAI,CACxB,KAAM,CAAAC,EAAE,CAAG,CAACF,QAAQ,CAAC3C,CAAC,CAAG4C,WAAW,CACpC,KAAM,CAAAE,EAAE,CAAG,CAACH,QAAQ,CAAC1C,CAAC,CAAG2C,WAAW,CACpC,KAAM,CAAAG,EAAE,CAAG,CAACJ,QAAQ,CAACzC,CAAC,CAAG0C,WAAW,CAEpC;AACA,KAAM,CAAAI,YAAY,CAAG,IAAI,CACzB,KAAM,CAAAC,SAAS,CAAG,CAACN,QAAQ,CAAC1C,CAAC,CAAG+C,YAAY,CAC5C,KAAM,CAAAE,SAAS,CAAGP,QAAQ,CAAC3C,CAAC,CAAGgD,YAAY,CAE3C;AACA,KAAM,CAAAG,UAAU,CAAG1C,WAAW,CAAG,GAAG,CACpC,KAAM,CAAA2C,MAAM,CAAG,CAAC9B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI4B,UAAU,CACjD,KAAM,CAAAE,MAAM,CAAG,CAAC/B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI4B,UAAU,CACjD,KAAM,CAAAG,MAAM,CAAG,CAAChC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI4B,UAAU,CAAG,GAAG,CAEvD,KAAM,CAAAI,WAAW,CAAAC,aAAA,CAAAA,aAAA,IACZb,QAAQ,MACXnB,EAAE,CAAEmB,QAAQ,CAACnB,EAAE,CAAG,IAAI,CAAGqB,EAAE,CAAGI,SAAS,CAAGG,MAAM,CAChD3B,EAAE,CAAEkB,QAAQ,CAAClB,EAAE,CAAG,IAAI,CAAGqB,EAAE,CAAGI,SAAS,CAAGG,MAAM,CAChD3B,EAAE,CAAEiB,QAAQ,CAACjB,EAAE,CAAG,IAAI,CAAGqB,EAAE,CAAGO,MAAM,CACpCtD,CAAC,CAAE2C,QAAQ,CAAC3C,CAAC,CAAG2C,QAAQ,CAACnB,EAAE,CAC3BvB,CAAC,CAAE0C,QAAQ,CAAC1C,CAAC,CAAG0C,QAAQ,CAAClB,EAAE,CAC3BvB,CAAC,CAAEyC,QAAQ,CAACzC,CAAC,CAAGyC,QAAQ,CAACjB,EAAE,CAC3BC,IAAI,CAAEgB,QAAQ,CAAChB,IAAI,CAAG,CAAC,EACxB,CAED;AACA,GAAI4B,WAAW,CAAC5B,IAAI,CAAG4B,WAAW,CAAC3B,OAAO,EACtCN,IAAI,CAACmC,IAAI,CAACF,WAAW,CAACvD,CAAC,EAAE,CAAC,CAAGuD,WAAW,CAACtD,CAAC,EAAE,CAAC,CAAGsD,WAAW,CAACrD,CAAC,EAAE,CAAC,CAAC,CAAG,GAAG,CAAE,CAC3E,KAAM,CAAAa,MAAM,CAAG,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CACjF,KAAM,CAAA2C,KAAK,CAAGpC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGD,IAAI,CAACqC,EAAE,CAAG,CAAC,CACzC,KAAM,CAAAC,MAAM,CAAG,EAAE,CAAGtC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CACvC,MAAO,CACLH,EAAE,CAAEP,aAAa,CAACQ,OAAO,EAAE,CAC3BrB,CAAC,CAAEsB,IAAI,CAACuC,GAAG,CAACH,KAAK,CAAC,CAAGE,MAAM,CAC3B3D,CAAC,CAAEqB,IAAI,CAACe,GAAG,CAACqB,KAAK,CAAC,CAAGE,MAAM,CAC3B1D,CAAC,CAAE,CAACoB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,EAAE,CAC7BC,EAAE,CAAE,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BE,EAAE,CAAE,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BG,EAAE,CAAE,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,CAAC,CAC7BI,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE,GAAG,CAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CACjCM,KAAK,CAAEd,MAAM,CAACO,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGR,MAAM,CAACgB,MAAM,CAAC,CAAC,CACxDC,IAAI,CAAE,CAAC,CAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAC5B,CAAC,CACH,CAEA,MAAO,CAAAgC,WAAW,CACpB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF3C,YAAY,CAACS,OAAO,CAAGyC,qBAAqB,CAAC7B,OAAO,CAAC,CACvD,CAAC,CAEDrB,YAAY,CAACS,OAAO,CAAGyC,qBAAqB,CAAC7B,OAAO,CAAC,CACrD,MAAO,IAAM,CACX,GAAIrB,YAAY,CAACS,OAAO,CAAE0C,oBAAoB,CAACnD,YAAY,CAACS,OAAO,CAAC,CACtE,CAAC,CACH,CAAC,CAAE,CAAClB,UAAU,CAAEE,QAAQ,CAAEI,WAAW,CAAC,CAAC,CAEvC,KAAM,CAAAuD,iBAAiB,CAAG,QAAAA,CAACC,CAAmB,CAA+B,KAAAC,qBAAA,IAA7B,CAAAC,OAAgB,CAAAC,SAAA,CAAArC,MAAA,IAAAqC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CACtE,KAAM,CAAAE,IAAI,EAAAJ,qBAAA,CAAGpD,YAAY,CAACO,OAAO,UAAA6C,qBAAA,iBAApBA,qBAAA,CAAsBK,qBAAqB,CAAC,CAAC,CAC1D,GAAI,CAACD,IAAI,CAAE,OAEX,KAAM,CAAAE,OAAO,CAAGF,IAAI,CAACG,IAAI,CAAGH,IAAI,CAACI,KAAK,CAAG,CAAC,CAC1C,KAAM,CAAAC,OAAO,CAAGL,IAAI,CAACM,GAAG,CAAGN,IAAI,CAACO,MAAM,CAAG,CAAC,CAC1C,KAAM,CAAAC,MAAM,CAAGb,CAAC,CAACc,OAAO,CAAGP,OAAO,CAClC,KAAM,CAAAQ,MAAM,CAAGf,CAAC,CAACgB,OAAO,CAAGN,OAAO,CAElC,GAAIR,OAAO,CAAE,CACX/D,aAAa,CAAC,IAAI,CAAC,CACnBO,YAAY,CAACU,OAAO,CAAG,CAAErB,CAAC,CAAEiE,CAAC,CAACc,OAAO,CAAE9E,CAAC,CAAEgE,CAAC,CAACgB,OAAQ,CAAC,CACvD,CAAC,IAAM,IAAI9E,UAAU,CAAE,CACrB,KAAM,CAAA+E,MAAM,CAAGjB,CAAC,CAACc,OAAO,CAAGpE,YAAY,CAACU,OAAO,CAACrB,CAAC,CACjD,KAAM,CAAAmF,MAAM,CAAGlB,CAAC,CAACgB,OAAO,CAAGtE,YAAY,CAACU,OAAO,CAACpB,CAAC,CACjD,KAAM,CAAAmF,SAAS,CAAG9D,IAAI,CAACmC,IAAI,CAACyB,MAAM,EAAE,CAAC,CAAGC,MAAM,EAAE,CAAC,CAAC,CAAG,GAAG,CAExDzE,cAAc,CAACyB,IAAI,EAAIb,IAAI,CAACmB,GAAG,CAAC,CAAC,CAAEN,IAAI,CAAGiD,SAAS,CAAG,GAAG,CAAC,CAAC,CAE3D,KAAM,CAAAC,aAAa,CAAG,GAAG,CACzBtF,WAAW,CAACoC,IAAI,GAAK,CACnBnC,CAAC,CAAEmC,IAAI,CAACnC,CAAC,CAAGmF,MAAM,CAAGE,aAAa,CAClCpF,CAAC,CAAEkC,IAAI,CAAClC,CAAC,CAAGiF,MAAM,CAAGG,aAAa,CAClCnF,CAAC,CAAEiC,IAAI,CAACjC,CAAC,CAAG,CAACgF,MAAM,CAAGC,MAAM,EAAI,GAClC,CAAC,CAAC,CAAC,CAEH7E,WAAW,CAAC,CACVN,CAAC,CAAEmF,MAAM,CAAG,GAAG,CACflF,CAAC,CAAEiF,MAAM,CAAG,GAAG,CACfhF,CAAC,CAAE,CAACgF,MAAM,CAAGC,MAAM,EAAI,GACzB,CAAC,CAAC,CAEFxE,YAAY,CAACU,OAAO,CAAG,CAAErB,CAAC,CAAEiE,CAAC,CAACc,OAAO,CAAE9E,CAAC,CAAEgE,CAAC,CAACgB,OAAQ,CAAC,CACvD,CACF,CAAC,CAED,KAAM,CAAAK,gBAAgB,CAAI3C,QAAuB,EAAK,CACpD,KAAM,CAAA4C,SAAS,CAAG5C,QAAQ,CAAChB,IAAI,CAAGgB,QAAQ,CAACf,OAAO,CAClD,KAAM,CAAA4D,OAAO,CAAGlE,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAE,CAAC,CAAC,CAAG+C,SAAS,EAAI9E,WAAW,CAAC,CAC5D,KAAM,CAAAgF,QAAQ,CAAGnE,IAAI,CAACmC,IAAI,CAACd,QAAQ,CAAC3C,CAAC,EAAE,CAAC,CAAG2C,QAAQ,CAAC1C,CAAC,EAAE,CAAC,CAAG0C,QAAQ,CAACzC,CAAC,EAAE,CAAC,CAAC,CACzE,KAAM,CAAAwF,WAAW,CAAG,GAAG,CACvB,KAAM,CAAAC,KAAK,CAAGrE,IAAI,CAACkB,GAAG,CAAC,GAAG,CAAE,CAAC,CAAGlB,IAAI,CAACsE,GAAG,CAACjD,QAAQ,CAACzC,CAAC,CAAC,CAAG,GAAG,CAAC,CAE3D,MAAO,CACL2F,QAAQ,CAAE,UAAmB,CAC7BpB,IAAI,CAAE,KAAK,CACXG,GAAG,CAAE,KAAK,CACVF,KAAK,IAAAoB,MAAA,CAAKnD,QAAQ,CAACX,IAAI,MAAI,CAC3B6C,MAAM,IAAAiB,MAAA,CAAKnD,QAAQ,CAACX,IAAI,MAAI,CAC5B+D,SAAS,yDAAAD,MAAA,CAEOnD,QAAQ,CAAC3C,CAAC,CAAG2F,KAAK,SAAAG,MAAA,CAAOnD,QAAQ,CAAC1C,CAAC,CAAG0F,KAAK,6BAAAG,MAAA,CACjDH,KAAK,aACd,CACDH,OAAO,CAAEA,OAAO,CAChBQ,UAAU,4BAAAF,MAAA,CAA6BnD,QAAQ,CAACd,KAAK,yBAAuB,CAC5EoE,YAAY,CAAE,KAAK,CACnBC,SAAS,QAAAJ,MAAA,CAASnD,QAAQ,CAACX,IAAI,CAAG,CAAC,QAAA8D,MAAA,CAAMnD,QAAQ,CAACd,KAAK,CAAE,CACzDsE,aAAa,CAAE,MAAe,CAC9BC,MAAM,CAAE9E,IAAI,CAAC+E,KAAK,CAAC1D,QAAQ,CAACzC,CAAC,CAAG,GAAG,CAAC,CACpCoG,MAAM,eAAAR,MAAA,CAAgBrF,WAAW,CAAG,GAAG,KACzC,CAAC,CACH,CAAC,CAED,KAAM,CAAA8F,aAAa,CAAG9F,WAAW,CAAG,GAAG,CAEvC,mBACEb,KAAA,QAAK4G,SAAS,CAAC,4IAA4I,CAAAC,QAAA,eAEzJ/G,IAAA,QAAK8G,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACzC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAChE,GAAG,CAAC,CAACiE,CAAC,CAAEzF,CAAC,gBACtBxB,IAAA,QAEE8G,SAAS,CAAC,8CAA8C,CACxDI,KAAK,CAAE,CACLlC,KAAK,IAAAoB,MAAA,CAAK,GAAG,CAAG5E,CAAC,CAAG,GAAG,MAAI,CAC3B2D,MAAM,IAAAiB,MAAA,CAAK,GAAG,CAAG5E,CAAC,CAAG,GAAG,MAAI,CAC5BuD,IAAI,IAAAqB,MAAA,CAAK,EAAE,CAAG5E,CAAC,CAAG,EAAE,KAAG,CACvB0D,GAAG,IAAAkB,MAAA,CAAK,EAAE,CAAG5E,CAAC,CAAG,EAAE,KAAG,CACtB8E,UAAU,4BAAAF,MAAA,CACR,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAAC5E,CAAC,CAAC,oBAC1C,CAClB2F,cAAc,IAAAf,MAAA,CAAK5E,CAAC,CAAG,GAAG,KAAG,CAC7B4F,iBAAiB,IAAAhB,MAAA,CAAK,CAAC,CAAG5E,CAAC,KAAG,CAC9B6E,SAAS,WAAAD,MAAA,CAAYhG,QAAQ,CAACI,CAAC,EAAIgB,CAAC,CAAG,CAAC,CAAC,gBAAA4E,MAAA,CAAcrF,WAAW,KACpE,CAAE,EAbGS,CAcN,CACF,CAAC,CACC,CAAC,cAENtB,KAAA,QAAK4G,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC/G,IAAA,OACE8G,SAAS,CAAC,+HAA+H,CACzII,KAAK,CAAE,CACLN,MAAM,eAAAR,MAAA,CAAgB,CAAC,CAAGrF,WAAW,KAAG,CACxCsG,UAAU,gCAAAjB,MAAA,CAAiCrF,WAAW,KACxD,CAAE,CAAAgG,QAAA,CACH,mBAED,CAAI,CAAC,cAEL7G,KAAA,QACEoH,GAAG,CAAElG,YAAa,CAClB0F,SAAS,CAAC,2EAA2E,CACrFI,KAAK,CAAE,CAAElB,WAAW,CAAE,QAAS,CAAE,CACjCuB,WAAW,CAAGhD,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE,IAAI,CAAE,CAC/CiD,WAAW,CAAGjD,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAE,CACzCkD,SAAS,CAAEA,CAAA,GAAM/G,aAAa,CAAC,KAAK,CAAE,CACtCgH,YAAY,CAAEA,CAAA,GAAMhH,aAAa,CAAC,KAAK,CAAE,CAAAqG,QAAA,eAGzC/G,IAAA,QAAK8G,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAClDlG,SAAS,CAACmC,GAAG,CAACC,QAAQ,eACrBjD,IAAA,QAAuBkH,KAAK,CAAEtB,gBAAgB,CAAC3C,QAAQ,CAAE,EAA/CA,QAAQ,CAACvB,EAAwC,CAC5D,CAAC,CACC,CAAC,cAIN1B,IAAA,QACE8G,SAAS,CAAC,wCAAwC,CAClDI,KAAK,CAAE,CACLb,SAAS,YAAAD,MAAA,CAAahG,QAAQ,CAACE,CAAC,kBAAA8F,MAAA,CAAgBhG,QAAQ,CAACG,CAAC,kBAAA6F,MAAA,CAAgBhG,QAAQ,CAACI,CAAC,QAAM,CAC1FmH,UAAU,CAAElH,UAAU,CAAG,MAAM,CAAG,6CAA6C,CAC/EmH,WAAW,CAAE,SAAS,CACtBpB,SAAS,0BAAAJ,MAAA,CACD,EAAE,CAAGS,aAAa,4CAAAT,MAAA,CACZ,EAAE,CAAGS,aAAa,wCAAAT,MAAA,CACxB,EAAE,CAAGS,aAAa,CAAG,CAAC,gCAC7B,CACDD,MAAM,eAAAR,MAAA,CAAgB,CAAC,CAAGrF,WAAW,CAAG,GAAG,KAC7C,CAAE,CAAAgG,QAAA,cAGF/G,IAAA,QACE8G,SAAS,CAAC,wCAAwC,CAClDI,KAAK,CAAE,CACLb,SAAS,YAAAD,MAAA,CAAahG,QAAQ,CAACE,CAAC,CAAG,GAAG,kBAAA8F,MAAA,CAAgBhG,QAAQ,CAACG,CAAC,CAAG,GAAG,kBAAA6F,MAAA,CAAgBhG,QAAQ,CAACI,CAAC,CAAG,CAAC,CAAC,QAAM,CAC3GoH,WAAW,CAAE,SAAS,CACtBpB,SAAS,4BAAAJ,MAAA,CACD,EAAE,CAAGS,aAAa,8CAAAT,MAAA,CACZ,EAAE,CAAGS,aAAa,kCAElC,CAAE,CAAAE,QAAA,cAGF/G,IAAA,QACE8G,SAAS,CAAC,wCAAwC,CAClDI,KAAK,CAAE,CACLb,SAAS,YAAAD,MAAA,CAAahG,QAAQ,CAACE,CAAC,CAAG,GAAG,kBAAA8F,MAAA,CAAgBhG,QAAQ,CAACG,CAAC,CAAG,GAAG,kBAAA6F,MAAA,CAAgBhG,QAAQ,CAACI,CAAC,CAAG,GAAG,QAAM,CAC5GoH,WAAW,CAAE,SAAS,CACtBpB,SAAS,8BAAAJ,MAAA,CACD,EAAE,CAAGS,aAAa,gDAAAT,MAAA,CACZ,EAAE,CAAGS,aAAa,oCAElC,CAAE,CAAAE,QAAA,cAGF/G,IAAA,QACE8G,SAAS,CAAC,uFAAuF,CACjGI,KAAK,CAAE,CACLb,SAAS,YAAAD,MAAA,CAAahG,QAAQ,CAACE,CAAC,CAAG,CAAC,GAAG,kBAAA8F,MAAA,CAAgBhG,QAAQ,CAACG,CAAC,CAAG,CAAC,GAAG,kBAAA6F,MAAA,CAAgBhG,QAAQ,CAACI,CAAC,CAAG,CAAC,QAAM,CAC5GgG,SAAS,gCAAAJ,MAAA,CACD,EAAE,CAAGS,aAAa,CAAG,CAAC,kDAAAT,MAAA,CAChB,EAAE,CAAGS,aAAa,sCAC/B,CACDD,MAAM,eAAAR,MAAA,CAAgB,CAAC,CAAGrF,WAAW,KACvC,CAAE,CAAAgG,QAAA,cAGF/G,IAAA,QACE8G,SAAS,CAAC,uHAAuH,CACjII,KAAK,CAAE,CACLV,SAAS,QAAAJ,MAAA,CAAS,EAAE,CAAGS,aAAa,qBAAAT,MAAA,CAAmB,EAAE,CAAGS,aAAa,CAAG,CAAC,cAAY,CACzFR,SAAS,UAAAD,MAAA,CAAW,CAAC,CAAGrF,WAAW,CAAG,GAAG,KAAG,CAC5C6F,MAAM,eAAAR,MAAA,CAAgB,CAAC,CAAGrF,WAAW,KACvC,CAAE,CACH,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAGL,CAAC,GAAGiG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAChE,GAAG,CAAC,CAACiE,CAAC,CAAEzF,CAAC,gBACtBxB,IAAA,QAEE8G,SAAS,CAAC,6DAA6D,CACvEI,KAAK,CAAE,CACLW,KAAK,IAAAzB,MAAA,CAAK,CAAC,EAAE,CAAG5E,CAAC,CAAG,EAAE,MAAI,CAC1BoG,WAAW,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAACpG,CAAC,CAAC,CAC5DsG,WAAW,CAAE,KAAK,CAClBzB,SAAS,gCAAAD,MAAA,CACGhG,QAAQ,CAACE,CAAC,EAAI,GAAG,CAAGkB,CAAC,CAAG,GAAG,CAAC,sCAAA4E,MAAA,CAC5BhG,QAAQ,CAACG,CAAC,EAAI,GAAG,CAAGiB,CAAC,CAAG,GAAG,CAAC,qCAAA4E,MAAA,CAC5BhG,QAAQ,CAACI,CAAC,EAAI,GAAG,CAAGgB,CAAC,CAAG,IAAI,CAAC,0BACxC,CACDgF,SAAS,aAAAJ,MAAA,CAAc,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAAC5E,CAAC,CAAC,CAAE,CACxEsE,OAAO,CAAE/E,WAAW,CAAG,GAAG,CAC1BgH,SAAS,SAAA3B,MAAA,CAAU,EAAE,CAAG5E,CAAC,CAAG,EAAE,6BAChC,CAAE,EAdGA,CAeN,CACF,CAAC,EACC,CAAC,cAENtB,KAAA,QAAK4G,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD7G,KAAA,QAAK4G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD/G,IAAA,SAAM8G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0CAAmC,CAAM,CAAC,cAC1E7G,KAAA,SAAM4G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,UAAQ,CAACnF,IAAI,CAAC+E,KAAK,CAAC5F,WAAW,CAAG,GAAG,CAAC,CAAC,GAAC,EAAM,CAAC,EAC9E,CAAC,cACNb,KAAA,MAAG4G,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,KACvC,CAACnF,IAAI,CAAC+E,KAAK,CAACvG,QAAQ,CAACE,CAAC,CAAC,CAAC,UAAK,CAACsB,IAAI,CAAC+E,KAAK,CAACvG,QAAQ,CAACG,CAAC,CAAC,CAAC,UAAK,CAACqB,IAAI,CAAC+E,KAAK,CAACvG,QAAQ,CAACI,CAAC,CAAC,CAAC,MACvF,EAAG,CAAC,cACJN,KAAA,MAAG4G,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,oBAAkB,CAAClG,SAAS,CAACwB,MAAM,EAAI,CAAC,EAC1E,CAAC,EACH,CAAC,cAENrC,IAAA,UAAA+G,QAAA,8IAKS,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5G,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}