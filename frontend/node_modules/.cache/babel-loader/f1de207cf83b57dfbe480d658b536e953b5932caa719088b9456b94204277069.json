{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/code_base/lms_project/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'./SamplesPage.css';// Simplified interface for shop display\n// Group videos by grade\nimport{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const VerticalCarousel=_ref=>{let{videos,grade,onVideoClick}=_ref;const containerRef=useRef(null);const trackRef=useRef(null);const animationRef=useRef(null);const lastUpdateRef=useRef(0);const[currentIndex,setCurrentIndex]=useState(0);const[isHovered,setIsHovered]=useState(false);const itemHeight=120;const visibleItems=3;const maxIndex=Math.max(0,videos.length-1);// Debounced animation with proper cleanup\nconst animate=useCallback(mouseY=>{const now=Date.now();if(now-lastUpdateRef.current<16)return;// ~60fps throttle\nlastUpdateRef.current=now;if(!containerRef.current||videos.length<=visibleItems)return;const containerRect=containerRef.current.getBoundingClientRect();const containerHeight=containerRect.height;const middleY=containerRect.top+containerHeight/2;const distanceFromCenter=mouseY-middleY;const threshold=30;if(Math.abs(distanceFromCenter)>threshold){const scrollDirection=distanceFromCenter>0?1:-1;const scrollSpeed=Math.min(Math.abs(distanceFromCenter)/2000,0.1);// Cap speed\nsetCurrentIndex(prev=>{const newIndex=prev+scrollDirection*scrollSpeed;return Math.max(0,Math.min(newIndex,maxIndex));});}},[maxIndex,videos.length,visibleItems]);// Proper animation frame management\nuseEffect(()=>{let currentMouseY=0;const animateLoop=()=>{if(isHovered){animate(currentMouseY);animationRef.current=requestAnimationFrame(animateLoop);}};const handleMouseMove=e=>{currentMouseY=e.clientY;};if(isHovered){document.addEventListener('mousemove',handleMouseMove);animationRef.current=requestAnimationFrame(animateLoop);}return()=>{document.removeEventListener('mousemove',handleMouseMove);if(animationRef.current){cancelAnimationFrame(animationRef.current);animationRef.current=null;}};},[isHovered,animate]);// Smooth track position updates\nuseEffect(()=>{if(!trackRef.current)return;const yPosition=-currentIndex*itemHeight;trackRef.current.style.transform=\"translateY(\".concat(yPosition,\"px)\");},[currentIndex,itemHeight]);const handleMouseEnter=()=>setIsHovered(true);const handleMouseLeave=()=>setIsHovered(false);if(videos.length===0){return/*#__PURE__*/_jsxs(\"div\",{className:\"carousel-wrapper\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"carousel-container-new\",children:/*#__PURE__*/_jsx(\"div\",{className:\"no-videos-placeholder-new\",children:/*#__PURE__*/_jsxs(\"p\",{children:[\"No videos for Grade \",grade]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"filename-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"filename-item empty\",children:\"No files\"})})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"carousel-wrapper\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"carousel-container-new \".concat(isHovered?'spinning':''),ref:containerRef,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave,children:/*#__PURE__*/_jsx(\"div\",{className:\"carousel-viewport-new\",children:/*#__PURE__*/_jsx(\"div\",{className:\"carousel-track-new\",ref:trackRef,children:videos.map((video,index)=>{const position=index-Math.floor(currentIndex);const isCenter=position===1;const isVisible=position>=0&&position<=2;return/*#__PURE__*/_jsx(\"div\",{className:\"carousel-item-new \".concat(isCenter?'center':'',\" \").concat(isVisible?'visible':''),children:/*#__PURE__*/_jsx(\"div\",{className:\"video-thumbnail-new\",onClick:()=>onVideoClick(video),children:/*#__PURE__*/_jsx(\"img\",{src:video.thumbnailUrl,alt:video.title,style:{width:'100%',height:'100%',objectFit:'cover',borderRadius:'6px'},onError:e=>{const target=e.target;// Retry loading the image once\nif(!target.dataset.retried){target.dataset.retried=\"true\";setTimeout(()=>{target.src=video.thumbnailUrl+\"?retry=\".concat(Date.now());},1000);}else{// Show fallback if retry failed\ntarget.style.backgroundColor='#daa520';target.style.display='flex';target.style.alignItems='center';target.style.justifyContent='center';target.style.color='white';target.style.fontSize='12px';target.textContent='Thumbnail';}}})})},video.id);})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"filename-list\",children:videos.map((video,index)=>{const position=index-Math.floor(currentIndex);const isCenter=position===1;const isVisible=position>=0&&position<=2;return/*#__PURE__*/_jsx(\"div\",{className:\"filename-item \".concat(isCenter?'center':'',\" \").concat(isVisible?'visible':''),children:video.filename},\"filename-\".concat(video.id));})})]});};const SamplesPage=()=>{const[videosByGrade,setVideosByGrade]=useState({});const[selectedVideo,setSelectedVideo]=useState(null);const[isLoading,setIsLoading]=useState(true);const[error,setError]=useState(null);const[gradeLoading,setGradeLoading]=useState({});const[gradeErrors,setGradeErrors]=useState({});// Improved API call with retry mechanism\nconst getVideoFilesFromAPI=useCallback(async function(grade){let retries=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3;try{const controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),10000);// 10s timeout\nconst response=await fetch(\"/api/videos/grade/\".concat(grade,\"/\"),{signal:controller.signal,headers:{'Accept':'application/json','Cache-Control':'no-cache'}});clearTimeout(timeoutId);if(!response.ok){if(response.status===404)return[];// No videos for this grade\nthrow new Error(\"HTTP \".concat(response.status));}const text=await response.text();if(!text.trim())return[];const data=JSON.parse(text);// Handle different response formats\nif(Array.isArray(data))return data;if(data&&Array.isArray(data.videos))return data.videos;if(data&&Array.isArray(data.files))return data.files;return[];}catch(error){if(retries>0){console.warn(\"Grade \".concat(grade,\" - API call failed, retrying... (\").concat(retries,\" retries left)\"));// Wait 1 second before retrying\nawait new Promise(resolve=>setTimeout(resolve,1000));return getVideoFilesFromAPI(grade,retries-1);}if(error instanceof Error){if(error.name==='AbortError'){console.warn(\"Grade \".concat(grade,\" - Request timeout\"));}else{console.error(\"Grade \".concat(grade,\" - API error:\"),error);}}else{console.error(\"Grade \".concat(grade,\" - An unknown error occurred:\"),error);}return[];}},[]);useEffect(()=>{const loadVideoSamples=async()=>{setIsLoading(true);setError(null);// Initialize loading states\nconst initialLoadingState={};for(let grade=1;grade<=8;grade++){initialLoadingState[grade]=true;}setGradeLoading(initialLoadingState);const videosByGradeTemp={};const errorsTemp={};// Initialize empty arrays\nfor(let grade=1;grade<=8;grade++){videosByGradeTemp[grade]=[];errorsTemp[grade]=null;}try{// Load all grades in parallel with proper error isolation\nconst gradePromises=Array.from({length:8},(_,i)=>{const grade=i+1;return getVideoFilesFromAPI(grade).then(videoFiles=>{if(videoFiles.length>0){videosByGradeTemp[grade]=videoFiles.map((filename,index)=>({id:\"grade-\".concat(grade,\"-video-\").concat(index),filename:filename,videoUrl:\"/media/videos/grade_\".concat(grade,\"/\").concat(filename),thumbnailUrl:\"/media/videos/grade_\".concat(grade,\"/\").concat(filename.replace(/\\.[^/.]+$/,'.jpg')),grade:grade,title:generateVideoTitle(filename,grade)}));}// Update loading state for this grade\nsetGradeLoading(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:false}));}).catch(error=>{console.error(\"Failed to load videos for grade \".concat(grade,\":\"),error);errorsTemp[grade]=\"Failed to load videos for grade \".concat(grade);setGradeErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:\"Failed to load videos for grade \".concat(grade)}));// Update loading state for this grade\nsetGradeLoading(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:false}));// Don't throw - just leave empty array\n});});await Promise.allSettled(gradePromises);setVideosByGrade(videosByGradeTemp);}catch(error){console.error('Error loading video samples:',error);setError('Failed to load video samples');}finally{setIsLoading(false);}};loadVideoSamples();},[getVideoFilesFromAPI]);const generateVideoTitle=(filename,grade)=>{const nameWithoutExt=filename.replace(/\\.[^/.]+$/,'');const cleaned=nameWithoutExt.replace(/[_-]/g,' ');const titleCase=cleaned.replace(/\\b\\w/g,l=>l.toUpperCase());return titleCase||\"Grade \".concat(grade,\" Video Sample\");};const handleVideoClick=video=>{setSelectedVideo(video);};const handleCloseVideo=()=>{setSelectedVideo(null);};if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"samples-page-new loading\",children:\"Loading video samples...\"});}if(error){return/*#__PURE__*/_jsxs(\"div\",{className:\"samples-page-new error-message\",children:[\"Error: \",error]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"samples-page-new\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"carousels-grid-new\",children:[1,2,3,4,5,6,7,8].map(grade=>/*#__PURE__*/_jsxs(\"div\",{className:\"grade-section\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"grade-title-new\",children:[\"Grade \",grade]}),gradeLoading[grade]?/*#__PURE__*/_jsx(\"div\",{className:\"carousel-wrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"carousel-container-new loading\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loading-placeholder-new\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Loading videos...\"})})})}):gradeErrors[grade]?/*#__PURE__*/_jsx(\"div\",{className:\"carousel-wrapper\",children:/*#__PURE__*/_jsx(\"div\",{className:\"carousel-container-new error\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"error-placeholder-new\",children:[/*#__PURE__*/_jsx(\"p\",{children:gradeErrors[grade]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{// Reset error and retry loading\nsetGradeErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:null}));setGradeLoading(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:true}));getVideoFilesFromAPI(grade).then(videoFiles=>{if(videoFiles.length>0){const updatedVideos=videoFiles.map((filename,index)=>({id:\"grade-\".concat(grade,\"-video-\").concat(index),filename:filename,videoUrl:\"/media/videos/grade_\".concat(grade,\"/\").concat(filename),thumbnailUrl:\"/media/videos/grade_\".concat(grade,\"/\").concat(filename.replace(/\\.[^/.]+$/,'.jpg')),grade:grade,title:generateVideoTitle(filename,grade)}));setVideosByGrade(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:updatedVideos}));}setGradeLoading(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:false}));}).catch(error=>{console.error(\"Failed to reload videos for grade \".concat(grade,\":\"),error);setGradeErrors(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:\"Failed to load videos for grade \".concat(grade)}));setGradeLoading(prev=>_objectSpread(_objectSpread({},prev),{},{[grade]:false}));});},children:\"Retry\"})]})})}):/*#__PURE__*/_jsx(VerticalCarousel,{videos:videosByGrade[grade]||[],grade:grade,onVideoClick:handleVideoClick})]},grade))}),selectedVideo&&/*#__PURE__*/_jsx(\"div\",{className:\"selected-video-section\",onClick:handleCloseVideo,children:/*#__PURE__*/_jsxs(\"div\",{className:\"video-content\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:handleCloseVideo,children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"video-player\",children:[/*#__PURE__*/_jsxs(\"h2\",{children:[selectedVideo.title,\" - Grade \",selectedVideo.grade]}),/*#__PURE__*/_jsxs(\"video\",{controls:true,width:\"100%\",style:{maxWidth:'600px'},children:[/*#__PURE__*/_jsx(\"source\",{src:selectedVideo.videoUrl,type:\"video/mp4\"}),\"Your browser does not support the video tag.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"video-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"purchase-button\",children:\"Add to Cart\"}),/*#__PURE__*/_jsx(\"button\",{className:\"info-button\",children:\"More Info\"})]})]})]})})]});};export default SamplesPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "jsxs", "_jsxs", "jsx", "_jsx", "VerticalCarousel", "_ref", "videos", "grade", "onVideoClick", "containerRef", "trackRef", "animationRef", "lastUpdateRef", "currentIndex", "setCurrentIndex", "isHovered", "setIsHovered", "itemHeight", "visibleItems", "maxIndex", "Math", "max", "length", "animate", "mouseY", "now", "Date", "current", "containerRect", "getBoundingClientRect", "containerHeight", "height", "middleY", "top", "distanceFromCenter", "threshold", "abs", "scrollDirection", "scrollSpeed", "min", "prev", "newIndex", "currentMouseY", "animateLoop", "requestAnimationFrame", "handleMouseMove", "e", "clientY", "document", "addEventListener", "removeEventListener", "cancelAnimationFrame", "yPosition", "style", "transform", "concat", "handleMouseEnter", "handleMouseLeave", "className", "children", "ref", "onMouseEnter", "onMouseLeave", "map", "video", "index", "position", "floor", "isCenter", "isVisible", "onClick", "src", "thumbnailUrl", "alt", "title", "width", "objectFit", "borderRadius", "onError", "target", "dataset", "retried", "setTimeout", "backgroundColor", "display", "alignItems", "justifyContent", "color", "fontSize", "textContent", "id", "filename", "SamplesPage", "videosByGrade", "setVideosByGrade", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "isLoading", "setIsLoading", "error", "setError", "gradeLoading", "setGradeLoading", "gradeErrors", "setGradeErrors", "getVideoFilesFromAPI", "retries", "arguments", "undefined", "controller", "AbortController", "timeoutId", "abort", "response", "fetch", "signal", "headers", "clearTimeout", "ok", "status", "Error", "text", "trim", "data", "JSON", "parse", "Array", "isArray", "files", "console", "warn", "Promise", "resolve", "name", "loadVideoSamples", "initialLoadingState", "videosByGradeTemp", "errorsTemp", "gradePromises", "from", "_", "i", "then", "videoFiles", "videoUrl", "replace", "generateVideoTitle", "_objectSpread", "catch", "allSettled", "nameWithoutExt", "cleaned", "titleCase", "l", "toUpperCase", "handleVideoClick", "handleCloseVideo", "updatedVideos", "stopPropagation", "controls", "max<PERSON><PERSON><PERSON>", "type"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport './SamplesPage.css';\n\n// Simplified interface for shop display\ninterface VideoSample {\n  id: string;\n  filename: string;\n  videoUrl: string;\n  thumbnailUrl: string;\n  grade: number;\n  title: string;\n}\n\n// Group videos by grade\ninterface VideosByGrade {\n  [grade: number]: VideoSample[];\n}\n\ninterface VerticalCarouselProps {\n  videos: VideoSample[];\n  grade: number;\n  onVideoClick: (video: VideoSample) => void;\n}\n\nconst VerticalCarousel: React.FC<VerticalCarouselProps> = ({ videos, grade, onVideoClick }) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const trackRef = useRef<HTMLDivElement>(null);\n  const animationRef = useRef<number | null>(null);\n  const lastUpdateRef = useRef<number>(0);\n  \n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isHovered, setIsHovered] = useState(false);\n  \n  const itemHeight = 120;\n  const visibleItems = 3;\n  const maxIndex = Math.max(0, videos.length - 1);\n\n  // Debounced animation with proper cleanup\n  const animate = useCallback((mouseY: number) => {\n    const now = Date.now();\n    if (now - lastUpdateRef.current < 16) return; // ~60fps throttle\n    lastUpdateRef.current = now;\n\n    if (!containerRef.current || videos.length <= visibleItems) return;\n\n    const containerRect = containerRef.current.getBoundingClientRect();\n    const containerHeight = containerRect.height;\n    const middleY = containerRect.top + containerHeight / 2;\n    \n    const distanceFromCenter = mouseY - middleY;\n    const threshold = 30;\n    \n    if (Math.abs(distanceFromCenter) > threshold) {\n      const scrollDirection = distanceFromCenter > 0 ? 1 : -1;\n      const scrollSpeed = Math.min(Math.abs(distanceFromCenter) / 2000, 0.1); // Cap speed\n      \n      setCurrentIndex(prev => {\n        const newIndex = prev + (scrollDirection * scrollSpeed);\n        return Math.max(0, Math.min(newIndex, maxIndex));\n      });\n    }\n  }, [maxIndex, videos.length, visibleItems]);\n\n  // Proper animation frame management\n  useEffect(() => {\n    let currentMouseY = 0;\n    \n    const animateLoop = () => {\n      if (isHovered) {\n        animate(currentMouseY);\n        animationRef.current = requestAnimationFrame(animateLoop);\n      }\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      currentMouseY = e.clientY;\n    };\n\n    if (isHovered) {\n      document.addEventListener('mousemove', handleMouseMove);\n      animationRef.current = requestAnimationFrame(animateLoop);\n    }\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = null;\n      }\n    };\n  }, [isHovered, animate]);\n\n  // Smooth track position updates\n  useEffect(() => {\n    if (!trackRef.current) return;\n    \n    const yPosition = -currentIndex * itemHeight;\n    trackRef.current.style.transform = `translateY(${yPosition}px)`;\n  }, [currentIndex, itemHeight]);\n\n  const handleMouseEnter = () => setIsHovered(true);\n  const handleMouseLeave = () => setIsHovered(false);\n\n\n  if (videos.length === 0) {\n    return (\n      <div className=\"carousel-wrapper\">\n        <div className=\"carousel-container-new\">\n          <div className=\"no-videos-placeholder-new\">\n            <p>No videos for Grade {grade}</p>\n          </div>\n        </div>\n        <div className=\"filename-list\">\n          <div className=\"filename-item empty\">No files</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"carousel-wrapper\">\n      <div\n        className={`carousel-container-new ${isHovered ? 'spinning' : ''}`}\n        ref={containerRef}\n        onMouseEnter={handleMouseEnter}\n        onMouseLeave={handleMouseLeave}\n      >\n        <div className=\"carousel-viewport-new\">\n          <div className=\"carousel-track-new\" ref={trackRef}>\n            {videos.map((video, index) => {\n              const position = index - Math.floor(currentIndex);\n              const isCenter = position === 1;\n              const isVisible = position >= 0 && position <= 2;\n              \n              return (\n                <div \n                  key={video.id} \n                  className={`carousel-item-new ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`}\n                >\n                  <div className=\"video-thumbnail-new\" onClick={() => onVideoClick(video)}>\n                    <img\n                      src={video.thumbnailUrl}\n                      alt={video.title}\n                      style={{\n                        width: '100%',\n                        height: '100%',\n                        objectFit: 'cover',\n                        borderRadius: '6px'\n                      }}\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement;\n                        // Retry loading the image once\n                        if (!target.dataset.retried) {\n                          target.dataset.retried = \"true\";\n                          setTimeout(() => {\n                            target.src = video.thumbnailUrl + `?retry=${Date.now()}`;\n                          }, 1000);\n                        } else {\n                          // Show fallback if retry failed\n                          target.style.backgroundColor = '#daa520';\n                          target.style.display = 'flex';\n                          target.style.alignItems = 'center';\n                          target.style.justifyContent = 'center';\n                          target.style.color = 'white';\n                          target.style.fontSize = '12px';\n                          target.textContent = 'Thumbnail';\n                        }\n                      }}\n                    />\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"filename-list\">\n        {videos.map((video, index) => {\n          const position = index - Math.floor(currentIndex);\n          const isCenter = position === 1;\n          const isVisible = position >= 0 && position <= 2;\n          \n          return (\n            <div \n              key={`filename-${video.id}`}\n              className={`filename-item ${isCenter ? 'center' : ''} ${isVisible ? 'visible' : ''}`}\n            >\n              {video.filename}\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nconst SamplesPage: React.FC = () => {\n  const [videosByGrade, setVideosByGrade] = useState<VideosByGrade>({});\n  const [selectedVideo, setSelectedVideo] = useState<VideoSample | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [gradeLoading, setGradeLoading] = useState<{[key: number]: boolean}>({});\n  const [gradeErrors, setGradeErrors] = useState<{[key: number]: string | null}>({});\n\n  // Improved API call with retry mechanism\n  const getVideoFilesFromAPI = useCallback(async (grade: number, retries = 3): Promise<string[]> => {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout\n      \n      const response = await fetch(`/api/videos/grade/${grade}/`, {\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n          'Cache-Control': 'no-cache'\n        }\n      });\n      \n      clearTimeout(timeoutId);\n      \n      if (!response.ok) {\n        if (response.status === 404) return []; // No videos for this grade\n        throw new Error(`HTTP ${response.status}`);\n      }\n      \n      const text = await response.text();\n      if (!text.trim()) return [];\n      \n      const data = JSON.parse(text);\n      \n      // Handle different response formats\n      if (Array.isArray(data)) return data;\n      if (data && Array.isArray(data.videos)) return data.videos;\n      if (data && Array.isArray(data.files)) return data.files;\n      \n      return [];\n    } catch (error: unknown) {\n      if (retries > 0) {\n        console.warn(`Grade ${grade} - API call failed, retrying... (${retries} retries left)`);\n        // Wait 1 second before retrying\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        return getVideoFilesFromAPI(grade, retries - 1);\n      }\n      \n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          console.warn(`Grade ${grade} - Request timeout`);\n        } else {\n          console.error(`Grade ${grade} - API error:`, error);\n        }\n      } else {\n        console.error(`Grade ${grade} - An unknown error occurred:`, error);\n      }\n      return [];\n    }\n  }, []);\n\n  useEffect(() => {\n    const loadVideoSamples = async () => {\n      setIsLoading(true);\n      setError(null);\n      \n      // Initialize loading states\n      const initialLoadingState: {[key: number]: boolean} = {};\n      for (let grade = 1; grade <= 8; grade++) {\n        initialLoadingState[grade] = true;\n      }\n      setGradeLoading(initialLoadingState);\n      \n      const videosByGradeTemp: VideosByGrade = {};\n      const errorsTemp: {[key: number]: string | null} = {};\n      \n      // Initialize empty arrays\n      for (let grade = 1; grade <= 8; grade++) {\n        videosByGradeTemp[grade] = [];\n        errorsTemp[grade] = null;\n      }\n\n      try {\n        // Load all grades in parallel with proper error isolation\n        const gradePromises = Array.from({ length: 8 }, (_, i) => {\n          const grade = i + 1;\n          return getVideoFilesFromAPI(grade)\n            .then(videoFiles => {\n              if (videoFiles.length > 0) {\n                videosByGradeTemp[grade] = videoFiles.map((filename, index) => ({\n                  id: `grade-${grade}-video-${index}`,\n                  filename: filename,\n                  videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                  thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                  grade: grade,\n                  title: generateVideoTitle(filename, grade)\n                }));\n              }\n              // Update loading state for this grade\n              setGradeLoading(prev => ({...prev, [grade]: false}));\n            })\n            .catch(error => {\n              console.error(`Failed to load videos for grade ${grade}:`, error);\n              errorsTemp[grade] = `Failed to load videos for grade ${grade}`;\n              setGradeErrors(prev => ({...prev, [grade]: `Failed to load videos for grade ${grade}`}));\n              // Update loading state for this grade\n              setGradeLoading(prev => ({...prev, [grade]: false}));\n              // Don't throw - just leave empty array\n            });\n        });\n\n        await Promise.allSettled(gradePromises);\n        setVideosByGrade(videosByGradeTemp);\n        \n      } catch (error) {\n        console.error('Error loading video samples:', error);\n        setError('Failed to load video samples');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadVideoSamples();\n  }, [getVideoFilesFromAPI]);\n\n  const generateVideoTitle = (filename: string, grade: number): string => {\n    const nameWithoutExt = filename.replace(/\\.[^/.]+$/, '');\n    const cleaned = nameWithoutExt.replace(/[_-]/g, ' ');\n    const titleCase = cleaned.replace(/\\b\\w/g, l => l.toUpperCase());\n    return titleCase || `Grade ${grade} Video Sample`;\n  };\n\n  const handleVideoClick = (video: VideoSample) => {\n    setSelectedVideo(video);\n  };\n\n  const handleCloseVideo = () => {\n    setSelectedVideo(null);\n  };\n\n  if (isLoading) {\n    return <div className=\"samples-page-new loading\">Loading video samples...</div>;\n  }\n\n  if (error) {\n    return <div className=\"samples-page-new error-message\">Error: {error}</div>;\n  }\n\n  return (\n    <div className=\"samples-page-new\">\n      <div className=\"carousels-grid-new\">\n        {[1, 2, 3, 4, 5, 6, 7, 8].map(grade => (\n          <div key={grade} className=\"grade-section\">\n            <h2 className=\"grade-title-new\">Grade {grade}</h2>\n            {gradeLoading[grade] ? (\n              <div className=\"carousel-wrapper\">\n                <div className=\"carousel-container-new loading\">\n                  <div className=\"loading-placeholder-new\">\n                    <p>Loading videos...</p>\n                  </div>\n                </div>\n              </div>\n            ) : gradeErrors[grade] ? (\n              <div className=\"carousel-wrapper\">\n                <div className=\"carousel-container-new error\">\n                  <div className=\"error-placeholder-new\">\n                    <p>{gradeErrors[grade]}</p>\n                    <button\n                      onClick={() => {\n                        // Reset error and retry loading\n                        setGradeErrors(prev => ({...prev, [grade]: null}));\n                        setGradeLoading(prev => ({...prev, [grade]: true}));\n                        getVideoFilesFromAPI(grade).then(videoFiles => {\n                          if (videoFiles.length > 0) {\n                            const updatedVideos = videoFiles.map((filename, index) => ({\n                              id: `grade-${grade}-video-${index}`,\n                              filename: filename,\n                              videoUrl: `/media/videos/grade_${grade}/${filename}`,\n                              thumbnailUrl: `/media/videos/grade_${grade}/${filename.replace(/\\.[^/.]+$/, '.jpg')}`,\n                              grade: grade,\n                              title: generateVideoTitle(filename, grade)\n                            }));\n                            setVideosByGrade(prev => ({...prev, [grade]: updatedVideos}));\n                          }\n                          setGradeLoading(prev => ({...prev, [grade]: false}));\n                        }).catch(error => {\n                          console.error(`Failed to reload videos for grade ${grade}:`, error);\n                          setGradeErrors(prev => ({...prev, [grade]: `Failed to load videos for grade ${grade}`}));\n                          setGradeLoading(prev => ({...prev, [grade]: false}));\n                        });\n                      }}\n                    >\n                      Retry\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <VerticalCarousel\n                videos={videosByGrade[grade] || []}\n                grade={grade}\n                onVideoClick={handleVideoClick}\n              />\n            )}\n          </div>\n        ))}\n      </div>\n\n      {selectedVideo && (\n        <div className=\"selected-video-section\" onClick={handleCloseVideo}>\n          <div className=\"video-content\" onClick={(e) => e.stopPropagation()}>\n            <button className=\"close-button\" onClick={handleCloseVideo}>×</button>\n            <div className=\"video-player\">\n              <h2>{selectedVideo.title} - Grade {selectedVideo.grade}</h2>\n              <video controls width=\"100%\" style={{ maxWidth: '600px' }}>\n                <source src={selectedVideo.videoUrl} type=\"video/mp4\" />\n                Your browser does not support the video tag.\n              </video>\n              <div className=\"video-actions\">\n                <button className=\"purchase-button\">Add to Cart</button>\n                <button className=\"info-button\">More Info</button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SamplesPage;"], "mappings": "oIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,mBAAmB,CAE1B;AAUA;AAAA,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAWA,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAAqC,IAApC,CAAEC,MAAM,CAAEC,KAAK,CAAEC,YAAa,CAAC,CAAAH,IAAA,CACxF,KAAM,CAAAI,YAAY,CAAGX,MAAM,CAAiB,IAAI,CAAC,CACjD,KAAM,CAAAY,QAAQ,CAAGZ,MAAM,CAAiB,IAAI,CAAC,CAC7C,KAAM,CAAAa,YAAY,CAAGb,MAAM,CAAgB,IAAI,CAAC,CAChD,KAAM,CAAAc,aAAa,CAAGd,MAAM,CAAS,CAAC,CAAC,CAEvC,KAAM,CAACe,YAAY,CAAEC,eAAe,CAAC,CAAGlB,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAEjD,KAAM,CAAAqB,UAAU,CAAG,GAAG,CACtB,KAAM,CAAAC,YAAY,CAAG,CAAC,CACtB,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEf,MAAM,CAACgB,MAAM,CAAG,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAC,OAAO,CAAGxB,WAAW,CAAEyB,MAAc,EAAK,CAC9C,KAAM,CAAAC,GAAG,CAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,CACtB,GAAIA,GAAG,CAAGb,aAAa,CAACe,OAAO,CAAG,EAAE,CAAE,OAAQ;AAC9Cf,aAAa,CAACe,OAAO,CAAGF,GAAG,CAE3B,GAAI,CAAChB,YAAY,CAACkB,OAAO,EAAIrB,MAAM,CAACgB,MAAM,EAAIJ,YAAY,CAAE,OAE5D,KAAM,CAAAU,aAAa,CAAGnB,YAAY,CAACkB,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAClE,KAAM,CAAAC,eAAe,CAAGF,aAAa,CAACG,MAAM,CAC5C,KAAM,CAAAC,OAAO,CAAGJ,aAAa,CAACK,GAAG,CAAGH,eAAe,CAAG,CAAC,CAEvD,KAAM,CAAAI,kBAAkB,CAAGV,MAAM,CAAGQ,OAAO,CAC3C,KAAM,CAAAG,SAAS,CAAG,EAAE,CAEpB,GAAIf,IAAI,CAACgB,GAAG,CAACF,kBAAkB,CAAC,CAAGC,SAAS,CAAE,CAC5C,KAAM,CAAAE,eAAe,CAAGH,kBAAkB,CAAG,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CACvD,KAAM,CAAAI,WAAW,CAAGlB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACgB,GAAG,CAACF,kBAAkB,CAAC,CAAG,IAAI,CAAE,GAAG,CAAC,CAAE;AAExEpB,eAAe,CAAC0B,IAAI,EAAI,CACtB,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAAIH,eAAe,CAAGC,WAAY,CACvD,MAAO,CAAAlB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACmB,GAAG,CAACE,QAAQ,CAAEtB,QAAQ,CAAC,CAAC,CAClD,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAACA,QAAQ,CAAEb,MAAM,CAACgB,MAAM,CAAEJ,YAAY,CAAC,CAAC,CAE3C;AACArB,SAAS,CAAC,IAAM,CACd,GAAI,CAAA6C,aAAa,CAAG,CAAC,CAErB,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI5B,SAAS,CAAE,CACbQ,OAAO,CAACmB,aAAa,CAAC,CACtB/B,YAAY,CAACgB,OAAO,CAAGiB,qBAAqB,CAACD,WAAW,CAAC,CAC3D,CACF,CAAC,CAED,KAAM,CAAAE,eAAe,CAAIC,CAAa,EAAK,CACzCJ,aAAa,CAAGI,CAAC,CAACC,OAAO,CAC3B,CAAC,CAED,GAAIhC,SAAS,CAAE,CACbiC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEJ,eAAe,CAAC,CACvDlC,YAAY,CAACgB,OAAO,CAAGiB,qBAAqB,CAACD,WAAW,CAAC,CAC3D,CAEA,MAAO,IAAM,CACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEL,eAAe,CAAC,CAC1D,GAAIlC,YAAY,CAACgB,OAAO,CAAE,CACxBwB,oBAAoB,CAACxC,YAAY,CAACgB,OAAO,CAAC,CAC1ChB,YAAY,CAACgB,OAAO,CAAG,IAAI,CAC7B,CACF,CAAC,CACH,CAAC,CAAE,CAACZ,SAAS,CAAEQ,OAAO,CAAC,CAAC,CAExB;AACA1B,SAAS,CAAC,IAAM,CACd,GAAI,CAACa,QAAQ,CAACiB,OAAO,CAAE,OAEvB,KAAM,CAAAyB,SAAS,CAAG,CAACvC,YAAY,CAAGI,UAAU,CAC5CP,QAAQ,CAACiB,OAAO,CAAC0B,KAAK,CAACC,SAAS,eAAAC,MAAA,CAAiBH,SAAS,OAAK,CACjE,CAAC,CAAE,CAACvC,YAAY,CAAEI,UAAU,CAAC,CAAC,CAE9B,KAAM,CAAAuC,gBAAgB,CAAGA,CAAA,GAAMxC,YAAY,CAAC,IAAI,CAAC,CACjD,KAAM,CAAAyC,gBAAgB,CAAGA,CAAA,GAAMzC,YAAY,CAAC,KAAK,CAAC,CAGlD,GAAIV,MAAM,CAACgB,MAAM,GAAK,CAAC,CAAE,CACvB,mBACErB,KAAA,QAAKyD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BxD,IAAA,QAAKuD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrCxD,IAAA,QAAKuD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC1D,KAAA,MAAA0D,QAAA,EAAG,sBAAoB,CAACpD,KAAK,EAAI,CAAC,CAC/B,CAAC,CACH,CAAC,cACNJ,IAAA,QAAKuD,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BxD,IAAA,QAAKuD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,CAChD,CAAC,EACH,CAAC,CAEV,CAEA,mBACE1D,KAAA,QAAKyD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BxD,IAAA,QACEuD,SAAS,2BAAAH,MAAA,CAA4BxC,SAAS,CAAG,UAAU,CAAG,EAAE,CAAG,CACnE6C,GAAG,CAAEnD,YAAa,CAClBoD,YAAY,CAAEL,gBAAiB,CAC/BM,YAAY,CAAEL,gBAAiB,CAAAE,QAAA,cAE/BxD,IAAA,QAAKuD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpCxD,IAAA,QAAKuD,SAAS,CAAC,oBAAoB,CAACE,GAAG,CAAElD,QAAS,CAAAiD,QAAA,CAC/CrD,MAAM,CAACyD,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC5B,KAAM,CAAAC,QAAQ,CAAGD,KAAK,CAAG7C,IAAI,CAAC+C,KAAK,CAACtD,YAAY,CAAC,CACjD,KAAM,CAAAuD,QAAQ,CAAGF,QAAQ,GAAK,CAAC,CAC/B,KAAM,CAAAG,SAAS,CAAGH,QAAQ,EAAI,CAAC,EAAIA,QAAQ,EAAI,CAAC,CAEhD,mBACE/D,IAAA,QAEEuD,SAAS,sBAAAH,MAAA,CAAuBa,QAAQ,CAAG,QAAQ,CAAG,EAAE,MAAAb,MAAA,CAAIc,SAAS,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAV,QAAA,cAEzFxD,IAAA,QAAKuD,SAAS,CAAC,qBAAqB,CAACY,OAAO,CAAEA,CAAA,GAAM9D,YAAY,CAACwD,KAAK,CAAE,CAAAL,QAAA,cACtExD,IAAA,QACEoE,GAAG,CAAEP,KAAK,CAACQ,YAAa,CACxBC,GAAG,CAAET,KAAK,CAACU,KAAM,CACjBrB,KAAK,CAAE,CACLsB,KAAK,CAAE,MAAM,CACb5C,MAAM,CAAE,MAAM,CACd6C,SAAS,CAAE,OAAO,CAClBC,YAAY,CAAE,KAChB,CAAE,CACFC,OAAO,CAAGhC,CAAC,EAAK,CACd,KAAM,CAAAiC,MAAM,CAAGjC,CAAC,CAACiC,MAA0B,CAC3C;AACA,GAAI,CAACA,MAAM,CAACC,OAAO,CAACC,OAAO,CAAE,CAC3BF,MAAM,CAACC,OAAO,CAACC,OAAO,CAAG,MAAM,CAC/BC,UAAU,CAAC,IAAM,CACfH,MAAM,CAACR,GAAG,CAAGP,KAAK,CAACQ,YAAY,WAAAjB,MAAA,CAAa7B,IAAI,CAACD,GAAG,CAAC,CAAC,CAAE,CAC1D,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL;AACAsD,MAAM,CAAC1B,KAAK,CAAC8B,eAAe,CAAG,SAAS,CACxCJ,MAAM,CAAC1B,KAAK,CAAC+B,OAAO,CAAG,MAAM,CAC7BL,MAAM,CAAC1B,KAAK,CAACgC,UAAU,CAAG,QAAQ,CAClCN,MAAM,CAAC1B,KAAK,CAACiC,cAAc,CAAG,QAAQ,CACtCP,MAAM,CAAC1B,KAAK,CAACkC,KAAK,CAAG,OAAO,CAC5BR,MAAM,CAAC1B,KAAK,CAACmC,QAAQ,CAAG,MAAM,CAC9BT,MAAM,CAACU,WAAW,CAAG,WAAW,CAClC,CACF,CAAE,CACH,CAAC,CACC,CAAC,EAjCDzB,KAAK,CAAC0B,EAkCR,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CACH,CAAC,CACH,CAAC,cAENvF,IAAA,QAAKuD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BrD,MAAM,CAACyD,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC5B,KAAM,CAAAC,QAAQ,CAAGD,KAAK,CAAG7C,IAAI,CAAC+C,KAAK,CAACtD,YAAY,CAAC,CACjD,KAAM,CAAAuD,QAAQ,CAAGF,QAAQ,GAAK,CAAC,CAC/B,KAAM,CAAAG,SAAS,CAAGH,QAAQ,EAAI,CAAC,EAAIA,QAAQ,EAAI,CAAC,CAEhD,mBACE/D,IAAA,QAEEuD,SAAS,kBAAAH,MAAA,CAAmBa,QAAQ,CAAG,QAAQ,CAAG,EAAE,MAAAb,MAAA,CAAIc,SAAS,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAV,QAAA,CAEpFK,KAAK,CAAC2B,QAAQ,cAAApC,MAAA,CAHES,KAAK,CAAC0B,EAAE,CAItB,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,KAAM,CAAAE,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlG,QAAQ,CAAgB,CAAC,CAAC,CAAC,CACrE,KAAM,CAACmG,aAAa,CAAEC,gBAAgB,CAAC,CAAGpG,QAAQ,CAAqB,IAAI,CAAC,CAC5E,KAAM,CAACqG,SAAS,CAAEC,YAAY,CAAC,CAAGtG,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACuG,KAAK,CAAEC,QAAQ,CAAC,CAAGxG,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACyG,YAAY,CAAEC,eAAe,CAAC,CAAG1G,QAAQ,CAA2B,CAAC,CAAC,CAAC,CAC9E,KAAM,CAAC2G,WAAW,CAAEC,cAAc,CAAC,CAAG5G,QAAQ,CAAiC,CAAC,CAAC,CAAC,CAElF;AACA,KAAM,CAAA6G,oBAAoB,CAAG1G,WAAW,CAAC,eAAOQ,KAAa,CAAqC,IAAnC,CAAAmG,OAAO,CAAAC,SAAA,CAAArF,MAAA,IAAAqF,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CACxE,GAAI,CACF,KAAM,CAAAE,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAG7B,UAAU,CAAC,IAAM2B,UAAU,CAACG,KAAK,CAAC,CAAC,CAAE,KAAK,CAAC,CAAE;AAE/D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,sBAAA3D,MAAA,CAAsBhD,KAAK,MAAK,CAC1D4G,MAAM,CAAEN,UAAU,CAACM,MAAM,CACzBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBAAkB,CAC5B,eAAe,CAAE,UACnB,CACF,CAAC,CAAC,CAEFC,YAAY,CAACN,SAAS,CAAC,CAEvB,GAAI,CAACE,QAAQ,CAACK,EAAE,CAAE,CAChB,GAAIL,QAAQ,CAACM,MAAM,GAAK,GAAG,CAAE,MAAO,EAAE,CAAE;AACxC,KAAM,IAAI,CAAAC,KAAK,SAAAjE,MAAA,CAAS0D,QAAQ,CAACM,MAAM,CAAE,CAAC,CAC5C,CAEA,KAAM,CAAAE,IAAI,CAAG,KAAM,CAAAR,QAAQ,CAACQ,IAAI,CAAC,CAAC,CAClC,GAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAE,MAAO,EAAE,CAE3B,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,CAE7B;AACA,GAAIK,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,CAAE,MAAO,CAAAA,IAAI,CACpC,GAAIA,IAAI,EAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACrH,MAAM,CAAC,CAAE,MAAO,CAAAqH,IAAI,CAACrH,MAAM,CAC1D,GAAIqH,IAAI,EAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,KAAK,CAAC,CAAE,MAAO,CAAAL,IAAI,CAACK,KAAK,CAExD,MAAO,EAAE,CACX,CAAE,MAAO7B,KAAc,CAAE,CACvB,GAAIO,OAAO,CAAG,CAAC,CAAE,CACfuB,OAAO,CAACC,IAAI,UAAA3E,MAAA,CAAUhD,KAAK,sCAAAgD,MAAA,CAAoCmD,OAAO,kBAAgB,CAAC,CACvF;AACA,KAAM,IAAI,CAAAyB,OAAO,CAACC,OAAO,EAAIlD,UAAU,CAACkD,OAAO,CAAE,IAAI,CAAC,CAAC,CACvD,MAAO,CAAA3B,oBAAoB,CAAClG,KAAK,CAAEmG,OAAO,CAAG,CAAC,CAAC,CACjD,CAEA,GAAIP,KAAK,WAAY,CAAAqB,KAAK,CAAE,CAC1B,GAAIrB,KAAK,CAACkC,IAAI,GAAK,YAAY,CAAE,CAC/BJ,OAAO,CAACC,IAAI,UAAA3E,MAAA,CAAUhD,KAAK,sBAAoB,CAAC,CAClD,CAAC,IAAM,CACL0H,OAAO,CAAC9B,KAAK,UAAA5C,MAAA,CAAUhD,KAAK,kBAAiB4F,KAAK,CAAC,CACrD,CACF,CAAC,IAAM,CACL8B,OAAO,CAAC9B,KAAK,UAAA5C,MAAA,CAAUhD,KAAK,kCAAiC4F,KAAK,CAAC,CACrE,CACA,MAAO,EAAE,CACX,CACF,CAAC,CAAE,EAAE,CAAC,CAENtG,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyI,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCpC,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAmC,mBAA6C,CAAG,CAAC,CAAC,CACxD,IAAK,GAAI,CAAAhI,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,CAAC,CAAEA,KAAK,EAAE,CAAE,CACvCgI,mBAAmB,CAAChI,KAAK,CAAC,CAAG,IAAI,CACnC,CACA+F,eAAe,CAACiC,mBAAmB,CAAC,CAEpC,KAAM,CAAAC,iBAAgC,CAAG,CAAC,CAAC,CAC3C,KAAM,CAAAC,UAA0C,CAAG,CAAC,CAAC,CAErD;AACA,IAAK,GAAI,CAAAlI,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,CAAC,CAAEA,KAAK,EAAE,CAAE,CACvCiI,iBAAiB,CAACjI,KAAK,CAAC,CAAG,EAAE,CAC7BkI,UAAU,CAAClI,KAAK,CAAC,CAAG,IAAI,CAC1B,CAEA,GAAI,CACF;AACA,KAAM,CAAAmI,aAAa,CAAGZ,KAAK,CAACa,IAAI,CAAC,CAAErH,MAAM,CAAE,CAAE,CAAC,CAAE,CAACsH,CAAC,CAAEC,CAAC,GAAK,CACxD,KAAM,CAAAtI,KAAK,CAAGsI,CAAC,CAAG,CAAC,CACnB,MAAO,CAAApC,oBAAoB,CAAClG,KAAK,CAAC,CAC/BuI,IAAI,CAACC,UAAU,EAAI,CAClB,GAAIA,UAAU,CAACzH,MAAM,CAAG,CAAC,CAAE,CACzBkH,iBAAiB,CAACjI,KAAK,CAAC,CAAGwI,UAAU,CAAChF,GAAG,CAAC,CAAC4B,QAAQ,CAAE1B,KAAK,IAAM,CAC9DyB,EAAE,UAAAnC,MAAA,CAAWhD,KAAK,YAAAgD,MAAA,CAAUU,KAAK,CAAE,CACnC0B,QAAQ,CAAEA,QAAQ,CAClBqD,QAAQ,wBAAAzF,MAAA,CAAyBhD,KAAK,MAAAgD,MAAA,CAAIoC,QAAQ,CAAE,CACpDnB,YAAY,wBAAAjB,MAAA,CAAyBhD,KAAK,MAAAgD,MAAA,CAAIoC,QAAQ,CAACsD,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAAE,CACrF1I,KAAK,CAAEA,KAAK,CACZmE,KAAK,CAAEwE,kBAAkB,CAACvD,QAAQ,CAAEpF,KAAK,CAC3C,CAAC,CAAC,CAAC,CACL,CACA;AACA+F,eAAe,CAAC9D,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,KAAK,EAAE,CAAC,CACtD,CAAC,CAAC,CACD6I,KAAK,CAACjD,KAAK,EAAI,CACd8B,OAAO,CAAC9B,KAAK,oCAAA5C,MAAA,CAAoChD,KAAK,MAAK4F,KAAK,CAAC,CACjEsC,UAAU,CAAClI,KAAK,CAAC,oCAAAgD,MAAA,CAAsChD,KAAK,CAAE,CAC9DiG,cAAc,CAAChE,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,qCAAAgD,MAAA,CAAsChD,KAAK,CAAE,EAAE,CAAC,CACxF;AACA+F,eAAe,CAAC9D,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,KAAK,EAAE,CAAC,CACpD;AACF,CAAC,CAAC,CACN,CAAC,CAAC,CAEF,KAAM,CAAA4H,OAAO,CAACkB,UAAU,CAACX,aAAa,CAAC,CACvC5C,gBAAgB,CAAC0C,iBAAiB,CAAC,CAErC,CAAE,MAAOrC,KAAK,CAAE,CACd8B,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDC,QAAQ,CAAC,8BAA8B,CAAC,CAC1C,CAAC,OAAS,CACRF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAEDoC,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAAC7B,oBAAoB,CAAC,CAAC,CAE1B,KAAM,CAAAyC,kBAAkB,CAAGA,CAACvD,QAAgB,CAAEpF,KAAa,GAAa,CACtE,KAAM,CAAA+I,cAAc,CAAG3D,QAAQ,CAACsD,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CACxD,KAAM,CAAAM,OAAO,CAAGD,cAAc,CAACL,OAAO,CAAC,OAAO,CAAE,GAAG,CAAC,CACpD,KAAM,CAAAO,SAAS,CAAGD,OAAO,CAACN,OAAO,CAAC,OAAO,CAAEQ,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAChE,MAAO,CAAAF,SAAS,WAAAjG,MAAA,CAAahD,KAAK,iBAAe,CACnD,CAAC,CAED,KAAM,CAAAoJ,gBAAgB,CAAI3F,KAAkB,EAAK,CAC/CgC,gBAAgB,CAAChC,KAAK,CAAC,CACzB,CAAC,CAED,KAAM,CAAA4F,gBAAgB,CAAGA,CAAA,GAAM,CAC7B5D,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,GAAIC,SAAS,CAAE,CACb,mBAAO9F,IAAA,QAAKuD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,0BAAwB,CAAK,CAAC,CACjF,CAEA,GAAIwC,KAAK,CAAE,CACT,mBAAOlG,KAAA,QAAKyD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAC,SAAO,CAACwC,KAAK,EAAM,CAAC,CAC7E,CAEA,mBACElG,KAAA,QAAKyD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BxD,IAAA,QAAKuD,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACI,GAAG,CAACxD,KAAK,eACjCN,KAAA,QAAiByD,SAAS,CAAC,eAAe,CAAAC,QAAA,eACxC1D,KAAA,OAAIyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,QAAM,CAACpD,KAAK,EAAK,CAAC,CACjD8F,YAAY,CAAC9F,KAAK,CAAC,cAClBJ,IAAA,QAAKuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BxD,IAAA,QAAKuD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CxD,IAAA,QAAKuD,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCxD,IAAA,MAAAwD,QAAA,CAAG,mBAAiB,CAAG,CAAC,CACrB,CAAC,CACH,CAAC,CACH,CAAC,CACJ4C,WAAW,CAAChG,KAAK,CAAC,cACpBJ,IAAA,QAAKuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BxD,IAAA,QAAKuD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C1D,KAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCxD,IAAA,MAAAwD,QAAA,CAAI4C,WAAW,CAAChG,KAAK,CAAC,CAAI,CAAC,cAC3BJ,IAAA,WACEmE,OAAO,CAAEA,CAAA,GAAM,CACb;AACAkC,cAAc,CAAChE,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,IAAI,EAAE,CAAC,CAClD+F,eAAe,CAAC9D,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,IAAI,EAAE,CAAC,CACnDkG,oBAAoB,CAAClG,KAAK,CAAC,CAACuI,IAAI,CAACC,UAAU,EAAI,CAC7C,GAAIA,UAAU,CAACzH,MAAM,CAAG,CAAC,CAAE,CACzB,KAAM,CAAAuI,aAAa,CAAGd,UAAU,CAAChF,GAAG,CAAC,CAAC4B,QAAQ,CAAE1B,KAAK,IAAM,CACzDyB,EAAE,UAAAnC,MAAA,CAAWhD,KAAK,YAAAgD,MAAA,CAAUU,KAAK,CAAE,CACnC0B,QAAQ,CAAEA,QAAQ,CAClBqD,QAAQ,wBAAAzF,MAAA,CAAyBhD,KAAK,MAAAgD,MAAA,CAAIoC,QAAQ,CAAE,CACpDnB,YAAY,wBAAAjB,MAAA,CAAyBhD,KAAK,MAAAgD,MAAA,CAAIoC,QAAQ,CAACsD,OAAO,CAAC,WAAW,CAAE,MAAM,CAAC,CAAE,CACrF1I,KAAK,CAAEA,KAAK,CACZmE,KAAK,CAAEwE,kBAAkB,CAACvD,QAAQ,CAAEpF,KAAK,CAC3C,CAAC,CAAC,CAAC,CACHuF,gBAAgB,CAACtD,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAGsJ,aAAa,EAAE,CAAC,CAC/D,CACAvD,eAAe,CAAC9D,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,KAAK,EAAE,CAAC,CACtD,CAAC,CAAC,CAAC6I,KAAK,CAACjD,KAAK,EAAI,CAChB8B,OAAO,CAAC9B,KAAK,sCAAA5C,MAAA,CAAsChD,KAAK,MAAK4F,KAAK,CAAC,CACnEK,cAAc,CAAChE,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,qCAAAgD,MAAA,CAAsChD,KAAK,CAAE,EAAE,CAAC,CACxF+F,eAAe,CAAC9D,IAAI,EAAA2G,aAAA,CAAAA,aAAA,IAAS3G,IAAI,MAAE,CAACjC,KAAK,EAAG,KAAK,EAAE,CAAC,CACtD,CAAC,CAAC,CACJ,CAAE,CAAAoD,QAAA,CACH,OAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,cAENxD,IAAA,CAACC,gBAAgB,EACfE,MAAM,CAAEuF,aAAa,CAACtF,KAAK,CAAC,EAAI,EAAG,CACnCA,KAAK,CAAEA,KAAM,CACbC,YAAY,CAAEmJ,gBAAiB,CAChC,CACF,GAnDOpJ,KAoDL,CACN,CAAC,CACC,CAAC,CAELwF,aAAa,eACZ5F,IAAA,QAAKuD,SAAS,CAAC,wBAAwB,CAACY,OAAO,CAAEsF,gBAAiB,CAAAjG,QAAA,cAChE1D,KAAA,QAAKyD,SAAS,CAAC,eAAe,CAACY,OAAO,CAAGxB,CAAC,EAAKA,CAAC,CAACgH,eAAe,CAAC,CAAE,CAAAnG,QAAA,eACjExD,IAAA,WAAQuD,SAAS,CAAC,cAAc,CAACY,OAAO,CAAEsF,gBAAiB,CAAAjG,QAAA,CAAC,MAAC,CAAQ,CAAC,cACtE1D,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1D,KAAA,OAAA0D,QAAA,EAAKoC,aAAa,CAACrB,KAAK,CAAC,WAAS,CAACqB,aAAa,CAACxF,KAAK,EAAK,CAAC,cAC5DN,KAAA,UAAO8J,QAAQ,MAACpF,KAAK,CAAC,MAAM,CAACtB,KAAK,CAAE,CAAE2G,QAAQ,CAAE,OAAQ,CAAE,CAAArG,QAAA,eACxDxD,IAAA,WAAQoE,GAAG,CAAEwB,aAAa,CAACiD,QAAS,CAACiB,IAAI,CAAC,WAAW,CAAE,CAAC,+CAE1D,EAAO,CAAC,cACRhK,KAAA,QAAKyD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BxD,IAAA,WAAQuD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,aAAW,CAAQ,CAAC,cACxDxD,IAAA,WAAQuD,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,WAAS,CAAQ,CAAC,EAC/C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAiC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}