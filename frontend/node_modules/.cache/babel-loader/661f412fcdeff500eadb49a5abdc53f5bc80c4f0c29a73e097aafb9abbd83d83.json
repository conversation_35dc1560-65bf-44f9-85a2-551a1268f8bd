{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport '../Xtra/Gyroscope.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateVideoPage = () => {\n  _s();\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n  const formFields = [{\n    label: \"lesson title\",\n    type: \"text\",\n    placeholder: \"enter your video lesson title...\"\n  }, {\n    label: \"student's first name\",\n    type: \"text\",\n    placeholder: \"All Of This Is Optional!!\"\n  }, {\n    label: \"student's age\",\n    type: \"select\",\n    options: [\"6\", \"7\", \"8\", \"9\"]\n  }, {\n    label: \"teacher's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"]\n  }, {\n    label: \"student's voice\",\n    type: \"select\",\n    options: [\"male\", \"female\", \"clone your own\"]\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"westworld-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"particles-container\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-particle\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 3}s`,\n          animationDuration: `${2 + Math.random() * 2}s`\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"milk-waves\",\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"100%\",\n        height: \"100%\",\n        viewBox: \"0 0 1000 1000\",\n        preserveAspectRatio: \"none\",\n        children: [/*#__PURE__*/_jsxDEV(\"defs\", {\n          children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"milkGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"100%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#ffffff\",\n              stopOpacity: \"0.8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"50%\",\n              stopColor: \"#f8fafc\",\n              stopOpacity: \"0.6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#f1f5f9\",\n              stopOpacity: \"0.4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\",\n          fill: \"url(#milkGradient)\",\n          className: \"milk-surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `form-container ${formVisible ? 'visible' : 'hidden'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"main-title\",\n            children: \"CREATE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"title-line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-fields\",\n          children: [formFields.map((field, index) => {\n            // ... (previous field rendering logic remains the same)\n          }), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `gyroscope-wrapper ${childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gyroscope-test-container\",\n              onClick: () => console.log('Generate video clicked!'),\n              children: /*#__PURE__*/_jsxDEV(Gyroscope, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `gyroscope-section ${childFieldsVisible && currentField > formFields.length - 1 ? 'visible' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(Gyroscope, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid-pattern\",\n        children: [...Array(400)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid-cell\"\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 40\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ambient-light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 3\n  }, this);\n};\n_s(CreateVideoPage, \"yQFMt5143mRLX7AkiooqDviJs0o=\");\n_c = CreateVideoPage;\nexport default CreateVideoPage;\nvar _c;\n$RefreshReg$(_c, \"CreateVideoPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Gyroscope", "jsxDEV", "_jsxDEV", "CreateVideoPage", "_s", "formVisible", "setFormVisible", "current<PERSON><PERSON>", "setCurrentField", "lessonTitle", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childFieldsVisible", "<PERSON><PERSON><PERSON><PERSON>FieldsVisible", "setTimeout", "length", "timer", "setInterval", "prev", "formFields", "clearInterval", "label", "type", "placeholder", "options", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "preserveAspectRatio", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "stopOpacity", "d", "fill", "field", "index", "onClick", "console", "log", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './CreateVideoPage.css';\nimport Gyroscope from '../Xtra/Gyroscope';\nimport '../Xtra/Gyroscope.css';\n\nconst CreateVideoPage = () => {\n  const [formVisible, setFormVisible] = useState(false);\n  const [currentField, setCurrentField] = useState(0);\n  const [lessonTitle, setLessonTitle] = useState('');\n  const [childFieldsVisible, setChildFieldsVisible] = useState(false);\n\n  useEffect(() => {\n    // Delayed entrance for dramatic effect\n    setTimeout(() => {\n      setFormVisible(true);\n      setCurrentField(1); // Show lesson title\n    }, 800);\n  }, []);\n\n  useEffect(() => {\n    if (lessonTitle.length >= 3) {\n      setChildFieldsVisible(true);\n    } else {\n      setChildFieldsVisible(false);\n      setCurrentField(1); // Reset to show only first\n    }\n  }, [lessonTitle]);\n\n  useEffect(() => {\n    if (formVisible && childFieldsVisible) {\n      const timer = setInterval(() => {\n        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);\n      }, 600);\n\n      return () => clearInterval(timer);\n    }\n  }, [formVisible, childFieldsVisible]);\n\n  const formFields = [\n    { label: \"lesson title\", type: \"text\", placeholder: \"enter your video lesson title...\" },\n    { label: \"student's first name\", type: \"text\", placeholder: \"All Of This Is Optional!!\" },\n    { label: \"student's age\", type: \"select\", options: [\"6\", \"7\", \"8\", \"9\"] },\n    { label: \"teacher's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] },\n    { label: \"student's voice\", type: \"select\", options: [\"male\", \"female\", \"clone your own\"] }\n  ];\n\n  return (\n  <div className=\"westworld-container\">\n    {/* Floating particles for depth */}\n    <div className=\"particles-container\">\n      {[...Array(20)].map((_, i) => (\n        <div\n          key={i}\n          className=\"floating-particle\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n            animationDelay: `${Math.random() * 3}s`,\n            animationDuration: `${2 + Math.random() * 2}s`\n          }}\n        />\n      ))}\n    </div>\n\n    {/* Milk-like surface waves */}\n    <div className=\"milk-waves\">\n      <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 1000 1000\" preserveAspectRatio=\"none\">\n        <defs>\n          <linearGradient id=\"milkGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#ffffff\" stopOpacity=\"0.8\" />\n            <stop offset=\"50%\" stopColor=\"#f8fafc\" stopOpacity=\"0.6\" />\n            <stop offset=\"100%\" stopColor=\"#f1f5f9\" stopOpacity=\"0.4\" />\n          </linearGradient>\n        </defs>\n        <path\n          d=\"M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z\"\n          fill=\"url(#milkGradient)\"\n          className=\"milk-surface\"\n        />\n      </svg>\n    </div>\n\n    {/* Main content */}\n    <div className=\"content-wrapper\">\n      <div className={`form-container ${formVisible ? 'visible' : 'hidden'}`}>\n        {/* Title */}\n        <div className=\"title-section\">\n          <h1 className=\"main-title\">CREATE</h1>\n          <div className=\"title-line\"></div>\n        </div>\n\n        {/* Form */}\n        <div className=\"form-fields\">\n          {formFields.map((field, index) => {\n            // ... (previous field rendering logic remains the same)\n          })}\n\n          {/* Gyroscope Generate Button */}\n          <div className={`gyroscope-wrapper ${\n            childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'\n          }`}>\n            <div\n              className=\"gyroscope-test-container\"\n              onClick={() => console.log('Generate video clicked!')}\n            >\n              <Gyroscope />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Gyroscope Section */}\n      <div className={`gyroscope-section ${childFieldsVisible && currentField > formFields.length - 1 ? 'visible' : ''}`}>\n        <Gyroscope />\n      </div>\n    </div>\n\n    {/* Grid overlay and ambient light */}\n    <div className=\"grid-overlay\">\n      <div className=\"grid-pattern\">\n        {[...Array(400)].map((_, i) => <div key={i} className=\"grid-cell\"></div>)}\n      </div>\n    </div>\n    <div className=\"ambient-light\"></div>\n  </div>\n);\n};\n\nexport default CreateVideoPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,uBAAuB;AAC9B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd;IACAc,UAAU,CAAC,MAAM;MACfP,cAAc,CAAC,IAAI,CAAC;MACpBE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EAENT,SAAS,CAAC,MAAM;IACd,IAAIU,WAAW,CAACK,MAAM,IAAI,CAAC,EAAE;MAC3BF,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,MAAM;MACLA,qBAAqB,CAAC,KAAK,CAAC;MAC5BJ,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EAEjBV,SAAS,CAAC,MAAM;IACd,IAAIM,WAAW,IAAIM,kBAAkB,EAAE;MACrC,MAAMI,KAAK,GAAGC,WAAW,CAAC,MAAM;QAC9BR,eAAe,CAACS,IAAI,IAAIA,IAAI,GAAGC,UAAU,CAACJ,MAAM,GAAGG,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC;MACrE,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAME,aAAa,CAACJ,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACV,WAAW,EAAEM,kBAAkB,CAAC,CAAC;EAErC,MAAMO,UAAU,GAAG,CACjB;IAAEE,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAAmC,CAAC,EACxF;IAAEF,KAAK,EAAE,sBAAsB;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAA4B,CAAC,EACzF;IAAEF,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAAE,CAAC,EACzE;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB;EAAE,CAAC,EAC3F;IAAEH,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,gBAAgB;EAAE,CAAC,CAC5F;EAED,oBACArB,OAAA;IAAKsB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElCvB,OAAA;MAAKsB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EACjC,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvB3B,OAAA;QAEEsB,SAAS,EAAC,mBAAmB;QAC7BM,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C;MAAE,GAPGJ,CAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQP,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBvB,OAAA;QAAKuC,KAAK,EAAC,MAAM;QAACC,MAAM,EAAC,MAAM;QAACC,OAAO,EAAC,eAAe;QAACC,mBAAmB,EAAC,MAAM;QAAAnB,QAAA,gBAChFvB,OAAA;UAAAuB,QAAA,eACEvB,OAAA;YAAgB2C,EAAE,EAAC,cAAc;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAACC,EAAE,EAAC,MAAM;YAAAxB,QAAA,gBACnEvB,OAAA;cAAMgD,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DtC,OAAA;cAAMgD,MAAM,EAAC,KAAK;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DtC,OAAA;cAAMgD,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACPtC,OAAA;UACEmD,CAAC,EAAC,wDAAwD;UAC1DC,IAAI,EAAC,oBAAoB;UACzB9B,SAAS,EAAC;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAKsB,SAAS,EAAE,kBAAkBnB,WAAW,GAAG,SAAS,GAAG,QAAQ,EAAG;QAAAoB,QAAA,gBAErEvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvB,OAAA;YAAIsB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCtC,OAAA;YAAKsB,SAAS,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGNtC,OAAA;UAAKsB,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBP,UAAU,CAACS,GAAG,CAAC,CAAC4B,KAAK,EAAEC,KAAK,KAAK;YAChC;UAAA,CACD,CAAC,eAGFtD,OAAA;YAAKsB,SAAS,EAAE,qBACdb,kBAAkB,IAAIJ,YAAY,GAAGW,UAAU,CAACJ,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ,EAChF;YAAAW,QAAA,eACDvB,OAAA;cACEsB,SAAS,EAAC,0BAA0B;cACpCiC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAE;cAAAlC,QAAA,eAEtDvB,OAAA,CAACF,SAAS;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtC,OAAA;QAAKsB,SAAS,EAAE,qBAAqBb,kBAAkB,IAAIJ,YAAY,GAAGW,UAAU,CAACJ,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,EAAG;QAAAW,QAAA,eACjHvB,OAAA,CAACF,SAAS;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKsB,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BvB,OAAA;QAAKsB,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B,CAAC,GAAGC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAAK3B,OAAA;UAAasB,SAAS,EAAC;QAAW,GAAxBK,CAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtC,OAAA;MAAKsB,SAAS,EAAC;IAAe;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CAAC;AAER,CAAC;AAACpC,EAAA,CAzHID,eAAe;AAAAyD,EAAA,GAAfzD,eAAe;AA2HrB,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}