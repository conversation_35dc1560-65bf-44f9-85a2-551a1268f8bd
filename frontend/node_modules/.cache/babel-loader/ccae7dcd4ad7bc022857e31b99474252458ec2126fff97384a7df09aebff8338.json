{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null;\nvar ReflectApply = R && typeof R.apply === 'function' ? R.apply : function ReflectApply(target, receiver, args) {\n  return Function.prototype.apply.call(target, receiver, args);\n};\nvar ReflectOwnKeys;\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys;\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n};\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function () {\n    return defaultMaxListeners;\n  },\n  set: function (arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\nEventEmitter.init = function () {\n  if (this._events === undefined || this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined) return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = type === 'error';\n  var events = this._events;\n  if (events !== undefined) doError = doError && events.error === undefined;else if (!doError) return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0) er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n  var handler = events[type];\n  if (handler === undefined) return false;\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i) ReflectApply(listeners[i], this, args);\n  }\n  return true;\n};\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n  checkListener(listener);\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type, listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] = prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' + existing.length + ' ' + String(type) + ' listeners ' + 'added. Use emitter.setMaxListeners() to ' + 'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n  return target;\n}\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\nEventEmitter.prototype.prependListener = function prependListener(type, listener) {\n  return _addListener(this, type, listener, true);\n};\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0) return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\nfunction _onceWrap(target, type, listener) {\n  var state = {\n    fired: false,\n    wrapFn: undefined,\n    target: target,\n    type: type,\n    listener: listener\n  };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\nEventEmitter.prototype.prependOnceListener = function prependOnceListener(type, listener) {\n  checkListener(listener);\n  this.prependListener(type, _onceWrap(this, type, listener));\n  return this;\n};\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener = function removeListener(type, listener) {\n  var list, events, position, i, originalListener;\n  checkListener(listener);\n  events = this._events;\n  if (events === undefined) return this;\n  list = events[type];\n  if (list === undefined) return this;\n  if (list === listener || list.listener === listener) {\n    if (--this._eventsCount === 0) this._events = Object.create(null);else {\n      delete events[type];\n      if (events.removeListener) this.emit('removeListener', type, list.listener || listener);\n    }\n  } else if (typeof list !== 'function') {\n    position = -1;\n    for (i = list.length - 1; i >= 0; i--) {\n      if (list[i] === listener || list[i].listener === listener) {\n        originalListener = list[i].listener;\n        position = i;\n        break;\n      }\n    }\n    if (position < 0) return this;\n    if (position === 0) list.shift();else {\n      spliceOne(list, position);\n    }\n    if (list.length === 1) events[type] = list[0];\n    if (events.removeListener !== undefined) this.emit('removeListener', type, originalListener || listener);\n  }\n  return this;\n};\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(type) {\n  var listeners, events, i;\n  events = this._events;\n  if (events === undefined) return this;\n\n  // not listening for removeListener, no need to emit\n  if (events.removeListener === undefined) {\n    if (arguments.length === 0) {\n      this._events = Object.create(null);\n      this._eventsCount = 0;\n    } else if (events[type] !== undefined) {\n      if (--this._eventsCount === 0) this._events = Object.create(null);else delete events[type];\n    }\n    return this;\n  }\n\n  // emit removeListener for all listeners on all events\n  if (arguments.length === 0) {\n    var keys = Object.keys(events);\n    var key;\n    for (i = 0; i < keys.length; ++i) {\n      key = keys[i];\n      if (key === 'removeListener') continue;\n      this.removeAllListeners(key);\n    }\n    this.removeAllListeners('removeListener');\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n    return this;\n  }\n  listeners = events[type];\n  if (typeof listeners === 'function') {\n    this.removeListener(type, listeners);\n  } else if (listeners !== undefined) {\n    // LIFO order\n    for (i = listeners.length - 1; i >= 0; i--) {\n      this.removeListener(type, listeners[i]);\n    }\n  }\n  return this;\n};\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n  if (events === undefined) return [];\n  var evlistener = events[type];\n  if (evlistener === undefined) return [];\n  if (typeof evlistener === 'function') return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n  return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\nEventEmitter.listenerCount = function (emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n  if (events !== undefined) {\n    var evlistener = events[type];\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n  return 0;\n}\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i) copy[i] = arr[i];\n  return copy;\n}\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++) list[index] = list[index + 1];\n  list.pop();\n}\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    }\n    ;\n    eventTargetAgnosticAddListener(emitter, name, resolver, {\n      once: true\n    });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, {\n        once: true\n      });\n    }\n  });\n}\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}", "map": {"version": 3, "names": ["R", "Reflect", "ReflectApply", "apply", "target", "receiver", "args", "Function", "prototype", "call", "ReflectOwnKeys", "ownKeys", "Object", "getOwnPropertySymbols", "getOwnPropertyNames", "concat", "ProcessEmitWarning", "warning", "console", "warn", "NumberIsNaN", "Number", "isNaN", "value", "EventEmitter", "init", "module", "exports", "once", "_events", "undefined", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "listener", "TypeError", "defineProperty", "enumerable", "get", "set", "arg", "RangeError", "getPrototypeOf", "create", "setMaxListeners", "n", "_getMaxListeners", "that", "getMaxListeners", "emit", "type", "i", "arguments", "length", "push", "do<PERSON><PERSON><PERSON>", "events", "error", "er", "Error", "err", "message", "context", "handler", "len", "listeners", "arrayClone", "_addListener", "prepend", "m", "existing", "newListener", "unshift", "warned", "w", "String", "name", "emitter", "count", "addListener", "on", "prependListener", "onceWrapper", "fired", "removeListener", "wrapFn", "_onceWrap", "state", "wrapped", "bind", "prependOnceListener", "list", "position", "originalListener", "shift", "spliceOne", "off", "removeAllListeners", "keys", "key", "_listeners", "unwrap", "evlistener", "unwrapListeners", "rawListeners", "listenerCount", "eventNames", "arr", "copy", "Array", "index", "pop", "ret", "Promise", "resolve", "reject", "errorListener", "resolver", "slice", "eventTargetAgnosticAddListener", "addErrorHandlerIfEventEmitter", "flags", "addEventListener", "wrapListener", "removeEventListener"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/events/events.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,CAAC,GAAG,OAAOC,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,IAAI;AACpD,IAAIC,YAAY,GAAGF,CAAC,IAAI,OAAOA,CAAC,CAACG,KAAK,KAAK,UAAU,GACjDH,CAAC,CAACG,KAAK,GACP,SAASD,YAAYA,CAACE,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE;EAC9C,OAAOC,QAAQ,CAACC,SAAS,CAACL,KAAK,CAACM,IAAI,CAACL,MAAM,EAAEC,QAAQ,EAAEC,IAAI,CAAC;AAC9D,CAAC;AAEH,IAAII,cAAc;AAClB,IAAIV,CAAC,IAAI,OAAOA,CAAC,CAACW,OAAO,KAAK,UAAU,EAAE;EACxCD,cAAc,GAAGV,CAAC,CAACW,OAAO;AAC5B,CAAC,MAAM,IAAIC,MAAM,CAACC,qBAAqB,EAAE;EACvCH,cAAc,GAAG,SAASA,cAAcA,CAACN,MAAM,EAAE;IAC/C,OAAOQ,MAAM,CAACE,mBAAmB,CAACV,MAAM,CAAC,CACtCW,MAAM,CAACH,MAAM,CAACC,qBAAqB,CAACT,MAAM,CAAC,CAAC;EACjD,CAAC;AACH,CAAC,MAAM;EACLM,cAAc,GAAG,SAASA,cAAcA,CAACN,MAAM,EAAE;IAC/C,OAAOQ,MAAM,CAACE,mBAAmB,CAACV,MAAM,CAAC;EAC3C,CAAC;AACH;AAEA,SAASY,kBAAkBA,CAACC,OAAO,EAAE;EACnC,IAAIC,OAAO,IAAIA,OAAO,CAACC,IAAI,EAAED,OAAO,CAACC,IAAI,CAACF,OAAO,CAAC;AACpD;AAEA,IAAIG,WAAW,GAAGC,MAAM,CAACC,KAAK,IAAI,SAASF,WAAWA,CAACG,KAAK,EAAE;EAC5D,OAAOA,KAAK,KAAKA,KAAK;AACxB,CAAC;AAED,SAASC,YAAYA,CAAA,EAAG;EACtBA,YAAY,CAACC,IAAI,CAAChB,IAAI,CAAC,IAAI,CAAC;AAC9B;AACAiB,MAAM,CAACC,OAAO,GAAGH,YAAY;AAC7BE,MAAM,CAACC,OAAO,CAACC,IAAI,GAAGA,IAAI;;AAE1B;AACAJ,YAAY,CAACA,YAAY,GAAGA,YAAY;AAExCA,YAAY,CAAChB,SAAS,CAACqB,OAAO,GAAGC,SAAS;AAC1CN,YAAY,CAAChB,SAAS,CAACuB,YAAY,GAAG,CAAC;AACvCP,YAAY,CAAChB,SAAS,CAACwB,aAAa,GAAGF,SAAS;;AAEhD;AACA;AACA,IAAIG,mBAAmB,GAAG,EAAE;AAE5B,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,MAAM,IAAIC,SAAS,CAAC,kEAAkE,GAAG,OAAOD,QAAQ,CAAC;EAC3G;AACF;AAEAvB,MAAM,CAACyB,cAAc,CAACb,YAAY,EAAE,qBAAqB,EAAE;EACzDc,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAW;IACd,OAAON,mBAAmB;EAC5B,CAAC;EACDO,GAAG,EAAE,SAAAA,CAASC,GAAG,EAAE;IACjB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,IAAIrB,WAAW,CAACqB,GAAG,CAAC,EAAE;MAC1D,MAAM,IAAIC,UAAU,CAAC,iGAAiG,GAAGD,GAAG,GAAG,GAAG,CAAC;IACrI;IACAR,mBAAmB,GAAGQ,GAAG;EAC3B;AACF,CAAC,CAAC;AAEFjB,YAAY,CAACC,IAAI,GAAG,YAAW;EAE7B,IAAI,IAAI,CAACI,OAAO,KAAKC,SAAS,IAC1B,IAAI,CAACD,OAAO,KAAKjB,MAAM,CAAC+B,cAAc,CAAC,IAAI,CAAC,CAACd,OAAO,EAAE;IACxD,IAAI,CAACA,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAACb,YAAY,GAAG,CAAC;EACvB;EAEA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,IAAIF,SAAS;AACtD,CAAC;;AAED;AACA;AACAN,YAAY,CAAChB,SAAS,CAACqC,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;EACnE,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAI1B,WAAW,CAAC0B,CAAC,CAAC,EAAE;IACpD,MAAM,IAAIJ,UAAU,CAAC,+EAA+E,GAAGI,CAAC,GAAG,GAAG,CAAC;EACjH;EACA,IAAI,CAACd,aAAa,GAAGc,CAAC;EACtB,OAAO,IAAI;AACb,CAAC;AAED,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAIA,IAAI,CAAChB,aAAa,KAAKF,SAAS,EAClC,OAAON,YAAY,CAACS,mBAAmB;EACzC,OAAOe,IAAI,CAAChB,aAAa;AAC3B;AAEAR,YAAY,CAAChB,SAAS,CAACyC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;EAClE,OAAOF,gBAAgB,CAAC,IAAI,CAAC;AAC/B,CAAC;AAEDvB,YAAY,CAAChB,SAAS,CAAC0C,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAE;EAChD,IAAI7C,IAAI,GAAG,EAAE;EACb,KAAK,IAAI8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE9C,IAAI,CAACiD,IAAI,CAACF,SAAS,CAACD,CAAC,CAAC,CAAC;EAClE,IAAII,OAAO,GAAIL,IAAI,KAAK,OAAQ;EAEhC,IAAIM,MAAM,GAAG,IAAI,CAAC5B,OAAO;EACzB,IAAI4B,MAAM,KAAK3B,SAAS,EACtB0B,OAAO,GAAIA,OAAO,IAAIC,MAAM,CAACC,KAAK,KAAK5B,SAAU,CAAC,KAC/C,IAAI,CAAC0B,OAAO,EACf,OAAO,KAAK;;EAEd;EACA,IAAIA,OAAO,EAAE;IACX,IAAIG,EAAE;IACN,IAAIrD,IAAI,CAACgD,MAAM,GAAG,CAAC,EACjBK,EAAE,GAAGrD,IAAI,CAAC,CAAC,CAAC;IACd,IAAIqD,EAAE,YAAYC,KAAK,EAAE;MACvB;MACA;MACA,MAAMD,EAAE,CAAC,CAAC;IACZ;IACA;IACA,IAAIE,GAAG,GAAG,IAAID,KAAK,CAAC,kBAAkB,IAAID,EAAE,GAAG,IAAI,GAAGA,EAAE,CAACG,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IAC7ED,GAAG,CAACE,OAAO,GAAGJ,EAAE;IAChB,MAAME,GAAG,CAAC,CAAC;EACb;EAEA,IAAIG,OAAO,GAAGP,MAAM,CAACN,IAAI,CAAC;EAE1B,IAAIa,OAAO,KAAKlC,SAAS,EACvB,OAAO,KAAK;EAEd,IAAI,OAAOkC,OAAO,KAAK,UAAU,EAAE;IACjC9D,YAAY,CAAC8D,OAAO,EAAE,IAAI,EAAE1D,IAAI,CAAC;EACnC,CAAC,MAAM;IACL,IAAI2D,GAAG,GAAGD,OAAO,CAACV,MAAM;IACxB,IAAIY,SAAS,GAAGC,UAAU,CAACH,OAAO,EAAEC,GAAG,CAAC;IACxC,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,GAAG,EAAE,EAAEb,CAAC,EAC1BlD,YAAY,CAACgE,SAAS,CAACd,CAAC,CAAC,EAAE,IAAI,EAAE9C,IAAI,CAAC;EAC1C;EAEA,OAAO,IAAI;AACb,CAAC;AAED,SAAS8D,YAAYA,CAAChE,MAAM,EAAE+C,IAAI,EAAEhB,QAAQ,EAAEkC,OAAO,EAAE;EACrD,IAAIC,CAAC;EACL,IAAIb,MAAM;EACV,IAAIc,QAAQ;EAEZrC,aAAa,CAACC,QAAQ,CAAC;EAEvBsB,MAAM,GAAGrD,MAAM,CAACyB,OAAO;EACvB,IAAI4B,MAAM,KAAK3B,SAAS,EAAE;IACxB2B,MAAM,GAAGrD,MAAM,CAACyB,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC;IAC7CxC,MAAM,CAAC2B,YAAY,GAAG,CAAC;EACzB,CAAC,MAAM;IACL;IACA;IACA,IAAI0B,MAAM,CAACe,WAAW,KAAK1C,SAAS,EAAE;MACpC1B,MAAM,CAAC8C,IAAI,CAAC,aAAa,EAAEC,IAAI,EACnBhB,QAAQ,CAACA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,GAAGA,QAAQ,CAAC;;MAE7D;MACA;MACAsB,MAAM,GAAGrD,MAAM,CAACyB,OAAO;IACzB;IACA0C,QAAQ,GAAGd,MAAM,CAACN,IAAI,CAAC;EACzB;EAEA,IAAIoB,QAAQ,KAAKzC,SAAS,EAAE;IAC1B;IACAyC,QAAQ,GAAGd,MAAM,CAACN,IAAI,CAAC,GAAGhB,QAAQ;IAClC,EAAE/B,MAAM,CAAC2B,YAAY;EACvB,CAAC,MAAM;IACL,IAAI,OAAOwC,QAAQ,KAAK,UAAU,EAAE;MAClC;MACAA,QAAQ,GAAGd,MAAM,CAACN,IAAI,CAAC,GACrBkB,OAAO,GAAG,CAAClC,QAAQ,EAAEoC,QAAQ,CAAC,GAAG,CAACA,QAAQ,EAAEpC,QAAQ,CAAC;MACvD;IACF,CAAC,MAAM,IAAIkC,OAAO,EAAE;MAClBE,QAAQ,CAACE,OAAO,CAACtC,QAAQ,CAAC;IAC5B,CAAC,MAAM;MACLoC,QAAQ,CAAChB,IAAI,CAACpB,QAAQ,CAAC;IACzB;;IAEA;IACAmC,CAAC,GAAGvB,gBAAgB,CAAC3C,MAAM,CAAC;IAC5B,IAAIkE,CAAC,GAAG,CAAC,IAAIC,QAAQ,CAACjB,MAAM,GAAGgB,CAAC,IAAI,CAACC,QAAQ,CAACG,MAAM,EAAE;MACpDH,QAAQ,CAACG,MAAM,GAAG,IAAI;MACtB;MACA;MACA,IAAIC,CAAC,GAAG,IAAIf,KAAK,CAAC,8CAA8C,GAC5CW,QAAQ,CAACjB,MAAM,GAAG,GAAG,GAAGsB,MAAM,CAACzB,IAAI,CAAC,GAAG,aAAa,GACpD,0CAA0C,GAC1C,gBAAgB,CAAC;MACrCwB,CAAC,CAACE,IAAI,GAAG,6BAA6B;MACtCF,CAAC,CAACG,OAAO,GAAG1E,MAAM;MAClBuE,CAAC,CAACxB,IAAI,GAAGA,IAAI;MACbwB,CAAC,CAACI,KAAK,GAAGR,QAAQ,CAACjB,MAAM;MACzBtC,kBAAkB,CAAC2D,CAAC,CAAC;IACvB;EACF;EAEA,OAAOvE,MAAM;AACf;AAEAoB,YAAY,CAAChB,SAAS,CAACwE,WAAW,GAAG,SAASA,WAAWA,CAAC7B,IAAI,EAAEhB,QAAQ,EAAE;EACxE,OAAOiC,YAAY,CAAC,IAAI,EAAEjB,IAAI,EAAEhB,QAAQ,EAAE,KAAK,CAAC;AAClD,CAAC;AAEDX,YAAY,CAAChB,SAAS,CAACyE,EAAE,GAAGzD,YAAY,CAAChB,SAAS,CAACwE,WAAW;AAE9DxD,YAAY,CAAChB,SAAS,CAAC0E,eAAe,GAClC,SAASA,eAAeA,CAAC/B,IAAI,EAAEhB,QAAQ,EAAE;EACvC,OAAOiC,YAAY,CAAC,IAAI,EAAEjB,IAAI,EAAEhB,QAAQ,EAAE,IAAI,CAAC;AACjD,CAAC;AAEL,SAASgD,WAAWA,CAAA,EAAG;EACrB,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACf,IAAI,CAAChF,MAAM,CAACiF,cAAc,CAAC,IAAI,CAAClC,IAAI,EAAE,IAAI,CAACmC,MAAM,CAAC;IAClD,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI/B,SAAS,CAACC,MAAM,KAAK,CAAC,EACxB,OAAO,IAAI,CAACnB,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAACL,MAAM,CAAC;IACxC,OAAO,IAAI,CAAC+B,QAAQ,CAAChC,KAAK,CAAC,IAAI,CAACC,MAAM,EAAEiD,SAAS,CAAC;EACpD;AACF;AAEA,SAASkC,SAASA,CAACnF,MAAM,EAAE+C,IAAI,EAAEhB,QAAQ,EAAE;EACzC,IAAIqD,KAAK,GAAG;IAAEJ,KAAK,EAAE,KAAK;IAAEE,MAAM,EAAExD,SAAS;IAAE1B,MAAM,EAAEA,MAAM;IAAE+C,IAAI,EAAEA,IAAI;IAAEhB,QAAQ,EAAEA;EAAS,CAAC;EAC/F,IAAIsD,OAAO,GAAGN,WAAW,CAACO,IAAI,CAACF,KAAK,CAAC;EACrCC,OAAO,CAACtD,QAAQ,GAAGA,QAAQ;EAC3BqD,KAAK,CAACF,MAAM,GAAGG,OAAO;EACtB,OAAOA,OAAO;AAChB;AAEAjE,YAAY,CAAChB,SAAS,CAACoB,IAAI,GAAG,SAASA,IAAIA,CAACuB,IAAI,EAAEhB,QAAQ,EAAE;EAC1DD,aAAa,CAACC,QAAQ,CAAC;EACvB,IAAI,CAAC8C,EAAE,CAAC9B,IAAI,EAAEoC,SAAS,CAAC,IAAI,EAAEpC,IAAI,EAAEhB,QAAQ,CAAC,CAAC;EAC9C,OAAO,IAAI;AACb,CAAC;AAEDX,YAAY,CAAChB,SAAS,CAACmF,mBAAmB,GACtC,SAASA,mBAAmBA,CAACxC,IAAI,EAAEhB,QAAQ,EAAE;EAC3CD,aAAa,CAACC,QAAQ,CAAC;EACvB,IAAI,CAAC+C,eAAe,CAAC/B,IAAI,EAAEoC,SAAS,CAAC,IAAI,EAAEpC,IAAI,EAAEhB,QAAQ,CAAC,CAAC;EAC3D,OAAO,IAAI;AACb,CAAC;;AAEL;AACAX,YAAY,CAAChB,SAAS,CAAC6E,cAAc,GACjC,SAASA,cAAcA,CAAClC,IAAI,EAAEhB,QAAQ,EAAE;EACtC,IAAIyD,IAAI,EAAEnC,MAAM,EAAEoC,QAAQ,EAAEzC,CAAC,EAAE0C,gBAAgB;EAE/C5D,aAAa,CAACC,QAAQ,CAAC;EAEvBsB,MAAM,GAAG,IAAI,CAAC5B,OAAO;EACrB,IAAI4B,MAAM,KAAK3B,SAAS,EACtB,OAAO,IAAI;EAEb8D,IAAI,GAAGnC,MAAM,CAACN,IAAI,CAAC;EACnB,IAAIyC,IAAI,KAAK9D,SAAS,EACpB,OAAO,IAAI;EAEb,IAAI8D,IAAI,KAAKzD,QAAQ,IAAIyD,IAAI,CAACzD,QAAQ,KAAKA,QAAQ,EAAE;IACnD,IAAI,EAAE,IAAI,CAACJ,YAAY,KAAK,CAAC,EAC3B,IAAI,CAACF,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC,CAAC,KAChC;MACH,OAAOa,MAAM,CAACN,IAAI,CAAC;MACnB,IAAIM,MAAM,CAAC4B,cAAc,EACvB,IAAI,CAACnC,IAAI,CAAC,gBAAgB,EAAEC,IAAI,EAAEyC,IAAI,CAACzD,QAAQ,IAAIA,QAAQ,CAAC;IAChE;EACF,CAAC,MAAM,IAAI,OAAOyD,IAAI,KAAK,UAAU,EAAE;IACrCC,QAAQ,GAAG,CAAC,CAAC;IAEb,KAAKzC,CAAC,GAAGwC,IAAI,CAACtC,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACrC,IAAIwC,IAAI,CAACxC,CAAC,CAAC,KAAKjB,QAAQ,IAAIyD,IAAI,CAACxC,CAAC,CAAC,CAACjB,QAAQ,KAAKA,QAAQ,EAAE;QACzD2D,gBAAgB,GAAGF,IAAI,CAACxC,CAAC,CAAC,CAACjB,QAAQ;QACnC0D,QAAQ,GAAGzC,CAAC;QACZ;MACF;IACF;IAEA,IAAIyC,QAAQ,GAAG,CAAC,EACd,OAAO,IAAI;IAEb,IAAIA,QAAQ,KAAK,CAAC,EAChBD,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,KACV;MACHC,SAAS,CAACJ,IAAI,EAAEC,QAAQ,CAAC;IAC3B;IAEA,IAAID,IAAI,CAACtC,MAAM,KAAK,CAAC,EACnBG,MAAM,CAACN,IAAI,CAAC,GAAGyC,IAAI,CAAC,CAAC,CAAC;IAExB,IAAInC,MAAM,CAAC4B,cAAc,KAAKvD,SAAS,EACrC,IAAI,CAACoB,IAAI,CAAC,gBAAgB,EAAEC,IAAI,EAAE2C,gBAAgB,IAAI3D,QAAQ,CAAC;EACnE;EAEA,OAAO,IAAI;AACb,CAAC;AAELX,YAAY,CAAChB,SAAS,CAACyF,GAAG,GAAGzE,YAAY,CAAChB,SAAS,CAAC6E,cAAc;AAElE7D,YAAY,CAAChB,SAAS,CAAC0F,kBAAkB,GACrC,SAASA,kBAAkBA,CAAC/C,IAAI,EAAE;EAChC,IAAIe,SAAS,EAAET,MAAM,EAAEL,CAAC;EAExBK,MAAM,GAAG,IAAI,CAAC5B,OAAO;EACrB,IAAI4B,MAAM,KAAK3B,SAAS,EACtB,OAAO,IAAI;;EAEb;EACA,IAAI2B,MAAM,CAAC4B,cAAc,KAAKvD,SAAS,EAAE;IACvC,IAAIuB,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACzB,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC;MAClC,IAAI,CAACb,YAAY,GAAG,CAAC;IACvB,CAAC,MAAM,IAAI0B,MAAM,CAACN,IAAI,CAAC,KAAKrB,SAAS,EAAE;MACrC,IAAI,EAAE,IAAI,CAACC,YAAY,KAAK,CAAC,EAC3B,IAAI,CAACF,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC,CAAC,KAEnC,OAAOa,MAAM,CAACN,IAAI,CAAC;IACvB;IACA,OAAO,IAAI;EACb;;EAEA;EACA,IAAIE,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI6C,IAAI,GAAGvF,MAAM,CAACuF,IAAI,CAAC1C,MAAM,CAAC;IAC9B,IAAI2C,GAAG;IACP,KAAKhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+C,IAAI,CAAC7C,MAAM,EAAE,EAAEF,CAAC,EAAE;MAChCgD,GAAG,GAAGD,IAAI,CAAC/C,CAAC,CAAC;MACb,IAAIgD,GAAG,KAAK,gBAAgB,EAAE;MAC9B,IAAI,CAACF,kBAAkB,CAACE,GAAG,CAAC;IAC9B;IACA,IAAI,CAACF,kBAAkB,CAAC,gBAAgB,CAAC;IACzC,IAAI,CAACrE,OAAO,GAAGjB,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC;IAClC,IAAI,CAACb,YAAY,GAAG,CAAC;IACrB,OAAO,IAAI;EACb;EAEAmC,SAAS,GAAGT,MAAM,CAACN,IAAI,CAAC;EAExB,IAAI,OAAOe,SAAS,KAAK,UAAU,EAAE;IACnC,IAAI,CAACmB,cAAc,CAAClC,IAAI,EAAEe,SAAS,CAAC;EACtC,CAAC,MAAM,IAAIA,SAAS,KAAKpC,SAAS,EAAE;IAClC;IACA,KAAKsB,CAAC,GAAGc,SAAS,CAACZ,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACiC,cAAc,CAAClC,IAAI,EAAEe,SAAS,CAACd,CAAC,CAAC,CAAC;IACzC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAEL,SAASiD,UAAUA,CAACjG,MAAM,EAAE+C,IAAI,EAAEmD,MAAM,EAAE;EACxC,IAAI7C,MAAM,GAAGrD,MAAM,CAACyB,OAAO;EAE3B,IAAI4B,MAAM,KAAK3B,SAAS,EACtB,OAAO,EAAE;EAEX,IAAIyE,UAAU,GAAG9C,MAAM,CAACN,IAAI,CAAC;EAC7B,IAAIoD,UAAU,KAAKzE,SAAS,EAC1B,OAAO,EAAE;EAEX,IAAI,OAAOyE,UAAU,KAAK,UAAU,EAClC,OAAOD,MAAM,GAAG,CAACC,UAAU,CAACpE,QAAQ,IAAIoE,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC;EAEpE,OAAOD,MAAM,GACXE,eAAe,CAACD,UAAU,CAAC,GAAGpC,UAAU,CAACoC,UAAU,EAAEA,UAAU,CAACjD,MAAM,CAAC;AAC3E;AAEA9B,YAAY,CAAChB,SAAS,CAAC0D,SAAS,GAAG,SAASA,SAASA,CAACf,IAAI,EAAE;EAC1D,OAAOkD,UAAU,CAAC,IAAI,EAAElD,IAAI,EAAE,IAAI,CAAC;AACrC,CAAC;AAED3B,YAAY,CAAChB,SAAS,CAACiG,YAAY,GAAG,SAASA,YAAYA,CAACtD,IAAI,EAAE;EAChE,OAAOkD,UAAU,CAAC,IAAI,EAAElD,IAAI,EAAE,KAAK,CAAC;AACtC,CAAC;AAED3B,YAAY,CAACkF,aAAa,GAAG,UAAS5B,OAAO,EAAE3B,IAAI,EAAE;EACnD,IAAI,OAAO2B,OAAO,CAAC4B,aAAa,KAAK,UAAU,EAAE;IAC/C,OAAO5B,OAAO,CAAC4B,aAAa,CAACvD,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,OAAOuD,aAAa,CAACjG,IAAI,CAACqE,OAAO,EAAE3B,IAAI,CAAC;EAC1C;AACF,CAAC;AAED3B,YAAY,CAAChB,SAAS,CAACkG,aAAa,GAAGA,aAAa;AACpD,SAASA,aAAaA,CAACvD,IAAI,EAAE;EAC3B,IAAIM,MAAM,GAAG,IAAI,CAAC5B,OAAO;EAEzB,IAAI4B,MAAM,KAAK3B,SAAS,EAAE;IACxB,IAAIyE,UAAU,GAAG9C,MAAM,CAACN,IAAI,CAAC;IAE7B,IAAI,OAAOoD,UAAU,KAAK,UAAU,EAAE;MACpC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAKzE,SAAS,EAAE;MACnC,OAAOyE,UAAU,CAACjD,MAAM;IAC1B;EACF;EAEA,OAAO,CAAC;AACV;AAEA9B,YAAY,CAAChB,SAAS,CAACmG,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;EACxD,OAAO,IAAI,CAAC5E,YAAY,GAAG,CAAC,GAAGrB,cAAc,CAAC,IAAI,CAACmB,OAAO,CAAC,GAAG,EAAE;AAClE,CAAC;AAED,SAASsC,UAAUA,CAACyC,GAAG,EAAE9D,CAAC,EAAE;EAC1B,IAAI+D,IAAI,GAAG,IAAIC,KAAK,CAAChE,CAAC,CAAC;EACvB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,CAAC,EAAE,EAAEM,CAAC,EACxByD,IAAI,CAACzD,CAAC,CAAC,GAAGwD,GAAG,CAACxD,CAAC,CAAC;EAClB,OAAOyD,IAAI;AACb;AAEA,SAASb,SAASA,CAACJ,IAAI,EAAEmB,KAAK,EAAE;EAC9B,OAAOA,KAAK,GAAG,CAAC,GAAGnB,IAAI,CAACtC,MAAM,EAAEyD,KAAK,EAAE,EACrCnB,IAAI,CAACmB,KAAK,CAAC,GAAGnB,IAAI,CAACmB,KAAK,GAAG,CAAC,CAAC;EAC/BnB,IAAI,CAACoB,GAAG,CAAC,CAAC;AACZ;AAEA,SAASR,eAAeA,CAACI,GAAG,EAAE;EAC5B,IAAIK,GAAG,GAAG,IAAIH,KAAK,CAACF,GAAG,CAACtD,MAAM,CAAC;EAC/B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,GAAG,CAAC3D,MAAM,EAAE,EAAEF,CAAC,EAAE;IACnC6D,GAAG,CAAC7D,CAAC,CAAC,GAAGwD,GAAG,CAACxD,CAAC,CAAC,CAACjB,QAAQ,IAAIyE,GAAG,CAACxD,CAAC,CAAC;EACpC;EACA,OAAO6D,GAAG;AACZ;AAEA,SAASrF,IAAIA,CAACkD,OAAO,EAAED,IAAI,EAAE;EAC3B,OAAO,IAAIqC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC5C,SAASC,aAAaA,CAACxD,GAAG,EAAE;MAC1BiB,OAAO,CAACO,cAAc,CAACR,IAAI,EAAEyC,QAAQ,CAAC;MACtCF,MAAM,CAACvD,GAAG,CAAC;IACb;IAEA,SAASyD,QAAQA,CAAA,EAAG;MAClB,IAAI,OAAOxC,OAAO,CAACO,cAAc,KAAK,UAAU,EAAE;QAChDP,OAAO,CAACO,cAAc,CAAC,OAAO,EAAEgC,aAAa,CAAC;MAChD;MACAF,OAAO,CAAC,EAAE,CAACI,KAAK,CAAC9G,IAAI,CAAC4C,SAAS,CAAC,CAAC;IACnC;IAAC;IAEDmE,8BAA8B,CAAC1C,OAAO,EAAED,IAAI,EAAEyC,QAAQ,EAAE;MAAE1F,IAAI,EAAE;IAAK,CAAC,CAAC;IACvE,IAAIiD,IAAI,KAAK,OAAO,EAAE;MACpB4C,6BAA6B,CAAC3C,OAAO,EAAEuC,aAAa,EAAE;QAAEzF,IAAI,EAAE;MAAK,CAAC,CAAC;IACvE;EACF,CAAC,CAAC;AACJ;AAEA,SAAS6F,6BAA6BA,CAAC3C,OAAO,EAAEd,OAAO,EAAE0D,KAAK,EAAE;EAC9D,IAAI,OAAO5C,OAAO,CAACG,EAAE,KAAK,UAAU,EAAE;IACpCuC,8BAA8B,CAAC1C,OAAO,EAAE,OAAO,EAAEd,OAAO,EAAE0D,KAAK,CAAC;EAClE;AACF;AAEA,SAASF,8BAA8BA,CAAC1C,OAAO,EAAED,IAAI,EAAE1C,QAAQ,EAAEuF,KAAK,EAAE;EACtE,IAAI,OAAO5C,OAAO,CAACG,EAAE,KAAK,UAAU,EAAE;IACpC,IAAIyC,KAAK,CAAC9F,IAAI,EAAE;MACdkD,OAAO,CAAClD,IAAI,CAACiD,IAAI,EAAE1C,QAAQ,CAAC;IAC9B,CAAC,MAAM;MACL2C,OAAO,CAACG,EAAE,CAACJ,IAAI,EAAE1C,QAAQ,CAAC;IAC5B;EACF,CAAC,MAAM,IAAI,OAAO2C,OAAO,CAAC6C,gBAAgB,KAAK,UAAU,EAAE;IACzD;IACA;IACA7C,OAAO,CAAC6C,gBAAgB,CAAC9C,IAAI,EAAE,SAAS+C,YAAYA,CAACnF,GAAG,EAAE;MACxD;MACA;MACA,IAAIiF,KAAK,CAAC9F,IAAI,EAAE;QACdkD,OAAO,CAAC+C,mBAAmB,CAAChD,IAAI,EAAE+C,YAAY,CAAC;MACjD;MACAzF,QAAQ,CAACM,GAAG,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,MAAM,IAAIL,SAAS,CAAC,qEAAqE,GAAG,OAAO0C,OAAO,CAAC;EAC7G;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}