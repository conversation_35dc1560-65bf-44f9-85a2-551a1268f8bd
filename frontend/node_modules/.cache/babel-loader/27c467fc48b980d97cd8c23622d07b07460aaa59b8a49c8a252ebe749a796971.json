{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nconst ScaleLetterTransition = ({\n  isActive,\n  to,\n  onComplete\n}) => {\n  _s();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (isActive) {\n      startTransition();\n    }\n  }, [isActive]);\n  const startTransition = () => {\n    // Find the 'a' in SCaLE\n    const scaleTitle = document.querySelector('.hero-section h1');\n    if (scaleTitle) {\n      const rect = scaleTitle.getBoundingClientRect();\n\n      // Calculate position of the 'a' in \"SCaLE\" \n      // S-C-a-L-E: 'a' is 3rd out of 5 letters, so about 50% across\n      const letterCenterX = rect.left + rect.width * 0.51;\n      const letterCenterY = rect.top + rect.height * 0.5;\n\n      // Apply zoom directly to document.body\n      document.body.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';\n      document.body.style.transformOrigin = `${letterCenterX}px ${letterCenterY}px`;\n      document.body.style.transform = `scale(290)`;\n\n      // Navigate after zoom completes\n      setTimeout(() => {\n        // Reset body transform before navigating so back button works\n        document.body.style.transform = '';\n        document.body.style.transition = '';\n        document.body.style.transformOrigin = '';\n        navigate(to);\n        onComplete();\n      }, 3200);\n    } else {\n      setTimeout(() => {\n        navigate(to);\n        onComplete();\n      }, 100);\n    }\n  };\n  return null;\n};\n_s(ScaleLetterTransition, \"0pNeyzXk/ByIxyERsdaIrG6js9s=\", false, function () {\n  return [useNavigate];\n});\n_c = ScaleLetterTransition;\nexport default ScaleLetterTransition;\nvar _c;\n$RefreshReg$(_c, \"ScaleLetterTransition\");", "map": {"version": 3, "names": ["useEffect", "useNavigate", "ScaleLetterTransition", "isActive", "to", "onComplete", "_s", "navigate", "startTransition", "scaleTitle", "document", "querySelector", "rect", "getBoundingClientRect", "letterCenterX", "left", "width", "letterCenterY", "top", "height", "body", "style", "transition", "transform<PERSON><PERSON>in", "transform", "setTimeout", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/src/components/ScaleLetterTransition.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\ninterface ScaleLetterTransitionProps {\n  isActive: boolean;\n  to: string;\n  onComplete: () => void;\n}\n\nconst ScaleLetterTransition: React.FC<ScaleLetterTransitionProps> = ({\n  isActive,\n  to,\n  onComplete\n}) => {\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (isActive) {\n      startTransition();\n    }\n  }, [isActive]);\n\n  const startTransition = () => {\n    // Find the 'a' in SCaLE\n    const scaleTitle = document.querySelector('.hero-section h1');\n    \n    if (scaleTitle) {\n      const rect = scaleTitle.getBoundingClientRect();\n      \n      // Calculate position of the 'a' in \"SCaLE\" \n      // S-C-a-L-E: 'a' is 3rd out of 5 letters, so about 50% across\n      const letterCenterX = rect.left + (rect.width * 0.51);\n      const letterCenterY = rect.top + (rect.height * 0.5);\n\n      // Apply zoom directly to document.body\n      document.body.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';\n      document.body.style.transformOrigin = `${letterCenterX}px ${letterCenterY}px`;\n      document.body.style.transform = `scale(290)`;\n\n      // Navigate after zoom completes\n      setTimeout(() => {\n        // Reset body transform before navigating so back button works\n        document.body.style.transform = '';\n        document.body.style.transition = '';\n        document.body.style.transformOrigin = '';\n        \n        navigate(to);\n        onComplete();\n      }, 3200);\n    } else {\n      setTimeout(() => {\n        navigate(to);\n        onComplete();\n      }, 100);\n    }\n  };\n\n  return null;\n};\n\nexport default ScaleLetterTransition;"], "mappings": ";AAAA,SAAgBA,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAQ9C,MAAMC,qBAA2D,GAAGA,CAAC;EACnEC,QAAQ;EACRC,EAAE;EACFC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,IAAIG,QAAQ,EAAE;MACZK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;IAE7D,IAAIF,UAAU,EAAE;MACd,MAAMG,IAAI,GAAGH,UAAU,CAACI,qBAAqB,CAAC,CAAC;;MAE/C;MACA;MACA,MAAMC,aAAa,GAAGF,IAAI,CAACG,IAAI,GAAIH,IAAI,CAACI,KAAK,GAAG,IAAK;MACrD,MAAMC,aAAa,GAAGL,IAAI,CAACM,GAAG,GAAIN,IAAI,CAACO,MAAM,GAAG,GAAI;;MAEpD;MACAT,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACC,UAAU,GAAG,mDAAmD;MACpFZ,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACE,eAAe,GAAG,GAAGT,aAAa,MAAMG,aAAa,IAAI;MAC7EP,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACG,SAAS,GAAG,YAAY;;MAE5C;MACAC,UAAU,CAAC,MAAM;QACf;QACAf,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACG,SAAS,GAAG,EAAE;QAClCd,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACC,UAAU,GAAG,EAAE;QACnCZ,QAAQ,CAACU,IAAI,CAACC,KAAK,CAACE,eAAe,GAAG,EAAE;QAExChB,QAAQ,CAACH,EAAE,CAAC;QACZC,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLoB,UAAU,CAAC,MAAM;QACflB,QAAQ,CAACH,EAAE,CAAC;QACZC,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAED,OAAO,IAAI;AACb,CAAC;AAACC,EAAA,CAjDIJ,qBAA2D;EAAA,QAK9CD,WAAW;AAAA;AAAAyB,EAAA,GALxBxB,qBAA2D;AAmDjE,eAAeA,qBAAqB;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}