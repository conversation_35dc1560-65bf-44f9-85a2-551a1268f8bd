{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function () {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) try {\n      if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n      if (y = 0, t) op = [op[0] & 2, t.value];\n      switch (op[0]) {\n        case 0:\n        case 1:\n          t = op;\n          break;\n        case 4:\n          _.label++;\n          return {\n            value: op[1],\n            done: false\n          };\n        case 5:\n          _.label++;\n          y = op[1];\n          op = [0];\n          continue;\n        case 7:\n          op = _.ops.pop();\n          _.trys.pop();\n          continue;\n        default:\n          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n            _ = 0;\n            continue;\n          }\n          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n            _.label = op[1];\n            break;\n          }\n          if (op[0] === 6 && _.label < t[1]) {\n            _.label = t[1];\n            t = op;\n            break;\n          }\n          if (t && _.label < t[2]) {\n            _.label = t[2];\n            _.ops.push(op);\n            break;\n          }\n          if (t[2]) _.ops.pop();\n          _.trys.pop();\n          continue;\n      }\n      op = body.call(thisArg, _);\n    } catch (e) {\n      op = [6, e];\n      y = 0;\n    } finally {\n      f = t = 0;\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from) {\n  for (var i = 0, il = from.length, j = to.length; i < il; i++, j++) to[j] = from[i];\n  return to;\n};\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = function (obj, key, value) {\n  return key in obj ? __defProp(obj, key, {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: value\n  }) : obj[key] = value;\n};\nvar __spreadValues = function (a, b) {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var _i = 0, _c = __getOwnPropSymbols(b); _i < _c.length; _i++) {\n    var prop = _c[_i];\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __spreadProps = function (a, b) {\n  return __defProps(a, __getOwnPropDescs(b));\n};\nvar __async = function (__this, __arguments, generator) {\n  return new Promise(function (resolve, reject) {\n    var fulfilled = function (value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = function (value) {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = function (x) {\n      return x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    };\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n// src/index.ts\nimport { enableES5 } from \"immer\";\nexport * from \"redux\";\nimport { default as default2, current as current2, freeze, original, isDraft as isDraft4 } from \"immer\";\nimport { createSelector as createSelector2 } from \"reselect\";\n// src/createDraftSafeSelector.ts\nimport { current, isDraft } from \"immer\";\nimport { createSelector } from \"reselect\";\nvar createDraftSafeSelector = function () {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var selector = createSelector.apply(void 0, args);\n  var wrappedSelector = function (value) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    return selector.apply(void 0, __spreadArray([isDraft(value) ? current(value) : value], rest));\n  };\n  return wrappedSelector;\n};\n// src/configureStore.ts\nimport { createStore, compose as compose2, applyMiddleware, combineReducers } from \"redux\";\n// src/devtoolsExtension.ts\nimport { compose } from \"redux\";\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return compose;\n  return compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\n  return function (noop2) {\n    return noop2;\n  };\n};\n// src/isPlainObject.ts\nfunction isPlainObject(value) {\n  if (typeof value !== \"object\" || value === null) return false;\n  var proto = Object.getPrototypeOf(value);\n  if (proto === null) return true;\n  var baseProto = proto;\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto);\n  }\n  return proto === baseProto;\n}\n// src/getDefaultMiddleware.ts\nimport thunkMiddleware from \"redux-thunk\";\n// src/tsHelpers.ts\nvar hasMatchFunction = function (v) {\n  return v && typeof v.match === \"function\";\n};\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (prepareAction) {\n      var prepared = prepareAction.apply(void 0, args);\n      if (!prepared) {\n        throw new Error(\"prepareAction did not return an object\");\n      }\n      return __spreadValues(__spreadValues({\n        type: type,\n        payload: prepared.payload\n      }, \"meta\" in prepared && {\n        meta: prepared.meta\n      }), \"error\" in prepared && {\n        error: prepared.error\n      });\n    }\n    return {\n      type: type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = function () {\n    return \"\" + type;\n  };\n  actionCreator.type = type;\n  actionCreator.match = function (action) {\n    return action.type === type;\n  };\n  return actionCreator;\n}\nfunction isAction(action) {\n  return isPlainObject(action) && \"type\" in action;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return isAction(action) && typeof action.type === \"string\" && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\nfunction getType(actionCreator) {\n  return \"\" + actionCreator;\n}\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  var splitType = type ? (\"\" + type).split(\"/\") : [];\n  var actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return \"Detected an action creator with type \\\"\" + (type || \"unknown\") + \"\\\" being dispatched. \\nMake sure you're calling the action creator before dispatching, i.e. `dispatch(\" + actionName + \"())` instead of `dispatch(\" + actionName + \")`. This is necessary even if the action has no payload.\";\n}\nfunction createActionCreatorInvariantMiddleware(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (process.env.NODE_ENV === \"production\") {\n    return function () {\n      return function (next) {\n        return function (action) {\n          return next(action);\n        };\n      };\n    };\n  }\n  var _c = options.isActionCreator,\n    isActionCreator2 = _c === void 0 ? isActionCreator : _c;\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (isActionCreator2(action)) {\n          console.warn(getMessage(action.type));\n        }\n        return next(action);\n      };\n    };\n  };\n}\n// src/utils.ts\nimport createNextState, { isDraftable } from \"immer\";\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  var elapsed = 0;\n  return {\n    measureTime: function (fn) {\n      var started = Date.now();\n      try {\n        return fn();\n      } finally {\n        var finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded: function () {\n      if (elapsed > maxDelay) {\n        console.warn(fnName + \" took \" + elapsed + \"ms, which is more than the warning threshold of \" + maxDelay + \"ms. \\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\\nIt is disabled in production builds, so you don't need to worry about that.\");\n      }\n    }\n  };\n}\nvar MiddlewareArray = /** @class */function (_super) {\n  __extends(MiddlewareArray, _super);\n  function MiddlewareArray() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var _this = _super.apply(this, args) || this;\n    Object.setPrototypeOf(_this, MiddlewareArray.prototype);\n    return _this;\n  }\n  Object.defineProperty(MiddlewareArray, Symbol.species, {\n    get: function () {\n      return MiddlewareArray;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MiddlewareArray.prototype.concat = function () {\n    var arr = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      arr[_i] = arguments[_i];\n    }\n    return _super.prototype.concat.apply(this, arr);\n  };\n  MiddlewareArray.prototype.prepend = function () {\n    var arr = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      arr[_i] = arguments[_i];\n    }\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr[0].concat(this))))();\n    }\n    return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr.concat(this))))();\n  };\n  return MiddlewareArray;\n}(Array);\nvar EnhancerArray = /** @class */function (_super) {\n  __extends(EnhancerArray, _super);\n  function EnhancerArray() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var _this = _super.apply(this, args) || this;\n    Object.setPrototypeOf(_this, EnhancerArray.prototype);\n    return _this;\n  }\n  Object.defineProperty(EnhancerArray, Symbol.species, {\n    get: function () {\n      return EnhancerArray;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  EnhancerArray.prototype.concat = function () {\n    var arr = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      arr[_i] = arguments[_i];\n    }\n    return _super.prototype.concat.apply(this, arr);\n  };\n  EnhancerArray.prototype.prepend = function () {\n    var arr = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      arr[_i] = arguments[_i];\n    }\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr[0].concat(this))))();\n    }\n    return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr.concat(this))))();\n  };\n  return EnhancerArray;\n}(Array);\nfunction freezeDraftable(val) {\n  return isDraftable(val) ? createNextState(val, function () {}) : val;\n}\n// src/immutableStateInvariantMiddleware.ts\nvar isProduction = process.env.NODE_ENV === \"production\";\nvar prefix = \"Invariant failed\";\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n  if (isProduction) {\n    throw new Error(prefix);\n  }\n  throw new Error(prefix + \": \" + (message || \"\"));\n}\nfunction stringify(obj, serializer, indent, decycler) {\n  return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\n}\nfunction getSerialize(serializer, decycler) {\n  var stack = [],\n    keys = [];\n  if (!decycler) decycler = function (_, value) {\n    if (stack[0] === value) return \"[Circular ~]\";\n    return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n  };\n  return function (key, value) {\n    if (stack.length > 0) {\n      var thisPos = stack.indexOf(this);\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n      if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n    } else stack.push(value);\n    return serializer == null ? value : serializer.call(this, key, value);\n  };\n}\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  var trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations: function () {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths, obj, path, checkedObjects) {\n  if (ignorePaths === void 0) {\n    ignorePaths = [];\n  }\n  if (path === void 0) {\n    path = \"\";\n  }\n  if (checkedObjects === void 0) {\n    checkedObjects = new Set();\n  }\n  var tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (var key in obj) {\n      var childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths, trackedProperty, obj, sameParentRef, path) {\n  if (ignoredPaths === void 0) {\n    ignoredPaths = [];\n  }\n  if (sameParentRef === void 0) {\n    sameParentRef = false;\n  }\n  if (path === void 0) {\n    path = \"\";\n  }\n  var prevObj = trackedProperty ? trackedProperty.value : void 0;\n  var sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path: path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  var keysToDetect = {};\n  for (var key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (var key in obj) {\n    keysToDetect[key] = true;\n  }\n  var hasIgnoredPaths = ignoredPaths.length > 0;\n  var _loop_1 = function (key) {\n    var nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      var hasMatches = ignoredPaths.some(function (ignored) {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        return \"continue\";\n      }\n    }\n    var result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return {\n        value: result\n      };\n    }\n  };\n  for (var key in keysToDetect) {\n    var state_1 = _loop_1(key);\n    if (typeof state_1 === \"object\") return state_1.value;\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (process.env.NODE_ENV === \"production\") {\n    return function () {\n      return function (next) {\n        return function (action) {\n          return next(action);\n        };\n      };\n    };\n  }\n  var _c = options.isImmutable,\n    isImmutable = _c === void 0 ? isImmutableDefault : _c,\n    ignoredPaths = options.ignoredPaths,\n    _d = options.warnAfter,\n    warnAfter = _d === void 0 ? 32 : _d,\n    ignore = options.ignore;\n  ignoredPaths = ignoredPaths || ignore;\n  var track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n  return function (_c) {\n    var getState = _c.getState;\n    var state = getState();\n    var tracker = track(state);\n    var result;\n    return function (next) {\n      return function (action) {\n        var measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(function () {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          invariant(!result.wasMutated, \"A state mutation was detected between dispatches, in the path '\" + (result.path || \"\") + \"'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\n        });\n        var dispatchedAction = next(action);\n        measureUtils.measureTime(function () {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          result.wasMutated && invariant(!result.wasMutated, \"A state mutation was detected inside a dispatch, in the path: \" + (result.path || \"\") + \". Take a look at the reducer(s) handling the action \" + stringify(action) + \". (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  };\n}\n// src/serializableStateInvariantMiddleware.ts\nfunction isPlain(val) {\n  var type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\n}\nfunction findNonSerializableValue(value, path, isSerializable, getEntries, ignoredPaths, cache) {\n  if (path === void 0) {\n    path = \"\";\n  }\n  if (isSerializable === void 0) {\n    isSerializable = isPlain;\n  }\n  if (ignoredPaths === void 0) {\n    ignoredPaths = [];\n  }\n  var foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value: value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache == null ? void 0 : cache.has(value)) return false;\n  var entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  var hasIgnoredPaths = ignoredPaths.length > 0;\n  var _loop_2 = function (key, nestedValue) {\n    var nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      var hasMatches = ignoredPaths.some(function (ignored) {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        return \"continue\";\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        value: {\n          keyPath: nestedPath,\n          value: nestedValue\n        }\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return {\n          value: foundNestedSerializable\n        };\n      }\n    }\n  };\n  for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n    var _c = entries_1[_i],\n      key = _c[0],\n      nestedValue = _c[1];\n    var state_2 = _loop_2(key, nestedValue);\n    if (typeof state_2 === \"object\") return state_2.value;\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (var _i = 0, _c = Object.values(value); _i < _c.length; _i++) {\n    var nestedValue = _c[_i];\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (process.env.NODE_ENV === \"production\") {\n    return function () {\n      return function (next) {\n        return function (action) {\n          return next(action);\n        };\n      };\n    };\n  }\n  var _c = options.isSerializable,\n    isSerializable = _c === void 0 ? isPlain : _c,\n    getEntries = options.getEntries,\n    _d = options.ignoredActions,\n    ignoredActions = _d === void 0 ? [] : _d,\n    _e = options.ignoredActionPaths,\n    ignoredActionPaths = _e === void 0 ? [\"meta.arg\", \"meta.baseQueryMeta\"] : _e,\n    _f = options.ignoredPaths,\n    ignoredPaths = _f === void 0 ? [] : _f,\n    _g = options.warnAfter,\n    warnAfter = _g === void 0 ? 32 : _g,\n    _h = options.ignoreState,\n    ignoreState = _h === void 0 ? false : _h,\n    _j = options.ignoreActions,\n    ignoreActions = _j === void 0 ? false : _j,\n    _k = options.disableCache,\n    disableCache = _k === void 0 ? false : _k;\n  var cache = !disableCache && WeakSet ? new WeakSet() : void 0;\n  return function (storeAPI) {\n    return function (next) {\n      return function (action) {\n        var result = next(action);\n        var measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n        if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n          measureUtils.measureTime(function () {\n            var foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n            if (foundActionNonSerializableValue) {\n              var keyPath = foundActionNonSerializableValue.keyPath,\n                value = foundActionNonSerializableValue.value;\n              console.error(\"A non-serializable value was detected in an action, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n            }\n          });\n        }\n        if (!ignoreState) {\n          measureUtils.measureTime(function () {\n            var state = storeAPI.getState();\n            var foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n            if (foundStateNonSerializableValue) {\n              var keyPath = foundStateNonSerializableValue.keyPath,\n                value = foundStateNonSerializableValue.value;\n              console.error(\"A non-serializable value was detected in the state, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the reducer(s) handling this action type: \" + action.type + \".\\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)\");\n            }\n          });\n          measureUtils.warnIfExceeded();\n        }\n        return result;\n      };\n    };\n  };\n}\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nfunction curryGetDefaultMiddleware() {\n  return function curriedGetDefaultMiddleware(options) {\n    return getDefaultMiddleware(options);\n  };\n}\nfunction getDefaultMiddleware(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _c = options.thunk,\n    thunk = _c === void 0 ? true : _c,\n    _d = options.immutableCheck,\n    immutableCheck = _d === void 0 ? true : _d,\n    _e = options.serializableCheck,\n    serializableCheck = _e === void 0 ? true : _e,\n    _f = options.actionCreatorCheck,\n    actionCreatorCheck = _f === void 0 ? true : _f;\n  var middlewareArray = new MiddlewareArray();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(thunkMiddleware.withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== \"production\") {\n    if (immutableCheck) {\n      var immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      var serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      var actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n}\n// src/configureStore.ts\nvar IS_PRODUCTION = process.env.NODE_ENV === \"production\";\nfunction configureStore(options) {\n  var curriedGetDefaultMiddleware = curryGetDefaultMiddleware();\n  var _c = options || {},\n    _d = _c.reducer,\n    reducer = _d === void 0 ? void 0 : _d,\n    _e = _c.middleware,\n    middleware = _e === void 0 ? curriedGetDefaultMiddleware() : _e,\n    _f = _c.devTools,\n    devTools = _f === void 0 ? true : _f,\n    _g = _c.preloadedState,\n    preloadedState = _g === void 0 ? void 0 : _g,\n    _h = _c.enhancers,\n    enhancers = _h === void 0 ? void 0 : _h;\n  var rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if (isPlainObject(reducer)) {\n    rootReducer = combineReducers(reducer);\n  } else {\n    throw new Error('\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\n  }\n  var finalMiddleware = middleware;\n  if (typeof finalMiddleware === \"function\") {\n    finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware);\n    if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\n      throw new Error(\"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  }\n  if (!IS_PRODUCTION && finalMiddleware.some(function (item) {\n    return typeof item !== \"function\";\n  })) {\n    throw new Error(\"each middleware provided to configureStore must be a function\");\n  }\n  var middlewareEnhancer = applyMiddleware.apply(void 0, finalMiddleware);\n  var finalCompose = compose2;\n  if (devTools) {\n    finalCompose = composeWithDevTools(__spreadValues({\n      trace: !IS_PRODUCTION\n    }, typeof devTools === \"object\" && devTools));\n  }\n  var defaultEnhancers = new EnhancerArray(middlewareEnhancer);\n  var storeEnhancers = defaultEnhancers;\n  if (Array.isArray(enhancers)) {\n    storeEnhancers = __spreadArray([middlewareEnhancer], enhancers);\n  } else if (typeof enhancers === \"function\") {\n    storeEnhancers = enhancers(defaultEnhancers);\n  }\n  var composedEnhancer = finalCompose.apply(void 0, storeEnhancers);\n  return createStore(rootReducer, preloadedState, composedEnhancer);\n}\n// src/createReducer.ts\nimport createNextState2, { isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  var actionsMap = {};\n  var actionMatchers = [];\n  var defaultCaseReducer;\n  var builder = {\n    addCase: function (typeOrActionCreator, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (actionMatchers.length > 0) {\n          throw new Error(\"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error(\"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      var type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(\"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error(\"`builder.addCase` cannot be called with two reducers for the same action type\");\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher: function (matcher, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(\"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher: matcher,\n        reducer: reducer\n      });\n      return builder;\n    },\n    addDefaultCase: function (reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(\"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nvar hasWarnedAboutObjectNotation = false;\nfunction createReducer(initialState, mapOrBuilderCallback, actionMatchers, defaultCaseReducer) {\n  if (actionMatchers === void 0) {\n    actionMatchers = [];\n  }\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      if (!hasWarnedAboutObjectNotation) {\n        hasWarnedAboutObjectNotation = true;\n        console.warn(\"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n      }\n    }\n  }\n  var _c = typeof mapOrBuilderCallback === \"function\" ? executeReducerBuilderCallback(mapOrBuilderCallback) : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer],\n    actionsMap = _c[0],\n    finalActionMatchers = _c[1],\n    finalDefaultCaseReducer = _c[2];\n  var getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = function () {\n      return freezeDraftable(initialState());\n    };\n  } else {\n    var frozenInitialState_1 = freezeDraftable(initialState);\n    getInitialState = function () {\n      return frozenInitialState_1;\n    };\n  }\n  function reducer(state, action) {\n    if (state === void 0) {\n      state = getInitialState();\n    }\n    var caseReducers = __spreadArray([actionsMap[action.type]], finalActionMatchers.filter(function (_c) {\n      var matcher = _c.matcher;\n      return matcher(action);\n    }).map(function (_c) {\n      var reducer2 = _c.reducer;\n      return reducer2;\n    }));\n    if (caseReducers.filter(function (cr) {\n      return !!cr;\n    }).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce(function (previousState, caseReducer) {\n      if (caseReducer) {\n        if (isDraft2(previousState)) {\n          var draft = previousState;\n          var result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!isDraftable2(previousState)) {\n          var result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return createNextState2(previousState, function (draft) {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n// src/createSlice.ts\nvar hasWarnedAboutObjectNotation2 = false;\nfunction getType2(slice, actionKey) {\n  return slice + \"/\" + actionKey;\n}\nfunction createSlice(options) {\n  var name = options.name;\n  if (!name) {\n    throw new Error(\"`name` is a required option for createSlice\");\n  }\n  if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n    if (options.initialState === void 0) {\n      console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n    }\n  }\n  var initialState = typeof options.initialState == \"function\" ? options.initialState : freezeDraftable(options.initialState);\n  var reducers = options.reducers || {};\n  var reducerNames = Object.keys(reducers);\n  var sliceCaseReducersByName = {};\n  var sliceCaseReducersByType = {};\n  var actionCreators = {};\n  reducerNames.forEach(function (reducerName) {\n    var maybeReducerWithPrepare = reducers[reducerName];\n    var type = getType2(name, reducerName);\n    var caseReducer;\n    var prepareCallback;\n    if (\"reducer\" in maybeReducerWithPrepare) {\n      caseReducer = maybeReducerWithPrepare.reducer;\n      prepareCallback = maybeReducerWithPrepare.prepare;\n    } else {\n      caseReducer = maybeReducerWithPrepare;\n    }\n    sliceCaseReducersByName[reducerName] = caseReducer;\n    sliceCaseReducersByType[type] = caseReducer;\n    actionCreators[reducerName] = prepareCallback ? createAction(type, prepareCallback) : createAction(type);\n  });\n  function buildReducer() {\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof options.extraReducers === \"object\") {\n        if (!hasWarnedAboutObjectNotation2) {\n          hasWarnedAboutObjectNotation2 = true;\n          console.warn(\"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n    }\n    var _c = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers],\n      _d = _c[0],\n      extraReducers = _d === void 0 ? {} : _d,\n      _e = _c[1],\n      actionMatchers = _e === void 0 ? [] : _e,\n      _f = _c[2],\n      defaultCaseReducer = _f === void 0 ? void 0 : _f;\n    var finalCaseReducers = __spreadValues(__spreadValues({}, extraReducers), sliceCaseReducersByType);\n    return createReducer(initialState, function (builder) {\n      for (var key in finalCaseReducers) {\n        builder.addCase(key, finalCaseReducers[key]);\n      }\n      for (var _i = 0, actionMatchers_1 = actionMatchers; _i < actionMatchers_1.length; _i++) {\n        var m = actionMatchers_1[_i];\n        builder.addMatcher(m.matcher, m.reducer);\n      }\n      if (defaultCaseReducer) {\n        builder.addDefaultCase(defaultCaseReducer);\n      }\n    });\n  }\n  var _reducer;\n  return {\n    name: name,\n    reducer: function (state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    },\n    actions: actionCreators,\n    caseReducers: sliceCaseReducersByName,\n    getInitialState: function () {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n  };\n}\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory() {\n  function getInitialState(additionalState) {\n    if (additionalState === void 0) {\n      additionalState = {};\n    }\n    return Object.assign(getInitialEntityState(), additionalState);\n  }\n  return {\n    getInitialState: getInitialState\n  };\n}\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState) {\n    var selectIds = function (state) {\n      return state.ids;\n    };\n    var selectEntities = function (state) {\n      return state.entities;\n    };\n    var selectAll = createDraftSafeSelector(selectIds, selectEntities, function (ids, entities) {\n      return ids.map(function (id) {\n        return entities[id];\n      });\n    });\n    var selectId = function (_, id) {\n      return id;\n    };\n    var selectById = function (entities, id) {\n      return entities[id];\n    };\n    var selectTotal = createDraftSafeSelector(selectIds, function (ids) {\n      return ids.length;\n    });\n    if (!selectState) {\n      return {\n        selectIds: selectIds,\n        selectEntities: selectEntities,\n        selectAll: selectAll,\n        selectTotal: selectTotal,\n        selectById: createDraftSafeSelector(selectEntities, selectId, selectById)\n      };\n    }\n    var selectGlobalizedEntities = createDraftSafeSelector(selectState, selectEntities);\n    return {\n      selectIds: createDraftSafeSelector(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createDraftSafeSelector(selectState, selectAll),\n      selectTotal: createDraftSafeSelector(selectState, selectTotal),\n      selectById: createDraftSafeSelector(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors: getSelectors\n  };\n}\n// src/entities/state_adapter.ts\nimport createNextState3, { isDraft as isDraft3 } from \"immer\";\nfunction createSingleArgumentStateOperator(mutator) {\n  var operator = createStateOperator(function (_, state) {\n    return mutator(state);\n  });\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    var runMutator = function (draft) {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraft3(state)) {\n      runMutator(state);\n      return state;\n    } else {\n      return createNextState3(state, runMutator);\n    }\n  };\n}\n// src/entities/utils.ts\nfunction selectIdValue(entity, selectId) {\n  var key = selectId(entity);\n  if (process.env.NODE_ENV !== \"production\" && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  var added = [];\n  var updated = [];\n  for (var _i = 0, newEntities_1 = newEntities; _i < newEntities_1.length; _i++) {\n    var entity = newEntities_1[_i];\n    var id = selectIdValue(entity, selectId);\n    if (id in state.entities) {\n      updated.push({\n        id: id,\n        changes: entity\n      });\n    } else {\n      added.push(entity);\n    }\n  }\n  return [added, updated];\n}\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    var key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (var _i = 0, newEntities_2 = newEntities; _i < newEntities_2.length; _i++) {\n      var entity = newEntities_2[_i];\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    var key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (var _i = 0, newEntities_3 = newEntities; _i < newEntities_3.length; _i++) {\n      var entity = newEntities_3[_i];\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    var didMutate = false;\n    keys.forEach(function (key) {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter(function (id) {\n        return id in state.entities;\n      });\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    var original2 = state.entities[update.id];\n    var updated = Object.assign({}, original2, update.changes);\n    var newKey = selectIdValue(updated, selectId);\n    var hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    var newKeys = {};\n    var updatesPerEntity = {};\n    updates.forEach(function (update) {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          changes: __spreadValues(__spreadValues({}, updatesPerEntity[update.id] ? updatesPerEntity[update.id].changes : null), update.changes)\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    var didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      var didMutateIds = updates.filter(function (update) {\n        return takeNewKey(newKeys, update, state);\n      }).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.keys(state.entities);\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    var _c = splitAddedUpdatedEntities(newEntities, selectId, state),\n      added = _c[0],\n      updated = _c[1];\n    updateManyMutably(updated, state);\n    addManyMutably(added, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n// src/entities/sorted_state_adapter.ts\nfunction createSortedStateAdapter(selectId, sort) {\n  var _c = createUnsortedStateAdapter(selectId),\n    removeOne = _c.removeOne,\n    removeMany = _c.removeMany,\n    removeAll = _c.removeAll;\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    var models = newEntities.filter(function (model) {\n      return !(selectIdValue(model, selectId) in state.entities);\n    });\n    if (models.length !== 0) {\n      merge(models, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      merge(newEntities, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    var appliedUpdates = false;\n    for (var _i = 0, updates_1 = updates; _i < updates_1.length; _i++) {\n      var update = updates_1[_i];\n      var entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      var newId = selectId(entity);\n      if (update.id !== newId) {\n        delete state.entities[update.id];\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      resortEntities(state);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    var _c = splitAddedUpdatedEntities(newEntities, selectId, state),\n      added = _c[0],\n      updated = _c[1];\n    updateManyMutably(updated, state);\n    addManyMutably(added, state);\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (var i = 0; i < a.length && i < b.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  function merge(models, state) {\n    models.forEach(function (model) {\n      state.entities[selectId(model)] = model;\n    });\n    resortEntities(state);\n  }\n  function resortEntities(state) {\n    var allEntities = Object.values(state.entities);\n    allEntities.sort(sort);\n    var newSortedIds = allEntities.map(selectId);\n    var ids = state.ids;\n    if (!areArraysEqual(ids, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  }\n  return {\n    removeOne: removeOne,\n    removeMany: removeMany,\n    removeAll: removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _c = __spreadValues({\n      sortComparer: false,\n      selectId: function (instance) {\n        return instance.id;\n      }\n    }, options),\n    selectId = _c.selectId,\n    sortComparer = _c.sortComparer;\n  var stateFactory = createInitialStateFactory();\n  var selectorsFactory = createSelectorsFactory();\n  var stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  return __spreadValues(__spreadValues(__spreadValues({\n    selectId: selectId,\n    sortComparer: sortComparer\n  }, stateFactory), selectorsFactory), stateAdapter);\n}\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = function (size) {\n  if (size === void 0) {\n    size = 21;\n  }\n  var id = \"\";\n  var i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = /** @class */function () {\n  function RejectWithValue(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  return RejectWithValue;\n}();\nvar FulfillWithMeta = /** @class */function () {\n  function FulfillWithMeta(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  return FulfillWithMeta;\n}();\nvar miniSerializeError = function (value) {\n  if (typeof value === \"object\" && value !== null) {\n    var simpleError = {};\n    for (var _i = 0, commonProperties_1 = commonProperties; _i < commonProperties_1.length; _i++) {\n      var property = commonProperties_1[_i];\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar createAsyncThunk = function () {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    var fulfilled = createAction(typePrefix + \"/fulfilled\", function (payload, requestId, arg, meta) {\n      return {\n        payload: payload,\n        meta: __spreadProps(__spreadValues({}, meta || {}), {\n          arg: arg,\n          requestId: requestId,\n          requestStatus: \"fulfilled\"\n        })\n      };\n    });\n    var pending = createAction(typePrefix + \"/pending\", function (requestId, arg, meta) {\n      return {\n        payload: void 0,\n        meta: __spreadProps(__spreadValues({}, meta || {}), {\n          arg: arg,\n          requestId: requestId,\n          requestStatus: \"pending\"\n        })\n      };\n    });\n    var rejected = createAction(typePrefix + \"/rejected\", function (error, requestId, arg, payload, meta) {\n      return {\n        payload: payload,\n        error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n        meta: __spreadProps(__spreadValues({}, meta || {}), {\n          arg: arg,\n          requestId: requestId,\n          rejectedWithValue: !!payload,\n          requestStatus: \"rejected\",\n          aborted: (error == null ? void 0 : error.name) === \"AbortError\",\n          condition: (error == null ? void 0 : error.name) === \"ConditionError\"\n        })\n      };\n    });\n    var displayedWarning = false;\n    var AC = typeof AbortController !== \"undefined\" ? AbortController : (/** @class */function () {\n      function class_1() {\n        this.signal = {\n          aborted: false,\n          addEventListener: function () {},\n          dispatchEvent: function () {\n            return false;\n          },\n          onabort: function () {},\n          removeEventListener: function () {},\n          reason: void 0,\n          throwIfAborted: function () {}\n        };\n      }\n      class_1.prototype.abort = function () {\n        if (process.env.NODE_ENV !== \"production\") {\n          if (!displayedWarning) {\n            displayedWarning = true;\n            console.info(\"This platform does not implement AbortController. \\nIf you want to use the AbortController to react to `abort` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.\");\n          }\n        }\n      };\n      return class_1;\n    }());\n    function actionCreator(arg) {\n      return function (dispatch, getState, extra) {\n        var requestId = (options == null ? void 0 : options.idGenerator) ? options.idGenerator(arg) : nanoid();\n        var abortController = new AC();\n        var abortReason;\n        var started = false;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        var promise2 = function () {\n          return __async(this, null, function () {\n            var _a, _b, finalAction, conditionResult, abortedPromise, err_1, skipDispatch;\n            return __generator(this, function (_c) {\n              switch (_c.label) {\n                case 0:\n                  _c.trys.push([0, 4,, 5]);\n                  conditionResult = (_a = options == null ? void 0 : options.condition) == null ? void 0 : _a.call(options, arg, {\n                    getState: getState,\n                    extra: extra\n                  });\n                  if (!isThenable(conditionResult)) return [3 /*break*/, 2];\n                  return [4 /*yield*/, conditionResult];\n                case 1:\n                  conditionResult = _c.sent();\n                  _c.label = 2;\n                case 2:\n                  if (conditionResult === false || abortController.signal.aborted) {\n                    throw {\n                      name: \"ConditionError\",\n                      message: \"Aborted due to condition callback returning false.\"\n                    };\n                  }\n                  started = true;\n                  abortedPromise = new Promise(function (_, reject) {\n                    return abortController.signal.addEventListener(\"abort\", function () {\n                      return reject({\n                        name: \"AbortError\",\n                        message: abortReason || \"Aborted\"\n                      });\n                    });\n                  });\n                  dispatch(pending(requestId, arg, (_b = options == null ? void 0 : options.getPendingMeta) == null ? void 0 : _b.call(options, {\n                    requestId: requestId,\n                    arg: arg\n                  }, {\n                    getState: getState,\n                    extra: extra\n                  })));\n                  return [4 /*yield*/, Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n                    dispatch: dispatch,\n                    getState: getState,\n                    extra: extra,\n                    requestId: requestId,\n                    signal: abortController.signal,\n                    abort: abort,\n                    rejectWithValue: function (value, meta) {\n                      return new RejectWithValue(value, meta);\n                    },\n                    fulfillWithValue: function (value, meta) {\n                      return new FulfillWithMeta(value, meta);\n                    }\n                  })).then(function (result) {\n                    if (result instanceof RejectWithValue) {\n                      throw result;\n                    }\n                    if (result instanceof FulfillWithMeta) {\n                      return fulfilled(result.payload, requestId, arg, result.meta);\n                    }\n                    return fulfilled(result, requestId, arg);\n                  })])];\n                case 3:\n                  finalAction = _c.sent();\n                  return [3 /*break*/, 5];\n                case 4:\n                  err_1 = _c.sent();\n                  finalAction = err_1 instanceof RejectWithValue ? rejected(null, requestId, arg, err_1.payload, err_1.meta) : rejected(err_1, requestId, arg);\n                  return [3 /*break*/, 5];\n                case 5:\n                  skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n                  if (!skipDispatch) {\n                    dispatch(finalAction);\n                  }\n                  return [2 /*return*/, finalAction];\n              }\n            });\n          });\n        }();\n        return Object.assign(promise2, {\n          abort: abort,\n          requestId: requestId,\n          arg: arg,\n          unwrap: function () {\n            return promise2.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending: pending,\n      rejected: rejected,\n      fulfilled: fulfilled,\n      typePrefix: typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = function () {\n    return createAsyncThunk2;\n  };\n  return createAsyncThunk2;\n}();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n// src/matchers.ts\nvar matches = function (matcher, action) {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf() {\n  var matchers = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    matchers[_i] = arguments[_i];\n  }\n  return function (action) {\n    return matchers.some(function (matcher) {\n      return matches(matcher, action);\n    });\n  };\n}\nfunction isAllOf() {\n  var matchers = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    matchers[_i] = arguments[_i];\n  }\n  return function (action) {\n    return matchers.every(function (matcher) {\n      return matches(matcher, action);\n    });\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  var hasValidRequestId = typeof action.meta.requestId === \"string\";\n  var hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending() {\n  var asyncThunks = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    asyncThunks[_i] = arguments[_i];\n  }\n  if (asyncThunks.length === 0) {\n    return function (action) {\n      return hasExpectedRequestMetadata(action, [\"pending\"]);\n    };\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return function (action) {\n    var matchers = asyncThunks.map(function (asyncThunk) {\n      return asyncThunk.pending;\n    });\n    var combinedMatcher = isAnyOf.apply(void 0, matchers);\n    return combinedMatcher(action);\n  };\n}\nfunction isRejected() {\n  var asyncThunks = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    asyncThunks[_i] = arguments[_i];\n  }\n  if (asyncThunks.length === 0) {\n    return function (action) {\n      return hasExpectedRequestMetadata(action, [\"rejected\"]);\n    };\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return function (action) {\n    var matchers = asyncThunks.map(function (asyncThunk) {\n      return asyncThunk.rejected;\n    });\n    var combinedMatcher = isAnyOf.apply(void 0, matchers);\n    return combinedMatcher(action);\n  };\n}\nfunction isRejectedWithValue() {\n  var asyncThunks = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    asyncThunks[_i] = arguments[_i];\n  }\n  var hasFlag = function (action) {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return function (action) {\n      var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\n      return combinedMatcher(action);\n    };\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return function (action) {\n    var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\n    return combinedMatcher(action);\n  };\n}\nfunction isFulfilled() {\n  var asyncThunks = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    asyncThunks[_i] = arguments[_i];\n  }\n  if (asyncThunks.length === 0) {\n    return function (action) {\n      return hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n    };\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return function (action) {\n    var matchers = asyncThunks.map(function (asyncThunk) {\n      return asyncThunk.fulfilled;\n    });\n    var combinedMatcher = isAnyOf.apply(void 0, matchers);\n    return combinedMatcher(action);\n  };\n}\nfunction isAsyncThunkAction() {\n  var asyncThunks = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    asyncThunks[_i] = arguments[_i];\n  }\n  if (asyncThunks.length === 0) {\n    return function (action) {\n      return hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n    };\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return function (action) {\n    var matchers = [];\n    for (var _i = 0, asyncThunks_1 = asyncThunks; _i < asyncThunks_1.length; _i++) {\n      var asyncThunk = asyncThunks_1[_i];\n      matchers.push(asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled);\n    }\n    var combinedMatcher = isAnyOf.apply(void 0, matchers);\n    return combinedMatcher(action);\n  };\n}\n// src/listenerMiddleware/utils.ts\nvar assertFunction = function (func, expected) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(expected + \" is not a function\");\n  }\n};\nvar noop = function () {};\nvar catchRejection = function (promise2, onError) {\n  if (onError === void 0) {\n    onError = noop;\n  }\n  promise2.catch(onError);\n  return promise2;\n};\nvar addAbortSignalListener = function (abortSignal, callback) {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return function () {\n    return abortSignal.removeEventListener(\"abort\", callback);\n  };\n};\nvar abortControllerWithReason = function (abortController, reason) {\n  var signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = \"task-\" + cancelled;\nvar taskCompleted = \"task-\" + completed;\nvar listenerCancelled = listener + \"-\" + cancelled;\nvar listenerCompleted = listener + \"-\" + completed;\nvar TaskAbortError = /** @class */function () {\n  function TaskAbortError(code) {\n    this.code = code;\n    this.name = \"TaskAbortError\";\n    this.message = task + \" \" + cancelled + \" (reason: \" + code + \")\";\n  }\n  return TaskAbortError;\n}();\n// src/listenerMiddleware/task.ts\nvar validateActive = function (signal) {\n  if (signal.aborted) {\n    throw new TaskAbortError(signal.reason);\n  }\n};\nfunction raceWithSignal(signal, promise2) {\n  var cleanup = noop;\n  return new Promise(function (resolve, reject) {\n    var notifyRejection = function () {\n      return reject(new TaskAbortError(signal.reason));\n    };\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise2.finally(function () {\n      return cleanup();\n    }).then(resolve, reject);\n  }).finally(function () {\n    cleanup = noop;\n  });\n}\nvar runTask = function (task2, cleanUp) {\n  return __async(void 0, null, function () {\n    var value, error_1;\n    return __generator(this, function (_c) {\n      switch (_c.label) {\n        case 0:\n          _c.trys.push([0, 3, 4, 5]);\n          return [4 /*yield*/, Promise.resolve()];\n        case 1:\n          _c.sent();\n          return [4 /*yield*/, task2()];\n        case 2:\n          value = _c.sent();\n          return [2 /*return*/, {\n            status: \"ok\",\n            value: value\n          }];\n        case 3:\n          error_1 = _c.sent();\n          return [2 /*return*/, {\n            status: error_1 instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n            error: error_1\n          }];\n        case 4:\n          cleanUp == null ? void 0 : cleanUp();\n          return [7 /*endfinally*/];\n        case 5:\n          return [2 /*return*/];\n      }\n    });\n  });\n};\nvar createPause = function (signal) {\n  return function (promise2) {\n    return catchRejection(raceWithSignal(signal, promise2).then(function (output) {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = function (signal) {\n  var pause = createPause(signal);\n  return function (timeoutMs) {\n    return pause(new Promise(function (resolve) {\n      return setTimeout(resolve, timeoutMs);\n    }));\n  };\n};\n// src/listenerMiddleware/index.ts\nvar assign = Object.assign;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = function (parentAbortSignal, parentBlockingPromises) {\n  var linkControllers = function (controller) {\n    return addAbortSignalListener(parentAbortSignal, function () {\n      return abortControllerWithReason(controller, parentAbortSignal.reason);\n    });\n  };\n  return function (taskExecutor, opts) {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    var childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    var result = runTask(function () {\n      return __async(void 0, null, function () {\n        var result2;\n        return __generator(this, function (_c) {\n          switch (_c.label) {\n            case 0:\n              validateActive(parentAbortSignal);\n              validateActive(childAbortController.signal);\n              return [4 /*yield*/, taskExecutor({\n                pause: createPause(childAbortController.signal),\n                delay: createDelay(childAbortController.signal),\n                signal: childAbortController.signal\n              })];\n            case 1:\n              result2 = _c.sent();\n              validateActive(childAbortController.signal);\n              return [2 /*return*/, result2];\n          }\n        });\n      });\n    }, function () {\n      return abortControllerWithReason(childAbortController, taskCompleted);\n    });\n    if (opts == null ? void 0 : opts.autoJoin) {\n      parentBlockingPromises.push(result);\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel: function () {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = function (startListening, signal) {\n  var take = function (predicate, timeout) {\n    return __async(void 0, null, function () {\n      var unsubscribe, tuplePromise, promises, output;\n      return __generator(this, function (_c) {\n        switch (_c.label) {\n          case 0:\n            validateActive(signal);\n            unsubscribe = function () {};\n            tuplePromise = new Promise(function (resolve, reject) {\n              var stopListening = startListening({\n                predicate: predicate,\n                effect: function (action, listenerApi) {\n                  listenerApi.unsubscribe();\n                  resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n                }\n              });\n              unsubscribe = function () {\n                stopListening();\n                reject();\n              };\n            });\n            promises = [tuplePromise];\n            if (timeout != null) {\n              promises.push(new Promise(function (resolve) {\n                return setTimeout(resolve, timeout, null);\n              }));\n            }\n            _c.label = 1;\n          case 1:\n            _c.trys.push([1,, 3, 4]);\n            return [4 /*yield*/, raceWithSignal(signal, Promise.race(promises))];\n          case 2:\n            output = _c.sent();\n            validateActive(signal);\n            return [2 /*return*/, output];\n          case 3:\n            unsubscribe();\n            return [7 /*endfinally*/];\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  return function (predicate, timeout) {\n    return catchRejection(take(predicate, timeout));\n  };\n};\nvar getListenerEntryPropsFrom = function (options) {\n  var type = options.type,\n    actionCreator = options.actionCreator,\n    matcher = options.matcher,\n    predicate = options.predicate,\n    effect = options.effect;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {} else {\n    throw new Error(\"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate: predicate,\n    type: type,\n    effect: effect\n  };\n};\nvar createListenerEntry = function (options) {\n  var _c = getListenerEntryPropsFrom(options),\n    type = _c.type,\n    predicate = _c.predicate,\n    effect = _c.effect;\n  var id = nanoid();\n  var entry = {\n    id: id,\n    effect: effect,\n    type: type,\n    predicate: predicate,\n    pending: new Set(),\n    unsubscribe: function () {\n      throw new Error(\"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n};\nvar cancelActiveListeners = function (entry) {\n  entry.pending.forEach(function (controller) {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = function (listenerMap) {\n  return function () {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = function (errorHandler, errorToNotify, errorInfo) {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(function () {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = createAction(alm + \"/add\");\nvar clearAllListeners = createAction(alm + \"/removeAll\");\nvar removeListener = createAction(alm + \"/remove\");\nvar defaultErrorHandler = function () {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  console.error.apply(console, __spreadArray([alm + \"/error\"], args));\n};\nfunction createListenerMiddleware(middlewareOptions) {\n  var _this = this;\n  if (middlewareOptions === void 0) {\n    middlewareOptions = {};\n  }\n  var listenerMap = new Map();\n  var extra = middlewareOptions.extra,\n    _c = middlewareOptions.onError,\n    onError = _c === void 0 ? defaultErrorHandler : _c;\n  assertFunction(onError, \"onError\");\n  var insertEntry = function (entry) {\n    entry.unsubscribe = function () {\n      return listenerMap.delete(entry.id);\n    };\n    listenerMap.set(entry.id, entry);\n    return function (cancelOptions) {\n      entry.unsubscribe();\n      if (cancelOptions == null ? void 0 : cancelOptions.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  var findListenerEntry = function (comparator) {\n    for (var _i = 0, _c = Array.from(listenerMap.values()); _i < _c.length; _i++) {\n      var entry = _c[_i];\n      if (comparator(entry)) {\n        return entry;\n      }\n    }\n    return void 0;\n  };\n  var startListening = function (options) {\n    var entry = findListenerEntry(function (existingEntry) {\n      return existingEntry.effect === options.effect;\n    });\n    if (!entry) {\n      entry = createListenerEntry(options);\n    }\n    return insertEntry(entry);\n  };\n  var stopListening = function (options) {\n    var _c = getListenerEntryPropsFrom(options),\n      type = _c.type,\n      effect = _c.effect,\n      predicate = _c.predicate;\n    var entry = findListenerEntry(function (entry2) {\n      var matchPredicateOrType = typeof type === \"string\" ? entry2.type === type : entry2.predicate === predicate;\n      return matchPredicateOrType && entry2.effect === effect;\n    });\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  var notifyListener = function (entry, action, api, getOriginalState) {\n    return __async(_this, null, function () {\n      var internalTaskController, take, autoJoinPromises, listenerError_1;\n      return __generator(this, function (_c) {\n        switch (_c.label) {\n          case 0:\n            internalTaskController = new AbortController();\n            take = createTakePattern(startListening, internalTaskController.signal);\n            autoJoinPromises = [];\n            _c.label = 1;\n          case 1:\n            _c.trys.push([1, 3, 4, 6]);\n            entry.pending.add(internalTaskController);\n            return [4 /*yield*/, Promise.resolve(entry.effect(action, assign({}, api, {\n              getOriginalState: getOriginalState,\n              condition: function (predicate, timeout) {\n                return take(predicate, timeout).then(Boolean);\n              },\n              take: take,\n              delay: createDelay(internalTaskController.signal),\n              pause: createPause(internalTaskController.signal),\n              extra: extra,\n              signal: internalTaskController.signal,\n              fork: createFork(internalTaskController.signal, autoJoinPromises),\n              unsubscribe: entry.unsubscribe,\n              subscribe: function () {\n                listenerMap.set(entry.id, entry);\n              },\n              cancelActiveListeners: function () {\n                entry.pending.forEach(function (controller, _, set) {\n                  if (controller !== internalTaskController) {\n                    abortControllerWithReason(controller, listenerCancelled);\n                    set.delete(controller);\n                  }\n                });\n              }\n            })))];\n          case 2:\n            _c.sent();\n            return [3 /*break*/, 6];\n          case 3:\n            listenerError_1 = _c.sent();\n            if (!(listenerError_1 instanceof TaskAbortError)) {\n              safelyNotifyError(onError, listenerError_1, {\n                raisedBy: \"effect\"\n              });\n            }\n            return [3 /*break*/, 6];\n          case 4:\n            return [4 /*yield*/, Promise.allSettled(autoJoinPromises)];\n          case 5:\n            _c.sent();\n            abortControllerWithReason(internalTaskController, listenerCompleted);\n            entry.pending.delete(internalTaskController);\n            return [7 /*endfinally*/];\n          case 6:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  var clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  var middleware = function (api) {\n    return function (next) {\n      return function (action) {\n        if (!isAction(action)) {\n          return next(action);\n        }\n        if (addListener.match(action)) {\n          return startListening(action.payload);\n        }\n        if (clearAllListeners.match(action)) {\n          clearListenerMiddleware();\n          return;\n        }\n        if (removeListener.match(action)) {\n          return stopListening(action.payload);\n        }\n        var originalState = api.getState();\n        var getOriginalState = function () {\n          if (originalState === INTERNAL_NIL_TOKEN) {\n            throw new Error(alm + \": getOriginalState can only be called synchronously\");\n          }\n          return originalState;\n        };\n        var result;\n        try {\n          result = next(action);\n          if (listenerMap.size > 0) {\n            var currentState = api.getState();\n            var listenerEntries = Array.from(listenerMap.values());\n            for (var _i = 0, listenerEntries_1 = listenerEntries; _i < listenerEntries_1.length; _i++) {\n              var entry = listenerEntries_1[_i];\n              var runListener = false;\n              try {\n                runListener = entry.predicate(action, currentState, originalState);\n              } catch (predicateError) {\n                runListener = false;\n                safelyNotifyError(onError, predicateError, {\n                  raisedBy: \"predicate\"\n                });\n              }\n              if (!runListener) {\n                continue;\n              }\n              notifyListener(entry, action, api, getOriginalState);\n            }\n          }\n        } finally {\n          originalState = INTERNAL_NIL_TOKEN;\n        }\n        return result;\n      };\n    };\n  };\n  return {\n    middleware: middleware,\n    startListening: startListening,\n    stopListening: stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n}\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = function () {\n  return function (payload) {\n    var _c;\n    return {\n      payload: payload,\n      meta: (_c = {}, _c[SHOULD_AUTOBATCH] = true, _c)\n    };\n  };\n};\nvar promise;\nvar queueMicrotaskShim = typeof queueMicrotask === \"function\" ? queueMicrotask.bind(typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : globalThis) : function (cb) {\n  return (promise || (promise = Promise.resolve())).then(cb).catch(function (err) {\n    return setTimeout(function () {\n      throw err;\n    }, 0);\n  });\n};\nvar createQueueWithTimer = function (timeout) {\n  return function (notify) {\n    setTimeout(notify, timeout);\n  };\n};\nvar rAF = typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10);\nvar autoBatchEnhancer = function (options) {\n  if (options === void 0) {\n    options = {\n      type: \"raf\"\n    };\n  }\n  return function (next) {\n    return function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      var store = next.apply(void 0, args);\n      var notifying = true;\n      var shouldNotifyAtEndOfTick = false;\n      var notificationQueued = false;\n      var listeners = new Set();\n      var queueCallback = options.type === \"tick\" ? queueMicrotaskShim : options.type === \"raf\" ? rAF : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n      var notifyListeners = function () {\n        notificationQueued = false;\n        if (shouldNotifyAtEndOfTick) {\n          shouldNotifyAtEndOfTick = false;\n          listeners.forEach(function (l) {\n            return l();\n          });\n        }\n      };\n      return Object.assign({}, store, {\n        subscribe: function (listener2) {\n          var wrappedListener = function () {\n            return notifying && listener2();\n          };\n          var unsubscribe = store.subscribe(wrappedListener);\n          listeners.add(listener2);\n          return function () {\n            unsubscribe();\n            listeners.delete(listener2);\n          };\n        },\n        dispatch: function (action) {\n          var _a;\n          try {\n            notifying = !((_a = action == null ? void 0 : action.meta) == null ? void 0 : _a[SHOULD_AUTOBATCH]);\n            shouldNotifyAtEndOfTick = !notifying;\n            if (shouldNotifyAtEndOfTick) {\n              if (!notificationQueued) {\n                notificationQueued = true;\n                queueCallback(notifyListeners);\n              }\n            }\n            return store.dispatch(action);\n          } finally {\n            notifying = true;\n          }\n        }\n      });\n    };\n  };\n};\n// src/index.ts\nenableES5();\nexport { EnhancerArray, MiddlewareArray, SHOULD_AUTOBATCH, TaskAbortError, addListener, autoBatchEnhancer, clearAllListeners, configureStore, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, default2 as createNextState, createReducer, createSelector2 as createSelector, createSerializableStateInvariantMiddleware, createSlice, current2 as current, findNonSerializableValue, freeze, getDefaultMiddleware, getType, isAction, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isDraft4 as isDraft, isFSA as isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isPlainObject, isRejected, isRejectedWithValue, miniSerializeError, nanoid, original, prepareAutoBatched, removeListener, unwrapResult };", "map": {"version": 3, "names": ["enableES5", "default", "default2", "current", "current2", "freeze", "original", "isDraft", "isDraft4", "createSelector", "createSelector2", "createDraftSafeSelector", "args", "_i", "arguments", "length", "selector", "apply", "wrappedSelector", "value", "rest", "__spread<PERSON><PERSON>y", "createStore", "compose", "compose2", "applyMiddleware", "combineReducers", "composeWithDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "devToolsEnhancer", "__REDUX_DEVTOOLS_EXTENSION__", "noop2", "isPlainObject", "proto", "Object", "getPrototypeOf", "baseProto", "thunkMiddleware", "hasMatchFunction", "v", "match", "createAction", "type", "prepareAction", "actionCreator", "prepared", "Error", "__spreadValues", "payload", "meta", "error", "toString", "action", "isAction", "isActionCreator", "isFSA", "keys", "every", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "getType", "getMessage", "splitType", "split", "actionName", "createActionCreatorInvariantMiddleware", "options", "process", "env", "NODE_ENV", "next", "_c", "isActionCreator2", "console", "warn", "createNextState", "isDraftable", "getTimeMeasureUtils", "max<PERSON><PERSON><PERSON>", "fnName", "elapsed", "measureTime", "fn", "started", "Date", "now", "finished", "warnIfExceeded", "MiddlewareArray", "_super", "__extends", "_this", "setPrototypeOf", "prototype", "defineProperty", "Symbol", "species", "get", "concat", "arr", "prepend", "Array", "isArray", "bind", "EnhancerArray", "freezeDraftable", "val", "isProduction", "prefix", "invariant", "condition", "message", "stringify", "obj", "serializer", "indent", "decycler", "JSON", "getSerialize", "stack", "_", "slice", "join", "thisPos", "splice", "push", "Infinity", "call", "isImmutableDefault", "isFrozen", "trackForMutations", "isImmutable", "ignorePaths", "trackedProperties", "trackProperties", "detectMutations", "path", "checkedObjects", "Set", "tracked", "has", "add", "children", "child<PERSON><PERSON>", "ignoredPaths", "trackedProperty", "sameParentRef", "prevObj", "sameRef", "Number", "isNaN", "wasMutated", "keysToDetect", "hasIgnoredPaths", "nested<PERSON>ath", "hasMatches", "some", "ignored", "RegExp", "test", "result", "createImmutableStateInvariantMiddleware", "_d", "warnAfter", "ignore", "track", "getState", "state", "tracker", "measureUtils", "dispatchedAction", "<PERSON><PERSON><PERSON>", "findNonSerializableValue", "isSerializable", "getEntries", "cache", "foundNestedSerializable", "keyP<PERSON>", "entries", "nestedV<PERSON>ue", "entries_1", "isNestedFrozen", "values", "createSerializableStateInvariantMiddleware", "ignoredActions", "_e", "ignoredActionPaths", "_f", "_g", "_h", "ignoreState", "_j", "ignoreActions", "_k", "disableCache", "WeakSet", "storeAPI", "foundActionNonSerializableValue", "foundStateNonSerializableValue", "isBoolean", "x", "curryGetDefaultMiddleware", "curriedGetDefaultMiddleware", "getDefaultMiddleware", "thunk", "immutableCheck", "serializableCheck", "actionCreatorCheck", "middlewareArray", "withExtraArgument", "extraArgument", "immutableOptions", "unshift", "serializableOptions", "actionCreatorOptions", "IS_PRODUCTION", "configureStore", "reducer", "middleware", "devTools", "preloadedState", "enhancers", "rootReducer", "finalMiddleware", "item", "middlewareEnhancer", "finalCompose", "trace", "defaultEnhancers", "storeEnhancers", "composedEnhancer", "createNextState2", "isDraft2", "isDraftable2", "executeReducerBuilderCallback", "builderCallback", "actionsMap", "actionMatchers", "defaultCaseReducer", "builder", "addCase", "typeOrActionCreator", "addMatcher", "matcher", "addDefaultCase", "isStateFunction", "hasWarnedAboutObjectNotation", "createReducer", "initialState", "mapOrBuilderCallback", "finalActionMatchers", "finalDefaultCaseReducer", "getInitialState", "frozenInitialState_1", "caseReducers", "filter", "map", "reducer2", "cr", "reduce", "previousState", "caseReducer", "draft", "hasWarnedAboutObjectNotation2", "getType2", "action<PERSON>ey", "createSlice", "name", "reducers", "reducerNames", "sliceCaseReducersByName", "sliceCaseReducersByType", "actionCreators", "for<PERSON>ach", "reducerName", "maybeReducerWithPrepare", "prepareCallback", "prepare", "buildReducer", "extraReducers", "finalCaseReducers", "actionMatchers_1", "m", "_reducer", "actions", "getInitialEntityState", "ids", "entities", "createInitialStateFactory", "additionalState", "assign", "createSelectorsFactory", "getSelectors", "selectState", "selectIds", "selectEntities", "selectAll", "id", "selectId", "selectById", "selectTotal", "selectGlobalizedEntities", "createNextState3", "isDraft3", "createSingleArgumentStateOperator", "mutator", "operator", "createStateOperator", "operation", "arg", "isPayloadActionArgument", "arg2", "runMutator", "selectIdValue", "entity", "ensureEntitiesArray", "splitAddedUpdatedEntities", "newEntities", "added", "updated", "newEntities_1", "changes", "createUnsortedStateAdapter", "addOneMutably", "addManyMutably", "newEntities_2", "setOneMutably", "setManyMutably", "newEntities_3", "setAllMutably", "removeOneMutably", "removeManyMutably", "didMutate", "removeAllMutably", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "original2", "new<PERSON>ey", "has<PERSON>ew<PERSON><PERSON>", "updateOneMutably", "updateManyMutably", "updates", "newKeys", "updatesPerEntity", "didMutateEntities", "didMutateIds", "upsertOneMutably", "upsertManyMutably", "removeAll", "addOne", "addMany", "setOne", "setMany", "setAll", "updateOne", "updateMany", "upsertOne", "upsertMany", "removeOne", "remove<PERSON>any", "createSortedStateAdapter", "sort", "models", "model", "merge", "appliedUpdates", "updates_1", "newId", "resortEntities", "areArraysEqual", "a", "b", "i", "allEntities", "newSortedIds", "createEntityAdapter", "sortComparer", "instance", "stateFactory", "selectorsFactory", "stateAdapter", "url<PERSON>l<PERSON><PERSON>", "nanoid", "size", "Math", "random", "commonProperties", "RejectWithValue", "FulfillWithMeta", "miniSerializeError", "simpleError", "commonProperties_1", "property", "String", "createAsyncThunk", "createAsyncThunk2", "typePrefix", "payloadCreator", "fulfilled", "requestId", "__spreadProps", "requestStatus", "pending", "rejected", "serializeError", "rejectedWithValue", "aborted", "displayedWarning", "AC", "AbortController", "class_1", "signal", "addEventListener", "dispatchEvent", "<PERSON>ab<PERSON>", "removeEventListener", "reason", "throwIfAborted", "abort", "info", "dispatch", "extra", "idGenerator", "abortController", "abortReason", "promise2", "__async", "conditionResult", "_a", "isThenable", "sent", "abortedPromise", "Promise", "reject", "_b", "getPendingMeta", "race", "resolve", "rejectWithValue", "fulfillWithValue", "then", "finalAction", "err_1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchConditionRejection", "unwrap", "unwrapResult", "withTypes", "matches", "isAnyOf", "matchers", "isAllOf", "hasExpectedRequestMetadata", "validStatus", "hasValidRequestId", "hasValidRequestStatus", "isAsyncThunkArray", "isPending", "asyncThunks", "asyncThunk", "combinedMatcher", "isRejected", "isRejectedWithValue", "hasFlag", "isFulfilled", "isAsyncThunkAction", "asyncThunks_1", "assertFunction", "func", "expected", "TypeError", "noop", "catchRejection", "onError", "catch", "addAbortSignalListener", "abortSignal", "callback", "once", "abortControllerWithReason", "enumerable", "configurable", "writable", "task", "listener", "completed", "cancelled", "taskCancelled", "taskCompleted", "listenerCancelled", "listenerCompleted", "TaskAbortError", "code", "validateActive", "raceWithSignal", "cleanup", "notifyRejection", "finally", "runTask", "task2", "cleanUp", "status", "error_1", "createPause", "output", "createDelay", "pause", "timeoutMs", "setTimeout", "INTERNAL_NIL_TOKEN", "alm", "createFork", "parentAbortSignal", "parentBlockingPromises", "linkControllers", "controller", "taskExecutor", "opts", "childAbortController", "delay", "result2", "autoJoin", "cancel", "createTakePattern", "startListening", "take", "predicate", "timeout", "unsubscribe", "tuplePromise", "stopListening", "effect", "listenerApi", "getOriginalState", "promises", "getListenerEntryPropsFrom", "createListenerEntry", "entry", "cancelActiveListeners", "createClearListenerMiddleware", "listenerMap", "clear", "safelyNotifyError", "<PERSON><PERSON><PERSON><PERSON>", "errorToNotify", "errorInfo", "errorHandlerError", "addListener", "clearAllListeners", "removeListener", "defaultErrorHandler", "createListenerMiddleware", "middlewareOptions", "Map", "insertEntry", "delete", "set", "cancelOptions", "cancelActive", "findListenerEntry", "comparator", "from", "existingEntry", "entry2", "matchPredicateOrType", "notifyL<PERSON>ener", "api", "internalTaskController", "autoJoinPromises", "Boolean", "fork", "subscribe", "listenerError_1", "<PERSON><PERSON><PERSON>", "allSettled", "clearListenerMiddleware", "originalState", "currentState", "listenerEntries", "listenerEntries_1", "runListener", "predicateError", "clearListeners", "SHOULD_AUTOBATCH", "prepareAutoBatched", "promise", "queueMicrot<PERSON><PERSON><PERSON>", "queueMicrotask", "global", "globalThis", "cb", "err", "createQueueWithTimer", "notify", "rAF", "requestAnimationFrame", "autoBatchEnhancer", "store", "notifying", "shouldNotifyAtEndOfTick", "notificationQueued", "listeners", "queue<PERSON>allback", "queueNotification", "notifyListeners", "l", "listener2", "wrappedListener"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/index.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/createDraftSafeSelector.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/configureStore.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/devtoolsExtension.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/isPlainObject.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/getDefaultMiddleware.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/tsHelpers.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/createAction.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/actionCreatorInvariantMiddleware.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/utils.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/immutableStateInvariantMiddleware.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/serializableStateInvariantMiddleware.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/createReducer.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/mapBuilders.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/createSlice.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/entity_state.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/state_selectors.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/state_adapter.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/utils.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/unsorted_state_adapter.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/sorted_state_adapter.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/entities/create_adapter.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/nanoid.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/createAsyncThunk.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/matchers.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/utils.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/exceptions.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/task.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/listenerMiddleware/index.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/src/autoBatchEnhancer.ts", "/Users/<USER>/code_base/lms_project/frontend/node_modules/@reduxjs/toolkit/dist/redux-toolkit.esm.js"], "sourcesContent": ["import { enableES5 } from 'immer'\r\nexport * from 'redux'\r\nexport {\r\n  default as createNextState,\r\n  current,\r\n  freeze,\r\n  original,\r\n  isDraft,\r\n} from 'immer'\r\nexport type { Draft } from 'immer'\r\nexport { createSelector } from 'reselect'\r\nexport type {\r\n  Selector,\r\n  OutputParametricSelector,\r\n  OutputSelector,\r\n  ParametricSelector,\r\n} from 'reselect'\r\nexport { createDraftSafeSelector } from './createDraftSafeSelector'\r\nexport type { ThunkAction, ThunkDispatch, ThunkMiddleware } from 'redux-thunk'\r\n\r\n// We deliberately enable Immer's ES5 support, on the grounds that\r\n// we assume RTK will be used with React Native and other Proxy-less\r\n// environments.  In addition, that's how Immer 4 behaved, and since\r\n// we want to ship this in an RTK minor, we should keep the same behavior.\r\nenableES5()\r\n\r\nexport {\r\n  // js\r\n  configureStore,\r\n} from './configureStore'\r\nexport type {\r\n  // types\r\n  ConfigureEnhancersCallback,\r\n  ConfigureStoreOptions,\r\n  EnhancedStore,\r\n} from './configureStore'\r\nexport type { DevToolsEnhancerOptions } from './devtoolsExtension'\r\nexport {\r\n  // js\r\n  createAction,\r\n  getType,\r\n  isAction,\r\n  isActionCreator,\r\n  isFSA as isFluxStandardAction,\r\n} from './createAction'\r\nexport type {\r\n  // types\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  ActionCreatorWithNonInferrablePayload,\r\n  ActionCreatorWithOptionalPayload,\r\n  ActionCreatorWithPayload,\r\n  ActionCreatorWithoutPayload,\r\n  ActionCreatorWithPreparedPayload,\r\n  PrepareAction,\r\n} from './createAction'\r\nexport {\r\n  // js\r\n  createReducer,\r\n} from './createReducer'\r\nexport type {\r\n  // types\r\n  Actions,\r\n  CaseReducer,\r\n  CaseReducers,\r\n} from './createReducer'\r\nexport {\r\n  // js\r\n  createSlice,\r\n} from './createSlice'\r\n\r\nexport type {\r\n  // types\r\n  CreateSliceOptions,\r\n  Slice,\r\n  CaseReducerActions,\r\n  SliceCaseReducers,\r\n  ValidateSliceCaseReducers,\r\n  CaseReducerWithPrepare,\r\n  SliceActionCreator,\r\n} from './createSlice'\r\nexport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nexport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createImmutableStateInvariantMiddleware,\r\n  isImmutableDefault,\r\n} from './immutableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  ImmutableStateInvariantMiddlewareOptions,\r\n} from './immutableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  createSerializableStateInvariantMiddleware,\r\n  findNonSerializableValue,\r\n  isPlain,\r\n} from './serializableStateInvariantMiddleware'\r\nexport type {\r\n  // types\r\n  SerializableStateInvariantMiddlewareOptions,\r\n} from './serializableStateInvariantMiddleware'\r\nexport {\r\n  // js\r\n  getDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nexport type {\r\n  // types\r\n  ActionReducerMapBuilder,\r\n} from './mapBuilders'\r\nexport { MiddlewareArray, EnhancerArray } from './utils'\r\n\r\nexport { createEntityAdapter } from './entities/create_adapter'\r\nexport type {\r\n  Dictionary,\r\n  EntityState,\r\n  EntityAdapter,\r\n  EntitySelectors,\r\n  EntityStateAdapter,\r\n  EntityId,\r\n  Update,\r\n  IdSelector,\r\n  Comparer,\r\n} from './entities/models'\r\n\r\nexport {\r\n  createAsyncThunk,\r\n  unwrapResult,\r\n  miniSerializeError,\r\n} from './createAsyncThunk'\r\nexport type {\r\n  AsyncThunk,\r\n  AsyncThunkOptions,\r\n  AsyncThunkAction,\r\n  AsyncThunkPayloadCreatorReturnValue,\r\n  AsyncThunkPayloadCreator,\r\n  SerializedError,\r\n} from './createAsyncThunk'\r\n\r\nexport {\r\n  // js\r\n  isAllOf,\r\n  isAnyOf,\r\n  isPending,\r\n  isRejected,\r\n  isFulfilled,\r\n  isAsyncThunkAction,\r\n  isRejectedWithValue,\r\n} from './matchers'\r\nexport type {\r\n  // types\r\n  ActionMatchingAllOf,\r\n  ActionMatchingAnyOf,\r\n} from './matchers'\r\n\r\nexport { nanoid } from './nanoid'\r\n\r\nexport { default as isPlainObject } from './isPlainObject'\r\n\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './listenerMiddleware/index'\r\nexport type { AnyListenerPredicate } from './listenerMiddleware/types'\r\n\r\nexport {\r\n  createListenerMiddleware,\r\n  addListener,\r\n  removeListener,\r\n  clearAllListeners,\r\n  TaskAbortError,\r\n} from './listenerMiddleware/index'\r\n\r\nexport {\r\n  SHOULD_AUTOBATCH,\r\n  prepareAutoBatched,\r\n  autoBatchEnhancer,\r\n} from './autoBatchEnhancer'\r\nexport type { AutoBatchOptions } from './autoBatchEnhancer'\r\n\r\nexport type { ExtractDispatchExtensions as TSHelpersExtractDispatchExtensions } from './tsHelpers'\r\n", "import { current, isDraft } from 'immer'\r\nimport { createSelector } from 'reselect'\r\n\r\n/**\r\n * \"Draft-Safe\" version of `reselect`'s `createSelector`:\r\n * If an `immer`-drafted object is passed into the resulting selector's first argument,\r\n * the selector will act on the current draft value, instead of returning a cached value\r\n * that might be possibly outdated if the draft has been modified since.\r\n * @public\r\n */\r\nexport const createDraftSafeSelector: typeof createSelector = (\r\n  ...args: unknown[]\r\n) => {\r\n  const selector = (createSelector as any)(...args)\r\n  const wrappedSelector = (value: unknown, ...rest: unknown[]) =>\r\n    selector(isDraft(value) ? current(value) : value, ...rest)\r\n  return wrappedSelector as any\r\n}\r\n", "import type {\r\n  Reducer,\r\n  ReducersMapObject,\r\n  Middleware,\r\n  Action,\r\n  AnyAction,\r\n  StoreEnhancer,\r\n  Store,\r\n  Dispatch,\r\n  PreloadedState,\r\n  CombinedState,\r\n} from 'redux'\r\nimport { createStore, compose, applyMiddleware, combineReducers } from 'redux'\r\nimport type { DevToolsEnhancerOptions as DevToolsOptions } from './devtoolsExtension'\r\nimport { composeWithDevTools } from './devtoolsExtension'\r\n\r\nimport isPlainObject from './isPlainObject'\r\nimport type {\r\n  ThunkMiddlewareFor,\r\n  CurriedGetDefaultMiddleware,\r\n} from './getDefaultMiddleware'\r\nimport { curryGetDefaultMiddleware } from './getDefaultMiddleware'\r\nimport type {\r\n  NoInfer,\r\n  ExtractDispatchExtensions,\r\n  ExtractStoreExtensions,\r\n  ExtractStateExtensions,\r\n} from './tsHelpers'\r\nimport { EnhancerArray } from './utils'\r\n\r\nconst IS_PRODUCTION = process.env.NODE_ENV === 'production'\r\n\r\n/**\r\n * Callback function type, to be used in `ConfigureStoreOptions.enhancers`\r\n *\r\n * @public\r\n */\r\nexport type ConfigureEnhancersCallback<E extends Enhancers = Enhancers> = (\r\n  defaultEnhancers: EnhancerArray<[StoreEnhancer<{}, {}>]>\r\n) => E\r\n\r\n/**\r\n * Options for `configureStore()`.\r\n *\r\n * @public\r\n */\r\nexport interface ConfigureStoreOptions<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> {\r\n  /**\r\n   * A single reducer function that will be used as the root reducer, or an\r\n   * object of slice reducers that will be passed to `combineReducers()`.\r\n   */\r\n  reducer: Reducer<S, A> | ReducersMapObject<S, A>\r\n\r\n  /**\r\n   * An array of Redux middleware to install. If not supplied, defaults to\r\n   * the set of middleware returned by `getDefaultMiddleware()`.\r\n   *\r\n   * @example `middleware: (gDM) => gDM().concat(logger, apiMiddleware, yourCustomMiddleware)`\r\n   * @see https://redux-toolkit.js.org/api/getDefaultMiddleware#intended-usage\r\n   */\r\n  middleware?: ((getDefaultMiddleware: CurriedGetDefaultMiddleware<S>) => M) | M\r\n\r\n  /**\r\n   * Whether to enable Redux DevTools integration. Defaults to `true`.\r\n   *\r\n   * Additional configuration can be done by passing Redux DevTools options\r\n   */\r\n  devTools?: boolean | DevToolsOptions\r\n\r\n  /**\r\n   * The initial state, same as Redux's createStore.\r\n   * You may optionally specify it to hydrate the state\r\n   * from the server in universal apps, or to restore a previously serialized\r\n   * user session. If you use `combineReducers()` to produce the root reducer\r\n   * function (either directly or indirectly by passing an object as `reducer`),\r\n   * this must be an object with the same shape as the reducer map keys.\r\n   */\r\n  /*\r\n  Not 100% correct but the best approximation we can get:\r\n  - if S is a `CombinedState` applying a second `CombinedState` on it does not change anything.\r\n  - if it is not, there could be two cases:\r\n    - `ReducersMapObject<S, A>` is being passed in. In this case, we will call `combineReducers` on it and `CombinedState<S>` is correct\r\n    - `Reducer<S, A>` is being passed in. In this case, actually `CombinedState<S>` is wrong and `S` would be correct.\r\n    As we cannot distinguish between those two cases without adding another generic parameter,\r\n    we just make the pragmatic assumption that the latter almost never happens.\r\n  */\r\n  preloadedState?: PreloadedState<CombinedState<NoInfer<S>>>\r\n\r\n  /**\r\n   * The store enhancers to apply. See Redux's `createStore()`.\r\n   * All enhancers will be included before the DevTools Extension enhancer.\r\n   * If you need to customize the order of enhancers, supply a callback\r\n   * function that will receive the original array (ie, `[applyMiddleware]`),\r\n   * and should return a new array (such as `[applyMiddleware, offline]`).\r\n   * If you only need to add middleware, you can use the `middleware` parameter instead.\r\n   */\r\n  enhancers?: E | ConfigureEnhancersCallback<E>\r\n}\r\n\r\ntype Middlewares<S> = ReadonlyArray<Middleware<{}, S>>\r\n\r\ntype Enhancers = ReadonlyArray<StoreEnhancer>\r\n\r\nexport interface ToolkitStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>\r\n> extends Store<S, A> {\r\n  /**\r\n   * The `dispatch` method of your store, enhanced by all its middlewares.\r\n   *\r\n   * @inheritdoc\r\n   */\r\n  dispatch: ExtractDispatchExtensions<M> & Dispatch<A>\r\n}\r\n\r\n/**\r\n * A Redux store returned by `configureStore()`. Supports dispatching\r\n * side-effectful _thunks_ in addition to plain actions.\r\n *\r\n * @public\r\n */\r\nexport type EnhancedStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = Middlewares<S>,\r\n  E extends Enhancers = Enhancers\r\n> = ToolkitStore<S & ExtractStateExtensions<E>, A, M> &\r\n  ExtractStoreExtensions<E>\r\n\r\n/**\r\n * A friendly abstraction over the standard Redux `createStore()` function.\r\n *\r\n * @param options The store configuration.\r\n * @returns A configured Redux store.\r\n *\r\n * @public\r\n */\r\nexport function configureStore<\r\n  S = any,\r\n  A extends Action = AnyAction,\r\n  M extends Middlewares<S> = [ThunkMiddlewareFor<S>],\r\n  E extends Enhancers = [StoreEnhancer]\r\n>(options: ConfigureStoreOptions<S, A, M, E>): EnhancedStore<S, A, M, E> {\r\n  const curriedGetDefaultMiddleware = curryGetDefaultMiddleware<S>()\r\n\r\n  const {\r\n    reducer = undefined,\r\n    middleware = curriedGetDefaultMiddleware(),\r\n    devTools = true,\r\n    preloadedState = undefined,\r\n    enhancers = undefined,\r\n  } = options || {}\r\n\r\n  let rootReducer: Reducer<S, A>\r\n\r\n  if (typeof reducer === 'function') {\r\n    rootReducer = reducer\r\n  } else if (isPlainObject(reducer)) {\r\n    rootReducer = combineReducers(reducer) as unknown as Reducer<S, A>\r\n  } else {\r\n    throw new Error(\r\n      '\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers'\r\n    )\r\n  }\r\n\r\n  let finalMiddleware = middleware\r\n  if (typeof finalMiddleware === 'function') {\r\n    finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware)\r\n\r\n    if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n      throw new Error(\r\n        'when using a middleware builder function, an array of middleware must be returned'\r\n      )\r\n    }\r\n  }\r\n  if (\r\n    !IS_PRODUCTION &&\r\n    finalMiddleware.some((item: any) => typeof item !== 'function')\r\n  ) {\r\n    throw new Error(\r\n      'each middleware provided to configureStore must be a function'\r\n    )\r\n  }\r\n\r\n  const middlewareEnhancer: StoreEnhancer = applyMiddleware(...finalMiddleware)\r\n\r\n  let finalCompose = compose\r\n\r\n  if (devTools) {\r\n    finalCompose = composeWithDevTools({\r\n      // Enable capture of stack traces for dispatched Redux actions\r\n      trace: !IS_PRODUCTION,\r\n      ...(typeof devTools === 'object' && devTools),\r\n    })\r\n  }\r\n\r\n  const defaultEnhancers = new EnhancerArray(middlewareEnhancer)\r\n  let storeEnhancers: Enhancers = defaultEnhancers\r\n\r\n  if (Array.isArray(enhancers)) {\r\n    storeEnhancers = [middlewareEnhancer, ...enhancers]\r\n  } else if (typeof enhancers === 'function') {\r\n    storeEnhancers = enhancers(defaultEnhancers)\r\n  }\r\n\r\n  const composedEnhancer = finalCompose(...storeEnhancers) as StoreEnhancer<any>\r\n\r\n  return createStore(rootReducer, preloadedState, composedEnhancer)\r\n}\r\n", "import type { Action, ActionCreator, StoreEnhancer } from 'redux'\r\nimport { compose } from 'redux'\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface DevToolsEnhancerOptions {\r\n  /**\r\n   * the instance name to be showed on the monitor page. Default value is `document.title`.\r\n   * If not specified and there's no document title, it will consist of `tabId` and `instanceId`.\r\n   */\r\n  name?: string\r\n  /**\r\n   * action creators functions to be available in the Dispatcher.\r\n   */\r\n  actionCreators?: ActionCreator<any>[] | { [key: string]: ActionCreator<any> }\r\n  /**\r\n   * if more than one action is dispatched in the indicated interval, all new actions will be collected and sent at once.\r\n   * It is the joint between performance and speed. When set to `0`, all actions will be sent instantly.\r\n   * Set it to a higher value when experiencing perf issues (also `maxAge` to a lower value).\r\n   *\r\n   * @default 500 ms.\r\n   */\r\n  latency?: number\r\n  /**\r\n   * (> 1) - maximum allowed actions to be stored in the history tree. The oldest actions are removed once maxAge is reached. It's critical for performance.\r\n   *\r\n   * @default 50\r\n   */\r\n  maxAge?: number\r\n  /**\r\n   * Customizes how actions and state are serialized and deserialized. Can be a boolean or object. If given a boolean, the behavior is the same as if you\r\n   * were to pass an object and specify `options` as a boolean. Giving an object allows fine-grained customization using the `replacer` and `reviver`\r\n   * functions.\r\n   */\r\n  serialize?:\r\n    | boolean\r\n    | {\r\n        /**\r\n         * - `undefined` - will use regular `JSON.stringify` to send data (it's the fast mode).\r\n         * - `false` - will handle also circular references.\r\n         * - `true` - will handle also date, regex, undefined, error objects, symbols, maps, sets and functions.\r\n         * - object, which contains `date`, `regex`, `undefined`, `error`, `symbol`, `map`, `set` and `function` keys.\r\n         *   For each of them you can indicate if to include (by setting as `true`).\r\n         *   For `function` key you can also specify a custom function which handles serialization.\r\n         *   See [`jsan`](https://github.com/kolodny/jsan) for more details.\r\n         */\r\n        options?:\r\n          | undefined\r\n          | boolean\r\n          | {\r\n              date?: true\r\n              regex?: true\r\n              undefined?: true\r\n              error?: true\r\n              symbol?: true\r\n              map?: true\r\n              set?: true\r\n              function?: true | ((fn: (...args: any[]) => any) => string)\r\n            }\r\n        /**\r\n         * [JSON replacer function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#The_replacer_parameter) used for both actions and states stringify.\r\n         * In addition, you can specify a data type by adding a [`__serializedType__`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/helpers/index.js#L4)\r\n         * key. So you can deserialize it back while importing or persisting data.\r\n         * Moreover, it will also [show a nice preview showing the provided custom type](https://cloud.githubusercontent.com/assets/7957859/21814330/a17d556a-d761-11e6-85ef-159dd12f36c5.png):\r\n         */\r\n        replacer?: (key: string, value: unknown) => any\r\n        /**\r\n         * [JSON `reviver` function](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Using_the_reviver_parameter)\r\n         * used for parsing the imported actions and states. See [`remotedev-serialize`](https://github.com/zalmoxisus/remotedev-serialize/blob/master/immutable/serialize.js#L8-L41)\r\n         * as an example on how to serialize special data types and get them back.\r\n         */\r\n        reviver?: (key: string, value: unknown) => any\r\n        /**\r\n         * Automatically serialize/deserialize immutablejs via [remotedev-serialize](https://github.com/zalmoxisus/remotedev-serialize).\r\n         * Just pass the Immutable library. It will support all ImmutableJS structures. You can even export them into a file and get them back.\r\n         * The only exception is `Record` class, for which you should pass this in addition the references to your classes in `refs`.\r\n         */\r\n        immutable?: any\r\n        /**\r\n         * ImmutableJS `Record` classes used to make possible restore its instances back when importing, persisting...\r\n         */\r\n        refs?: any\r\n      }\r\n  /**\r\n   * function which takes `action` object and id number as arguments, and should return `action` object back.\r\n   */\r\n  actionSanitizer?: <A extends Action>(action: A, id: number) => A\r\n  /**\r\n   * function which takes `state` object and index as arguments, and should return `state` object back.\r\n   */\r\n  stateSanitizer?: <S>(state: S, index: number) => S\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsDenylist instead.\r\n   */\r\n  actionsBlacklist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsWhitelist` specified, `actionsBlacklist` is ignored.\r\n   * @deprecated Use actionsAllowlist instead.\r\n   */\r\n  actionsWhitelist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsDenylist?: string | string[]\r\n  /**\r\n   * *string or array of strings as regex* - actions types to be hidden / shown in the monitors (while passed to the reducers).\r\n   * If `actionsAllowlist` specified, `actionsDenylist` is ignored.\r\n   */\r\n  actionsAllowlist?: string | string[]\r\n  /**\r\n   * called for every action before sending, takes `state` and `action` object, and returns `true` in case it allows sending the current data to the monitor.\r\n   * Use it as a more advanced version of `actionsDenylist`/`actionsAllowlist` parameters.\r\n   */\r\n  predicate?: <S, A extends Action>(state: S, action: A) => boolean\r\n  /**\r\n   * if specified as `false`, it will not record the changes till clicking on `Start recording` button.\r\n   * Available only for Redux enhancer, for others use `autoPause`.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldRecordChanges?: boolean\r\n  /**\r\n   * if specified, whenever clicking on `Pause recording` button and there are actions in the history log, will add this action type.\r\n   * If not specified, will commit when paused. Available only for Redux enhancer.\r\n   *\r\n   * @default \"@@PAUSED\"\"\r\n   */\r\n  pauseActionType?: string\r\n  /**\r\n   * auto pauses when the extension’s window is not opened, and so has zero impact on your app when not in use.\r\n   * Not available for Redux enhancer (as it already does it but storing the data to be sent).\r\n   *\r\n   * @default false\r\n   */\r\n  autoPause?: boolean\r\n  /**\r\n   * if specified as `true`, it will not allow any non-monitor actions to be dispatched till clicking on `Unlock changes` button.\r\n   * Available only for Redux enhancer.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldStartLocked?: boolean\r\n  /**\r\n   * if set to `false`, will not recompute the states on hot reloading (or on replacing the reducers). Available only for Redux enhancer.\r\n   *\r\n   * @default true\r\n   */\r\n  shouldHotReload?: boolean\r\n  /**\r\n   * if specified as `true`, whenever there's an exception in reducers, the monitors will show the error message, and next actions will not be dispatched.\r\n   *\r\n   * @default false\r\n   */\r\n  shouldCatchErrors?: boolean\r\n  /**\r\n   * If you want to restrict the extension, specify the features you allow.\r\n   * If not specified, all of the features are enabled. When set as an object, only those included as `true` will be allowed.\r\n   * Note that except `true`/`false`, `import` and `export` can be set as `custom` (which is by default for Redux enhancer), meaning that the importing/exporting occurs on the client side.\r\n   * Otherwise, you'll get/set the data right from the monitor part.\r\n   */\r\n  features?: {\r\n    /**\r\n     * start/pause recording of dispatched actions\r\n     */\r\n    pause?: boolean\r\n    /**\r\n     * lock/unlock dispatching actions and side effects\r\n     */\r\n    lock?: boolean\r\n    /**\r\n     * persist states on page reloading\r\n     */\r\n    persist?: boolean\r\n    /**\r\n     * export history of actions in a file\r\n     */\r\n    export?: boolean | 'custom'\r\n    /**\r\n     * import history of actions from a file\r\n     */\r\n    import?: boolean | 'custom'\r\n    /**\r\n     * jump back and forth (time travelling)\r\n     */\r\n    jump?: boolean\r\n    /**\r\n     * skip (cancel) actions\r\n     */\r\n    skip?: boolean\r\n    /**\r\n     * drag and drop actions in the history list\r\n     */\r\n    reorder?: boolean\r\n    /**\r\n     * dispatch custom actions or action creators\r\n     */\r\n    dispatch?: boolean\r\n    /**\r\n     * generate tests for the selected actions\r\n     */\r\n    test?: boolean\r\n  }\r\n  /**\r\n   * Set to true or a stacktrace-returning function to record call stack traces for dispatched actions.\r\n   * Defaults to false.\r\n   */\r\n  trace?: boolean | (<A extends Action>(action: A) => string)\r\n  /**\r\n   * The maximum number of stack trace entries to record per action. Defaults to 10.\r\n   */\r\n  traceLimit?: number\r\n}\r\n\r\ntype Compose = typeof compose\r\n\r\ninterface ComposeWithDevTools {\r\n  (options: DevToolsEnhancerOptions): Compose\r\n  <StoreExt>(...funcs: StoreEnhancer<StoreExt>[]): StoreEnhancer<StoreExt>\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const composeWithDevTools: ComposeWithDevTools =\r\n  typeof window !== 'undefined' &&\r\n  (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\r\n    : function () {\r\n        if (arguments.length === 0) return undefined\r\n        if (typeof arguments[0] === 'object') return compose\r\n        return compose.apply(null, arguments as any as Function[])\r\n      }\r\n\r\n/**\r\n * @public\r\n */\r\nexport const devToolsEnhancer: {\r\n  (options: DevToolsEnhancerOptions): StoreEnhancer<any>\r\n} =\r\n  typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    ? (window as any).__REDUX_DEVTOOLS_EXTENSION__\r\n    : function () {\r\n        return function (noop) {\r\n          return noop\r\n        }\r\n      }\r\n", "/**\r\n * Returns true if the passed value is \"plain\" object, i.e. an object whose\r\n * prototype is the root `Object.prototype`. This includes objects created\r\n * using object literals, but not for instance for class instances.\r\n *\r\n * @param {any} value The value to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n *\r\n * @public\r\n */\r\nexport default function isPlainObject(value: unknown): value is object {\r\n  if (typeof value !== 'object' || value === null) return false\r\n\r\n  let proto = Object.getPrototypeOf(value)\r\n  if (proto === null) return true\r\n\r\n  let baseProto = proto\r\n  while (Object.getPrototypeOf(baseProto) !== null) {\r\n    baseProto = Object.getPrototypeOf(baseProto)\r\n  }\r\n\r\n  return proto === baseProto\r\n}\r\n", "import type { Middleware, AnyAction } from 'redux'\r\nimport type { ThunkMiddleware } from 'redux-thunk'\r\nimport thunkMiddleware from 'redux-thunk'\r\nimport type { ActionCreatorInvariantMiddlewareOptions } from './actionCreatorInvariantMiddleware'\r\nimport { createActionCreatorInvariantMiddleware } from './actionCreatorInvariantMiddleware'\r\nimport type { ImmutableStateInvariantMiddlewareOptions } from './immutableStateInvariantMiddleware'\r\n/* PROD_START_REMOVE_UMD */\r\nimport { createImmutableStateInvariantMiddleware } from './immutableStateInvariantMiddleware'\r\n/* PROD_STOP_REMOVE_UMD */\r\n\r\nimport type { SerializableStateInvariantMiddlewareOptions } from './serializableStateInvariantMiddleware'\r\nimport { createSerializableStateInvariantMiddleware } from './serializableStateInvariantMiddleware'\r\nimport type { ExcludeFromTuple } from './tsHelpers'\r\nimport { MiddlewareArray } from './utils'\r\n\r\nfunction isBoolean(x: any): x is boolean {\r\n  return typeof x === 'boolean'\r\n}\r\n\r\ninterface ThunkOptions<E = any> {\r\n  extraArgument: E\r\n}\r\n\r\ninterface GetDefaultMiddlewareOptions {\r\n  thunk?: boolean | ThunkOptions\r\n  immutableCheck?: boolean | ImmutableStateInvariantMiddlewareOptions\r\n  serializableCheck?: boolean | SerializableStateInvariantMiddlewareOptions\r\n  actionCreatorCheck?: boolean | ActionCreatorInvariantMiddlewareOptions\r\n}\r\n\r\nexport type ThunkMiddlewareFor<\r\n  S,\r\n  O extends GetDefaultMiddlewareOptions = {}\r\n> = O extends {\r\n  thunk: false\r\n}\r\n  ? never\r\n  : O extends { thunk: { extraArgument: infer E } }\r\n  ? ThunkMiddleware<S, AnyAction, E>\r\n  : ThunkMiddleware<S, AnyAction>\r\n\r\nexport type CurriedGetDefaultMiddleware<S = any> = <\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options?: O\r\n) => MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>>\r\n\r\nexport function curryGetDefaultMiddleware<\r\n  S = any\r\n>(): CurriedGetDefaultMiddleware<S> {\r\n  return function curriedGetDefaultMiddleware(options) {\r\n    return getDefaultMiddleware(options)\r\n  }\r\n}\r\n\r\n/**\r\n * Returns any array containing the default middleware installed by\r\n * `configureStore()`. Useful if you want to configure your store with a custom\r\n * `middleware` array but still keep the default set.\r\n *\r\n * @return The default middleware used by `configureStore()`.\r\n *\r\n * @public\r\n *\r\n * @deprecated Prefer to use the callback notation for the `middleware` option in `configureStore`\r\n * to access a pre-typed `getDefaultMiddleware` instead.\r\n */\r\nexport function getDefaultMiddleware<\r\n  S = any,\r\n  O extends Partial<GetDefaultMiddlewareOptions> = {\r\n    thunk: true\r\n    immutableCheck: true\r\n    serializableCheck: true\r\n    actionCreatorCheck: true\r\n  }\r\n>(\r\n  options: O = {} as O\r\n): MiddlewareArray<ExcludeFromTuple<[ThunkMiddlewareFor<S, O>], never>> {\r\n  const {\r\n    thunk = true,\r\n    immutableCheck = true,\r\n    serializableCheck = true,\r\n    actionCreatorCheck = true,\r\n  } = options\r\n\r\n  let middlewareArray = new MiddlewareArray<Middleware[]>()\r\n\r\n  if (thunk) {\r\n    if (isBoolean(thunk)) {\r\n      middlewareArray.push(thunkMiddleware)\r\n    } else {\r\n      middlewareArray.push(\r\n        thunkMiddleware.withExtraArgument(thunk.extraArgument)\r\n      )\r\n    }\r\n  }\r\n\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (immutableCheck) {\r\n      /* PROD_START_REMOVE_UMD */\r\n      let immutableOptions: ImmutableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(immutableCheck)) {\r\n        immutableOptions = immutableCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createImmutableStateInvariantMiddleware(immutableOptions)\r\n      )\r\n      /* PROD_STOP_REMOVE_UMD */\r\n    }\r\n\r\n    if (serializableCheck) {\r\n      let serializableOptions: SerializableStateInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(serializableCheck)) {\r\n        serializableOptions = serializableCheck\r\n      }\r\n\r\n      middlewareArray.push(\r\n        createSerializableStateInvariantMiddleware(serializableOptions)\r\n      )\r\n    }\r\n    if (actionCreatorCheck) {\r\n      let actionCreatorOptions: ActionCreatorInvariantMiddlewareOptions = {}\r\n\r\n      if (!isBoolean(actionCreatorCheck)) {\r\n        actionCreatorOptions = actionCreatorCheck\r\n      }\r\n\r\n      middlewareArray.unshift(\r\n        createActionCreatorInvariantMiddleware(actionCreatorOptions)\r\n      )\r\n    }\r\n  }\r\n\r\n  return middlewareArray as any\r\n}\r\n", "import type { Middleware, StoreEnhancer } from 'redux'\r\nimport type { EnhancerArray, MiddlewareArray } from './utils'\r\n\r\n/**\r\n * return True if T is `any`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsAny<T, True, False = never> =\r\n  // test if we are going the left AND right path in the condition\r\n  true | false extends (T extends never ? true : false) ? True : False\r\n\r\n/**\r\n * return True if T is `unknown`, otherwise return False\r\n * taken from https://github.com/joonhocho/tsdef\r\n *\r\n * @internal\r\n */\r\nexport type IsUnknown<T, True, False = never> = unknown extends T\r\n  ? IsAny<T, False, True>\r\n  : False\r\n\r\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P]\r\n  ? True\r\n  : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsEmptyObj<T, True, False = never> = T extends any\r\n  ? keyof T extends never\r\n    ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>>\r\n    : False\r\n  : never\r\n\r\n/**\r\n * returns True if TS version is above 3.5, False if below.\r\n * uses feature detection to detect TS version >= 3.5\r\n * * versions below 3.5 will return `{}` for unresolvable interference\r\n * * versions above will return `unknown`\r\n *\r\n * @internal\r\n */\r\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<\r\n  ReturnType<<T>() => T>,\r\n  0,\r\n  1\r\n>]\r\n\r\n/**\r\n * @internal\r\n */\r\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<\r\n  IsUnknown<T, True, False>,\r\n  IsEmptyObj<T, True, IsUnknown<T, True, False>>\r\n>\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\n// Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\r\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [\r\n  infer Head,\r\n  ...infer Tail\r\n]\r\n  ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]>\r\n  : Acc\r\n\r\ntype ExtractDispatchFromMiddlewareTuple<\r\n  MiddlewareTuple extends any[],\r\n  Acc extends {}\r\n> = MiddlewareTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractDispatchFromMiddlewareTuple<\r\n      Tail,\r\n      Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractDispatchExtensions<M> = M extends MiddlewareArray<\r\n  infer MiddlewareTuple\r\n>\r\n  ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}>\r\n  : M extends ReadonlyArray<Middleware>\r\n  ? ExtractDispatchFromMiddlewareTuple<[...M], {}>\r\n  : never\r\n\r\ntype ExtractStoreExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStoreExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<infer Ext>\r\n        ? Ext extends {}\r\n          ? IsAny<Ext, {}, Ext>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\ntype ExtractStateExtensionsFromEnhancerTuple<\r\n  EnhancerTuple extends any[],\r\n  Acc extends {}\r\n> = EnhancerTuple extends [infer Head, ...infer Tail]\r\n  ? ExtractStateExtensionsFromEnhancerTuple<\r\n      Tail,\r\n      Acc &\r\n        (Head extends StoreEnhancer<any, infer StateExt>\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {})\r\n    >\r\n  : Acc\r\n\r\nexport type ExtractStateExtensions<E> = E extends EnhancerArray<\r\n  infer EnhancerTuple\r\n>\r\n  ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}>\r\n  : E extends ReadonlyArray<StoreEnhancer>\r\n  ? UnionToIntersection<\r\n      E[number] extends StoreEnhancer<any, infer StateExt>\r\n        ? StateExt extends {}\r\n          ? IsAny<StateExt, {}, StateExt>\r\n          : {}\r\n        : {}\r\n    >\r\n  : never\r\n\r\n/**\r\n * Helper type. Passes T out again, but boxes it in a way that it cannot\r\n * \"widen\" the type by accident if it is a generic that should be inferred\r\n * from elsewhere.\r\n *\r\n * @internal\r\n */\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>\r\n\r\nexport interface TypeGuard<T> {\r\n  (value: any): value is T\r\n}\r\n\r\nexport interface HasMatchFunction<T> {\r\n  match: TypeGuard<T>\r\n}\r\n\r\nexport const hasMatchFunction = <T>(\r\n  v: Matcher<T>\r\n): v is HasMatchFunction<T> => {\r\n  return v && typeof (v as HasMatchFunction<T>).match === 'function'\r\n}\r\n\r\n/** @public */\r\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>\r\n\r\n/** @public */\r\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<\r\n  infer T\r\n>\r\n  ? T\r\n  : never\r\n\r\nexport type Id<T> = { [K in keyof T]: T[K] } & {}\r\n", "import type { Action } from 'redux'\r\nimport type {\r\n  IsUnknownOrNonInferrable,\r\n  IfMaybeUndefined,\r\n  IfVoid,\r\n  IsAny,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport isPlainObject from './isPlainObject'\r\n\r\n/**\r\n * An action with a string type and an associated payload. This is the\r\n * type of action returned by `createAction()` action creators.\r\n *\r\n * @template P The type of the action's payload.\r\n * @template T the type used for the action type.\r\n * @template M The type of the action's meta (optional)\r\n * @template E The type of the action's error (optional)\r\n *\r\n * @public\r\n */\r\nexport type PayloadAction<\r\n  P = void,\r\n  T extends string = string,\r\n  M = never,\r\n  E = never\r\n> = {\r\n  payload: P\r\n  type: T\r\n} & ([M] extends [never]\r\n  ? {}\r\n  : {\r\n      meta: M\r\n    }) &\r\n  ([E] extends [never]\r\n    ? {}\r\n    : {\r\n        error: E\r\n      })\r\n\r\n/**\r\n * A \"prepare\" method to be used as the second parameter of `createAction`.\r\n * Takes any number of arguments and returns a Flux Standard Action without\r\n * type (will be added later) that *must* contain a payload (might be undefined).\r\n *\r\n * @public\r\n */\r\nexport type PrepareAction<P> =\r\n  | ((...args: any[]) => { payload: P })\r\n  | ((...args: any[]) => { payload: P; meta: any })\r\n  | ((...args: any[]) => { payload: P; error: any })\r\n  | ((...args: any[]) => { payload: P; meta: any; error: any })\r\n\r\n/**\r\n * Internal version of `ActionCreatorWithPreparedPayload`. Not to be used externally.\r\n *\r\n * @internal\r\n */\r\nexport type _ActionCreatorWithPreparedPayload<\r\n  PA extends PrepareAction<any> | void,\r\n  T extends string = string\r\n> = PA extends PrepareAction<infer P>\r\n  ? ActionCreatorWithPreparedPayload<\r\n      Parameters<PA>,\r\n      P,\r\n      T,\r\n      ReturnType<PA> extends {\r\n        error: infer E\r\n      }\r\n        ? E\r\n        : never,\r\n      ReturnType<PA> extends {\r\n        meta: infer M\r\n      }\r\n        ? M\r\n        : never\r\n    >\r\n  : void\r\n\r\n/**\r\n * Basic type for all action creators.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n */\r\nexport interface BaseActionCreator<P, T extends string, M = never, E = never> {\r\n  type: T\r\n  match: (action: Action<unknown>) => action is PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator that takes multiple arguments that are passed\r\n * to a `PrepareAction` method to create the final Action.\r\n * @typeParam Args arguments for the action creator function\r\n * @typeParam P `payload` type\r\n * @typeParam T `type` name\r\n * @typeParam E optional `error` type\r\n * @typeParam M optional `meta` type\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPreparedPayload<\r\n  Args extends unknown[],\r\n  P,\r\n  T extends string = string,\r\n  E = never,\r\n  M = never\r\n> extends BaseActionCreator<P, T, M, E> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with `Args` will return\r\n   * an Action with a payload of type `P` and (depending on the `PrepareAction`\r\n   * method used) a `meta`- and `error` property of types `M` and `E` respectively.\r\n   */\r\n  (...args: Args): PayloadAction<P, T, M, E>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes an optional payload of type `P`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithOptionalPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`.\r\n   * Calling it without an argument will return a PayloadAction with a payload of `undefined`.\r\n   */\r\n  (payload?: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that takes no payload.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithoutPayload<T extends string = string>\r\n  extends BaseActionCreator<undefined, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `undefined`\r\n   */\r\n  (noArgument: void): PayloadAction<undefined, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` that requires a payload of type P.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithPayload<P, T extends string = string>\r\n  extends BaseActionCreator<P, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload of `P`\r\n   */\r\n  (payload: P): PayloadAction<P, T>\r\n}\r\n\r\n/**\r\n * An action creator of type `T` whose `payload` type could not be inferred. Accepts everything as `payload`.\r\n *\r\n * @inheritdoc {redux#ActionCreator}\r\n *\r\n * @public\r\n */\r\nexport interface ActionCreatorWithNonInferrablePayload<\r\n  T extends string = string\r\n> extends BaseActionCreator<unknown, T> {\r\n  /**\r\n   * Calling this {@link redux#ActionCreator} with an argument will\r\n   * return a {@link PayloadAction} of type `T` with a payload\r\n   * of exactly the type of the argument.\r\n   */\r\n  <PT extends unknown>(payload: PT): PayloadAction<PT, T>\r\n}\r\n\r\n/**\r\n * An action creator that produces actions with a `payload` attribute.\r\n *\r\n * @typeParam P the `payload` type\r\n * @typeParam T the `type` of the resulting action\r\n * @typeParam PA if the resulting action is preprocessed by a `prepare` method, the signature of said method.\r\n *\r\n * @public\r\n */\r\nexport type PayloadActionCreator<\r\n  P = void,\r\n  T extends string = string,\r\n  PA extends PrepareAction<P> | void = void\r\n> = IfPrepareActionMethodProvided<\r\n  PA,\r\n  _ActionCreatorWithPreparedPayload<PA, T>,\r\n  // else\r\n  IsAny<\r\n    P,\r\n    ActionCreatorWithPayload<any, T>,\r\n    IsUnknownOrNonInferrable<\r\n      P,\r\n      ActionCreatorWithNonInferrablePayload<T>,\r\n      // else\r\n      IfVoid<\r\n        P,\r\n        ActionCreatorWithoutPayload<T>,\r\n        // else\r\n        IfMaybeUndefined<\r\n          P,\r\n          ActionCreatorWithOptionalPayload<P, T>,\r\n          // else\r\n          ActionCreatorWithPayload<P, T>\r\n        >\r\n      >\r\n    >\r\n  >\r\n>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<P = void, T extends string = string>(\r\n  type: T\r\n): PayloadActionCreator<P, T>\r\n\r\n/**\r\n * A utility function to create an action creator for the given action type\r\n * string. The action creator accepts a single argument, which will be included\r\n * in the action object as a field called payload. The action creator function\r\n * will also have its toString() overridden so that it returns the action type,\r\n * allowing it to be used in reducer logic that is looking for that action type.\r\n *\r\n * @param type The action type to use for created actions.\r\n * @param prepare (optional) a method that takes any number of arguments and returns { payload } or { payload, meta }.\r\n *                If this is given, the resulting action creator will pass its arguments to this method to calculate payload & meta.\r\n *\r\n * @public\r\n */\r\nexport function createAction<\r\n  PA extends PrepareAction<any>,\r\n  T extends string = string\r\n>(\r\n  type: T,\r\n  prepareAction: PA\r\n): PayloadActionCreator<ReturnType<PA>['payload'], T, PA>\r\n\r\nexport function createAction(type: string, prepareAction?: Function): any {\r\n  function actionCreator(...args: any[]) {\r\n    if (prepareAction) {\r\n      let prepared = prepareAction(...args)\r\n      if (!prepared) {\r\n        throw new Error('prepareAction did not return an object')\r\n      }\r\n\r\n      return {\r\n        type,\r\n        payload: prepared.payload,\r\n        ...('meta' in prepared && { meta: prepared.meta }),\r\n        ...('error' in prepared && { error: prepared.error }),\r\n      }\r\n    }\r\n    return { type, payload: args[0] }\r\n  }\r\n\r\n  actionCreator.toString = () => `${type}`\r\n\r\n  actionCreator.type = type\r\n\r\n  actionCreator.match = (action: Action<unknown>): action is PayloadAction =>\r\n    action.type === type\r\n\r\n  return actionCreator\r\n}\r\n\r\n/**\r\n * Returns true if value is a plain object with a `type` property.\r\n */\r\nexport function isAction(action: unknown): action is Action<unknown> {\r\n  return isPlainObject(action) && 'type' in action\r\n}\r\n\r\n/**\r\n * Returns true if value is an RTK-like action creator, with a static type property and match method.\r\n */\r\nexport function isActionCreator(\r\n  action: unknown\r\n): action is BaseActionCreator<unknown, string> & Function {\r\n  return (\r\n    typeof action === 'function' &&\r\n    'type' in action &&\r\n    // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\r\n    hasMatchFunction(action as any)\r\n  )\r\n}\r\n\r\n/**\r\n * Returns true if value is an action with a string type and valid Flux Standard Action keys.\r\n */\r\nexport function isFSA(action: unknown): action is {\r\n  type: string\r\n  payload?: unknown\r\n  error?: unknown\r\n  meta?: unknown\r\n} {\r\n  return (\r\n    isAction(action) &&\r\n    typeof action.type === 'string' &&\r\n    Object.keys(action).every(isValidKey)\r\n  )\r\n}\r\n\r\nfunction isValidKey(key: string) {\r\n  return ['type', 'payload', 'error', 'meta'].indexOf(key) > -1\r\n}\r\n\r\n/**\r\n * Returns the action type of the actions created by the passed\r\n * `createAction()`-generated action creator (arbitrary action creators\r\n * are not supported).\r\n *\r\n * @param action The action creator whose action type to get.\r\n * @returns The action type used by the action creator.\r\n *\r\n * @public\r\n */\r\nexport function getType<T extends string>(\r\n  actionCreator: PayloadActionCreator<any, T>\r\n): T {\r\n  return `${actionCreator}` as T\r\n}\r\n\r\n// helper types for more readable typings\r\n\r\ntype IfPrepareActionMethodProvided<\r\n  PA extends PrepareAction<any> | void,\r\n  True,\r\n  False\r\n> = PA extends (...args: any[]) => any ? True : False\r\n", "import type { Middleware } from 'redux'\r\nimport { isActionCreator as isRTKAction } from './createAction'\r\n\r\nexport interface ActionCreatorInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to identify whether a value is an action creator.\r\n   * The default checks for a function with a static type property and match method.\r\n   */\r\n  isActionCreator?: (action: unknown) => action is Function & { type?: unknown }\r\n}\r\n\r\nexport function getMessage(type?: unknown) {\r\n  const splitType = type ? `${type}`.split('/') : []\r\n  const actionName = splitType[splitType.length - 1] || 'actionCreator'\r\n  return `Detected an action creator with type \"${\r\n    type || 'unknown'\r\n  }\" being dispatched. \r\nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`\r\n}\r\n\r\nexport function createActionCreatorInvariantMiddleware(\r\n  options: ActionCreatorInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const { isActionCreator = isRTKAction } = options\r\n  return () => (next) => (action) => {\r\n    if (isActionCreator(action)) {\r\n      console.warn(getMessage(action.type))\r\n    }\r\n    return next(action)\r\n  }\r\n}\r\n", "import createNextState, { isDraftable } from 'immer'\r\nimport type { Middleware, StoreEnhancer } from 'redux'\r\n\r\nexport function getTimeMeasureUtils(maxDelay: number, fnName: string) {\r\n  let elapsed = 0\r\n  return {\r\n    measureTime<T>(fn: () => T): T {\r\n      const started = Date.now()\r\n      try {\r\n        return fn()\r\n      } finally {\r\n        const finished = Date.now()\r\n        elapsed += finished - started\r\n      }\r\n    },\r\n    warnIfExceeded() {\r\n      if (elapsed > maxDelay) {\r\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \r\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\r\nIt is disabled in production builds, so you don't need to worry about that.`)\r\n      }\r\n    },\r\n  }\r\n}\r\n\r\nexport function delay(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms))\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class MiddlewareArray<\r\n  Middlewares extends Middleware<any, any>[]\r\n> extends Array<Middlewares[number]> {\r\n  constructor(...items: Middlewares)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, MiddlewareArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return MiddlewareArray as any\r\n  }\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n\r\n  concat<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...Middlewares, ...AdditionalMiddlewares]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend<AdditionalMiddlewares extends ReadonlyArray<Middleware<any, any>>>(\r\n    ...items: AdditionalMiddlewares\r\n  ): MiddlewareArray<[...AdditionalMiddlewares, ...Middlewares]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new MiddlewareArray(...arr[0].concat(this))\r\n    }\r\n    return new MiddlewareArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport class EnhancerArray<\r\n  Enhancers extends StoreEnhancer<any, any>[]\r\n> extends Array<Enhancers[number]> {\r\n  constructor(...items: Enhancers)\r\n  constructor(...args: any[]) {\r\n    super(...args)\r\n    Object.setPrototypeOf(this, EnhancerArray.prototype)\r\n  }\r\n\r\n  static get [Symbol.species]() {\r\n    return EnhancerArray as any\r\n  }\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n\r\n  concat<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...Enhancers, ...AdditionalEnhancers]>\r\n  concat(...arr: any[]) {\r\n    return super.concat.apply(this, arr)\r\n  }\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend<AdditionalEnhancers extends ReadonlyArray<StoreEnhancer<any, any>>>(\r\n    ...items: AdditionalEnhancers\r\n  ): EnhancerArray<[...AdditionalEnhancers, ...Enhancers]>\r\n\r\n  prepend(...arr: any[]) {\r\n    if (arr.length === 1 && Array.isArray(arr[0])) {\r\n      return new EnhancerArray(...arr[0].concat(this))\r\n    }\r\n    return new EnhancerArray(...arr.concat(this))\r\n  }\r\n}\r\n\r\nexport function freezeDraftable<T>(val: T) {\r\n  return isDraftable(val) ? createNextState(val, () => {}) : val\r\n}\r\n", "import type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\ntype EntryProcessor = (key: string, value: any) => any\r\n\r\nconst isProduction: boolean = process.env.NODE_ENV === 'production'\r\nconst prefix: string = 'Invariant failed'\r\n\r\n// Throw an error if the condition fails\r\n// Strip out error messages for production\r\n// > Not providing an inline default argument for message as the result is smaller\r\nfunction invariant(condition: any, message?: string) {\r\n  if (condition) {\r\n    return\r\n  }\r\n  // Condition not passed\r\n\r\n  // In production we strip the message but still throw\r\n  if (isProduction) {\r\n    throw new Error(prefix)\r\n  }\r\n\r\n  // When not in production we allow the message to pass through\r\n  // *This block will be removed in production builds*\r\n  throw new Error(`${prefix}: ${message || ''}`)\r\n}\r\n\r\nfunction stringify(\r\n  obj: any,\r\n  serializer?: EntryProcessor,\r\n  indent?: string | number,\r\n  decycler?: EntryProcessor\r\n): string {\r\n  return JSON.stringify(obj, getSerialize(serializer, decycler), indent)\r\n}\r\n\r\nfunction getSerialize(\r\n  serializer?: EntryProcessor,\r\n  decycler?: EntryProcessor\r\n): EntryProcessor {\r\n  let stack: any[] = [],\r\n    keys: any[] = []\r\n\r\n  if (!decycler)\r\n    decycler = function (_: string, value: any) {\r\n      if (stack[0] === value) return '[Circular ~]'\r\n      return (\r\n        '[Circular ~.' + keys.slice(0, stack.indexOf(value)).join('.') + ']'\r\n      )\r\n    }\r\n\r\n  return function (this: any, key: string, value: any) {\r\n    if (stack.length > 0) {\r\n      var thisPos = stack.indexOf(this)\r\n      ~thisPos ? stack.splice(thisPos + 1) : stack.push(this)\r\n      ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key)\r\n      if (~stack.indexOf(value)) value = decycler!.call(this, key, value)\r\n    } else stack.push(value)\r\n\r\n    return serializer == null ? value : serializer.call(this, key, value)\r\n  }\r\n}\r\n\r\n/**\r\n * The default `isImmutable` function.\r\n *\r\n * @public\r\n */\r\nexport function isImmutableDefault(value: unknown): boolean {\r\n  return typeof value !== 'object' || value == null || Object.isFrozen(value)\r\n}\r\n\r\nexport function trackForMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths | undefined,\r\n  obj: any\r\n) {\r\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj)\r\n  return {\r\n    detectMutations() {\r\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj)\r\n    },\r\n  }\r\n}\r\n\r\ninterface TrackedProperty {\r\n  value: any\r\n  children: Record<string, any>\r\n}\r\n\r\nfunction trackProperties(\r\n  isImmutable: IsImmutableFunc,\r\n  ignorePaths: IgnorePaths = [],\r\n  obj: Record<string, any>,\r\n  path: string = '',\r\n  checkedObjects: Set<Record<string, any>> = new Set()\r\n) {\r\n  const tracked: Partial<TrackedProperty> = { value: obj }\r\n\r\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n    checkedObjects.add(obj);\r\n    tracked.children = {}\r\n\r\n    for (const key in obj) {\r\n      const childPath = path ? path + '.' + key : key\r\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n        continue\r\n      }\r\n\r\n      tracked.children[key] = trackProperties(\r\n        isImmutable,\r\n        ignorePaths,\r\n        obj[key],\r\n        childPath\r\n      )\r\n    }\r\n  }\r\n  return tracked as TrackedProperty\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\nfunction detectMutations(\r\n  isImmutable: IsImmutableFunc,\r\n  ignoredPaths: IgnorePaths = [],\r\n  trackedProperty: TrackedProperty,\r\n  obj: any,\r\n  sameParentRef: boolean = false,\r\n  path: string = ''\r\n): { wasMutated: boolean; path?: string } {\r\n  const prevObj = trackedProperty ? trackedProperty.value : undefined\r\n\r\n  const sameRef = prevObj === obj\r\n\r\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n    return { wasMutated: true, path }\r\n  }\r\n\r\n  if (isImmutable(prevObj) || isImmutable(obj)) {\r\n    return { wasMutated: false }\r\n  }\r\n\r\n  // Gather all keys from prev (tracked) and after objs\r\n  const keysToDetect: Record<string, boolean> = {}\r\n  for (let key in trackedProperty.children) {\r\n    keysToDetect[key] = true\r\n  }\r\n  for (let key in obj) {\r\n    keysToDetect[key] = true\r\n  }\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (let key in keysToDetect) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    const result = detectMutations(\r\n      isImmutable,\r\n      ignoredPaths,\r\n      trackedProperty.children[key],\r\n      obj[key],\r\n      sameRef,\r\n      nestedPath\r\n    )\r\n\r\n    if (result.wasMutated) {\r\n      return result\r\n    }\r\n  }\r\n  return { wasMutated: false }\r\n}\r\n\r\ntype IsImmutableFunc = (value: any) => boolean\r\n\r\n/**\r\n * Options for `createImmutableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface ImmutableStateInvariantMiddlewareOptions {\r\n  /**\r\n    Callback function to check if a value is considered to be immutable.\r\n    This function is applied recursively to every value contained in the state.\r\n    The default implementation will return true for primitive types \r\n    (like numbers, strings, booleans, null and undefined).\r\n   */\r\n  isImmutable?: IsImmutableFunc\r\n  /** \r\n    An array of dot-separated path strings that match named nodes from \r\n    the root state to ignore when checking for immutability.\r\n    Defaults to undefined\r\n   */\r\n  ignoredPaths?: IgnorePaths\r\n  /** Print a warning if checks take longer than N ms. Default: 32ms */\r\n  warnAfter?: number\r\n  // @deprecated. Use ignoredPaths\r\n  ignore?: string[]\r\n}\r\n\r\n/**\r\n * Creates a middleware that checks whether any state was mutated in between\r\n * dispatches or during a dispatch. If any mutations are detected, an error is\r\n * thrown.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createImmutableStateInvariantMiddleware(\r\n  options: ImmutableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n\r\n  let {\r\n    isImmutable = isImmutableDefault,\r\n    ignoredPaths,\r\n    warnAfter = 32,\r\n    ignore,\r\n  } = options\r\n\r\n  // Alias ignore->ignoredPaths, but prefer ignoredPaths if present\r\n  ignoredPaths = ignoredPaths || ignore\r\n\r\n  const track = trackForMutations.bind(null, isImmutable, ignoredPaths)\r\n\r\n  return ({ getState }) => {\r\n    let state = getState()\r\n    let tracker = track(state)\r\n\r\n    let result\r\n    return (next) => (action) => {\r\n      const measureUtils = getTimeMeasureUtils(\r\n        warnAfter,\r\n        'ImmutableStateInvariantMiddleware'\r\n      )\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        invariant(\r\n          !result.wasMutated,\r\n          `A state mutation was detected between dispatches, in the path '${\r\n            result.path || ''\r\n          }'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n        )\r\n      })\r\n\r\n      const dispatchedAction = next(action)\r\n\r\n      measureUtils.measureTime(() => {\r\n        state = getState()\r\n\r\n        result = tracker.detectMutations()\r\n        // Track before potentially not meeting the invariant\r\n        tracker = track(state)\r\n\r\n        result.wasMutated &&\r\n          invariant(\r\n            !result.wasMutated,\r\n            `A state mutation was detected inside a dispatch, in the path: ${\r\n              result.path || ''\r\n            }. Take a look at the reducer(s) handling the action ${stringify(\r\n              action\r\n            )}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`\r\n          )\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n\r\n      return dispatchedAction\r\n    }\r\n  }\r\n}\r\n", "import isPlainObject from './isPlainObject'\r\nimport type { Middleware } from 'redux'\r\nimport { getTimeMeasureUtils } from './utils'\r\n\r\n/**\r\n * Returns true if the passed value is \"plain\", i.e. a value that is either\r\n * directly JSON-serializable (boolean, number, string, array, plain object)\r\n * or `undefined`.\r\n *\r\n * @param val The value to check.\r\n *\r\n * @public\r\n */\r\nexport function isPlain(val: any) {\r\n  const type = typeof val\r\n  return (\r\n    val == null ||\r\n    type === 'string' ||\r\n    type === 'boolean' ||\r\n    type === 'number' ||\r\n    Array.isArray(val) ||\r\n    isPlainObject(val)\r\n  )\r\n}\r\n\r\ninterface NonSerializableValue {\r\n  keyPath: string\r\n  value: unknown\r\n}\r\n\r\ntype IgnorePaths = readonly (string | RegExp)[]\r\n\r\n/**\r\n * @public\r\n */\r\nexport function findNonSerializableValue(\r\n  value: unknown,\r\n  path: string = '',\r\n  isSerializable: (value: unknown) => boolean = isPlain,\r\n  getEntries?: (value: unknown) => [string, any][],\r\n  ignoredPaths: IgnorePaths = [],\r\n  cache?: WeakSet<object>\r\n): NonSerializableValue | false {\r\n  let foundNestedSerializable: NonSerializableValue | false\r\n\r\n  if (!isSerializable(value)) {\r\n    return {\r\n      keyPath: path || '<root>',\r\n      value: value,\r\n    }\r\n  }\r\n\r\n  if (typeof value !== 'object' || value === null) {\r\n    return false\r\n  }\r\n\r\n  if (cache?.has(value)) return false\r\n\r\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value)\r\n\r\n  const hasIgnoredPaths = ignoredPaths.length > 0\r\n\r\n  for (const [key, nestedValue] of entries) {\r\n    const nestedPath = path ? path + '.' + key : key\r\n\r\n    if (hasIgnoredPaths) {\r\n      const hasMatches = ignoredPaths.some((ignored) => {\r\n        if (ignored instanceof RegExp) {\r\n          return ignored.test(nestedPath)\r\n        }\r\n        return nestedPath === ignored\r\n      })\r\n      if (hasMatches) {\r\n        continue\r\n      }\r\n    }\r\n\r\n    if (!isSerializable(nestedValue)) {\r\n      return {\r\n        keyPath: nestedPath,\r\n        value: nestedValue,\r\n      }\r\n    }\r\n\r\n    if (typeof nestedValue === 'object') {\r\n      foundNestedSerializable = findNonSerializableValue(\r\n        nestedValue,\r\n        nestedPath,\r\n        isSerializable,\r\n        getEntries,\r\n        ignoredPaths,\r\n        cache\r\n      )\r\n\r\n      if (foundNestedSerializable) {\r\n        return foundNestedSerializable\r\n      }\r\n    }\r\n  }\r\n\r\n  if (cache && isNestedFrozen(value)) cache.add(value)\r\n\r\n  return false\r\n}\r\n\r\nexport function isNestedFrozen(value: object) {\r\n  if (!Object.isFrozen(value)) return false\r\n\r\n  for (const nestedValue of Object.values(value)) {\r\n    if (typeof nestedValue !== 'object' || nestedValue === null) continue\r\n\r\n    if (!isNestedFrozen(nestedValue)) return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\n/**\r\n * Options for `createSerializableStateInvariantMiddleware()`.\r\n *\r\n * @public\r\n */\r\nexport interface SerializableStateInvariantMiddlewareOptions {\r\n  /**\r\n   * The function to check if a value is considered serializable. This\r\n   * function is applied recursively to every value contained in the\r\n   * state. Defaults to `isPlain()`.\r\n   */\r\n  isSerializable?: (value: any) => boolean\r\n  /**\r\n   * The function that will be used to retrieve entries from each\r\n   * value.  If unspecified, `Object.entries` will be used. Defaults\r\n   * to `undefined`.\r\n   */\r\n  getEntries?: (value: any) => [string, any][]\r\n\r\n  /**\r\n   * An array of action types to ignore when checking for serializability.\r\n   * Defaults to []\r\n   */\r\n  ignoredActions?: string[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to\r\n   * ['meta.arg', 'meta.baseQueryMeta']\r\n   */\r\n  ignoredActionPaths?: (string | RegExp)[]\r\n\r\n  /**\r\n   * An array of dot-separated path strings or regular expressions to ignore\r\n   * when checking for serializability, Defaults to []\r\n   */\r\n  ignoredPaths?: (string | RegExp)[]\r\n  /**\r\n   * Execution time warning threshold. If the middleware takes longer\r\n   * than `warnAfter` ms, a warning will be displayed in the console.\r\n   * Defaults to 32ms.\r\n   */\r\n  warnAfter?: number\r\n\r\n  /**\r\n   * Opt out of checking state. When set to `true`, other state-related params will be ignored.\r\n   */\r\n  ignoreState?: boolean\r\n\r\n  /**\r\n   * Opt out of checking actions. When set to `true`, other action-related params will be ignored.\r\n   */\r\n  ignoreActions?: boolean\r\n\r\n  /**\r\n   * Opt out of caching the results. The cache uses a WeakSet and speeds up repeated checking processes.\r\n   * The cache is automatically disabled if no browser support for WeakSet is present.\r\n   */\r\n  disableCache?: boolean\r\n}\r\n\r\n/**\r\n * Creates a middleware that, after every state change, checks if the new\r\n * state is serializable. If a non-serializable value is found within the\r\n * state, an error is printed to the console.\r\n *\r\n * @param options Middleware options.\r\n *\r\n * @public\r\n */\r\nexport function createSerializableStateInvariantMiddleware(\r\n  options: SerializableStateInvariantMiddlewareOptions = {}\r\n): Middleware {\r\n  if (process.env.NODE_ENV === 'production') {\r\n    return () => (next) => (action) => next(action)\r\n  }\r\n  const {\r\n    isSerializable = isPlain,\r\n    getEntries,\r\n    ignoredActions = [],\r\n    ignoredActionPaths = ['meta.arg', 'meta.baseQueryMeta'],\r\n    ignoredPaths = [],\r\n    warnAfter = 32,\r\n    ignoreState = false,\r\n    ignoreActions = false,\r\n    disableCache = false,\r\n  } = options\r\n\r\n  const cache: WeakSet<object> | undefined =\r\n    !disableCache && WeakSet ? new WeakSet() : undefined\r\n\r\n  return (storeAPI) => (next) => (action) => {\r\n    const result = next(action)\r\n\r\n    const measureUtils = getTimeMeasureUtils(\r\n      warnAfter,\r\n      'SerializableStateInvariantMiddleware'\r\n    )\r\n\r\n    if (\r\n      !ignoreActions &&\r\n      !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)\r\n    ) {\r\n      measureUtils.measureTime(() => {\r\n        const foundActionNonSerializableValue = findNonSerializableValue(\r\n          action,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredActionPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundActionNonSerializableValue) {\r\n          const { keyPath, value } = foundActionNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            '\\nTake a look at the logic that dispatched this action: ',\r\n            action,\r\n            '\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)',\r\n            '\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)'\r\n          )\r\n        }\r\n      })\r\n    }\r\n\r\n    if (!ignoreState) {\r\n      measureUtils.measureTime(() => {\r\n        const state = storeAPI.getState()\r\n\r\n        const foundStateNonSerializableValue = findNonSerializableValue(\r\n          state,\r\n          '',\r\n          isSerializable,\r\n          getEntries,\r\n          ignoredPaths,\r\n          cache\r\n        )\r\n\r\n        if (foundStateNonSerializableValue) {\r\n          const { keyPath, value } = foundStateNonSerializableValue\r\n\r\n          console.error(\r\n            `A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`,\r\n            value,\r\n            `\r\nTake a look at the reducer(s) handling this action type: ${action.type}.\r\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`\r\n          )\r\n        }\r\n      })\r\n\r\n      measureUtils.warnIfExceeded()\r\n    }\r\n\r\n    return result\r\n  }\r\n}\r\n", "import type { Draft } from 'immer'\r\nimport createNextState, { isDraft, isDraftable } from 'immer'\r\nimport type { AnyAction, Action, Reducer } from 'redux'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\n/**\r\n * Defines a mapping from action types to corresponding action object shapes.\r\n *\r\n * @deprecated This should not be used manually - it is only used for internal\r\n *             inference purposes and should not have any further value.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type Actions<T extends keyof any = string> = Record<T, Action>\r\n\r\n/**\r\n * @deprecated use `TypeGuard` instead\r\n */\r\nexport interface ActionMatcher<A extends AnyAction> {\r\n  (action: AnyAction): action is A\r\n}\r\n\r\nexport type ActionMatcherDescription<S, A extends AnyAction> = {\r\n  matcher: ActionMatcher<A>\r\n  reducer: CaseReducer<S, NoInfer<A>>\r\n}\r\n\r\nexport type ReadonlyActionMatcherDescriptionCollection<S> = ReadonlyArray<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\nexport type ActionMatcherDescriptionCollection<S> = Array<\r\n  ActionMatcherDescription<S, any>\r\n>\r\n\r\n/**\r\n * A *case reducer* is a reducer function for a specific action type. Case\r\n * reducers can be composed to full reducers using `createReducer()`.\r\n *\r\n * Unlike a normal Redux reducer, a case reducer is never called with an\r\n * `undefined` state to determine the initial state. Instead, the initial\r\n * state is explicitly specified as an argument to `createReducer()`.\r\n *\r\n * In addition, a case reducer can choose to mutate the passed-in `state`\r\n * value directly instead of returning a new state. This does not actually\r\n * cause the store state to be mutated directly; instead, thanks to\r\n * [immer](https://github.com/mweststrate/immer), the mutations are\r\n * translated to copy operations that result in a new state.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducer<S = any, A extends Action = AnyAction> = (\r\n  state: Draft<S>,\r\n  action: A\r\n) => NoInfer<S> | void | Draft<NoInfer<S>>\r\n\r\n/**\r\n * A mapping from action types to case reducers for `createReducer()`.\r\n *\r\n * @deprecated This should not be used manually - it is only used\r\n *             for internal inference purposes and using it manually\r\n *             would lead to type erasure.\r\n *             It might be removed in the future.\r\n * @public\r\n */\r\nexport type CaseReducers<S, AS extends Actions> = {\r\n  [T in keyof AS]: AS[T] extends Action ? CaseReducer<S, AS[T]> : void\r\n}\r\n\r\nexport type NotFunction<T> = T extends Function ? never : T\r\n\r\nfunction isStateFunction<S>(x: unknown): x is () => S {\r\n  return typeof x === 'function'\r\n}\r\n\r\nexport type ReducerWithInitialState<S extends NotFunction<any>> = Reducer<S> & {\r\n  getInitialState: () => S\r\n}\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * @remarks\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n *\r\n * @overloadSummary\r\n * This overload accepts a callback function that receives a `builder` object as its argument.\r\n * That builder provides `addCase`, `addMatcher` and `addDefaultCase` functions that may be\r\n * called to define what actions this reducer will handle.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param builderCallback - `(builder: Builder) => void` A callback that receives a *builder* object to define\r\n *   case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AnyAction,\r\n  PayloadAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\nconst increment = createAction<number>(\"increment\");\r\nconst decrement = createAction<number>(\"decrement\");\r\n\r\nfunction isActionWithNumberPayload(\r\n  action: AnyAction\r\n): action is PayloadAction<number> {\r\n  return typeof action.payload === \"number\";\r\n}\r\n\r\nconst reducer = createReducer(\r\n  {\r\n    counter: 0,\r\n    sumOfNumberPayloads: 0,\r\n    unhandledActions: 0,\r\n  },\r\n  (builder) => {\r\n    builder\r\n      .addCase(increment, (state, action) => {\r\n        // action is inferred correctly here\r\n        state.counter += action.payload;\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {\r\n        state.counter -= action.payload;\r\n      })\r\n      // You can apply a \"matcher function\" to incoming actions\r\n      .addMatcher(isActionWithNumberPayload, (state, action) => {})\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {});\r\n  }\r\n);\r\n```\r\n * @public\r\n */\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): ReducerWithInitialState<S>\r\n\r\n/**\r\n * A utility function that allows defining a reducer as a mapping from action\r\n * type to *case reducer* functions that handle these action types. The\r\n * reducer's initial state is passed as the first argument.\r\n *\r\n * The body of every case reducer is implicitly wrapped with a call to\r\n * `produce()` from the [immer](https://github.com/mweststrate/immer) library.\r\n * This means that rather than returning a new state object, you can also\r\n * mutate the passed-in state object directly; these mutations will then be\r\n * automatically and efficiently translated into copies, giving you both\r\n * convenience and immutability.\r\n * \r\n * @overloadSummary\r\n * This overload accepts an object where the keys are string action types, and the values\r\n * are case reducer functions to handle those action types.\r\n *\r\n * @param initialState - `State | (() => State)`: The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n * @param actionsMap - An object mapping from action types to _case reducers_, each of which handles one specific action type.\r\n * @param actionMatchers - An array of matcher definitions in the form `{matcher, reducer}`.\r\n *   All matching reducers will be executed in order, independently if a case reducer matched or not.\r\n * @param defaultCaseReducer - A \"default case\" reducer that is executed if no case reducer and no matcher\r\n *   reducer was executed for this action.\r\n *\r\n * @example\r\n```js\r\nconst counterReducer = createReducer(0, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n\r\n// Alternately, use a \"lazy initializer\" to provide the initial state\r\n// (works with either form of createReducer)\r\nconst initialState = () => 0\r\nconst counterReducer = createReducer(initialState, {\r\n  increment: (state, action) => state + action.payload,\r\n  decrement: (state, action) => state - action.payload\r\n})\r\n```\r\n \r\n * Action creators that were generated using [`createAction`](./createAction) may be used directly as the keys here, using computed property syntax:\r\n\r\n```js\r\nconst increment = createAction('increment')\r\nconst decrement = createAction('decrement')\r\n\r\nconst counterReducer = createReducer(0, {\r\n  [increment]: (state, action) => state + action.payload,\r\n  [decrement.type]: (state, action) => state - action.payload\r\n})\r\n```\r\n * @public\r\n */\r\nexport function createReducer<\r\n  S extends NotFunction<any>,\r\n  CR extends CaseReducers<S, any> = CaseReducers<S, any>\r\n>(\r\n  initialState: S | (() => S),\r\n  actionsMap: CR,\r\n  actionMatchers?: ActionMatcherDescriptionCollection<S>,\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S>\r\n\r\nexport function createReducer<S extends NotFunction<any>>(\r\n  initialState: S | (() => S),\r\n  mapOrBuilderCallback:\r\n    | CaseReducers<S, any>\r\n    | ((builder: ActionReducerMapBuilder<S>) => void),\r\n  actionMatchers: ReadonlyActionMatcherDescriptionCollection<S> = [],\r\n  defaultCaseReducer?: CaseReducer<S>\r\n): ReducerWithInitialState<S> {\r\n  if (process.env.NODE_ENV !== 'production') {\r\n    if (typeof mapOrBuilderCallback === 'object') {\r\n      if (!hasWarnedAboutObjectNotation) {\r\n        hasWarnedAboutObjectNotation = true\r\n        console.warn(\r\n          \"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\"\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] =\r\n    typeof mapOrBuilderCallback === 'function'\r\n      ? executeReducerBuilderCallback(mapOrBuilderCallback)\r\n      : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer]\r\n\r\n  // Ensure the initial state gets frozen either way (if draftable)\r\n  let getInitialState: () => S\r\n  if (isStateFunction(initialState)) {\r\n    getInitialState = () => freezeDraftable(initialState())\r\n  } else {\r\n    const frozenInitialState = freezeDraftable(initialState)\r\n    getInitialState = () => frozenInitialState\r\n  }\r\n\r\n  function reducer(state = getInitialState(), action: any): S {\r\n    let caseReducers = [\r\n      actionsMap[action.type],\r\n      ...finalActionMatchers\r\n        .filter(({ matcher }) => matcher(action))\r\n        .map(({ reducer }) => reducer),\r\n    ]\r\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\r\n      caseReducers = [finalDefaultCaseReducer]\r\n    }\r\n\r\n    return caseReducers.reduce((previousState, caseReducer): S => {\r\n      if (caseReducer) {\r\n        if (isDraft(previousState)) {\r\n          // If it's already a draft, we must already be inside a `createNextState` call,\r\n          // likely because this is being wrapped in `createReducer`, `createSlice`, or nested\r\n          // inside an existing draft. It's safe to just pass the draft to the mutator.\r\n          const draft = previousState as Draft<S> // We can assume this is already a draft\r\n          const result = caseReducer(draft, action)\r\n\r\n          if (result === undefined) {\r\n            return previousState\r\n          }\r\n\r\n          return result as S\r\n        } else if (!isDraftable(previousState)) {\r\n          // If state is not draftable (ex: a primitive, such as 0), we want to directly\r\n          // return the caseReducer func and not wrap it with produce.\r\n          const result = caseReducer(previousState as any, action)\r\n\r\n          if (result === undefined) {\r\n            if (previousState === null) {\r\n              return previousState\r\n            }\r\n            throw Error(\r\n              'A case reducer on a non-draftable value must not return undefined'\r\n            )\r\n          }\r\n\r\n          return result as S\r\n        } else {\r\n          // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n          // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n          // these two types.\r\n          return createNextState(previousState, (draft: Draft<S>) => {\r\n            return caseReducer(draft, action)\r\n          })\r\n        }\r\n      }\r\n\r\n      return previousState\r\n    }, state)\r\n  }\r\n\r\n  reducer.getInitialState = getInitialState\r\n\r\n  return reducer as ReducerWithInitialState<S>\r\n}\r\n", "import type { Action, AnyAction } from 'redux'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ActionMatcherDescriptionCollection,\r\n} from './createReducer'\r\nimport type { TypeGuard } from './tsHelpers'\r\n\r\nexport interface TypedActionCreator<Type extends string> {\r\n  (...args: any[]): Action<Type>\r\n  type: Type\r\n}\r\n\r\n/**\r\n * A builder for an action <-> reducer map.\r\n *\r\n * @public\r\n */\r\nexport interface ActionReducerMapBuilder<State> {\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<ActionCreator extends TypedActionCreator<string>>(\r\n    actionCreator: ActionCreator,\r\n    reducer: CaseReducer<State, ReturnType<ActionCreator>>\r\n  ): ActionReducerMapBuilder<State>\r\n  /**\r\n   * Adds a case reducer to handle a single exact action type.\r\n   * @remarks\r\n   * All calls to `builder.addCase` must come before any calls to `builder.addMatcher` or `builder.addDefaultCase`.\r\n   * @param actionCreator - Either a plain action type string, or an action creator generated by [`createAction`](./createAction) that can be used to determine the action type.\r\n   * @param reducer - The actual case reducer function.\r\n   */\r\n  addCase<Type extends string, A extends Action<Type>>(\r\n    type: Type,\r\n    reducer: CaseReducer<State, A>\r\n  ): ActionReducerMapBuilder<State>\r\n\r\n  /**\r\n   * Allows you to match your incoming actions against your own filter function instead of only the `action.type` property.\r\n   * @remarks\r\n   * If multiple matcher reducers match, all of them will be executed in the order\r\n   * they were defined in - even if a case reducer already matched.\r\n   * All calls to `builder.addMatcher` must come after any calls to `builder.addCase` and before any calls to `builder.addDefaultCase`.\r\n   * @param matcher - A matcher function. In TypeScript, this should be a [type predicate](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#using-type-predicates)\r\n   *   function\r\n   * @param reducer - The actual case reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport {\r\n  createAction,\r\n  createReducer,\r\n  AsyncThunk,\r\n  AnyAction,\r\n} from \"@reduxjs/toolkit\";\r\n\r\ntype GenericAsyncThunk = AsyncThunk<unknown, unknown, any>;\r\n\r\ntype PendingAction = ReturnType<GenericAsyncThunk[\"pending\"]>;\r\ntype RejectedAction = ReturnType<GenericAsyncThunk[\"rejected\"]>;\r\ntype FulfilledAction = ReturnType<GenericAsyncThunk[\"fulfilled\"]>;\r\n\r\nconst initialState: Record<string, string> = {};\r\nconst resetAction = createAction(\"reset-tracked-loading-state\");\r\n\r\nfunction isPendingAction(action: AnyAction): action is PendingAction {\r\n  return action.type.endsWith(\"/pending\");\r\n}\r\n\r\nconst reducer = createReducer(initialState, (builder) => {\r\n  builder\r\n    .addCase(resetAction, () => initialState)\r\n    // matcher can be defined outside as a type predicate function\r\n    .addMatcher(isPendingAction, (state, action) => {\r\n      state[action.meta.requestId] = \"pending\";\r\n    })\r\n    .addMatcher(\r\n      // matcher can be defined inline as a type predicate function\r\n      (action): action is RejectedAction => action.type.endsWith(\"/rejected\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"rejected\";\r\n      }\r\n    )\r\n    // matcher can just return boolean and the matcher can receive a generic argument\r\n    .addMatcher<FulfilledAction>(\r\n      (action) => action.type.endsWith(\"/fulfilled\"),\r\n      (state, action) => {\r\n        state[action.meta.requestId] = \"fulfilled\";\r\n      }\r\n    );\r\n});\r\n```\r\n   */\r\n  addMatcher<A>(\r\n    matcher: TypeGuard<A> | ((action: any) => boolean),\r\n    reducer: CaseReducer<State, A extends AnyAction ? A : A & AnyAction>\r\n  ): Omit<ActionReducerMapBuilder<State>, 'addCase'>\r\n\r\n  /**\r\n   * Adds a \"default case\" reducer that is executed if no case reducer and no matcher\r\n   * reducer was executed for this action.\r\n   * @param reducer - The fallback \"default case\" reducer function.\r\n   *\r\n   * @example\r\n```ts\r\nimport { createReducer } from '@reduxjs/toolkit'\r\nconst initialState = { otherActions: 0 }\r\nconst reducer = createReducer(initialState, builder => {\r\n  builder\r\n    // .addCase(...)\r\n    // .addMatcher(...)\r\n    .addDefaultCase((state, action) => {\r\n      state.otherActions++\r\n    })\r\n})\r\n```\r\n   */\r\n  addDefaultCase(reducer: CaseReducer<State, AnyAction>): {}\r\n}\r\n\r\nexport function executeReducerBuilderCallback<S>(\r\n  builderCallback: (builder: ActionReducerMapBuilder<S>) => void\r\n): [\r\n  CaseReducers<S, any>,\r\n  ActionMatcherDescriptionCollection<S>,\r\n  CaseReducer<S, AnyAction> | undefined\r\n] {\r\n  const actionsMap: CaseReducers<S, any> = {}\r\n  const actionMatchers: ActionMatcherDescriptionCollection<S> = []\r\n  let defaultCaseReducer: CaseReducer<S, AnyAction> | undefined\r\n  const builder = {\r\n    addCase(\r\n      typeOrActionCreator: string | TypedActionCreator<any>,\r\n      reducer: CaseReducer<S>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        /*\r\n         to keep the definition by the user in line with actual behavior,\r\n         we enforce `addCase` to always be called before calling `addMatcher`\r\n         as matching cases take precedence over matchers\r\n         */\r\n        if (actionMatchers.length > 0) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addMatcher`'\r\n          )\r\n        }\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addCase` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      const type =\r\n        typeof typeOrActionCreator === 'string'\r\n          ? typeOrActionCreator\r\n          : typeOrActionCreator.type\r\n      if (!type) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with an empty action type'\r\n        )\r\n      }\r\n      if (type in actionsMap) {\r\n        throw new Error(\r\n          '`builder.addCase` cannot be called with two reducers for the same action type'\r\n        )\r\n      }\r\n      actionsMap[type] = reducer\r\n      return builder\r\n    },\r\n    addMatcher<A>(\r\n      matcher: TypeGuard<A>,\r\n      reducer: CaseReducer<S, A extends AnyAction ? A : A & AnyAction>\r\n    ) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error(\r\n            '`builder.addMatcher` should only be called before calling `builder.addDefaultCase`'\r\n          )\r\n        }\r\n      }\r\n      actionMatchers.push({ matcher, reducer })\r\n      return builder\r\n    },\r\n    addDefaultCase(reducer: CaseReducer<S, AnyAction>) {\r\n      if (process.env.NODE_ENV !== 'production') {\r\n        if (defaultCaseReducer) {\r\n          throw new Error('`builder.addDefaultCase` can only be called once')\r\n        }\r\n      }\r\n      defaultCaseReducer = reducer\r\n      return builder\r\n    },\r\n  }\r\n  builderCallback(builder)\r\n  return [actionsMap, actionMatchers, defaultCaseReducer]\r\n}\r\n", "import type { AnyAction, Reducer } from 'redux'\r\nimport { createNextState } from '.'\r\nimport type {\r\n  ActionCreatorWithoutPayload,\r\n  PayloadAction,\r\n  PayloadActionCreator,\r\n  PrepareAction,\r\n  _ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type {\r\n  CaseReducer,\r\n  CaseReducers,\r\n  ReducerWithInitialState,\r\n} from './createReducer'\r\nimport { createReducer, NotFunction } from './createReducer'\r\nimport type { ActionReducerMapBuilder } from './mapBuilders'\r\nimport { executeReducerBuilderCallback } from './mapBuilders'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { freezeDraftable } from './utils'\r\n\r\nlet hasWarnedAboutObjectNotation = false\r\n\r\n/**\r\n * An action creator attached to a slice.\r\n *\r\n * @deprecated please use PayloadActionCreator directly\r\n *\r\n * @public\r\n */\r\nexport type SliceActionCreator<P> = PayloadActionCreator<P>\r\n\r\n/**\r\n * The return value of `createSlice`\r\n *\r\n * @public\r\n */\r\nexport interface Slice<\r\n  State = any,\r\n  CaseReducers extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice name.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The slice's reducer.\r\n   */\r\n  reducer: Reducer<State>\r\n\r\n  /**\r\n   * Action creators for the types of actions that are handled by the slice\r\n   * reducer.\r\n   */\r\n  actions: CaseReducerActions<CaseReducers, Name>\r\n\r\n  /**\r\n   * The individual case reducer functions that were passed in the `reducers` parameter.\r\n   * This enables reuse and testing if they were defined inline when calling `createSlice`.\r\n   */\r\n  caseReducers: SliceDefinedCaseReducers<CaseReducers>\r\n\r\n  /**\r\n   * Provides access to the initial state value given to the slice.\r\n   * If a lazy state initializer was provided, it will be called and a fresh value returned.\r\n   */\r\n  getInitialState: () => State\r\n}\r\n\r\n/**\r\n * Options for `createSlice()`.\r\n *\r\n * @public\r\n */\r\nexport interface CreateSliceOptions<\r\n  State = any,\r\n  CR extends SliceCaseReducers<State> = SliceCaseReducers<State>,\r\n  Name extends string = string\r\n> {\r\n  /**\r\n   * The slice's name. Used to namespace the generated action types.\r\n   */\r\n  name: Name\r\n\r\n  /**\r\n   * The initial state that should be used when the reducer is called the first time. This may also be a \"lazy initializer\" function, which should return an initial state value when called. This will be used whenever the reducer is called with `undefined` as its state value, and is primarily useful for cases like reading initial state from `localStorage`.\r\n   */\r\n  initialState: State | (() => State)\r\n\r\n  /**\r\n   * A mapping from action types to action-type-specific *case reducer*\r\n   * functions. For every action type, a matching action creator will be\r\n   * generated using `createAction()`.\r\n   */\r\n  reducers: ValidateSliceCaseReducers<State, CR>\r\n\r\n  /**\r\n   * A callback that receives a *builder* object to define\r\n   * case reducers via calls to `builder.addCase(actionCreatorOrType, reducer)`.\r\n   * \r\n   * Alternatively, a mapping from action types to action-type-specific *case reducer*\r\n   * functions. These reducers should have existing action types used\r\n   * as the keys, and action creators will _not_ be generated.\r\n   * \r\n   * @example\r\n```ts\r\nimport { createAction, createSlice, Action, AnyAction } from '@reduxjs/toolkit'\r\nconst incrementBy = createAction<number>('incrementBy')\r\nconst decrement = createAction('decrement')\r\n\r\ninterface RejectedAction extends Action {\r\n  error: Error\r\n}\r\n\r\nfunction isRejectedAction(action: AnyAction): action is RejectedAction {\r\n  return action.type.endsWith('rejected')\r\n}\r\n\r\ncreateSlice({\r\n  name: 'counter',\r\n  initialState: 0,\r\n  reducers: {},\r\n  extraReducers: builder => {\r\n    builder\r\n      .addCase(incrementBy, (state, action) => {\r\n        // action is inferred correctly here if using TS\r\n      })\r\n      // You can chain calls, or have separate `builder.addCase()` lines each time\r\n      .addCase(decrement, (state, action) => {})\r\n      // You can match a range of action types\r\n      .addMatcher(\r\n        isRejectedAction,\r\n        // `action` will be inferred as a RejectedAction due to isRejectedAction being defined as a type guard\r\n        (state, action) => {}\r\n      )\r\n      // and provide a default case if no other handlers matched\r\n      .addDefaultCase((state, action) => {})\r\n    }\r\n})\r\n```\r\n   */\r\n  extraReducers?:\r\n    | CaseReducers<NoInfer<State>, any>\r\n    | ((builder: ActionReducerMapBuilder<NoInfer<State>>) => void)\r\n}\r\n\r\n/**\r\n * A CaseReducer with a `prepare` method.\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerWithPrepare<State, Action extends PayloadAction> = {\r\n  reducer: CaseReducer<State, Action>\r\n  prepare: PrepareAction<Action['payload']>\r\n}\r\n\r\n/**\r\n * The type describing a slice's `reducers` option.\r\n *\r\n * @public\r\n */\r\nexport type SliceCaseReducers<State> = {\r\n  [K: string]:\r\n    | CaseReducer<State, PayloadAction<any>>\r\n    | CaseReducerWithPrepare<State, PayloadAction<any, string, any, any>>\r\n}\r\n\r\ntype SliceActionType<\r\n  SliceName extends string,\r\n  ActionName extends keyof any\r\n> = ActionName extends string | number ? `${SliceName}/${ActionName}` : string\r\n\r\n/**\r\n * Derives the slice's `actions` property from the `reducers` options\r\n *\r\n * @public\r\n */\r\nexport type CaseReducerActions<\r\n  CaseReducers extends SliceCaseReducers<any>,\r\n  SliceName extends string\r\n> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends { prepare: any }\r\n    ? ActionCreatorForCaseReducerWithPrepare<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n    : ActionCreatorForCaseReducer<\r\n        CaseReducers[Type],\r\n        SliceActionType<SliceName, Type>\r\n      >\r\n}\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducerWithPrepare`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducerWithPrepare<\r\n  CR extends { prepare: any },\r\n  Type extends string\r\n> = _ActionCreatorWithPreparedPayload<CR['prepare'], Type>\r\n\r\n/**\r\n * Get a `PayloadActionCreator` type for a passed `CaseReducer`\r\n *\r\n * @internal\r\n */\r\ntype ActionCreatorForCaseReducer<CR, Type extends string> = CR extends (\r\n  state: any,\r\n  action: infer Action\r\n) => any\r\n  ? Action extends { payload: infer P }\r\n    ? PayloadActionCreator<P, Type>\r\n    : ActionCreatorWithoutPayload<Type>\r\n  : ActionCreatorWithoutPayload<Type>\r\n\r\n/**\r\n * Extracts the CaseReducers out of a `reducers` object, even if they are\r\n * tested into a `CaseReducerWithPrepare`.\r\n *\r\n * @internal\r\n */\r\ntype SliceDefinedCaseReducers<CaseReducers extends SliceCaseReducers<any>> = {\r\n  [Type in keyof CaseReducers]: CaseReducers[Type] extends {\r\n    reducer: infer Reducer\r\n  }\r\n    ? Reducer\r\n    : CaseReducers[Type]\r\n}\r\n\r\n/**\r\n * Used on a SliceCaseReducers object.\r\n * Ensures that if a CaseReducer is a `CaseReducerWithPrepare`, that\r\n * the `reducer` and the `prepare` function use the same type of `payload`.\r\n *\r\n * Might do additional such checks in the future.\r\n *\r\n * This type is only ever useful if you want to write your own wrapper around\r\n * `createSlice`. Please don't use it otherwise!\r\n *\r\n * @public\r\n */\r\nexport type ValidateSliceCaseReducers<\r\n  S,\r\n  ACR extends SliceCaseReducers<S>\r\n> = ACR &\r\n  {\r\n    [T in keyof ACR]: ACR[T] extends {\r\n      reducer(s: S, action?: infer A): any\r\n    }\r\n      ? {\r\n          prepare(...a: never[]): Omit<A, 'type'>\r\n        }\r\n      : {}\r\n  }\r\n\r\nfunction getType(slice: string, actionKey: string): string {\r\n  return `${slice}/${actionKey}`\r\n}\r\n\r\n/**\r\n * A function that accepts an initial state, an object full of reducer\r\n * functions, and a \"slice name\", and automatically generates\r\n * action creators and action types that correspond to the\r\n * reducers and state.\r\n *\r\n * The `reducer` argument is passed to `createReducer()`.\r\n *\r\n * @public\r\n */\r\nexport function createSlice<\r\n  State,\r\n  CaseReducers extends SliceCaseReducers<State>,\r\n  Name extends string = string\r\n>(\r\n  options: CreateSliceOptions<State, CaseReducers, Name>\r\n): Slice<State, CaseReducers, Name> {\r\n  const { name } = options\r\n  if (!name) {\r\n    throw new Error('`name` is a required option for createSlice')\r\n  }\r\n\r\n  if (\r\n    typeof process !== 'undefined' &&\r\n    process.env.NODE_ENV === 'development'\r\n  ) {\r\n    if (options.initialState === undefined) {\r\n      console.error(\r\n        'You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`'\r\n      )\r\n    }\r\n  }\r\n\r\n  const initialState =\r\n    typeof options.initialState == 'function'\r\n      ? options.initialState\r\n      : freezeDraftable(options.initialState)\r\n\r\n  const reducers = options.reducers || {}\r\n\r\n  const reducerNames = Object.keys(reducers)\r\n\r\n  const sliceCaseReducersByName: Record<string, CaseReducer> = {}\r\n  const sliceCaseReducersByType: Record<string, CaseReducer> = {}\r\n  const actionCreators: Record<string, Function> = {}\r\n\r\n  reducerNames.forEach((reducerName) => {\r\n    const maybeReducerWithPrepare = reducers[reducerName]\r\n    const type = getType(name, reducerName)\r\n\r\n    let caseReducer: CaseReducer<State, any>\r\n    let prepareCallback: PrepareAction<any> | undefined\r\n\r\n    if ('reducer' in maybeReducerWithPrepare) {\r\n      caseReducer = maybeReducerWithPrepare.reducer\r\n      prepareCallback = maybeReducerWithPrepare.prepare\r\n    } else {\r\n      caseReducer = maybeReducerWithPrepare\r\n    }\r\n\r\n    sliceCaseReducersByName[reducerName] = caseReducer\r\n    sliceCaseReducersByType[type] = caseReducer\r\n    actionCreators[reducerName] = prepareCallback\r\n      ? createAction(type, prepareCallback)\r\n      : createAction(type)\r\n  })\r\n\r\n  function buildReducer() {\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if (typeof options.extraReducers === 'object') {\r\n        if (!hasWarnedAboutObjectNotation) {\r\n          hasWarnedAboutObjectNotation = true\r\n          console.warn(\r\n            \"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\"\r\n          )\r\n        }\r\n      }\r\n    }\r\n    const [\r\n      extraReducers = {},\r\n      actionMatchers = [],\r\n      defaultCaseReducer = undefined,\r\n    ] =\r\n      typeof options.extraReducers === 'function'\r\n        ? executeReducerBuilderCallback(options.extraReducers)\r\n        : [options.extraReducers]\r\n\r\n    const finalCaseReducers = { ...extraReducers, ...sliceCaseReducersByType }\r\n\r\n    return createReducer(initialState, (builder) => {\r\n      for (let key in finalCaseReducers) {\r\n        builder.addCase(key, finalCaseReducers[key] as CaseReducer<any>)\r\n      }\r\n      for (let m of actionMatchers) {\r\n        builder.addMatcher(m.matcher, m.reducer)\r\n      }\r\n      if (defaultCaseReducer) {\r\n        builder.addDefaultCase(defaultCaseReducer)\r\n      }\r\n    })\r\n  }\r\n\r\n  let _reducer: ReducerWithInitialState<State>\r\n\r\n  return {\r\n    name,\r\n    reducer(state, action) {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer(state, action)\r\n    },\r\n    actions: actionCreators as any,\r\n    caseReducers: sliceCaseReducersByName as any,\r\n    getInitialState() {\r\n      if (!_reducer) _reducer = buildReducer()\r\n\r\n      return _reducer.getInitialState()\r\n    },\r\n  }\r\n}\r\n", "import type { EntityState } from './models'\r\n\r\nexport function getInitialEntityState<V>(): EntityState<V> {\r\n  return {\r\n    ids: [],\r\n    entities: {},\r\n  }\r\n}\r\n\r\nexport function createInitialStateFactory<V>() {\r\n  function getInitialState(): EntityState<V>\r\n  function getInitialState<S extends object>(\r\n    additionalState: S\r\n  ): EntityState<V> & S\r\n  function getInitialState(additionalState: any = {}): any {\r\n    return Object.assign(getInitialEntityState(), additionalState)\r\n  }\r\n\r\n  return { getInitialState }\r\n}\r\n", "import type { Selector } from 'reselect'\r\nimport { createDraftSafeSelector } from '../createDraftSafeSelector'\r\nimport type {\r\n  EntityState,\r\n  EntitySelectors,\r\n  Dictionary,\r\n  EntityId,\r\n} from './models'\r\n\r\nexport function createSelectorsFactory<T>() {\r\n  function getSelectors(): EntitySelectors<T, EntityState<T>>\r\n  function getSelectors<V>(\r\n    selectState: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, V>\r\n  function getSelectors<V>(\r\n    selectState?: (state: V) => EntityState<T>\r\n  ): EntitySelectors<T, any> {\r\n    const selectIds = (state: EntityState<T>) => state.ids\r\n\r\n    const selectEntities = (state: EntityState<T>) => state.entities\r\n\r\n    const selectAll = createDraftSafeSelector(\r\n      selectIds,\r\n      selectEntities,\r\n      (ids, entities): T[] => ids.map((id) => entities[id]!)\r\n    )\r\n\r\n    const selectId = (_: unknown, id: EntityId) => id\r\n\r\n    const selectById = (entities: Dictionary<T>, id: EntityId) => entities[id]\r\n\r\n    const selectTotal = createDraftSafeSelector(selectIds, (ids) => ids.length)\r\n\r\n    if (!selectState) {\r\n      return {\r\n        selectIds,\r\n        selectEntities,\r\n        selectAll,\r\n        selectTotal,\r\n        selectById: createDraftSafeSelector(\r\n          selectEntities,\r\n          selectId,\r\n          selectById\r\n        ),\r\n      }\r\n    }\r\n\r\n    const selectGlobalizedEntities = createDraftSafeSelector(\r\n      selectState as Selector<V, EntityState<T>>,\r\n      selectEntities\r\n    )\r\n\r\n    return {\r\n      selectIds: createDraftSafeSelector(selectState, selectIds),\r\n      selectEntities: selectGlobalizedEntities,\r\n      selectAll: createDraftSafeSelector(selectState, selectAll),\r\n      selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n      selectById: createDraftSafeSelector(\r\n        selectGlobalizedEntities,\r\n        selectId,\r\n        selectById\r\n      ),\r\n    }\r\n  }\r\n\r\n  return { getSelectors }\r\n}\r\n", "import createNextState, { isDraft } from 'immer'\r\nimport type { EntityState, PreventAny } from './models'\r\nimport type { PayloadAction } from '../createAction'\r\nimport { isFSA } from '../createAction'\r\nimport { IsAny } from '../tsHelpers'\r\n\r\nexport function createSingleArgumentStateOperator<V>(\r\n  mutator: (state: EntityState<V>) => void\r\n) {\r\n  const operator = createStateOperator((_: undefined, state: EntityState<V>) =>\r\n    mutator(state)\r\n  )\r\n\r\n  return function operation<S extends EntityState<V>>(\r\n    state: PreventAny<S, V>\r\n  ): S {\r\n    return operator(state as S, undefined)\r\n  }\r\n}\r\n\r\nexport function createStateOperator<V, R>(\r\n  mutator: (arg: R, state: EntityState<V>) => void\r\n) {\r\n  return function operation<S extends EntityState<V>>(\r\n    state: S,\r\n    arg: R | PayloadAction<R>\r\n  ): S {\r\n    function isPayloadActionArgument(\r\n      arg: R | PayloadAction<R>\r\n    ): arg is PayloadAction<R> {\r\n      return isFSA(arg)\r\n    }\r\n\r\n    const runMutator = (draft: EntityState<V>) => {\r\n      if (isPayloadActionArgument(arg)) {\r\n        mutator(arg.payload, draft)\r\n      } else {\r\n        mutator(arg, draft)\r\n      }\r\n    }\r\n\r\n    if (isDraft(state)) {\r\n      // we must already be inside a `createNextState` call, likely because\r\n      // this is being wrapped in `createReducer` or `createSlice`.\r\n      // It's safe to just pass the draft to the mutator.\r\n      runMutator(state)\r\n\r\n      // since it's a draft, we'll just return it\r\n      return state\r\n    } else {\r\n      // @ts-ignore createNextState() produces an Immutable<Draft<S>> rather\r\n      // than an Immutable<S>, and TypeScript cannot find out how to reconcile\r\n      // these two types.\r\n      return createNextState(state, runMutator)\r\n    }\r\n  }\r\n}\r\n", "import type { EntityState, IdSelector, Update, EntityId } from './models'\r\n\r\nexport function selectIdValue<T>(entity: T, selectId: IdSelector<T>) {\r\n  const key = selectId(entity)\r\n\r\n  if (process.env.NODE_ENV !== 'production' && key === undefined) {\r\n    console.warn(\r\n      'The entity passed to the `selectId` implementation returned undefined.',\r\n      'You should probably provide your own `selectId` implementation.',\r\n      'The entity that was passed:',\r\n      entity,\r\n      'The `selectId` implementation:',\r\n      selectId.toString()\r\n    )\r\n  }\r\n\r\n  return key\r\n}\r\n\r\nexport function ensureEntitiesArray<T>(\r\n  entities: readonly T[] | Record<EntityId, T>\r\n): readonly T[] {\r\n  if (!Array.isArray(entities)) {\r\n    entities = Object.values(entities)\r\n  }\r\n\r\n  return entities\r\n}\r\n\r\nexport function splitAddedUpdatedEntities<T>(\r\n  newEntities: readonly T[] | Record<EntityId, T>,\r\n  selectId: IdSelector<T>,\r\n  state: EntityState<T>\r\n): [T[], Update<T>[]] {\r\n  newEntities = ensureEntitiesArray(newEntities)\r\n\r\n  const added: T[] = []\r\n  const updated: Update<T>[] = []\r\n\r\n  for (const entity of newEntities) {\r\n    const id = selectIdValue(entity, selectId)\r\n    if (id in state.entities) {\r\n      updated.push({ id, changes: entity })\r\n    } else {\r\n      added.push(entity)\r\n    }\r\n  }\r\n  return [added, updated]\r\n}\r\n", "import type {\r\n  EntityState,\r\n  EntityStateAdapter,\r\n  IdSelector,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport {\r\n  createStateOperator,\r\n  createSingleArgumentStateOperator,\r\n} from './state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createUnsortedStateAdapter<T>(\r\n  selectId: IdSelector<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n\r\n    if (key in state.entities) {\r\n      return\r\n    }\r\n\r\n    state.ids.push(key)\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    for (const entity of newEntities) {\r\n      addOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    const key = selectIdValue(entity, selectId)\r\n    if (!(key in state.entities)) {\r\n      state.ids.push(key)\r\n    }\r\n    state.entities[key] = entity\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    for (const entity of newEntities) {\r\n      setOneMutably(entity, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    state.ids = []\r\n    state.entities = {}\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function removeOneMutably(key: EntityId, state: R): void {\r\n    return removeManyMutably([key], state)\r\n  }\r\n\r\n  function removeManyMutably(keys: readonly EntityId[], state: R): void {\r\n    let didMutate = false\r\n\r\n    keys.forEach((key) => {\r\n      if (key in state.entities) {\r\n        delete state.entities[key]\r\n        didMutate = true\r\n      }\r\n    })\r\n\r\n    if (didMutate) {\r\n      state.ids = state.ids.filter((id) => id in state.entities)\r\n    }\r\n  }\r\n\r\n  function removeAllMutably(state: R): void {\r\n    Object.assign(state, {\r\n      ids: [],\r\n      entities: {},\r\n    })\r\n  }\r\n\r\n  function takeNewKey(\r\n    keys: { [id: string]: EntityId },\r\n    update: Update<T>,\r\n    state: R\r\n  ): boolean {\r\n    const original = state.entities[update.id]\r\n    const updated: T = Object.assign({}, original, update.changes)\r\n    const newKey = selectIdValue(updated, selectId)\r\n    const hasNewKey = newKey !== update.id\r\n\r\n    if (hasNewKey) {\r\n      keys[update.id] = newKey\r\n      delete state.entities[update.id]\r\n    }\r\n\r\n    state.entities[newKey] = updated\r\n\r\n    return hasNewKey\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    const newKeys: { [id: string]: EntityId } = {}\r\n\r\n    const updatesPerEntity: { [id: string]: Update<T> } = {}\r\n\r\n    updates.forEach((update) => {\r\n      // Only apply updates to entities that currently exist\r\n      if (update.id in state.entities) {\r\n        // If there are multiple updates to one entity, merge them together\r\n        updatesPerEntity[update.id] = {\r\n          id: update.id,\r\n          // Spreads ignore falsy values, so this works even if there isn't\r\n          // an existing update already at this key\r\n          changes: {\r\n            ...(updatesPerEntity[update.id]\r\n              ? updatesPerEntity[update.id].changes\r\n              : null),\r\n            ...update.changes,\r\n          },\r\n        }\r\n      }\r\n    })\r\n\r\n    updates = Object.values(updatesPerEntity)\r\n\r\n    const didMutateEntities = updates.length > 0\r\n\r\n    if (didMutateEntities) {\r\n      const didMutateIds =\r\n        updates.filter((update) => takeNewKey(newKeys, update, state)).length >\r\n        0\r\n\r\n      if (didMutateIds) {\r\n        state.ids = Object.keys(state.entities)\r\n      }\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  return {\r\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n    addOne: createStateOperator(addOneMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n    removeOne: createStateOperator(removeOneMutably),\r\n    removeMany: createStateOperator(removeManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityState,\r\n  IdSelector,\r\n  Comparer,\r\n  EntityStateAdapter,\r\n  Update,\r\n  EntityId,\r\n} from './models'\r\nimport { createStateOperator } from './state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\nimport {\r\n  selectIdValue,\r\n  ensureEntitiesArray,\r\n  splitAddedUpdatedEntities,\r\n} from './utils'\r\n\r\nexport function createSortedStateAdapter<T>(\r\n  selectId: IdSelector<T>,\r\n  sort: Comparer<T>\r\n): EntityStateAdapter<T> {\r\n  type R = EntityState<T>\r\n\r\n  const { removeOne, removeMany, removeAll } =\r\n    createUnsortedStateAdapter(selectId)\r\n\r\n  function addOneMutably(entity: T, state: R): void {\r\n    return addManyMutably([entity], state)\r\n  }\r\n\r\n  function addManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n\r\n    const models = newEntities.filter(\r\n      (model) => !(selectIdValue(model, selectId) in state.entities)\r\n    )\r\n\r\n    if (models.length !== 0) {\r\n      merge(models, state)\r\n    }\r\n  }\r\n\r\n  function setOneMutably(entity: T, state: R): void {\r\n    return setManyMutably([entity], state)\r\n  }\r\n\r\n  function setManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    if (newEntities.length !== 0) {\r\n      merge(newEntities, state)\r\n    }\r\n  }\r\n\r\n  function setAllMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    newEntities = ensureEntitiesArray(newEntities)\r\n    state.entities = {}\r\n    state.ids = []\r\n\r\n    addManyMutably(newEntities, state)\r\n  }\r\n\r\n  function updateOneMutably(update: Update<T>, state: R): void {\r\n    return updateManyMutably([update], state)\r\n  }\r\n\r\n  function updateManyMutably(\r\n    updates: ReadonlyArray<Update<T>>,\r\n    state: R\r\n  ): void {\r\n    let appliedUpdates = false\r\n\r\n    for (let update of updates) {\r\n      const entity = state.entities[update.id]\r\n      if (!entity) {\r\n        continue\r\n      }\r\n\r\n      appliedUpdates = true\r\n\r\n      Object.assign(entity, update.changes)\r\n      const newId = selectId(entity)\r\n      if (update.id !== newId) {\r\n        delete state.entities[update.id]\r\n        state.entities[newId] = entity\r\n      }\r\n    }\r\n\r\n    if (appliedUpdates) {\r\n      resortEntities(state)\r\n    }\r\n  }\r\n\r\n  function upsertOneMutably(entity: T, state: R): void {\r\n    return upsertManyMutably([entity], state)\r\n  }\r\n\r\n  function upsertManyMutably(\r\n    newEntities: readonly T[] | Record<EntityId, T>,\r\n    state: R\r\n  ): void {\r\n    const [added, updated] = splitAddedUpdatedEntities<T>(\r\n      newEntities,\r\n      selectId,\r\n      state\r\n    )\r\n\r\n    updateManyMutably(updated, state)\r\n    addManyMutably(added, state)\r\n  }\r\n\r\n  function areArraysEqual(a: readonly unknown[], b: readonly unknown[]) {\r\n    if (a.length !== b.length) {\r\n      return false\r\n    }\r\n\r\n    for (let i = 0; i < a.length && i < b.length; i++) {\r\n      if (a[i] === b[i]) {\r\n        continue\r\n      }\r\n      return false\r\n    }\r\n    return true\r\n  }\r\n\r\n  function merge(models: readonly T[], state: R): void {\r\n    // Insert/overwrite all new/updated\r\n    models.forEach((model) => {\r\n      state.entities[selectId(model)] = model\r\n    })\r\n\r\n    resortEntities(state)\r\n  }\r\n\r\n  function resortEntities(state: R) {\r\n    const allEntities = Object.values(state.entities) as T[]\r\n    allEntities.sort(sort)\r\n\r\n    const newSortedIds = allEntities.map(selectId)\r\n    const { ids } = state\r\n\r\n    if (!areArraysEqual(ids, newSortedIds)) {\r\n      state.ids = newSortedIds\r\n    }\r\n  }\r\n\r\n  return {\r\n    removeOne,\r\n    removeMany,\r\n    removeAll,\r\n    addOne: createStateOperator(addOneMutably),\r\n    updateOne: createStateOperator(updateOneMutably),\r\n    upsertOne: createStateOperator(upsertOneMutably),\r\n    setOne: createStateOperator(setOneMutably),\r\n    setMany: createStateOperator(setManyMutably),\r\n    setAll: createStateOperator(setAllMutably),\r\n    addMany: createStateOperator(addManyMutably),\r\n    updateMany: createStateOperator(updateManyMutably),\r\n    upsertMany: createStateOperator(upsertManyMutably),\r\n  }\r\n}\r\n", "import type {\r\n  EntityDefinition,\r\n  Comparer,\r\n  IdSelector,\r\n  EntityAdapter,\r\n} from './models'\r\nimport { createInitialStateFactory } from './entity_state'\r\nimport { createSelectorsFactory } from './state_selectors'\r\nimport { createSortedStateAdapter } from './sorted_state_adapter'\r\nimport { createUnsortedStateAdapter } from './unsorted_state_adapter'\r\n\r\n/**\r\n *\r\n * @param options\r\n *\r\n * @public\r\n */\r\nexport function createEntityAdapter<T>(\r\n  options: {\r\n    selectId?: IdSelector<T>\r\n    sortComparer?: false | Comparer<T>\r\n  } = {}\r\n): EntityAdapter<T> {\r\n  const { selectId, sortComparer }: EntityDefinition<T> = {\r\n    sortComparer: false,\r\n    selectId: (instance: any) => instance.id,\r\n    ...options,\r\n  }\r\n\r\n  const stateFactory = createInitialStateFactory<T>()\r\n  const selectorsFactory = createSelectorsFactory<T>()\r\n  const stateAdapter = sortComparer\r\n    ? createSortedStateAdapter(selectId, sortComparer)\r\n    : createUnsortedStateAdapter(selectId)\r\n\r\n  return {\r\n    selectId,\r\n    sortComparer,\r\n    ...stateFactory,\r\n    ...selectorsFactory,\r\n    ...stateAdapter,\r\n  }\r\n}\r\n", "// Borrowed from https://github.com/ai/nanoid/blob/3.0.2/non-secure/index.js\r\n// This alphabet uses `A-Za-z0-9_-` symbols. A genetic algorithm helped\r\n// optimize the gzip compression for this alphabet.\r\nlet urlAlphabet =\r\n  'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW'\r\n\r\n/**\r\n *\r\n * @public\r\n */\r\nexport let nanoid = (size = 21) => {\r\n  let id = ''\r\n  // A compact alternative for `for (var i = 0; i < step; i++)`.\r\n  let i = size\r\n  while (i--) {\r\n    // `| 0` is more compact and faster than `Math.floor()`.\r\n    id += urlAlphabet[(Math.random() * 64) | 0]\r\n  }\r\n  return id\r\n}\r\n", "import type { Dispatch, AnyAction } from 'redux'\r\nimport type {\r\n  PayloadAction,\r\n  ActionCreatorWithPreparedPayload,\r\n} from './createAction'\r\nimport { createAction } from './createAction'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport type { FallbackIfUnknown, Id, IsAny, IsUnknown } from './tsHelpers'\r\nimport { nanoid } from './nanoid'\r\n\r\n// @ts-ignore we need the import of these types due to a bundling issue.\r\ntype _Keep = PayloadAction | ActionCreatorWithPreparedPayload<any, unknown>\r\n\r\nexport type BaseThunkAPI<\r\n  S,\r\n  E,\r\n  D extends Dispatch = Dispatch,\r\n  RejectedValue = unknown,\r\n  RejectedMeta = unknown,\r\n  FulfilledMeta = unknown\r\n> = {\r\n  dispatch: D\r\n  getState: () => S\r\n  extra: E\r\n  requestId: string\r\n  signal: AbortSignal\r\n  abort: (reason?: string) => void\r\n  rejectWithValue: IsUnknown<\r\n    RejectedMeta,\r\n    (value: RejectedValue) => RejectWithValue<RejectedValue, RejectedMeta>,\r\n    (\r\n      value: RejectedValue,\r\n      meta: RejectedMeta\r\n    ) => RejectWithValue<RejectedValue, RejectedMeta>\r\n  >\r\n  fulfillWithValue: IsUnknown<\r\n    FulfilledMeta,\r\n    <FulfilledValue>(value: FulfilledValue) => FulfilledValue,\r\n    <FulfilledValue>(\r\n      value: FulfilledValue,\r\n      meta: FulfilledMeta\r\n    ) => FulfillWithMeta<FulfilledValue, FulfilledMeta>\r\n  >\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport interface SerializedError {\r\n  name?: string\r\n  message?: string\r\n  stack?: string\r\n  code?: string\r\n}\r\n\r\nconst commonProperties: Array<keyof SerializedError> = [\r\n  'name',\r\n  'message',\r\n  'stack',\r\n  'code',\r\n]\r\n\r\nclass RejectWithValue<Payload, RejectedMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'RejectWithValue'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: RejectedMeta\r\n  ) {}\r\n}\r\n\r\nclass FulfillWithMeta<Payload, FulfilledMeta> {\r\n  /*\r\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\r\n  does not exist at runtime\r\n  */\r\n  private readonly _type!: 'FulfillWithMeta'\r\n  constructor(\r\n    public readonly payload: Payload,\r\n    public readonly meta: FulfilledMeta\r\n  ) {}\r\n}\r\n\r\n/**\r\n * Serializes an error into a plain object.\r\n * Reworked from https://github.com/sindresorhus/serialize-error\r\n *\r\n * @public\r\n */\r\nexport const miniSerializeError = (value: any): SerializedError => {\r\n  if (typeof value === 'object' && value !== null) {\r\n    const simpleError: SerializedError = {}\r\n    for (const property of commonProperties) {\r\n      if (typeof value[property] === 'string') {\r\n        simpleError[property] = value[property]\r\n      }\r\n    }\r\n\r\n    return simpleError\r\n  }\r\n\r\n  return { message: String(value) }\r\n}\r\n\r\ntype AsyncThunkConfig = {\r\n  state?: unknown\r\n  dispatch?: Dispatch\r\n  extra?: unknown\r\n  rejectValue?: unknown\r\n  serializedErrorType?: unknown\r\n  pendingMeta?: unknown\r\n  fulfilledMeta?: unknown\r\n  rejectedMeta?: unknown\r\n}\r\n\r\ntype GetState<ThunkApiConfig> = ThunkApiConfig extends {\r\n  state: infer State\r\n}\r\n  ? State\r\n  : unknown\r\ntype GetExtra<ThunkApiConfig> = ThunkApiConfig extends { extra: infer Extra }\r\n  ? Extra\r\n  : unknown\r\ntype GetDispatch<ThunkApiConfig> = ThunkApiConfig extends {\r\n  dispatch: infer Dispatch\r\n}\r\n  ? FallbackIfUnknown<\r\n      Dispatch,\r\n      ThunkDispatch<\r\n        GetState<ThunkApiConfig>,\r\n        GetExtra<ThunkApiConfig>,\r\n        AnyAction\r\n      >\r\n    >\r\n  : ThunkDispatch<GetState<ThunkApiConfig>, GetExtra<ThunkApiConfig>, AnyAction>\r\n\r\nexport type GetThunkAPI<ThunkApiConfig> = BaseThunkAPI<\r\n  GetState<ThunkApiConfig>,\r\n  GetExtra<ThunkApiConfig>,\r\n  GetDispatch<ThunkApiConfig>,\r\n  GetRejectValue<ThunkApiConfig>,\r\n  GetRejectedMeta<ThunkApiConfig>,\r\n  GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\ntype GetRejectValue<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectValue: infer RejectValue\r\n}\r\n  ? RejectValue\r\n  : unknown\r\n\r\ntype GetPendingMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  pendingMeta: infer PendingMeta\r\n}\r\n  ? PendingMeta\r\n  : unknown\r\n\r\ntype GetFulfilledMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  fulfilledMeta: infer FulfilledMeta\r\n}\r\n  ? FulfilledMeta\r\n  : unknown\r\n\r\ntype GetRejectedMeta<ThunkApiConfig> = ThunkApiConfig extends {\r\n  rejectedMeta: infer RejectedMeta\r\n}\r\n  ? RejectedMeta\r\n  : unknown\r\n\r\ntype GetSerializedErrorType<ThunkApiConfig> = ThunkApiConfig extends {\r\n  serializedErrorType: infer GetSerializedErrorType\r\n}\r\n  ? GetSerializedErrorType\r\n  : SerializedError\r\n\r\ntype MaybePromise<T> = T | Promise<T> | (T extends any ? Promise<T> : never)\r\n\r\n/**\r\n * A type describing the return value of the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreatorReturnValue<\r\n  Returned,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = MaybePromise<\r\n  | IsUnknown<\r\n      GetFulfilledMeta<ThunkApiConfig>,\r\n      Returned,\r\n      FulfillWithMeta<Returned, GetFulfilledMeta<ThunkApiConfig>>\r\n    >\r\n  | RejectWithValue<\r\n      GetRejectValue<ThunkApiConfig>,\r\n      GetRejectedMeta<ThunkApiConfig>\r\n    >\r\n>\r\n/**\r\n * A type describing the `payloadCreator` argument to `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkPayloadCreator<\r\n  Returned,\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = (\r\n  arg: ThunkArg,\r\n  thunkAPI: GetThunkAPI<ThunkApiConfig>\r\n) => AsyncThunkPayloadCreatorReturnValue<Returned, ThunkApiConfig>\r\n\r\n/**\r\n * A ThunkAction created by `createAsyncThunk`.\r\n * Dispatching it returns a Promise for either a\r\n * fulfilled or rejected action.\r\n * Also, the returned value contains an `abort()` method\r\n * that allows the asyncAction to be cancelled from the outside.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkAction<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = (\r\n  dispatch: GetDispatch<ThunkApiConfig>,\r\n  getState: () => GetState<ThunkApiConfig>,\r\n  extra: GetExtra<ThunkApiConfig>\r\n) => Promise<\r\n  | ReturnType<AsyncThunkFulfilledActionCreator<Returned, ThunkArg>>\r\n  | ReturnType<AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>>\r\n> & {\r\n  abort: (reason?: string) => void\r\n  requestId: string\r\n  arg: ThunkArg\r\n  unwrap: () => Promise<Returned>\r\n}\r\n\r\ntype AsyncThunkActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = IsAny<\r\n  ThunkArg,\r\n  // any handling\r\n  (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n  // unknown handling\r\n  unknown extends ThunkArg\r\n    ? (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument not specified or specified as void or undefined\r\n    : [ThunkArg] extends [void] | [undefined]\r\n    ? () => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains void\r\n    : [void] extends [ThunkArg] // make optional\r\n    ? (arg?: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> // argument contains undefined\r\n    : [undefined] extends [ThunkArg]\r\n    ? WithStrictNullChecks<\r\n        // with strict nullChecks: make optional\r\n        (\r\n          arg?: ThunkArg\r\n        ) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>,\r\n        // without strict null checks this will match everything, so don't make it optional\r\n        (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n      > // default case: normal argument\r\n    : (arg: ThunkArg) => AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * Options object for `createAsyncThunk`.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunkOptions<\r\n  ThunkArg = void,\r\n  ThunkApiConfig extends AsyncThunkConfig = {}\r\n> = {\r\n  /**\r\n   * A method to control whether the asyncThunk should be executed. Has access to the\r\n   * `arg`, `api.getState()` and `api.extra` arguments.\r\n   *\r\n   * @returns `false` if it should be skipped\r\n   */\r\n  condition?(\r\n    arg: ThunkArg,\r\n    api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n  ): MaybePromise<boolean | undefined>\r\n  /**\r\n   * If `condition` returns `false`, the asyncThunk will be skipped.\r\n   * This option allows you to control whether a `rejected` action with `meta.condition == false`\r\n   * will be dispatched or not.\r\n   *\r\n   * @default `false`\r\n   */\r\n  dispatchConditionRejection?: boolean\r\n\r\n  serializeError?: (x: unknown) => GetSerializedErrorType<ThunkApiConfig>\r\n\r\n  /**\r\n   * A function to use when generating the `requestId` for the request sequence.\r\n   *\r\n   * @default `nanoid`\r\n   */\r\n  idGenerator?: (arg: ThunkArg) => string\r\n} & IsUnknown<\r\n  GetPendingMeta<ThunkApiConfig>,\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     *\r\n     * Using this optional overload will not modify the types correctly, this overload is only in place to support JavaScript users.\r\n     * Please use the `ThunkApiConfig` parameter `pendingMeta` to get access to a correctly typed overload\r\n     */\r\n    getPendingMeta?(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  },\r\n  {\r\n    /**\r\n     * A method to generate additional properties to be added to `meta` of the pending action.\r\n     */\r\n    getPendingMeta(\r\n      base: {\r\n        arg: ThunkArg\r\n        requestId: string\r\n      },\r\n      api: Pick<GetThunkAPI<ThunkApiConfig>, 'getState' | 'extra'>\r\n    ): GetPendingMeta<ThunkApiConfig>\r\n  }\r\n>\r\n\r\nexport type AsyncThunkPendingActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [string, ThunkArg, GetPendingMeta<ThunkApiConfig>?],\r\n  undefined,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'pending'\r\n  } & GetPendingMeta<ThunkApiConfig>\r\n>\r\n\r\nexport type AsyncThunkRejectedActionCreator<\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [\r\n    Error | null,\r\n    string,\r\n    ThunkArg,\r\n    GetRejectValue<ThunkApiConfig>?,\r\n    GetRejectedMeta<ThunkApiConfig>?\r\n  ],\r\n  GetRejectValue<ThunkApiConfig> | undefined,\r\n  string,\r\n  GetSerializedErrorType<ThunkApiConfig>,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'rejected'\r\n    aborted: boolean\r\n    condition: boolean\r\n  } & (\r\n    | ({ rejectedWithValue: false } & {\r\n        [K in keyof GetRejectedMeta<ThunkApiConfig>]?: undefined\r\n      })\r\n    | ({ rejectedWithValue: true } & GetRejectedMeta<ThunkApiConfig>)\r\n  )\r\n>\r\n\r\nexport type AsyncThunkFulfilledActionCreator<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig = {}\r\n> = ActionCreatorWithPreparedPayload<\r\n  [Returned, string, ThunkArg, GetFulfilledMeta<ThunkApiConfig>?],\r\n  Returned,\r\n  string,\r\n  never,\r\n  {\r\n    arg: ThunkArg\r\n    requestId: string\r\n    requestStatus: 'fulfilled'\r\n  } & GetFulfilledMeta<ThunkApiConfig>\r\n>\r\n\r\n/**\r\n * A type describing the return value of `createAsyncThunk`.\r\n * Might be useful for wrapping `createAsyncThunk` in custom abstractions.\r\n *\r\n * @public\r\n */\r\nexport type AsyncThunk<\r\n  Returned,\r\n  ThunkArg,\r\n  ThunkApiConfig extends AsyncThunkConfig\r\n> = AsyncThunkActionCreator<Returned, ThunkArg, ThunkApiConfig> & {\r\n  pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig>\r\n  rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig>\r\n  fulfilled: AsyncThunkFulfilledActionCreator<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig\r\n  >\r\n  typePrefix: string\r\n}\r\n\r\ntype OverrideThunkApiConfigs<OldConfig, NewConfig> = Id<\r\n  NewConfig & Omit<OldConfig, keyof NewConfig>\r\n>\r\n\r\ntype CreateAsyncThunk<CurriedThunkApiConfig extends AsyncThunkConfig> = {\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  // separate signature without `AsyncThunkConfig` for better inference\r\n  <Returned, ThunkArg = void>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      CurriedThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, CurriedThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, CurriedThunkApiConfig>\r\n\r\n  /**\r\n   *\r\n   * @param typePrefix\r\n   * @param payloadCreator\r\n   * @param options\r\n   *\r\n   * @public\r\n   */\r\n  <Returned, ThunkArg, ThunkApiConfig extends AsyncThunkConfig>(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >,\r\n    options?: AsyncThunkOptions<\r\n      ThunkArg,\r\n      OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n    >\r\n  ): AsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n\r\n  withTypes<ThunkApiConfig extends AsyncThunkConfig>(): CreateAsyncThunk<\r\n    OverrideThunkApiConfigs<CurriedThunkApiConfig, ThunkApiConfig>\r\n  >\r\n}\r\n\r\nexport const createAsyncThunk = (() => {\r\n  function createAsyncThunk<\r\n    Returned,\r\n    ThunkArg,\r\n    ThunkApiConfig extends AsyncThunkConfig\r\n  >(\r\n    typePrefix: string,\r\n    payloadCreator: AsyncThunkPayloadCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    >,\r\n    options?: AsyncThunkOptions<ThunkArg, ThunkApiConfig>\r\n  ): AsyncThunk<Returned, ThunkArg, ThunkApiConfig> {\r\n    type RejectedValue = GetRejectValue<ThunkApiConfig>\r\n    type PendingMeta = GetPendingMeta<ThunkApiConfig>\r\n    type FulfilledMeta = GetFulfilledMeta<ThunkApiConfig>\r\n    type RejectedMeta = GetRejectedMeta<ThunkApiConfig>\r\n\r\n    const fulfilled: AsyncThunkFulfilledActionCreator<\r\n      Returned,\r\n      ThunkArg,\r\n      ThunkApiConfig\r\n    > = createAction(\r\n      typePrefix + '/fulfilled',\r\n      (\r\n        payload: Returned,\r\n        requestId: string,\r\n        arg: ThunkArg,\r\n        meta?: FulfilledMeta\r\n      ) => ({\r\n        payload,\r\n        meta: {\r\n          ...((meta as any) || {}),\r\n          arg,\r\n          requestId,\r\n          requestStatus: 'fulfilled' as const,\r\n        },\r\n      })\r\n    )\r\n\r\n    const pending: AsyncThunkPendingActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/pending',\r\n        (requestId: string, arg: ThunkArg, meta?: PendingMeta) => ({\r\n          payload: undefined,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            requestStatus: 'pending' as const,\r\n          },\r\n        })\r\n      )\r\n\r\n    const rejected: AsyncThunkRejectedActionCreator<ThunkArg, ThunkApiConfig> =\r\n      createAction(\r\n        typePrefix + '/rejected',\r\n        (\r\n          error: Error | null,\r\n          requestId: string,\r\n          arg: ThunkArg,\r\n          payload?: RejectedValue,\r\n          meta?: RejectedMeta\r\n        ) => ({\r\n          payload,\r\n          error: ((options && options.serializeError) || miniSerializeError)(\r\n            error || 'Rejected'\r\n          ) as GetSerializedErrorType<ThunkApiConfig>,\r\n          meta: {\r\n            ...((meta as any) || {}),\r\n            arg,\r\n            requestId,\r\n            rejectedWithValue: !!payload,\r\n            requestStatus: 'rejected' as const,\r\n            aborted: error?.name === 'AbortError',\r\n            condition: error?.name === 'ConditionError',\r\n          },\r\n        })\r\n      )\r\n\r\n    let displayedWarning = false\r\n\r\n    const AC =\r\n      typeof AbortController !== 'undefined'\r\n        ? AbortController\r\n        : class implements AbortController {\r\n            signal = {\r\n              aborted: false,\r\n              addEventListener() {},\r\n              dispatchEvent() {\r\n                return false\r\n              },\r\n              onabort() {},\r\n              removeEventListener() {},\r\n              reason: undefined,\r\n              throwIfAborted() {},\r\n            }\r\n            abort() {\r\n              if (process.env.NODE_ENV !== 'production') {\r\n                if (!displayedWarning) {\r\n                  displayedWarning = true\r\n                  console.info(\r\n                    `This platform does not implement AbortController. \r\nIf you want to use the AbortController to react to \\`abort\\` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.`\r\n                  )\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n    function actionCreator(\r\n      arg: ThunkArg\r\n    ): AsyncThunkAction<Returned, ThunkArg, ThunkApiConfig> {\r\n      return (dispatch, getState, extra) => {\r\n        const requestId = options?.idGenerator\r\n          ? options.idGenerator(arg)\r\n          : nanoid()\r\n\r\n        const abortController = new AC()\r\n        let abortReason: string | undefined\r\n\r\n        let started = false\r\n        function abort(reason?: string) {\r\n          abortReason = reason\r\n          abortController.abort()\r\n        }\r\n\r\n        const promise = (async function () {\r\n          let finalAction: ReturnType<typeof fulfilled | typeof rejected>\r\n          try {\r\n            let conditionResult = options?.condition?.(arg, { getState, extra })\r\n            if (isThenable(conditionResult)) {\r\n              conditionResult = await conditionResult\r\n            }\r\n\r\n            if (conditionResult === false || abortController.signal.aborted) {\r\n              // eslint-disable-next-line no-throw-literal\r\n              throw {\r\n                name: 'ConditionError',\r\n                message: 'Aborted due to condition callback returning false.',\r\n              }\r\n            }\r\n            started = true\r\n\r\n            const abortedPromise = new Promise<never>((_, reject) =>\r\n              abortController.signal.addEventListener('abort', () =>\r\n                reject({\r\n                  name: 'AbortError',\r\n                  message: abortReason || 'Aborted',\r\n                })\r\n              )\r\n            )\r\n            dispatch(\r\n              pending(\r\n                requestId,\r\n                arg,\r\n                options?.getPendingMeta?.(\r\n                  { requestId, arg },\r\n                  { getState, extra }\r\n                )\r\n              )\r\n            )\r\n            finalAction = await Promise.race([\r\n              abortedPromise,\r\n              Promise.resolve(\r\n                payloadCreator(arg, {\r\n                  dispatch,\r\n                  getState,\r\n                  extra,\r\n                  requestId,\r\n                  signal: abortController.signal,\r\n                  abort,\r\n                  rejectWithValue: ((\r\n                    value: RejectedValue,\r\n                    meta?: RejectedMeta\r\n                  ) => {\r\n                    return new RejectWithValue(value, meta)\r\n                  }) as any,\r\n                  fulfillWithValue: ((value: unknown, meta?: FulfilledMeta) => {\r\n                    return new FulfillWithMeta(value, meta)\r\n                  }) as any,\r\n                })\r\n              ).then((result) => {\r\n                if (result instanceof RejectWithValue) {\r\n                  throw result\r\n                }\r\n                if (result instanceof FulfillWithMeta) {\r\n                  return fulfilled(result.payload, requestId, arg, result.meta)\r\n                }\r\n                return fulfilled(result as any, requestId, arg)\r\n              }),\r\n            ])\r\n          } catch (err) {\r\n            finalAction =\r\n              err instanceof RejectWithValue\r\n                ? rejected(null, requestId, arg, err.payload, err.meta)\r\n                : rejected(err as any, requestId, arg)\r\n          }\r\n          // We dispatch the result action _after_ the catch, to avoid having any errors\r\n          // here get swallowed by the try/catch block,\r\n          // per https://twitter.com/dan_abramov/status/770914221638942720\r\n          // and https://github.com/reduxjs/redux-toolkit/blob/e85eb17b39a2118d859f7b7746e0f3fee523e089/docs/tutorials/advanced-tutorial.md#async-error-handling-logic-in-thunks\r\n\r\n          const skipDispatch =\r\n            options &&\r\n            !options.dispatchConditionRejection &&\r\n            rejected.match(finalAction) &&\r\n            (finalAction as any).meta.condition\r\n\r\n          if (!skipDispatch) {\r\n            dispatch(finalAction)\r\n          }\r\n          return finalAction\r\n        })()\r\n        return Object.assign(promise as Promise<any>, {\r\n          abort,\r\n          requestId,\r\n          arg,\r\n          unwrap() {\r\n            return promise.then<any>(unwrapResult)\r\n          },\r\n        })\r\n      }\r\n    }\r\n\r\n    return Object.assign(\r\n      actionCreator as AsyncThunkActionCreator<\r\n        Returned,\r\n        ThunkArg,\r\n        ThunkApiConfig\r\n      >,\r\n      {\r\n        pending,\r\n        rejected,\r\n        fulfilled,\r\n        typePrefix,\r\n      }\r\n    )\r\n  }\r\n  createAsyncThunk.withTypes = () => createAsyncThunk\r\n\r\n  return createAsyncThunk as CreateAsyncThunk<AsyncThunkConfig>\r\n})()\r\n\r\ninterface UnwrappableAction {\r\n  payload: any\r\n  meta?: any\r\n  error?: any\r\n}\r\n\r\ntype UnwrappedActionPayload<T extends UnwrappableAction> = Exclude<\r\n  T,\r\n  { error: any }\r\n>['payload']\r\n\r\n/**\r\n * @public\r\n */\r\nexport function unwrapResult<R extends UnwrappableAction>(\r\n  action: R\r\n): UnwrappedActionPayload<R> {\r\n  if (action.meta && action.meta.rejectedWithValue) {\r\n    throw action.payload\r\n  }\r\n  if (action.error) {\r\n    throw action.error\r\n  }\r\n  return action.payload\r\n}\r\n\r\ntype WithStrictNullChecks<True, False> = undefined extends boolean\r\n  ? False\r\n  : True\r\n\r\nfunction isThenable(value: any): value is PromiseLike<any> {\r\n  return (\r\n    value !== null &&\r\n    typeof value === 'object' &&\r\n    typeof value.then === 'function'\r\n  )\r\n}\r\n", "import type {\r\n  Action<PERSON>romMatcher,\r\n  Matcher,\r\n  UnionToIntersection,\r\n} from './tsHelpers'\r\nimport { hasMatchFunction } from './tsHelpers'\r\nimport type {\r\n  AsyncThunk,\r\n  AsyncThunkFulfilledActionCreator,\r\n  AsyncThunkPendingActionCreator,\r\n  AsyncThunkRejectedActionCreator,\r\n} from './createAsyncThunk'\r\n\r\n/** @public */\r\nexport type ActionMatchingAnyOf<Matchers extends [...Matcher<any>[]]> =\r\n  ActionFromMatcher<Matchers[number]>\r\n\r\n/** @public */\r\nexport type ActionMatchingAllOf<Matchers extends [...Matcher<any>[]]> =\r\n  UnionToIntersection<ActionMatchingAnyOf<Matchers>>\r\n\r\nconst matches = (matcher: Matcher<any>, action: any) => {\r\n  if (hasMatchFunction(matcher)) {\r\n    return matcher.match(action)\r\n  } else {\r\n    return matcher(action)\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches any one of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAnyOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAnyOf<Matchers> => {\r\n    return matchers.some((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action matches all of the supplied type guards or action\r\n * creators.\r\n *\r\n * @param matchers The type guards or action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAllOf<Matchers extends [...Matcher<any>[]]>(\r\n  ...matchers: Matchers\r\n) {\r\n  return (action: any): action is ActionMatchingAllOf<Matchers> => {\r\n    return matchers.every((matcher) => matches(matcher, action))\r\n  }\r\n}\r\n\r\n/**\r\n * @param action A redux action\r\n * @param validStatus An array of valid meta.requestStatus values\r\n *\r\n * @internal\r\n */\r\nexport function hasExpectedRequestMetadata(\r\n  action: any,\r\n  validStatus: readonly string[]\r\n) {\r\n  if (!action || !action.meta) return false\r\n\r\n  const hasValidRequestId = typeof action.meta.requestId === 'string'\r\n  const hasValidRequestStatus =\r\n    validStatus.indexOf(action.meta.requestStatus) > -1\r\n\r\n  return hasValidRequestId && hasValidRequestStatus\r\n}\r\n\r\nfunction isAsyncThunkArray(a: [any] | AnyAsyncThunk[]): a is AnyAsyncThunk[] {\r\n  return (\r\n    typeof a[0] === 'function' &&\r\n    'pending' in a[0] &&\r\n    'fulfilled' in a[0] &&\r\n    'rejected' in a[0]\r\n  )\r\n}\r\n\r\nexport type UnknownAsyncThunkPendingAction = ReturnType<\r\n  AsyncThunkPendingActionCreator<unknown>\r\n>\r\n\r\nexport type PendingActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['pending']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is pending.\r\n *\r\n * @public\r\n */\r\nexport function isPending(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkPendingAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is pending.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is PendingActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a pending thunk action\r\n * @public\r\n */\r\nexport function isPending(action: any): action is UnknownAsyncThunkPendingAction\r\nexport function isPending<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['pending'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isPending()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is PendingActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.pending\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected.\r\n *\r\n * @public\r\n */\r\nexport function isRejected(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is RejectedActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action\r\n * @public\r\n */\r\nexport function isRejected(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejected<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejected()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.rejected\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkRejectedWithValueAction = ReturnType<\r\n  AsyncThunkRejectedActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type RejectedWithValueActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['rejected']> &\r\n    (T extends AsyncThunk<any, any, { rejectValue: infer RejectedValue }>\r\n      ? { payload: RejectedValue }\r\n      : unknown)\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is rejected with value.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkRejectedAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is rejected with value.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (\r\n  action: any\r\n) => action is RejectedWithValueActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a rejected thunk action with value\r\n * @public\r\n */\r\nexport function isRejectedWithValue(\r\n  action: any\r\n): action is UnknownAsyncThunkRejectedAction\r\nexport function isRejectedWithValue<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  const hasFlag = (action: any): action is any => {\r\n    return action && action.meta && action.meta.rejectedWithValue\r\n  }\r\n\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => {\r\n      const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n      return combinedMatcher(action)\r\n    }\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isRejectedWithValue()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is RejectedActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    const combinedMatcher = isAllOf(isRejected(...asyncThunks), hasFlag)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkFulfilledAction = ReturnType<\r\n  AsyncThunkFulfilledActionCreator<unknown, unknown>\r\n>\r\n\r\nexport type FulfilledActionFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  ActionFromMatcher<T['fulfilled']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator, and that\r\n * the action is fulfilled.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkFulfilledAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators,\r\n * and that the action is fulfilled.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is FulfilledActionFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a fulfilled thunk action\r\n * @public\r\n */\r\nexport function isFulfilled(\r\n  action: any\r\n): action is UnknownAsyncThunkFulfilledAction\r\nexport function isFulfilled<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) => hasExpectedRequestMetadata(action, ['fulfilled'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isFulfilled()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is FulfilledActionFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = asyncThunks.map(\r\n      (asyncThunk) => asyncThunk.fulfilled\r\n    ) as any\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n\r\nexport type UnknownAsyncThunkAction =\r\n  | UnknownAsyncThunkPendingAction\r\n  | UnknownAsyncThunkRejectedAction\r\n  | UnknownAsyncThunkFulfilledAction\r\n\r\nexport type AnyAsyncThunk = {\r\n  pending: { match: (action: any) => action is any }\r\n  fulfilled: { match: (action: any) => action is any }\r\n  rejected: { match: (action: any) => action is any }\r\n}\r\n\r\nexport type ActionsFromAsyncThunk<T extends AnyAsyncThunk> =\r\n  | ActionFromMatcher<T['pending']>\r\n  | ActionFromMatcher<T['fulfilled']>\r\n  | ActionFromMatcher<T['rejected']>\r\n\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action was created by an async thunk action creator.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(): (\r\n  action: any\r\n) => action is UnknownAsyncThunkAction\r\n/**\r\n * A higher-order function that returns a function that may be used to check\r\n * whether an action belongs to one of the provided async thunk action creators.\r\n *\r\n * @param asyncThunks (optional) The async thunk action creators to match against.\r\n *\r\n * @public\r\n */\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(\r\n  ...asyncThunks: AsyncThunks\r\n): (action: any) => action is ActionsFromAsyncThunk<AsyncThunks[number]>\r\n/**\r\n * Tests if `action` is a thunk action\r\n * @public\r\n */\r\nexport function isAsyncThunkAction(\r\n  action: any\r\n): action is UnknownAsyncThunkAction\r\nexport function isAsyncThunkAction<\r\n  AsyncThunks extends [AnyAsyncThunk, ...AnyAsyncThunk[]]\r\n>(...asyncThunks: AsyncThunks | [any]) {\r\n  if (asyncThunks.length === 0) {\r\n    return (action: any) =>\r\n      hasExpectedRequestMetadata(action, ['pending', 'fulfilled', 'rejected'])\r\n  }\r\n\r\n  if (!isAsyncThunkArray(asyncThunks)) {\r\n    return isAsyncThunkAction()(asyncThunks[0])\r\n  }\r\n\r\n  return (\r\n    action: any\r\n  ): action is ActionsFromAsyncThunk<AsyncThunks[number]> => {\r\n    // note: this type will be correct because we have at least 1 asyncThunk\r\n    const matchers: [Matcher<any>, ...Matcher<any>[]] = [] as any\r\n\r\n    for (const asyncThunk of asyncThunks) {\r\n      matchers.push(\r\n        asyncThunk.pending,\r\n        asyncThunk.rejected,\r\n        asyncThunk.fulfilled\r\n      )\r\n    }\r\n\r\n    const combinedMatcher = isAnyOf(...matchers)\r\n\r\n    return combinedMatcher(action)\r\n  }\r\n}\r\n", "import type { AbortSignalWithReason } from './types'\r\n\r\nexport const assertFunction: (\r\n  func: unknown,\r\n  expected: string\r\n) => asserts func is (...args: unknown[]) => unknown = (\r\n  func: unknown,\r\n  expected: string\r\n) => {\r\n  if (typeof func !== 'function') {\r\n    throw new TypeError(`${expected} is not a function`)\r\n  }\r\n}\r\n\r\nexport const noop = () => {}\r\n\r\nexport const catchRejection = <T>(\r\n  promise: Promise<T>,\r\n  onError = noop\r\n): Promise<T> => {\r\n  promise.catch(onError)\r\n\r\n  return promise\r\n}\r\n\r\nexport const addAbortSignalListener = (\r\n  abortSignal: AbortSignal,\r\n  callback: (evt: Event) => void\r\n) => {\r\n  abortSignal.addEventListener('abort', callback, { once: true })\r\n  return () => abortSignal.removeEventListener('abort', callback)\r\n}\r\n\r\n/**\r\n * Calls `abortController.abort(reason)` and patches `signal.reason`.\r\n * if it is not supported.\r\n *\r\n * At the time of writing `signal.reason` is available in FF chrome, edge node 17 and deno.\r\n * @param abortController\r\n * @param reason\r\n * @returns\r\n * @see https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/reason\r\n */\r\nexport const abortControllerWithReason = <T>(\r\n  abortController: AbortController,\r\n  reason: T\r\n): void => {\r\n  type Consumer<T> = (val: T) => void\r\n\r\n  const signal = abortController.signal as AbortSignalWithReason<T>\r\n\r\n  if (signal.aborted) {\r\n    return\r\n  }\r\n\r\n  // Patch `reason` if necessary.\r\n  // - We use defineProperty here because reason is a getter of `AbortSignal.__proto__`.\r\n  // - We need to patch 'reason' before calling `.abort()` because listeners to the 'abort'\r\n  // event are are notified immediately.\r\n  if (!('reason' in signal)) {\r\n    Object.defineProperty(signal, 'reason', {\r\n      enumerable: true,\r\n      value: reason,\r\n      configurable: true,\r\n      writable: true,\r\n    })\r\n  }\r\n\r\n  ;(abortController.abort as Consumer<typeof reason>)(reason)\r\n}\r\n", "import type { SerializedError } from '@reduxjs/toolkit'\r\n\r\nconst task = 'task'\r\nconst listener = 'listener'\r\nconst completed = 'completed'\r\nconst cancelled = 'cancelled'\r\n\r\n/* TaskAbortError error codes  */\r\nexport const taskCancelled = `task-${cancelled}` as const\r\nexport const taskCompleted = `task-${completed}` as const\r\nexport const listenerCancelled = `${listener}-${cancelled}` as const\r\nexport const listenerCompleted = `${listener}-${completed}` as const\r\n\r\nexport class TaskAbortError implements SerializedError {\r\n  name = 'TaskAbortError'\r\n  message: string\r\n  constructor(public code: string | undefined) {\r\n    this.message = `${task} ${cancelled} (reason: ${code})`\r\n  }\r\n}\r\n", "import { TaskAbortError } from './exceptions'\r\nimport type { AbortSignalWithReason, TaskResult } from './types'\r\nimport { addAbortSignalListener, catchRejection, noop } from './utils'\r\n\r\n/**\r\n * Synchronously raises {@link TaskAbortError} if the task tied to the input `signal` has been cancelled.\r\n * @param signal\r\n * @param reason\r\n * @see {TaskAbortError}\r\n */\r\nexport const validateActive = (signal: AbortSignal): void => {\r\n  if (signal.aborted) {\r\n    throw new TaskAbortError((signal as AbortSignalWithReason<string>).reason)\r\n  }\r\n}\r\n\r\n/**\r\n * Generates a race between the promise(s) and the AbortSignal\r\n * This avoids `Promise.race()`-related memory leaks:\r\n * https://github.com/nodejs/node/issues/17469#issuecomment-349794909\r\n */\r\nexport function raceWithSignal<T>(\r\n  signal: AbortSignalWithReason<string>,\r\n  promise: Promise<T>\r\n): Promise<T> {\r\n  let cleanup = noop\r\n  return new Promise<T>((resolve, reject) => {\r\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason))\r\n\r\n    if (signal.aborted) {\r\n      notifyRejection()\r\n      return\r\n    }\r\n\r\n    cleanup = addAbortSignalListener(signal, notifyRejection)\r\n    promise.finally(() => cleanup()).then(resolve, reject)\r\n  }).finally(() => {\r\n    // after this point, replace `cleanup` with a noop, so there is no reference to `signal` any more\r\n    cleanup = noop\r\n  })\r\n}\r\n\r\n/**\r\n * Runs a task and returns promise that resolves to {@link TaskResult}.\r\n * Second argument is an optional `cleanUp` function that always runs after task.\r\n *\r\n * **Note:** `runTask` runs the executor in the next microtask.\r\n * @returns\r\n */\r\nexport const runTask = async <T>(\r\n  task: () => Promise<T>,\r\n  cleanUp?: () => void\r\n): Promise<TaskResult<T>> => {\r\n  try {\r\n    await Promise.resolve()\r\n    const value = await task()\r\n    return {\r\n      status: 'ok',\r\n      value,\r\n    }\r\n  } catch (error: any) {\r\n    return {\r\n      status: error instanceof TaskAbortError ? 'cancelled' : 'rejected',\r\n      error,\r\n    }\r\n  } finally {\r\n    cleanUp?.()\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and a promise returns another promise that resolves\r\n * as soon the input promise is provided or rejects as soon as\r\n * `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createPause = <T>(signal: AbortSignal) => {\r\n  return (promise: Promise<T>): Promise<T> => {\r\n    return catchRejection(\r\n      raceWithSignal(signal, promise).then((output) => {\r\n        validateActive(signal)\r\n        return output\r\n      })\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * Given an input `AbortSignal` and `timeoutMs` returns a promise that resolves\r\n * after `timeoutMs` or rejects as soon as `AbortSignal.abort` is `true`.\r\n * @param signal\r\n * @returns\r\n */\r\nexport const createDelay = (signal: AbortSignal) => {\r\n  const pause = createPause<void>(signal)\r\n  return (timeoutMs: number): Promise<void> => {\r\n    return pause(new Promise<void>((resolve) => setTimeout(resolve, timeoutMs)))\r\n  }\r\n}\r\n", "import type { Dispatch, AnyAction, MiddlewareAPI } from 'redux'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport { createAction, isAction } from '../createAction'\r\nimport { nanoid } from '../nanoid'\r\n\r\nimport type {\r\n  ListenerMiddleware,\r\n  ListenerMiddlewareInstance,\r\n  AddListenerOverloads,\r\n  AnyListenerPredicate,\r\n  CreateListenerMiddlewareOptions,\r\n  TypedAddListener,\r\n  TypedCreateListenerEntry,\r\n  FallbackAddListenerOptions,\r\n  ListenerEntry,\r\n  ListenerErrorHandler,\r\n  UnsubscribeListener,\r\n  TakePattern,\r\n  ListenerErrorInfo,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  TypedRemoveListener,\r\n  TaskResult,\r\n  AbortSignalWithReason,\r\n  UnsubscribeListenerOptions,\r\n  ForkOptions,\r\n} from './types'\r\nimport {\r\n  abortControllerWithReason,\r\n  addAbortSignalListener,\r\n  assertFunction,\r\n  catchRejection,\r\n} from './utils'\r\nimport {\r\n  listenerCancelled,\r\n  listenerCompleted,\r\n  TaskAbortError,\r\n  taskCancelled,\r\n  taskCompleted,\r\n} from './exceptions'\r\nimport {\r\n  runTask,\r\n  validateActive,\r\n  createPause,\r\n  createDelay,\r\n  raceWithSignal,\r\n} from './task'\r\nexport { TaskAbortError } from './exceptions'\r\nexport type {\r\n  ListenerEffect,\r\n  ListenerMiddleware,\r\n  ListenerEffectAPI,\r\n  ListenerMiddlewareInstance,\r\n  CreateListenerMiddlewareOptions,\r\n  ListenerErrorHandler,\r\n  TypedStartListening,\r\n  TypedAddListener,\r\n  TypedStopListening,\r\n  TypedRemoveListener,\r\n  UnsubscribeListener,\r\n  UnsubscribeListenerOptions,\r\n  ForkedTaskExecutor,\r\n  ForkedTask,\r\n  ForkedTaskAPI,\r\n  AsyncTaskExecutor,\r\n  SyncTaskExecutor,\r\n  TaskCancelled,\r\n  TaskRejected,\r\n  TaskResolved,\r\n  TaskResult,\r\n} from './types'\r\n\r\n//Overly-aggressive byte-shaving\r\nconst { assign } = Object\r\n/**\r\n * @internal\r\n */\r\nconst INTERNAL_NIL_TOKEN = {} as const\r\n\r\nconst alm = 'listenerMiddleware' as const\r\n\r\nconst createFork = (\r\n  parentAbortSignal: AbortSignalWithReason<unknown>,\r\n  parentBlockingPromises: Promise<any>[]\r\n) => {\r\n  const linkControllers = (controller: AbortController) =>\r\n    addAbortSignalListener(parentAbortSignal, () =>\r\n      abortControllerWithReason(controller, parentAbortSignal.reason)\r\n    )\r\n\r\n  return <T>(\r\n    taskExecutor: ForkedTaskExecutor<T>,\r\n    opts?: ForkOptions\r\n  ): ForkedTask<T> => {\r\n    assertFunction(taskExecutor, 'taskExecutor')\r\n    const childAbortController = new AbortController()\r\n\r\n    linkControllers(childAbortController)\r\n\r\n    const result = runTask<T>(\r\n      async (): Promise<T> => {\r\n        validateActive(parentAbortSignal)\r\n        validateActive(childAbortController.signal)\r\n        const result = (await taskExecutor({\r\n          pause: createPause(childAbortController.signal),\r\n          delay: createDelay(childAbortController.signal),\r\n          signal: childAbortController.signal,\r\n        })) as T\r\n        validateActive(childAbortController.signal)\r\n        return result\r\n      },\r\n      () => abortControllerWithReason(childAbortController, taskCompleted)\r\n    )\r\n\r\n    if (opts?.autoJoin) {\r\n      parentBlockingPromises.push(result)\r\n    }\r\n\r\n    return {\r\n      result: createPause<TaskResult<T>>(parentAbortSignal)(result),\r\n      cancel() {\r\n        abortControllerWithReason(childAbortController, taskCancelled)\r\n      },\r\n    }\r\n  }\r\n}\r\n\r\nconst createTakePattern = <S>(\r\n  startListening: AddListenerOverloads<\r\n    UnsubscribeListener,\r\n    S,\r\n    Dispatch<AnyAction>\r\n  >,\r\n  signal: AbortSignal\r\n): TakePattern<S> => {\r\n  /**\r\n   * A function that takes a ListenerPredicate and an optional timeout,\r\n   * and resolves when either the predicate returns `true` based on an action\r\n   * state combination or when the timeout expires.\r\n   * If the parent listener is canceled while waiting, this will throw a\r\n   * TaskAbortError.\r\n   */\r\n  const take = async <P extends AnyListenerPredicate<S>>(\r\n    predicate: P,\r\n    timeout: number | undefined\r\n  ) => {\r\n    validateActive(signal)\r\n\r\n    // Placeholder unsubscribe function until the listener is added\r\n    let unsubscribe: UnsubscribeListener = () => {}\r\n\r\n    const tuplePromise = new Promise<[AnyAction, S, S]>((resolve, reject) => {\r\n      // Inside the Promise, we synchronously add the listener.\r\n      let stopListening = startListening({\r\n        predicate: predicate as any,\r\n        effect: (action, listenerApi): void => {\r\n          // One-shot listener that cleans up as soon as the predicate passes\r\n          listenerApi.unsubscribe()\r\n          // Resolve the promise with the same arguments the predicate saw\r\n          resolve([\r\n            action,\r\n            listenerApi.getState(),\r\n            listenerApi.getOriginalState(),\r\n          ])\r\n        },\r\n      })\r\n      unsubscribe = () => {\r\n        stopListening()\r\n        reject()\r\n      }\r\n    })\r\n\r\n    const promises: (Promise<null> | Promise<[AnyAction, S, S]>)[] = [\r\n      tuplePromise,\r\n    ]\r\n\r\n    if (timeout != null) {\r\n      promises.push(\r\n        new Promise<null>((resolve) => setTimeout(resolve, timeout, null))\r\n      )\r\n    }\r\n\r\n    try {\r\n      const output = await raceWithSignal(signal, Promise.race(promises))\r\n\r\n      validateActive(signal)\r\n      return output\r\n    } finally {\r\n      // Always clean up the listener\r\n      unsubscribe()\r\n    }\r\n  }\r\n\r\n  return ((predicate: AnyListenerPredicate<S>, timeout: number | undefined) =>\r\n    catchRejection(take(predicate, timeout))) as TakePattern<S>\r\n}\r\n\r\nconst getListenerEntryPropsFrom = (options: FallbackAddListenerOptions) => {\r\n  let { type, actionCreator, matcher, predicate, effect } = options\r\n\r\n  if (type) {\r\n    predicate = createAction(type).match\r\n  } else if (actionCreator) {\r\n    type = actionCreator!.type\r\n    predicate = actionCreator.match\r\n  } else if (matcher) {\r\n    predicate = matcher\r\n  } else if (predicate) {\r\n    // pass\r\n  } else {\r\n    throw new Error(\r\n      'Creating or removing a listener requires one of the known fields for matching an action'\r\n    )\r\n  }\r\n\r\n  assertFunction(effect, 'options.listener')\r\n\r\n  return { predicate, type, effect }\r\n}\r\n\r\n/** Accepts the possible options for creating a listener, and returns a formatted listener entry */\r\nexport const createListenerEntry: TypedCreateListenerEntry<unknown> = (\r\n  options: FallbackAddListenerOptions\r\n) => {\r\n  const { type, predicate, effect } = getListenerEntryPropsFrom(options)\r\n\r\n  const id = nanoid()\r\n  const entry: ListenerEntry<unknown> = {\r\n    id,\r\n    effect,\r\n    type,\r\n    predicate,\r\n    pending: new Set<AbortController>(),\r\n    unsubscribe: () => {\r\n      throw new Error('Unsubscribe not initialized')\r\n    },\r\n  }\r\n\r\n  return entry\r\n}\r\n\r\nconst cancelActiveListeners = (\r\n  entry: ListenerEntry<unknown, Dispatch<AnyAction>>\r\n) => {\r\n  entry.pending.forEach((controller) => {\r\n    abortControllerWithReason(controller, listenerCancelled)\r\n  })\r\n}\r\n\r\nconst createClearListenerMiddleware = (\r\n  listenerMap: Map<string, ListenerEntry>\r\n) => {\r\n  return () => {\r\n    listenerMap.forEach(cancelActiveListeners)\r\n\r\n    listenerMap.clear()\r\n  }\r\n}\r\n\r\n/**\r\n * Safely reports errors to the `errorHandler` provided.\r\n * Errors that occur inside `errorHandler` are notified in a new task.\r\n * Inspired by [rxjs reportUnhandledError](https://github.com/ReactiveX/rxjs/blob/6fafcf53dc9e557439b25debaeadfd224b245a66/src/internal/util/reportUnhandledError.ts)\r\n * @param errorHandler\r\n * @param errorToNotify\r\n */\r\nconst safelyNotifyError = (\r\n  errorHandler: ListenerErrorHandler,\r\n  errorToNotify: unknown,\r\n  errorInfo: ListenerErrorInfo\r\n): void => {\r\n  try {\r\n    errorHandler(errorToNotify, errorInfo)\r\n  } catch (errorHandlerError) {\r\n    // We cannot let an error raised here block the listener queue.\r\n    // The error raised here will be picked up by `window.onerror`, `process.on('error')` etc...\r\n    setTimeout(() => {\r\n      throw errorHandlerError\r\n    }, 0)\r\n  }\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport const addListener = createAction(\r\n  `${alm}/add`\r\n) as TypedAddListener<unknown>\r\n\r\n/**\r\n * @public\r\n */\r\nexport const clearAllListeners = createAction(`${alm}/removeAll`)\r\n\r\n/**\r\n * @public\r\n */\r\nexport const removeListener = createAction(\r\n  `${alm}/remove`\r\n) as TypedRemoveListener<unknown>\r\n\r\nconst defaultErrorHandler: ListenerErrorHandler = (...args: unknown[]) => {\r\n  console.error(`${alm}/error`, ...args)\r\n}\r\n\r\n/**\r\n * @public\r\n */\r\nexport function createListenerMiddleware<\r\n  S = unknown,\r\n  D extends Dispatch<AnyAction> = ThunkDispatch<S, unknown, AnyAction>,\r\n  ExtraArgument = unknown\r\n>(middlewareOptions: CreateListenerMiddlewareOptions<ExtraArgument> = {}) {\r\n  const listenerMap = new Map<string, ListenerEntry>()\r\n  const { extra, onError = defaultErrorHandler } = middlewareOptions\r\n\r\n  assertFunction(onError, 'onError')\r\n\r\n  const insertEntry = (entry: ListenerEntry) => {\r\n    entry.unsubscribe = () => listenerMap.delete(entry!.id)\r\n\r\n    listenerMap.set(entry.id, entry)\r\n    return (cancelOptions?: UnsubscribeListenerOptions) => {\r\n      entry.unsubscribe()\r\n      if (cancelOptions?.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n  }\r\n\r\n  const findListenerEntry = (\r\n    comparator: (entry: ListenerEntry) => boolean\r\n  ): ListenerEntry | undefined => {\r\n    for (const entry of Array.from(listenerMap.values())) {\r\n      if (comparator(entry)) {\r\n        return entry\r\n      }\r\n    }\r\n\r\n    return undefined\r\n  }\r\n\r\n  const startListening = (options: FallbackAddListenerOptions) => {\r\n    let entry = findListenerEntry(\r\n      (existingEntry) => existingEntry.effect === options.effect\r\n    )\r\n\r\n    if (!entry) {\r\n      entry = createListenerEntry(options as any)\r\n    }\r\n\r\n    return insertEntry(entry)\r\n  }\r\n\r\n  const stopListening = (\r\n    options: FallbackAddListenerOptions & UnsubscribeListenerOptions\r\n  ): boolean => {\r\n    const { type, effect, predicate } = getListenerEntryPropsFrom(options)\r\n\r\n    const entry = findListenerEntry((entry) => {\r\n      const matchPredicateOrType =\r\n        typeof type === 'string'\r\n          ? entry.type === type\r\n          : entry.predicate === predicate\r\n\r\n      return matchPredicateOrType && entry.effect === effect\r\n    })\r\n\r\n    if (entry) {\r\n      entry.unsubscribe()\r\n      if (options.cancelActive) {\r\n        cancelActiveListeners(entry)\r\n      }\r\n    }\r\n\r\n    return !!entry\r\n  }\r\n\r\n  const notifyListener = async (\r\n    entry: ListenerEntry<unknown, Dispatch<AnyAction>>,\r\n    action: AnyAction,\r\n    api: MiddlewareAPI,\r\n    getOriginalState: () => S\r\n  ) => {\r\n    const internalTaskController = new AbortController()\r\n    const take = createTakePattern(\r\n      startListening,\r\n      internalTaskController.signal\r\n    )\r\n    const autoJoinPromises: Promise<any>[] = []\r\n\r\n    try {\r\n      entry.pending.add(internalTaskController)\r\n      await Promise.resolve(\r\n        entry.effect(\r\n          action,\r\n          // Use assign() rather than ... to avoid extra helper functions added to bundle\r\n          assign({}, api, {\r\n            getOriginalState,\r\n            condition: (\r\n              predicate: AnyListenerPredicate<any>,\r\n              timeout?: number\r\n            ) => take(predicate, timeout).then(Boolean),\r\n            take,\r\n            delay: createDelay(internalTaskController.signal),\r\n            pause: createPause<any>(internalTaskController.signal),\r\n            extra,\r\n            signal: internalTaskController.signal,\r\n            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n            unsubscribe: entry.unsubscribe,\r\n            subscribe: () => {\r\n              listenerMap.set(entry.id, entry)\r\n            },\r\n            cancelActiveListeners: () => {\r\n              entry.pending.forEach((controller, _, set) => {\r\n                if (controller !== internalTaskController) {\r\n                  abortControllerWithReason(controller, listenerCancelled)\r\n                  set.delete(controller)\r\n                }\r\n              })\r\n            },\r\n          })\r\n        )\r\n      )\r\n    } catch (listenerError) {\r\n      if (!(listenerError instanceof TaskAbortError)) {\r\n        safelyNotifyError(onError, listenerError, {\r\n          raisedBy: 'effect',\r\n        })\r\n      }\r\n    } finally {\r\n      await Promise.allSettled(autoJoinPromises)\r\n\r\n      abortControllerWithReason(internalTaskController, listenerCompleted) // Notify that the task has completed\r\n      entry.pending.delete(internalTaskController)\r\n    }\r\n  }\r\n\r\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap)\r\n\r\n  const middleware: ListenerMiddleware<S, D, ExtraArgument> =\r\n    (api) => (next) => (action) => {\r\n      if (!isAction(action)) {\r\n        // we only want to notify listeners for action objects\r\n        return next(action)\r\n      }\r\n\r\n      if (addListener.match(action)) {\r\n        return startListening(action.payload)\r\n      }\r\n\r\n      if (clearAllListeners.match(action)) {\r\n        clearListenerMiddleware()\r\n        return\r\n      }\r\n\r\n      if (removeListener.match(action)) {\r\n        return stopListening(action.payload)\r\n      }\r\n\r\n      // Need to get this state _before_ the reducer processes the action\r\n      let originalState: S | typeof INTERNAL_NIL_TOKEN = api.getState()\r\n\r\n      // `getOriginalState` can only be called synchronously.\r\n      // @see https://github.com/reduxjs/redux-toolkit/discussions/1648#discussioncomment-1932820\r\n      const getOriginalState = (): S => {\r\n        if (originalState === INTERNAL_NIL_TOKEN) {\r\n          throw new Error(\r\n            `${alm}: getOriginalState can only be called synchronously`\r\n          )\r\n        }\r\n\r\n        return originalState as S\r\n      }\r\n\r\n      let result: unknown\r\n\r\n      try {\r\n        // Actually forward the action to the reducer before we handle listeners\r\n        result = next(action)\r\n\r\n        if (listenerMap.size > 0) {\r\n          let currentState = api.getState()\r\n          // Work around ESBuild+TS transpilation issue\r\n          const listenerEntries = Array.from(listenerMap.values())\r\n          for (let entry of listenerEntries) {\r\n            let runListener = false\r\n\r\n            try {\r\n              runListener = entry.predicate(action, currentState, originalState)\r\n            } catch (predicateError) {\r\n              runListener = false\r\n\r\n              safelyNotifyError(onError, predicateError, {\r\n                raisedBy: 'predicate',\r\n              })\r\n            }\r\n\r\n            if (!runListener) {\r\n              continue\r\n            }\r\n\r\n            notifyListener(entry, action, api, getOriginalState)\r\n          }\r\n        }\r\n      } finally {\r\n        // Remove `originalState` store from this scope.\r\n        originalState = INTERNAL_NIL_TOKEN\r\n      }\r\n\r\n      return result\r\n    }\r\n\r\n  return {\r\n    middleware,\r\n    startListening,\r\n    stopListening,\r\n    clearListeners: clearListenerMiddleware,\r\n  } as ListenerMiddlewareInstance<S, D, ExtraArgument>\r\n}\r\n", "import type { StoreEnhancer } from 'redux'\r\n\r\nexport const SHOULD_AUTOBATCH = 'RTK_autoBatch'\r\n\r\nexport const prepareAutoBatched =\r\n  <T>() =>\r\n  (payload: T): { payload: T; meta: unknown } => ({\r\n    payload,\r\n    meta: { [SHOULD_AUTOBATCH]: true },\r\n  })\r\n\r\n// TODO Remove this in 2.0\r\n// Copied from https://github.com/feross/queue-microtask\r\nlet promise: Promise<any>\r\nconst queueMicrotaskShim =\r\n  typeof queueMicrotask === 'function'\r\n    ? queueMicrotask.bind(\r\n        typeof window !== 'undefined'\r\n          ? window\r\n          : typeof global !== 'undefined'\r\n          ? global\r\n          : globalThis\r\n      )\r\n    : // reuse resolved promise, and allocate it lazily\r\n      (cb: () => void) =>\r\n        (promise || (promise = Promise.resolve())).then(cb).catch((err: any) =>\r\n          setTimeout(() => {\r\n            throw err\r\n          }, 0)\r\n        )\r\n\r\nconst createQueueWithTimer = (timeout: number) => {\r\n  return (notify: () => void) => {\r\n    setTimeout(notify, timeout)\r\n  }\r\n}\r\n\r\n// requestAnimationFrame won't exist in SSR environments.\r\n// Fall back to a vague approximation just to keep from erroring.\r\nconst rAF =\r\n  typeof window !== 'undefined' && window.requestAnimationFrame\r\n    ? window.requestAnimationFrame\r\n    : createQueueWithTimer(10)\r\n\r\nexport type AutoBatchOptions =\r\n  | { type: 'tick' }\r\n  | { type: 'timer'; timeout: number }\r\n  | { type: 'raf' }\r\n  | { type: 'callback'; queueNotification: (notify: () => void) => void }\r\n\r\n/**\r\n * A Redux store enhancer that watches for \"low-priority\" actions, and delays\r\n * notifying subscribers until either the queued callback executes or the\r\n * next \"standard-priority\" action is dispatched.\r\n *\r\n * This allows dispatching multiple \"low-priority\" actions in a row with only\r\n * a single subscriber notification to the UI after the sequence of actions\r\n * is finished, thus improving UI re-render performance.\r\n *\r\n * Watches for actions with the `action.meta[SHOULD_AUTOBATCH]` attribute.\r\n * This can be added to `action.meta` manually, or by using the\r\n * `prepareAutoBatched` helper.\r\n *\r\n * By default, it will queue a notification for the end of the event loop tick.\r\n * However, you can pass several other options to configure the behavior:\r\n * - `{type: 'tick'}: queues using `queueMicrotask` (default)\r\n * - `{type: 'timer, timeout: number}`: queues using `setTimeout`\r\n * - `{type: 'raf'}`: queues using `requestAnimationFrame`\r\n * - `{type: 'callback', queueNotification: (notify: () => void) => void}: lets you provide your own callback\r\n *\r\n *\r\n */\r\nexport const autoBatchEnhancer =\r\n  (options: AutoBatchOptions = { type: 'raf' }): StoreEnhancer =>\r\n  (next) =>\r\n  (...args) => {\r\n    const store = next(...args)\r\n\r\n    let notifying = true\r\n    let shouldNotifyAtEndOfTick = false\r\n    let notificationQueued = false\r\n\r\n    const listeners = new Set<() => void>()\r\n\r\n    const queueCallback =\r\n      options.type === 'tick'\r\n        ? queueMicrotaskShim\r\n        : options.type === 'raf'\r\n        ? rAF\r\n        : options.type === 'callback'\r\n        ? options.queueNotification\r\n        : createQueueWithTimer(options.timeout)\r\n\r\n    const notifyListeners = () => {\r\n      // We're running at the end of the event loop tick.\r\n      // Run the real listener callbacks to actually update the UI.\r\n      notificationQueued = false\r\n      if (shouldNotifyAtEndOfTick) {\r\n        shouldNotifyAtEndOfTick = false\r\n        listeners.forEach((l) => l())\r\n      }\r\n    }\r\n\r\n    return Object.assign({}, store, {\r\n      // Override the base `store.subscribe` method to keep original listeners\r\n      // from running if we're delaying notifications\r\n      subscribe(listener: () => void) {\r\n        // Each wrapped listener will only call the real listener if\r\n        // the `notifying` flag is currently active when it's called.\r\n        // This lets the base store work as normal, while the actual UI\r\n        // update becomes controlled by this enhancer.\r\n        const wrappedListener: typeof listener = () => notifying && listener()\r\n        const unsubscribe = store.subscribe(wrappedListener)\r\n        listeners.add(listener)\r\n        return () => {\r\n          unsubscribe()\r\n          listeners.delete(listener)\r\n        }\r\n      },\r\n      // Override the base `store.dispatch` method so that we can check actions\r\n      // for the `shouldAutoBatch` flag and determine if batching is active\r\n      dispatch(action: any) {\r\n        try {\r\n          // If the action does _not_ have the `shouldAutoBatch` flag,\r\n          // we resume/continue normal notify-after-each-dispatch behavior\r\n          notifying = !action?.meta?.[SHOULD_AUTOBATCH]\r\n          // If a `notifyListeners` microtask was queued, you can't cancel it.\r\n          // Instead, we set a flag so that it's a no-op when it does run\r\n          shouldNotifyAtEndOfTick = !notifying\r\n          if (shouldNotifyAtEndOfTick) {\r\n            // We've seen at least 1 action with `SHOULD_AUTOBATCH`. Try to queue\r\n            // a microtask to notify listeners at the end of the event loop tick.\r\n            // Make sure we only enqueue this _once_ per tick.\r\n            if (!notificationQueued) {\r\n              notificationQueued = true\r\n              queueCallback(notifyListeners)\r\n            }\r\n          }\r\n          // Go ahead and process the action as usual, including reducers.\r\n          // If normal notification behavior is enabled, the store will notify\r\n          // all of its own listeners, and the wrapper callbacks above will\r\n          // see `notifying` is true and pass on to the real listener callbacks.\r\n          // If we're \"batching\" behavior, then the wrapped callbacks will\r\n          // bail out, causing the base store notification behavior to be no-ops.\r\n          return store.dispatch(action)\r\n        } finally {\r\n          // Assume we're back to normal behavior after each action\r\n          notifying = true\r\n        }\r\n      },\r\n    })\r\n  }\r\n", "var __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        if (typeof b !== \"function\" && b !== null)\r\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nvar __defProp = Object.defineProperty;\r\nvar __defProps = Object.defineProperties;\r\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\r\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\r\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\r\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\r\nvar __defNormalProp = function (obj, key, value) { return key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value: value }) : obj[key] = value; };\r\nvar __spreadValues = function (a, b) {\r\n    for (var prop in b || (b = {}))\r\n        if (__hasOwnProp.call(b, prop))\r\n            __defNormalProp(a, prop, b[prop]);\r\n    if (__getOwnPropSymbols)\r\n        for (var _i = 0, _c = __getOwnPropSymbols(b); _i < _c.length; _i++) {\r\n            var prop = _c[_i];\r\n            if (__propIsEnum.call(b, prop))\r\n                __defNormalProp(a, prop, b[prop]);\r\n        }\r\n    return a;\r\n};\r\nvar __spreadProps = function (a, b) { return __defProps(a, __getOwnPropDescs(b)); };\r\nvar __async = function (__this, __arguments, generator) {\r\n    return new Promise(function (resolve, reject) {\r\n        var fulfilled = function (value) {\r\n            try {\r\n                step(generator.next(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var rejected = function (value) {\r\n            try {\r\n                step(generator.throw(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var step = function (x) { return x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected); };\r\n        step((generator = generator.apply(__this, __arguments)).next());\r\n    });\r\n};\r\n// src/index.ts\r\nimport { enableES5 } from \"immer\";\r\nexport * from \"redux\";\r\nimport { default as default2, current as current2, freeze, original, isDraft as isDraft4 } from \"immer\";\r\nimport { createSelector as createSelector2 } from \"reselect\";\r\n// src/createDraftSafeSelector.ts\r\nimport { current, isDraft } from \"immer\";\r\nimport { createSelector } from \"reselect\";\r\nvar createDraftSafeSelector = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    var selector = createSelector.apply(void 0, args);\r\n    var wrappedSelector = function (value) {\r\n        var rest = [];\r\n        for (var _i = 1; _i < arguments.length; _i++) {\r\n            rest[_i - 1] = arguments[_i];\r\n        }\r\n        return selector.apply(void 0, __spreadArray([isDraft(value) ? current(value) : value], rest));\r\n    };\r\n    return wrappedSelector;\r\n};\r\n// src/configureStore.ts\r\nimport { createStore, compose as compose2, applyMiddleware, combineReducers } from \"redux\";\r\n// src/devtoolsExtension.ts\r\nimport { compose } from \"redux\";\r\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function () {\r\n    if (arguments.length === 0)\r\n        return void 0;\r\n    if (typeof arguments[0] === \"object\")\r\n        return compose;\r\n    return compose.apply(null, arguments);\r\n};\r\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function () {\r\n    return function (noop2) {\r\n        return noop2;\r\n    };\r\n};\r\n// src/isPlainObject.ts\r\nfunction isPlainObject(value) {\r\n    if (typeof value !== \"object\" || value === null)\r\n        return false;\r\n    var proto = Object.getPrototypeOf(value);\r\n    if (proto === null)\r\n        return true;\r\n    var baseProto = proto;\r\n    while (Object.getPrototypeOf(baseProto) !== null) {\r\n        baseProto = Object.getPrototypeOf(baseProto);\r\n    }\r\n    return proto === baseProto;\r\n}\r\n// src/getDefaultMiddleware.ts\r\nimport thunkMiddleware from \"redux-thunk\";\r\n// src/tsHelpers.ts\r\nvar hasMatchFunction = function (v) {\r\n    return v && typeof v.match === \"function\";\r\n};\r\n// src/createAction.ts\r\nfunction createAction(type, prepareAction) {\r\n    function actionCreator() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        if (prepareAction) {\r\n            var prepared = prepareAction.apply(void 0, args);\r\n            if (!prepared) {\r\n                throw new Error(\"prepareAction did not return an object\");\r\n            }\r\n            return __spreadValues(__spreadValues({\r\n                type: type,\r\n                payload: prepared.payload\r\n            }, \"meta\" in prepared && { meta: prepared.meta }), \"error\" in prepared && { error: prepared.error });\r\n        }\r\n        return { type: type, payload: args[0] };\r\n    }\r\n    actionCreator.toString = function () { return \"\" + type; };\r\n    actionCreator.type = type;\r\n    actionCreator.match = function (action) { return action.type === type; };\r\n    return actionCreator;\r\n}\r\nfunction isAction(action) {\r\n    return isPlainObject(action) && \"type\" in action;\r\n}\r\nfunction isActionCreator(action) {\r\n    return typeof action === \"function\" && \"type\" in action && hasMatchFunction(action);\r\n}\r\nfunction isFSA(action) {\r\n    return isAction(action) && typeof action.type === \"string\" && Object.keys(action).every(isValidKey);\r\n}\r\nfunction isValidKey(key) {\r\n    return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\r\n}\r\nfunction getType(actionCreator) {\r\n    return \"\" + actionCreator;\r\n}\r\n// src/actionCreatorInvariantMiddleware.ts\r\nfunction getMessage(type) {\r\n    var splitType = type ? (\"\" + type).split(\"/\") : [];\r\n    var actionName = splitType[splitType.length - 1] || \"actionCreator\";\r\n    return \"Detected an action creator with type \\\"\" + (type || \"unknown\") + \"\\\" being dispatched. \\nMake sure you're calling the action creator before dispatching, i.e. `dispatch(\" + actionName + \"())` instead of `dispatch(\" + actionName + \")`. This is necessary even if the action has no payload.\";\r\n}\r\nfunction createActionCreatorInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isActionCreator, isActionCreator2 = _c === void 0 ? isActionCreator : _c;\r\n    return function () { return function (next) { return function (action) {\r\n        if (isActionCreator2(action)) {\r\n            console.warn(getMessage(action.type));\r\n        }\r\n        return next(action);\r\n    }; }; };\r\n}\r\n// src/utils.ts\r\nimport createNextState, { isDraftable } from \"immer\";\r\nfunction getTimeMeasureUtils(maxDelay, fnName) {\r\n    var elapsed = 0;\r\n    return {\r\n        measureTime: function (fn) {\r\n            var started = Date.now();\r\n            try {\r\n                return fn();\r\n            }\r\n            finally {\r\n                var finished = Date.now();\r\n                elapsed += finished - started;\r\n            }\r\n        },\r\n        warnIfExceeded: function () {\r\n            if (elapsed > maxDelay) {\r\n                console.warn(fnName + \" took \" + elapsed + \"ms, which is more than the warning threshold of \" + maxDelay + \"ms. \\nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\\nIt is disabled in production builds, so you don't need to worry about that.\");\r\n            }\r\n        }\r\n    };\r\n}\r\nvar MiddlewareArray = /** @class */ (function (_super) {\r\n    __extends(MiddlewareArray, _super);\r\n    function MiddlewareArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, MiddlewareArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(MiddlewareArray, Symbol.species, {\r\n        get: function () {\r\n            return MiddlewareArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    MiddlewareArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    MiddlewareArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (MiddlewareArray.bind.apply(MiddlewareArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return MiddlewareArray;\r\n}(Array));\r\nvar EnhancerArray = /** @class */ (function (_super) {\r\n    __extends(EnhancerArray, _super);\r\n    function EnhancerArray() {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var _this = _super.apply(this, args) || this;\r\n        Object.setPrototypeOf(_this, EnhancerArray.prototype);\r\n        return _this;\r\n    }\r\n    Object.defineProperty(EnhancerArray, Symbol.species, {\r\n        get: function () {\r\n            return EnhancerArray;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    EnhancerArray.prototype.concat = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        return _super.prototype.concat.apply(this, arr);\r\n    };\r\n    EnhancerArray.prototype.prepend = function () {\r\n        var arr = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            arr[_i] = arguments[_i];\r\n        }\r\n        if (arr.length === 1 && Array.isArray(arr[0])) {\r\n            return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr[0].concat(this))))();\r\n        }\r\n        return new (EnhancerArray.bind.apply(EnhancerArray, __spreadArray([void 0], arr.concat(this))))();\r\n    };\r\n    return EnhancerArray;\r\n}(Array));\r\nfunction freezeDraftable(val) {\r\n    return isDraftable(val) ? createNextState(val, function () {\r\n    }) : val;\r\n}\r\n// src/immutableStateInvariantMiddleware.ts\r\nvar isProduction = process.env.NODE_ENV === \"production\";\r\nvar prefix = \"Invariant failed\";\r\nfunction invariant(condition, message) {\r\n    if (condition) {\r\n        return;\r\n    }\r\n    if (isProduction) {\r\n        throw new Error(prefix);\r\n    }\r\n    throw new Error(prefix + \": \" + (message || \"\"));\r\n}\r\nfunction stringify(obj, serializer, indent, decycler) {\r\n    return JSON.stringify(obj, getSerialize(serializer, decycler), indent);\r\n}\r\nfunction getSerialize(serializer, decycler) {\r\n    var stack = [], keys = [];\r\n    if (!decycler)\r\n        decycler = function (_, value) {\r\n            if (stack[0] === value)\r\n                return \"[Circular ~]\";\r\n            return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\r\n        };\r\n    return function (key, value) {\r\n        if (stack.length > 0) {\r\n            var thisPos = stack.indexOf(this);\r\n            ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\r\n            ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\r\n            if (~stack.indexOf(value))\r\n                value = decycler.call(this, key, value);\r\n        }\r\n        else\r\n            stack.push(value);\r\n        return serializer == null ? value : serializer.call(this, key, value);\r\n    };\r\n}\r\nfunction isImmutableDefault(value) {\r\n    return typeof value !== \"object\" || value == null || Object.isFrozen(value);\r\n}\r\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\r\n    var trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\r\n    return {\r\n        detectMutations: function () {\r\n            return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\r\n        }\r\n    };\r\n}\r\nfunction trackProperties(isImmutable, ignorePaths, obj, path, checkedObjects) {\r\n    if (ignorePaths === void 0) { ignorePaths = []; }\r\n    if (path === void 0) { path = \"\"; }\r\n    if (checkedObjects === void 0) { checkedObjects = new Set(); }\r\n    var tracked = { value: obj };\r\n    if (!isImmutable(obj) && !checkedObjects.has(obj)) {\r\n        checkedObjects.add(obj);\r\n        tracked.children = {};\r\n        for (var key in obj) {\r\n            var childPath = path ? path + \".\" + key : key;\r\n            if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\r\n                continue;\r\n            }\r\n            tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\r\n        }\r\n    }\r\n    return tracked;\r\n}\r\nfunction detectMutations(isImmutable, ignoredPaths, trackedProperty, obj, sameParentRef, path) {\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    if (sameParentRef === void 0) { sameParentRef = false; }\r\n    if (path === void 0) { path = \"\"; }\r\n    var prevObj = trackedProperty ? trackedProperty.value : void 0;\r\n    var sameRef = prevObj === obj;\r\n    if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\r\n        return { wasMutated: true, path: path };\r\n    }\r\n    if (isImmutable(prevObj) || isImmutable(obj)) {\r\n        return { wasMutated: false };\r\n    }\r\n    var keysToDetect = {};\r\n    for (var key in trackedProperty.children) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    for (var key in obj) {\r\n        keysToDetect[key] = true;\r\n    }\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_1 = function (key) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        var result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\r\n        if (result.wasMutated) {\r\n            return { value: result };\r\n        }\r\n    };\r\n    for (var key in keysToDetect) {\r\n        var state_1 = _loop_1(key);\r\n        if (typeof state_1 === \"object\")\r\n            return state_1.value;\r\n    }\r\n    return { wasMutated: false };\r\n}\r\nfunction createImmutableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isImmutable, isImmutable = _c === void 0 ? isImmutableDefault : _c, ignoredPaths = options.ignoredPaths, _d = options.warnAfter, warnAfter = _d === void 0 ? 32 : _d, ignore = options.ignore;\r\n    ignoredPaths = ignoredPaths || ignore;\r\n    var track = trackForMutations.bind(null, isImmutable, ignoredPaths);\r\n    return function (_c) {\r\n        var getState = _c.getState;\r\n        var state = getState();\r\n        var tracker = track(state);\r\n        var result;\r\n        return function (next) { return function (action) {\r\n            var measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                invariant(!result.wasMutated, \"A state mutation was detected between dispatches, in the path '\" + (result.path || \"\") + \"'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            var dispatchedAction = next(action);\r\n            measureUtils.measureTime(function () {\r\n                state = getState();\r\n                result = tracker.detectMutations();\r\n                tracker = track(state);\r\n                result.wasMutated && invariant(!result.wasMutated, \"A state mutation was detected inside a dispatch, in the path: \" + (result.path || \"\") + \". Take a look at the reducer(s) handling the action \" + stringify(action) + \". (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)\");\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n            return dispatchedAction;\r\n        }; };\r\n    };\r\n}\r\n// src/serializableStateInvariantMiddleware.ts\r\nfunction isPlain(val) {\r\n    var type = typeof val;\r\n    return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\r\n}\r\nfunction findNonSerializableValue(value, path, isSerializable, getEntries, ignoredPaths, cache) {\r\n    if (path === void 0) { path = \"\"; }\r\n    if (isSerializable === void 0) { isSerializable = isPlain; }\r\n    if (ignoredPaths === void 0) { ignoredPaths = []; }\r\n    var foundNestedSerializable;\r\n    if (!isSerializable(value)) {\r\n        return {\r\n            keyPath: path || \"<root>\",\r\n            value: value\r\n        };\r\n    }\r\n    if (typeof value !== \"object\" || value === null) {\r\n        return false;\r\n    }\r\n    if (cache == null ? void 0 : cache.has(value))\r\n        return false;\r\n    var entries = getEntries != null ? getEntries(value) : Object.entries(value);\r\n    var hasIgnoredPaths = ignoredPaths.length > 0;\r\n    var _loop_2 = function (key, nestedValue) {\r\n        var nestedPath = path ? path + \".\" + key : key;\r\n        if (hasIgnoredPaths) {\r\n            var hasMatches = ignoredPaths.some(function (ignored) {\r\n                if (ignored instanceof RegExp) {\r\n                    return ignored.test(nestedPath);\r\n                }\r\n                return nestedPath === ignored;\r\n            });\r\n            if (hasMatches) {\r\n                return \"continue\";\r\n            }\r\n        }\r\n        if (!isSerializable(nestedValue)) {\r\n            return { value: {\r\n                    keyPath: nestedPath,\r\n                    value: nestedValue\r\n                } };\r\n        }\r\n        if (typeof nestedValue === \"object\") {\r\n            foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\r\n            if (foundNestedSerializable) {\r\n                return { value: foundNestedSerializable };\r\n            }\r\n        }\r\n    };\r\n    for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\r\n        var _c = entries_1[_i], key = _c[0], nestedValue = _c[1];\r\n        var state_2 = _loop_2(key, nestedValue);\r\n        if (typeof state_2 === \"object\")\r\n            return state_2.value;\r\n    }\r\n    if (cache && isNestedFrozen(value))\r\n        cache.add(value);\r\n    return false;\r\n}\r\nfunction isNestedFrozen(value) {\r\n    if (!Object.isFrozen(value))\r\n        return false;\r\n    for (var _i = 0, _c = Object.values(value); _i < _c.length; _i++) {\r\n        var nestedValue = _c[_i];\r\n        if (typeof nestedValue !== \"object\" || nestedValue === null)\r\n            continue;\r\n        if (!isNestedFrozen(nestedValue))\r\n            return false;\r\n    }\r\n    return true;\r\n}\r\nfunction createSerializableStateInvariantMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    if (process.env.NODE_ENV === \"production\") {\r\n        return function () { return function (next) { return function (action) { return next(action); }; }; };\r\n    }\r\n    var _c = options.isSerializable, isSerializable = _c === void 0 ? isPlain : _c, getEntries = options.getEntries, _d = options.ignoredActions, ignoredActions = _d === void 0 ? [] : _d, _e = options.ignoredActionPaths, ignoredActionPaths = _e === void 0 ? [\"meta.arg\", \"meta.baseQueryMeta\"] : _e, _f = options.ignoredPaths, ignoredPaths = _f === void 0 ? [] : _f, _g = options.warnAfter, warnAfter = _g === void 0 ? 32 : _g, _h = options.ignoreState, ignoreState = _h === void 0 ? false : _h, _j = options.ignoreActions, ignoreActions = _j === void 0 ? false : _j, _k = options.disableCache, disableCache = _k === void 0 ? false : _k;\r\n    var cache = !disableCache && WeakSet ? new WeakSet() : void 0;\r\n    return function (storeAPI) { return function (next) { return function (action) {\r\n        var result = next(action);\r\n        var measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\r\n        if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\r\n            measureUtils.measureTime(function () {\r\n                var foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\r\n                if (foundActionNonSerializableValue) {\r\n                    var keyPath = foundActionNonSerializableValue.keyPath, value = foundActionNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in an action, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\r\n                }\r\n            });\r\n        }\r\n        if (!ignoreState) {\r\n            measureUtils.measureTime(function () {\r\n                var state = storeAPI.getState();\r\n                var foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\r\n                if (foundStateNonSerializableValue) {\r\n                    var keyPath = foundStateNonSerializableValue.keyPath, value = foundStateNonSerializableValue.value;\r\n                    console.error(\"A non-serializable value was detected in the state, in the path: `\" + keyPath + \"`. Value:\", value, \"\\nTake a look at the reducer(s) handling this action type: \" + action.type + \".\\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)\");\r\n                }\r\n            });\r\n            measureUtils.warnIfExceeded();\r\n        }\r\n        return result;\r\n    }; }; };\r\n}\r\n// src/getDefaultMiddleware.ts\r\nfunction isBoolean(x) {\r\n    return typeof x === \"boolean\";\r\n}\r\nfunction curryGetDefaultMiddleware() {\r\n    return function curriedGetDefaultMiddleware(options) {\r\n        return getDefaultMiddleware(options);\r\n    };\r\n}\r\nfunction getDefaultMiddleware(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = options.thunk, thunk = _c === void 0 ? true : _c, _d = options.immutableCheck, immutableCheck = _d === void 0 ? true : _d, _e = options.serializableCheck, serializableCheck = _e === void 0 ? true : _e, _f = options.actionCreatorCheck, actionCreatorCheck = _f === void 0 ? true : _f;\r\n    var middlewareArray = new MiddlewareArray();\r\n    if (thunk) {\r\n        if (isBoolean(thunk)) {\r\n            middlewareArray.push(thunkMiddleware);\r\n        }\r\n        else {\r\n            middlewareArray.push(thunkMiddleware.withExtraArgument(thunk.extraArgument));\r\n        }\r\n    }\r\n    if (process.env.NODE_ENV !== \"production\") {\r\n        if (immutableCheck) {\r\n            var immutableOptions = {};\r\n            if (!isBoolean(immutableCheck)) {\r\n                immutableOptions = immutableCheck;\r\n            }\r\n            middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\r\n        }\r\n        if (serializableCheck) {\r\n            var serializableOptions = {};\r\n            if (!isBoolean(serializableCheck)) {\r\n                serializableOptions = serializableCheck;\r\n            }\r\n            middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\r\n        }\r\n        if (actionCreatorCheck) {\r\n            var actionCreatorOptions = {};\r\n            if (!isBoolean(actionCreatorCheck)) {\r\n                actionCreatorOptions = actionCreatorCheck;\r\n            }\r\n            middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\r\n        }\r\n    }\r\n    return middlewareArray;\r\n}\r\n// src/configureStore.ts\r\nvar IS_PRODUCTION = process.env.NODE_ENV === \"production\";\r\nfunction configureStore(options) {\r\n    var curriedGetDefaultMiddleware = curryGetDefaultMiddleware();\r\n    var _c = options || {}, _d = _c.reducer, reducer = _d === void 0 ? void 0 : _d, _e = _c.middleware, middleware = _e === void 0 ? curriedGetDefaultMiddleware() : _e, _f = _c.devTools, devTools = _f === void 0 ? true : _f, _g = _c.preloadedState, preloadedState = _g === void 0 ? void 0 : _g, _h = _c.enhancers, enhancers = _h === void 0 ? void 0 : _h;\r\n    var rootReducer;\r\n    if (typeof reducer === \"function\") {\r\n        rootReducer = reducer;\r\n    }\r\n    else if (isPlainObject(reducer)) {\r\n        rootReducer = combineReducers(reducer);\r\n    }\r\n    else {\r\n        throw new Error('\"reducer\" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');\r\n    }\r\n    var finalMiddleware = middleware;\r\n    if (typeof finalMiddleware === \"function\") {\r\n        finalMiddleware = finalMiddleware(curriedGetDefaultMiddleware);\r\n        if (!IS_PRODUCTION && !Array.isArray(finalMiddleware)) {\r\n            throw new Error(\"when using a middleware builder function, an array of middleware must be returned\");\r\n        }\r\n    }\r\n    if (!IS_PRODUCTION && finalMiddleware.some(function (item) { return typeof item !== \"function\"; })) {\r\n        throw new Error(\"each middleware provided to configureStore must be a function\");\r\n    }\r\n    var middlewareEnhancer = applyMiddleware.apply(void 0, finalMiddleware);\r\n    var finalCompose = compose2;\r\n    if (devTools) {\r\n        finalCompose = composeWithDevTools(__spreadValues({\r\n            trace: !IS_PRODUCTION\r\n        }, typeof devTools === \"object\" && devTools));\r\n    }\r\n    var defaultEnhancers = new EnhancerArray(middlewareEnhancer);\r\n    var storeEnhancers = defaultEnhancers;\r\n    if (Array.isArray(enhancers)) {\r\n        storeEnhancers = __spreadArray([middlewareEnhancer], enhancers);\r\n    }\r\n    else if (typeof enhancers === \"function\") {\r\n        storeEnhancers = enhancers(defaultEnhancers);\r\n    }\r\n    var composedEnhancer = finalCompose.apply(void 0, storeEnhancers);\r\n    return createStore(rootReducer, preloadedState, composedEnhancer);\r\n}\r\n// src/createReducer.ts\r\nimport createNextState2, { isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\r\n// src/mapBuilders.ts\r\nfunction executeReducerBuilderCallback(builderCallback) {\r\n    var actionsMap = {};\r\n    var actionMatchers = [];\r\n    var defaultCaseReducer;\r\n    var builder = {\r\n        addCase: function (typeOrActionCreator, reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (actionMatchers.length > 0) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addMatcher`\");\r\n                }\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            var type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\r\n            if (!type) {\r\n                throw new Error(\"`builder.addCase` cannot be called with an empty action type\");\r\n            }\r\n            if (type in actionsMap) {\r\n                throw new Error(\"`builder.addCase` cannot be called with two reducers for the same action type\");\r\n            }\r\n            actionsMap[type] = reducer;\r\n            return builder;\r\n        },\r\n        addMatcher: function (matcher, reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\r\n                }\r\n            }\r\n            actionMatchers.push({ matcher: matcher, reducer: reducer });\r\n            return builder;\r\n        },\r\n        addDefaultCase: function (reducer) {\r\n            if (process.env.NODE_ENV !== \"production\") {\r\n                if (defaultCaseReducer) {\r\n                    throw new Error(\"`builder.addDefaultCase` can only be called once\");\r\n                }\r\n            }\r\n            defaultCaseReducer = reducer;\r\n            return builder;\r\n        }\r\n    };\r\n    builderCallback(builder);\r\n    return [actionsMap, actionMatchers, defaultCaseReducer];\r\n}\r\n// src/createReducer.ts\r\nfunction isStateFunction(x) {\r\n    return typeof x === \"function\";\r\n}\r\nvar hasWarnedAboutObjectNotation = false;\r\nfunction createReducer(initialState, mapOrBuilderCallback, actionMatchers, defaultCaseReducer) {\r\n    if (actionMatchers === void 0) { actionMatchers = []; }\r\n    if (process.env.NODE_ENV !== \"production\") {\r\n        if (typeof mapOrBuilderCallback === \"object\") {\r\n            if (!hasWarnedAboutObjectNotation) {\r\n                hasWarnedAboutObjectNotation = true;\r\n                console.warn(\"The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\r\n            }\r\n        }\r\n    }\r\n    var _c = typeof mapOrBuilderCallback === \"function\" ? executeReducerBuilderCallback(mapOrBuilderCallback) : [mapOrBuilderCallback, actionMatchers, defaultCaseReducer], actionsMap = _c[0], finalActionMatchers = _c[1], finalDefaultCaseReducer = _c[2];\r\n    var getInitialState;\r\n    if (isStateFunction(initialState)) {\r\n        getInitialState = function () { return freezeDraftable(initialState()); };\r\n    }\r\n    else {\r\n        var frozenInitialState_1 = freezeDraftable(initialState);\r\n        getInitialState = function () { return frozenInitialState_1; };\r\n    }\r\n    function reducer(state, action) {\r\n        if (state === void 0) { state = getInitialState(); }\r\n        var caseReducers = __spreadArray([\r\n            actionsMap[action.type]\r\n        ], finalActionMatchers.filter(function (_c) {\r\n            var matcher = _c.matcher;\r\n            return matcher(action);\r\n        }).map(function (_c) {\r\n            var reducer2 = _c.reducer;\r\n            return reducer2;\r\n        }));\r\n        if (caseReducers.filter(function (cr) { return !!cr; }).length === 0) {\r\n            caseReducers = [finalDefaultCaseReducer];\r\n        }\r\n        return caseReducers.reduce(function (previousState, caseReducer) {\r\n            if (caseReducer) {\r\n                if (isDraft2(previousState)) {\r\n                    var draft = previousState;\r\n                    var result = caseReducer(draft, action);\r\n                    if (result === void 0) {\r\n                        return previousState;\r\n                    }\r\n                    return result;\r\n                }\r\n                else if (!isDraftable2(previousState)) {\r\n                    var result = caseReducer(previousState, action);\r\n                    if (result === void 0) {\r\n                        if (previousState === null) {\r\n                            return previousState;\r\n                        }\r\n                        throw Error(\"A case reducer on a non-draftable value must not return undefined\");\r\n                    }\r\n                    return result;\r\n                }\r\n                else {\r\n                    return createNextState2(previousState, function (draft) {\r\n                        return caseReducer(draft, action);\r\n                    });\r\n                }\r\n            }\r\n            return previousState;\r\n        }, state);\r\n    }\r\n    reducer.getInitialState = getInitialState;\r\n    return reducer;\r\n}\r\n// src/createSlice.ts\r\nvar hasWarnedAboutObjectNotation2 = false;\r\nfunction getType2(slice, actionKey) {\r\n    return slice + \"/\" + actionKey;\r\n}\r\nfunction createSlice(options) {\r\n    var name = options.name;\r\n    if (!name) {\r\n        throw new Error(\"`name` is a required option for createSlice\");\r\n    }\r\n    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n        if (options.initialState === void 0) {\r\n            console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\r\n        }\r\n    }\r\n    var initialState = typeof options.initialState == \"function\" ? options.initialState : freezeDraftable(options.initialState);\r\n    var reducers = options.reducers || {};\r\n    var reducerNames = Object.keys(reducers);\r\n    var sliceCaseReducersByName = {};\r\n    var sliceCaseReducersByType = {};\r\n    var actionCreators = {};\r\n    reducerNames.forEach(function (reducerName) {\r\n        var maybeReducerWithPrepare = reducers[reducerName];\r\n        var type = getType2(name, reducerName);\r\n        var caseReducer;\r\n        var prepareCallback;\r\n        if (\"reducer\" in maybeReducerWithPrepare) {\r\n            caseReducer = maybeReducerWithPrepare.reducer;\r\n            prepareCallback = maybeReducerWithPrepare.prepare;\r\n        }\r\n        else {\r\n            caseReducer = maybeReducerWithPrepare;\r\n        }\r\n        sliceCaseReducersByName[reducerName] = caseReducer;\r\n        sliceCaseReducersByType[type] = caseReducer;\r\n        actionCreators[reducerName] = prepareCallback ? createAction(type, prepareCallback) : createAction(type);\r\n    });\r\n    function buildReducer() {\r\n        if (process.env.NODE_ENV !== \"production\") {\r\n            if (typeof options.extraReducers === \"object\") {\r\n                if (!hasWarnedAboutObjectNotation2) {\r\n                    hasWarnedAboutObjectNotation2 = true;\r\n                    console.warn(\"The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\r\n                }\r\n            }\r\n        }\r\n        var _c = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers], _d = _c[0], extraReducers = _d === void 0 ? {} : _d, _e = _c[1], actionMatchers = _e === void 0 ? [] : _e, _f = _c[2], defaultCaseReducer = _f === void 0 ? void 0 : _f;\r\n        var finalCaseReducers = __spreadValues(__spreadValues({}, extraReducers), sliceCaseReducersByType);\r\n        return createReducer(initialState, function (builder) {\r\n            for (var key in finalCaseReducers) {\r\n                builder.addCase(key, finalCaseReducers[key]);\r\n            }\r\n            for (var _i = 0, actionMatchers_1 = actionMatchers; _i < actionMatchers_1.length; _i++) {\r\n                var m = actionMatchers_1[_i];\r\n                builder.addMatcher(m.matcher, m.reducer);\r\n            }\r\n            if (defaultCaseReducer) {\r\n                builder.addDefaultCase(defaultCaseReducer);\r\n            }\r\n        });\r\n    }\r\n    var _reducer;\r\n    return {\r\n        name: name,\r\n        reducer: function (state, action) {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer(state, action);\r\n        },\r\n        actions: actionCreators,\r\n        caseReducers: sliceCaseReducersByName,\r\n        getInitialState: function () {\r\n            if (!_reducer)\r\n                _reducer = buildReducer();\r\n            return _reducer.getInitialState();\r\n        }\r\n    };\r\n}\r\n// src/entities/entity_state.ts\r\nfunction getInitialEntityState() {\r\n    return {\r\n        ids: [],\r\n        entities: {}\r\n    };\r\n}\r\nfunction createInitialStateFactory() {\r\n    function getInitialState(additionalState) {\r\n        if (additionalState === void 0) { additionalState = {}; }\r\n        return Object.assign(getInitialEntityState(), additionalState);\r\n    }\r\n    return { getInitialState: getInitialState };\r\n}\r\n// src/entities/state_selectors.ts\r\nfunction createSelectorsFactory() {\r\n    function getSelectors(selectState) {\r\n        var selectIds = function (state) { return state.ids; };\r\n        var selectEntities = function (state) { return state.entities; };\r\n        var selectAll = createDraftSafeSelector(selectIds, selectEntities, function (ids, entities) { return ids.map(function (id) { return entities[id]; }); });\r\n        var selectId = function (_, id) { return id; };\r\n        var selectById = function (entities, id) { return entities[id]; };\r\n        var selectTotal = createDraftSafeSelector(selectIds, function (ids) { return ids.length; });\r\n        if (!selectState) {\r\n            return {\r\n                selectIds: selectIds,\r\n                selectEntities: selectEntities,\r\n                selectAll: selectAll,\r\n                selectTotal: selectTotal,\r\n                selectById: createDraftSafeSelector(selectEntities, selectId, selectById)\r\n            };\r\n        }\r\n        var selectGlobalizedEntities = createDraftSafeSelector(selectState, selectEntities);\r\n        return {\r\n            selectIds: createDraftSafeSelector(selectState, selectIds),\r\n            selectEntities: selectGlobalizedEntities,\r\n            selectAll: createDraftSafeSelector(selectState, selectAll),\r\n            selectTotal: createDraftSafeSelector(selectState, selectTotal),\r\n            selectById: createDraftSafeSelector(selectGlobalizedEntities, selectId, selectById)\r\n        };\r\n    }\r\n    return { getSelectors: getSelectors };\r\n}\r\n// src/entities/state_adapter.ts\r\nimport createNextState3, { isDraft as isDraft3 } from \"immer\";\r\nfunction createSingleArgumentStateOperator(mutator) {\r\n    var operator = createStateOperator(function (_, state) { return mutator(state); });\r\n    return function operation(state) {\r\n        return operator(state, void 0);\r\n    };\r\n}\r\nfunction createStateOperator(mutator) {\r\n    return function operation(state, arg) {\r\n        function isPayloadActionArgument(arg2) {\r\n            return isFSA(arg2);\r\n        }\r\n        var runMutator = function (draft) {\r\n            if (isPayloadActionArgument(arg)) {\r\n                mutator(arg.payload, draft);\r\n            }\r\n            else {\r\n                mutator(arg, draft);\r\n            }\r\n        };\r\n        if (isDraft3(state)) {\r\n            runMutator(state);\r\n            return state;\r\n        }\r\n        else {\r\n            return createNextState3(state, runMutator);\r\n        }\r\n    };\r\n}\r\n// src/entities/utils.ts\r\nfunction selectIdValue(entity, selectId) {\r\n    var key = selectId(entity);\r\n    if (process.env.NODE_ENV !== \"production\" && key === void 0) {\r\n        console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\r\n    }\r\n    return key;\r\n}\r\nfunction ensureEntitiesArray(entities) {\r\n    if (!Array.isArray(entities)) {\r\n        entities = Object.values(entities);\r\n    }\r\n    return entities;\r\n}\r\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\r\n    newEntities = ensureEntitiesArray(newEntities);\r\n    var added = [];\r\n    var updated = [];\r\n    for (var _i = 0, newEntities_1 = newEntities; _i < newEntities_1.length; _i++) {\r\n        var entity = newEntities_1[_i];\r\n        var id = selectIdValue(entity, selectId);\r\n        if (id in state.entities) {\r\n            updated.push({ id: id, changes: entity });\r\n        }\r\n        else {\r\n            added.push(entity);\r\n        }\r\n    }\r\n    return [added, updated];\r\n}\r\n// src/entities/unsorted_state_adapter.ts\r\nfunction createUnsortedStateAdapter(selectId) {\r\n    function addOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (key in state.entities) {\r\n            return;\r\n        }\r\n        state.ids.push(key);\r\n        state.entities[key] = entity;\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_2 = newEntities; _i < newEntities_2.length; _i++) {\r\n            var entity = newEntities_2[_i];\r\n            addOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        var key = selectIdValue(entity, selectId);\r\n        if (!(key in state.entities)) {\r\n            state.ids.push(key);\r\n        }\r\n        state.entities[key] = entity;\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        for (var _i = 0, newEntities_3 = newEntities; _i < newEntities_3.length; _i++) {\r\n            var entity = newEntities_3[_i];\r\n            setOneMutably(entity, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.ids = [];\r\n        state.entities = {};\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function removeOneMutably(key, state) {\r\n        return removeManyMutably([key], state);\r\n    }\r\n    function removeManyMutably(keys, state) {\r\n        var didMutate = false;\r\n        keys.forEach(function (key) {\r\n            if (key in state.entities) {\r\n                delete state.entities[key];\r\n                didMutate = true;\r\n            }\r\n        });\r\n        if (didMutate) {\r\n            state.ids = state.ids.filter(function (id) { return id in state.entities; });\r\n        }\r\n    }\r\n    function removeAllMutably(state) {\r\n        Object.assign(state, {\r\n            ids: [],\r\n            entities: {}\r\n        });\r\n    }\r\n    function takeNewKey(keys, update, state) {\r\n        var original2 = state.entities[update.id];\r\n        var updated = Object.assign({}, original2, update.changes);\r\n        var newKey = selectIdValue(updated, selectId);\r\n        var hasNewKey = newKey !== update.id;\r\n        if (hasNewKey) {\r\n            keys[update.id] = newKey;\r\n            delete state.entities[update.id];\r\n        }\r\n        state.entities[newKey] = updated;\r\n        return hasNewKey;\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var newKeys = {};\r\n        var updatesPerEntity = {};\r\n        updates.forEach(function (update) {\r\n            if (update.id in state.entities) {\r\n                updatesPerEntity[update.id] = {\r\n                    id: update.id,\r\n                    changes: __spreadValues(__spreadValues({}, updatesPerEntity[update.id] ? updatesPerEntity[update.id].changes : null), update.changes)\r\n                };\r\n            }\r\n        });\r\n        updates = Object.values(updatesPerEntity);\r\n        var didMutateEntities = updates.length > 0;\r\n        if (didMutateEntities) {\r\n            var didMutateIds = updates.filter(function (update) { return takeNewKey(newKeys, update, state); }).length > 0;\r\n            if (didMutateIds) {\r\n                state.ids = Object.keys(state.entities);\r\n            }\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    return {\r\n        removeAll: createSingleArgumentStateOperator(removeAllMutably),\r\n        addOne: createStateOperator(addOneMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably),\r\n        removeOne: createStateOperator(removeOneMutably),\r\n        removeMany: createStateOperator(removeManyMutably)\r\n    };\r\n}\r\n// src/entities/sorted_state_adapter.ts\r\nfunction createSortedStateAdapter(selectId, sort) {\r\n    var _c = createUnsortedStateAdapter(selectId), removeOne = _c.removeOne, removeMany = _c.removeMany, removeAll = _c.removeAll;\r\n    function addOneMutably(entity, state) {\r\n        return addManyMutably([entity], state);\r\n    }\r\n    function addManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        var models = newEntities.filter(function (model) { return !(selectIdValue(model, selectId) in state.entities); });\r\n        if (models.length !== 0) {\r\n            merge(models, state);\r\n        }\r\n    }\r\n    function setOneMutably(entity, state) {\r\n        return setManyMutably([entity], state);\r\n    }\r\n    function setManyMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        if (newEntities.length !== 0) {\r\n            merge(newEntities, state);\r\n        }\r\n    }\r\n    function setAllMutably(newEntities, state) {\r\n        newEntities = ensureEntitiesArray(newEntities);\r\n        state.entities = {};\r\n        state.ids = [];\r\n        addManyMutably(newEntities, state);\r\n    }\r\n    function updateOneMutably(update, state) {\r\n        return updateManyMutably([update], state);\r\n    }\r\n    function updateManyMutably(updates, state) {\r\n        var appliedUpdates = false;\r\n        for (var _i = 0, updates_1 = updates; _i < updates_1.length; _i++) {\r\n            var update = updates_1[_i];\r\n            var entity = state.entities[update.id];\r\n            if (!entity) {\r\n                continue;\r\n            }\r\n            appliedUpdates = true;\r\n            Object.assign(entity, update.changes);\r\n            var newId = selectId(entity);\r\n            if (update.id !== newId) {\r\n                delete state.entities[update.id];\r\n                state.entities[newId] = entity;\r\n            }\r\n        }\r\n        if (appliedUpdates) {\r\n            resortEntities(state);\r\n        }\r\n    }\r\n    function upsertOneMutably(entity, state) {\r\n        return upsertManyMutably([entity], state);\r\n    }\r\n    function upsertManyMutably(newEntities, state) {\r\n        var _c = splitAddedUpdatedEntities(newEntities, selectId, state), added = _c[0], updated = _c[1];\r\n        updateManyMutably(updated, state);\r\n        addManyMutably(added, state);\r\n    }\r\n    function areArraysEqual(a, b) {\r\n        if (a.length !== b.length) {\r\n            return false;\r\n        }\r\n        for (var i = 0; i < a.length && i < b.length; i++) {\r\n            if (a[i] === b[i]) {\r\n                continue;\r\n            }\r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n    function merge(models, state) {\r\n        models.forEach(function (model) {\r\n            state.entities[selectId(model)] = model;\r\n        });\r\n        resortEntities(state);\r\n    }\r\n    function resortEntities(state) {\r\n        var allEntities = Object.values(state.entities);\r\n        allEntities.sort(sort);\r\n        var newSortedIds = allEntities.map(selectId);\r\n        var ids = state.ids;\r\n        if (!areArraysEqual(ids, newSortedIds)) {\r\n            state.ids = newSortedIds;\r\n        }\r\n    }\r\n    return {\r\n        removeOne: removeOne,\r\n        removeMany: removeMany,\r\n        removeAll: removeAll,\r\n        addOne: createStateOperator(addOneMutably),\r\n        updateOne: createStateOperator(updateOneMutably),\r\n        upsertOne: createStateOperator(upsertOneMutably),\r\n        setOne: createStateOperator(setOneMutably),\r\n        setMany: createStateOperator(setManyMutably),\r\n        setAll: createStateOperator(setAllMutably),\r\n        addMany: createStateOperator(addManyMutably),\r\n        updateMany: createStateOperator(updateManyMutably),\r\n        upsertMany: createStateOperator(upsertManyMutably)\r\n    };\r\n}\r\n// src/entities/create_adapter.ts\r\nfunction createEntityAdapter(options) {\r\n    if (options === void 0) { options = {}; }\r\n    var _c = __spreadValues({\r\n        sortComparer: false,\r\n        selectId: function (instance) { return instance.id; }\r\n    }, options), selectId = _c.selectId, sortComparer = _c.sortComparer;\r\n    var stateFactory = createInitialStateFactory();\r\n    var selectorsFactory = createSelectorsFactory();\r\n    var stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\r\n    return __spreadValues(__spreadValues(__spreadValues({\r\n        selectId: selectId,\r\n        sortComparer: sortComparer\r\n    }, stateFactory), selectorsFactory), stateAdapter);\r\n}\r\n// src/nanoid.ts\r\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\r\nvar nanoid = function (size) {\r\n    if (size === void 0) { size = 21; }\r\n    var id = \"\";\r\n    var i = size;\r\n    while (i--) {\r\n        id += urlAlphabet[Math.random() * 64 | 0];\r\n    }\r\n    return id;\r\n};\r\n// src/createAsyncThunk.ts\r\nvar commonProperties = [\r\n    \"name\",\r\n    \"message\",\r\n    \"stack\",\r\n    \"code\"\r\n];\r\nvar RejectWithValue = /** @class */ (function () {\r\n    function RejectWithValue(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return RejectWithValue;\r\n}());\r\nvar FulfillWithMeta = /** @class */ (function () {\r\n    function FulfillWithMeta(payload, meta) {\r\n        this.payload = payload;\r\n        this.meta = meta;\r\n    }\r\n    return FulfillWithMeta;\r\n}());\r\nvar miniSerializeError = function (value) {\r\n    if (typeof value === \"object\" && value !== null) {\r\n        var simpleError = {};\r\n        for (var _i = 0, commonProperties_1 = commonProperties; _i < commonProperties_1.length; _i++) {\r\n            var property = commonProperties_1[_i];\r\n            if (typeof value[property] === \"string\") {\r\n                simpleError[property] = value[property];\r\n            }\r\n        }\r\n        return simpleError;\r\n    }\r\n    return { message: String(value) };\r\n};\r\nvar createAsyncThunk = (function () {\r\n    function createAsyncThunk2(typePrefix, payloadCreator, options) {\r\n        var fulfilled = createAction(typePrefix + \"/fulfilled\", function (payload, requestId, arg, meta) { return ({\r\n            payload: payload,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"fulfilled\"\r\n            })\r\n        }); });\r\n        var pending = createAction(typePrefix + \"/pending\", function (requestId, arg, meta) { return ({\r\n            payload: void 0,\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                requestStatus: \"pending\"\r\n            })\r\n        }); });\r\n        var rejected = createAction(typePrefix + \"/rejected\", function (error, requestId, arg, payload, meta) { return ({\r\n            payload: payload,\r\n            error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\r\n            meta: __spreadProps(__spreadValues({}, meta || {}), {\r\n                arg: arg,\r\n                requestId: requestId,\r\n                rejectedWithValue: !!payload,\r\n                requestStatus: \"rejected\",\r\n                aborted: (error == null ? void 0 : error.name) === \"AbortError\",\r\n                condition: (error == null ? void 0 : error.name) === \"ConditionError\"\r\n            })\r\n        }); });\r\n        var displayedWarning = false;\r\n        var AC = typeof AbortController !== \"undefined\" ? AbortController : /** @class */ (function () {\r\n            function class_1() {\r\n                this.signal = {\r\n                    aborted: false,\r\n                    addEventListener: function () {\r\n                    },\r\n                    dispatchEvent: function () {\r\n                        return false;\r\n                    },\r\n                    onabort: function () {\r\n                    },\r\n                    removeEventListener: function () {\r\n                    },\r\n                    reason: void 0,\r\n                    throwIfAborted: function () {\r\n                    }\r\n                };\r\n            }\r\n            class_1.prototype.abort = function () {\r\n                if (process.env.NODE_ENV !== \"production\") {\r\n                    if (!displayedWarning) {\r\n                        displayedWarning = true;\r\n                        console.info(\"This platform does not implement AbortController. \\nIf you want to use the AbortController to react to `abort` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'.\");\r\n                    }\r\n                }\r\n            };\r\n            return class_1;\r\n        }());\r\n        function actionCreator(arg) {\r\n            return function (dispatch, getState, extra) {\r\n                var requestId = (options == null ? void 0 : options.idGenerator) ? options.idGenerator(arg) : nanoid();\r\n                var abortController = new AC();\r\n                var abortReason;\r\n                var started = false;\r\n                function abort(reason) {\r\n                    abortReason = reason;\r\n                    abortController.abort();\r\n                }\r\n                var promise2 = function () {\r\n                    return __async(this, null, function () {\r\n                        var _a, _b, finalAction, conditionResult, abortedPromise, err_1, skipDispatch;\r\n                        return __generator(this, function (_c) {\r\n                            switch (_c.label) {\r\n                                case 0:\r\n                                    _c.trys.push([0, 4, , 5]);\r\n                                    conditionResult = (_a = options == null ? void 0 : options.condition) == null ? void 0 : _a.call(options, arg, { getState: getState, extra: extra });\r\n                                    if (!isThenable(conditionResult)) return [3 /*break*/, 2];\r\n                                    return [4 /*yield*/, conditionResult];\r\n                                case 1:\r\n                                    conditionResult = _c.sent();\r\n                                    _c.label = 2;\r\n                                case 2:\r\n                                    if (conditionResult === false || abortController.signal.aborted) {\r\n                                        throw {\r\n                                            name: \"ConditionError\",\r\n                                            message: \"Aborted due to condition callback returning false.\"\r\n                                        };\r\n                                    }\r\n                                    started = true;\r\n                                    abortedPromise = new Promise(function (_, reject) { return abortController.signal.addEventListener(\"abort\", function () { return reject({\r\n                                        name: \"AbortError\",\r\n                                        message: abortReason || \"Aborted\"\r\n                                    }); }); });\r\n                                    dispatch(pending(requestId, arg, (_b = options == null ? void 0 : options.getPendingMeta) == null ? void 0 : _b.call(options, { requestId: requestId, arg: arg }, { getState: getState, extra: extra })));\r\n                                    return [4 /*yield*/, Promise.race([\r\n                                            abortedPromise,\r\n                                            Promise.resolve(payloadCreator(arg, {\r\n                                                dispatch: dispatch,\r\n                                                getState: getState,\r\n                                                extra: extra,\r\n                                                requestId: requestId,\r\n                                                signal: abortController.signal,\r\n                                                abort: abort,\r\n                                                rejectWithValue: function (value, meta) {\r\n                                                    return new RejectWithValue(value, meta);\r\n                                                },\r\n                                                fulfillWithValue: function (value, meta) {\r\n                                                    return new FulfillWithMeta(value, meta);\r\n                                                }\r\n                                            })).then(function (result) {\r\n                                                if (result instanceof RejectWithValue) {\r\n                                                    throw result;\r\n                                                }\r\n                                                if (result instanceof FulfillWithMeta) {\r\n                                                    return fulfilled(result.payload, requestId, arg, result.meta);\r\n                                                }\r\n                                                return fulfilled(result, requestId, arg);\r\n                                            })\r\n                                        ])];\r\n                                case 3:\r\n                                    finalAction = _c.sent();\r\n                                    return [3 /*break*/, 5];\r\n                                case 4:\r\n                                    err_1 = _c.sent();\r\n                                    finalAction = err_1 instanceof RejectWithValue ? rejected(null, requestId, arg, err_1.payload, err_1.meta) : rejected(err_1, requestId, arg);\r\n                                    return [3 /*break*/, 5];\r\n                                case 5:\r\n                                    skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\r\n                                    if (!skipDispatch) {\r\n                                        dispatch(finalAction);\r\n                                    }\r\n                                    return [2 /*return*/, finalAction];\r\n                            }\r\n                        });\r\n                    });\r\n                }();\r\n                return Object.assign(promise2, {\r\n                    abort: abort,\r\n                    requestId: requestId,\r\n                    arg: arg,\r\n                    unwrap: function () {\r\n                        return promise2.then(unwrapResult);\r\n                    }\r\n                });\r\n            };\r\n        }\r\n        return Object.assign(actionCreator, {\r\n            pending: pending,\r\n            rejected: rejected,\r\n            fulfilled: fulfilled,\r\n            typePrefix: typePrefix\r\n        });\r\n    }\r\n    createAsyncThunk2.withTypes = function () { return createAsyncThunk2; };\r\n    return createAsyncThunk2;\r\n})();\r\nfunction unwrapResult(action) {\r\n    if (action.meta && action.meta.rejectedWithValue) {\r\n        throw action.payload;\r\n    }\r\n    if (action.error) {\r\n        throw action.error;\r\n    }\r\n    return action.payload;\r\n}\r\nfunction isThenable(value) {\r\n    return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\r\n}\r\n// src/matchers.ts\r\nvar matches = function (matcher, action) {\r\n    if (hasMatchFunction(matcher)) {\r\n        return matcher.match(action);\r\n    }\r\n    else {\r\n        return matcher(action);\r\n    }\r\n};\r\nfunction isAnyOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.some(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction isAllOf() {\r\n    var matchers = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        matchers[_i] = arguments[_i];\r\n    }\r\n    return function (action) {\r\n        return matchers.every(function (matcher) { return matches(matcher, action); });\r\n    };\r\n}\r\nfunction hasExpectedRequestMetadata(action, validStatus) {\r\n    if (!action || !action.meta)\r\n        return false;\r\n    var hasValidRequestId = typeof action.meta.requestId === \"string\";\r\n    var hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\r\n    return hasValidRequestId && hasValidRequestStatus;\r\n}\r\nfunction isAsyncThunkArray(a) {\r\n    return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\r\n}\r\nfunction isPending() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isPending()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.pending; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejected() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejected()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.rejected; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isRejectedWithValue() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    var hasFlag = function (action) {\r\n        return action && action.meta && action.meta.rejectedWithValue;\r\n    };\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) {\r\n            var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n            return combinedMatcher(action);\r\n        };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isRejectedWithValue()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var combinedMatcher = isAllOf(isRejected.apply(void 0, asyncThunks), hasFlag);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isFulfilled() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"fulfilled\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isFulfilled()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = asyncThunks.map(function (asyncThunk) { return asyncThunk.fulfilled; });\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\nfunction isAsyncThunkAction() {\r\n    var asyncThunks = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        asyncThunks[_i] = arguments[_i];\r\n    }\r\n    if (asyncThunks.length === 0) {\r\n        return function (action) { return hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]); };\r\n    }\r\n    if (!isAsyncThunkArray(asyncThunks)) {\r\n        return isAsyncThunkAction()(asyncThunks[0]);\r\n    }\r\n    return function (action) {\r\n        var matchers = [];\r\n        for (var _i = 0, asyncThunks_1 = asyncThunks; _i < asyncThunks_1.length; _i++) {\r\n            var asyncThunk = asyncThunks_1[_i];\r\n            matchers.push(asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled);\r\n        }\r\n        var combinedMatcher = isAnyOf.apply(void 0, matchers);\r\n        return combinedMatcher(action);\r\n    };\r\n}\r\n// src/listenerMiddleware/utils.ts\r\nvar assertFunction = function (func, expected) {\r\n    if (typeof func !== \"function\") {\r\n        throw new TypeError(expected + \" is not a function\");\r\n    }\r\n};\r\nvar noop = function () {\r\n};\r\nvar catchRejection = function (promise2, onError) {\r\n    if (onError === void 0) { onError = noop; }\r\n    promise2.catch(onError);\r\n    return promise2;\r\n};\r\nvar addAbortSignalListener = function (abortSignal, callback) {\r\n    abortSignal.addEventListener(\"abort\", callback, { once: true });\r\n    return function () { return abortSignal.removeEventListener(\"abort\", callback); };\r\n};\r\nvar abortControllerWithReason = function (abortController, reason) {\r\n    var signal = abortController.signal;\r\n    if (signal.aborted) {\r\n        return;\r\n    }\r\n    if (!(\"reason\" in signal)) {\r\n        Object.defineProperty(signal, \"reason\", {\r\n            enumerable: true,\r\n            value: reason,\r\n            configurable: true,\r\n            writable: true\r\n        });\r\n    }\r\n    ;\r\n    abortController.abort(reason);\r\n};\r\n// src/listenerMiddleware/exceptions.ts\r\nvar task = \"task\";\r\nvar listener = \"listener\";\r\nvar completed = \"completed\";\r\nvar cancelled = \"cancelled\";\r\nvar taskCancelled = \"task-\" + cancelled;\r\nvar taskCompleted = \"task-\" + completed;\r\nvar listenerCancelled = listener + \"-\" + cancelled;\r\nvar listenerCompleted = listener + \"-\" + completed;\r\nvar TaskAbortError = /** @class */ (function () {\r\n    function TaskAbortError(code) {\r\n        this.code = code;\r\n        this.name = \"TaskAbortError\";\r\n        this.message = task + \" \" + cancelled + \" (reason: \" + code + \")\";\r\n    }\r\n    return TaskAbortError;\r\n}());\r\n// src/listenerMiddleware/task.ts\r\nvar validateActive = function (signal) {\r\n    if (signal.aborted) {\r\n        throw new TaskAbortError(signal.reason);\r\n    }\r\n};\r\nfunction raceWithSignal(signal, promise2) {\r\n    var cleanup = noop;\r\n    return new Promise(function (resolve, reject) {\r\n        var notifyRejection = function () { return reject(new TaskAbortError(signal.reason)); };\r\n        if (signal.aborted) {\r\n            notifyRejection();\r\n            return;\r\n        }\r\n        cleanup = addAbortSignalListener(signal, notifyRejection);\r\n        promise2.finally(function () { return cleanup(); }).then(resolve, reject);\r\n    }).finally(function () {\r\n        cleanup = noop;\r\n    });\r\n}\r\nvar runTask = function (task2, cleanUp) { return __async(void 0, null, function () {\r\n    var value, error_1;\r\n    return __generator(this, function (_c) {\r\n        switch (_c.label) {\r\n            case 0:\r\n                _c.trys.push([0, 3, 4, 5]);\r\n                return [4 /*yield*/, Promise.resolve()];\r\n            case 1:\r\n                _c.sent();\r\n                return [4 /*yield*/, task2()];\r\n            case 2:\r\n                value = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: \"ok\",\r\n                        value: value\r\n                    }];\r\n            case 3:\r\n                error_1 = _c.sent();\r\n                return [2 /*return*/, {\r\n                        status: error_1 instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\r\n                        error: error_1\r\n                    }];\r\n            case 4:\r\n                cleanUp == null ? void 0 : cleanUp();\r\n                return [7 /*endfinally*/];\r\n            case 5: return [2 /*return*/];\r\n        }\r\n    });\r\n}); };\r\nvar createPause = function (signal) {\r\n    return function (promise2) {\r\n        return catchRejection(raceWithSignal(signal, promise2).then(function (output) {\r\n            validateActive(signal);\r\n            return output;\r\n        }));\r\n    };\r\n};\r\nvar createDelay = function (signal) {\r\n    var pause = createPause(signal);\r\n    return function (timeoutMs) {\r\n        return pause(new Promise(function (resolve) { return setTimeout(resolve, timeoutMs); }));\r\n    };\r\n};\r\n// src/listenerMiddleware/index.ts\r\nvar assign = Object.assign;\r\nvar INTERNAL_NIL_TOKEN = {};\r\nvar alm = \"listenerMiddleware\";\r\nvar createFork = function (parentAbortSignal, parentBlockingPromises) {\r\n    var linkControllers = function (controller) { return addAbortSignalListener(parentAbortSignal, function () { return abortControllerWithReason(controller, parentAbortSignal.reason); }); };\r\n    return function (taskExecutor, opts) {\r\n        assertFunction(taskExecutor, \"taskExecutor\");\r\n        var childAbortController = new AbortController();\r\n        linkControllers(childAbortController);\r\n        var result = runTask(function () { return __async(void 0, null, function () {\r\n            var result2;\r\n            return __generator(this, function (_c) {\r\n                switch (_c.label) {\r\n                    case 0:\r\n                        validateActive(parentAbortSignal);\r\n                        validateActive(childAbortController.signal);\r\n                        return [4 /*yield*/, taskExecutor({\r\n                                pause: createPause(childAbortController.signal),\r\n                                delay: createDelay(childAbortController.signal),\r\n                                signal: childAbortController.signal\r\n                            })];\r\n                    case 1:\r\n                        result2 = _c.sent();\r\n                        validateActive(childAbortController.signal);\r\n                        return [2 /*return*/, result2];\r\n                }\r\n            });\r\n        }); }, function () { return abortControllerWithReason(childAbortController, taskCompleted); });\r\n        if (opts == null ? void 0 : opts.autoJoin) {\r\n            parentBlockingPromises.push(result);\r\n        }\r\n        return {\r\n            result: createPause(parentAbortSignal)(result),\r\n            cancel: function () {\r\n                abortControllerWithReason(childAbortController, taskCancelled);\r\n            }\r\n        };\r\n    };\r\n};\r\nvar createTakePattern = function (startListening, signal) {\r\n    var take = function (predicate, timeout) { return __async(void 0, null, function () {\r\n        var unsubscribe, tuplePromise, promises, output;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    validateActive(signal);\r\n                    unsubscribe = function () {\r\n                    };\r\n                    tuplePromise = new Promise(function (resolve, reject) {\r\n                        var stopListening = startListening({\r\n                            predicate: predicate,\r\n                            effect: function (action, listenerApi) {\r\n                                listenerApi.unsubscribe();\r\n                                resolve([\r\n                                    action,\r\n                                    listenerApi.getState(),\r\n                                    listenerApi.getOriginalState()\r\n                                ]);\r\n                            }\r\n                        });\r\n                        unsubscribe = function () {\r\n                            stopListening();\r\n                            reject();\r\n                        };\r\n                    });\r\n                    promises = [\r\n                        tuplePromise\r\n                    ];\r\n                    if (timeout != null) {\r\n                        promises.push(new Promise(function (resolve) { return setTimeout(resolve, timeout, null); }));\r\n                    }\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, , 3, 4]);\r\n                    return [4 /*yield*/, raceWithSignal(signal, Promise.race(promises))];\r\n                case 2:\r\n                    output = _c.sent();\r\n                    validateActive(signal);\r\n                    return [2 /*return*/, output];\r\n                case 3:\r\n                    unsubscribe();\r\n                    return [7 /*endfinally*/];\r\n                case 4: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    return function (predicate, timeout) { return catchRejection(take(predicate, timeout)); };\r\n};\r\nvar getListenerEntryPropsFrom = function (options) {\r\n    var type = options.type, actionCreator = options.actionCreator, matcher = options.matcher, predicate = options.predicate, effect = options.effect;\r\n    if (type) {\r\n        predicate = createAction(type).match;\r\n    }\r\n    else if (actionCreator) {\r\n        type = actionCreator.type;\r\n        predicate = actionCreator.match;\r\n    }\r\n    else if (matcher) {\r\n        predicate = matcher;\r\n    }\r\n    else if (predicate) {\r\n    }\r\n    else {\r\n        throw new Error(\"Creating or removing a listener requires one of the known fields for matching an action\");\r\n    }\r\n    assertFunction(effect, \"options.listener\");\r\n    return { predicate: predicate, type: type, effect: effect };\r\n};\r\nvar createListenerEntry = function (options) {\r\n    var _c = getListenerEntryPropsFrom(options), type = _c.type, predicate = _c.predicate, effect = _c.effect;\r\n    var id = nanoid();\r\n    var entry = {\r\n        id: id,\r\n        effect: effect,\r\n        type: type,\r\n        predicate: predicate,\r\n        pending: new Set(),\r\n        unsubscribe: function () {\r\n            throw new Error(\"Unsubscribe not initialized\");\r\n        }\r\n    };\r\n    return entry;\r\n};\r\nvar cancelActiveListeners = function (entry) {\r\n    entry.pending.forEach(function (controller) {\r\n        abortControllerWithReason(controller, listenerCancelled);\r\n    });\r\n};\r\nvar createClearListenerMiddleware = function (listenerMap) {\r\n    return function () {\r\n        listenerMap.forEach(cancelActiveListeners);\r\n        listenerMap.clear();\r\n    };\r\n};\r\nvar safelyNotifyError = function (errorHandler, errorToNotify, errorInfo) {\r\n    try {\r\n        errorHandler(errorToNotify, errorInfo);\r\n    }\r\n    catch (errorHandlerError) {\r\n        setTimeout(function () {\r\n            throw errorHandlerError;\r\n        }, 0);\r\n    }\r\n};\r\nvar addListener = createAction(alm + \"/add\");\r\nvar clearAllListeners = createAction(alm + \"/removeAll\");\r\nvar removeListener = createAction(alm + \"/remove\");\r\nvar defaultErrorHandler = function () {\r\n    var args = [];\r\n    for (var _i = 0; _i < arguments.length; _i++) {\r\n        args[_i] = arguments[_i];\r\n    }\r\n    console.error.apply(console, __spreadArray([alm + \"/error\"], args));\r\n};\r\nfunction createListenerMiddleware(middlewareOptions) {\r\n    var _this = this;\r\n    if (middlewareOptions === void 0) { middlewareOptions = {}; }\r\n    var listenerMap = new Map();\r\n    var extra = middlewareOptions.extra, _c = middlewareOptions.onError, onError = _c === void 0 ? defaultErrorHandler : _c;\r\n    assertFunction(onError, \"onError\");\r\n    var insertEntry = function (entry) {\r\n        entry.unsubscribe = function () { return listenerMap.delete(entry.id); };\r\n        listenerMap.set(entry.id, entry);\r\n        return function (cancelOptions) {\r\n            entry.unsubscribe();\r\n            if (cancelOptions == null ? void 0 : cancelOptions.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        };\r\n    };\r\n    var findListenerEntry = function (comparator) {\r\n        for (var _i = 0, _c = Array.from(listenerMap.values()); _i < _c.length; _i++) {\r\n            var entry = _c[_i];\r\n            if (comparator(entry)) {\r\n                return entry;\r\n            }\r\n        }\r\n        return void 0;\r\n    };\r\n    var startListening = function (options) {\r\n        var entry = findListenerEntry(function (existingEntry) { return existingEntry.effect === options.effect; });\r\n        if (!entry) {\r\n            entry = createListenerEntry(options);\r\n        }\r\n        return insertEntry(entry);\r\n    };\r\n    var stopListening = function (options) {\r\n        var _c = getListenerEntryPropsFrom(options), type = _c.type, effect = _c.effect, predicate = _c.predicate;\r\n        var entry = findListenerEntry(function (entry2) {\r\n            var matchPredicateOrType = typeof type === \"string\" ? entry2.type === type : entry2.predicate === predicate;\r\n            return matchPredicateOrType && entry2.effect === effect;\r\n        });\r\n        if (entry) {\r\n            entry.unsubscribe();\r\n            if (options.cancelActive) {\r\n                cancelActiveListeners(entry);\r\n            }\r\n        }\r\n        return !!entry;\r\n    };\r\n    var notifyListener = function (entry, action, api, getOriginalState) { return __async(_this, null, function () {\r\n        var internalTaskController, take, autoJoinPromises, listenerError_1;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    internalTaskController = new AbortController();\r\n                    take = createTakePattern(startListening, internalTaskController.signal);\r\n                    autoJoinPromises = [];\r\n                    _c.label = 1;\r\n                case 1:\r\n                    _c.trys.push([1, 3, 4, 6]);\r\n                    entry.pending.add(internalTaskController);\r\n                    return [4 /*yield*/, Promise.resolve(entry.effect(action, assign({}, api, {\r\n                            getOriginalState: getOriginalState,\r\n                            condition: function (predicate, timeout) { return take(predicate, timeout).then(Boolean); },\r\n                            take: take,\r\n                            delay: createDelay(internalTaskController.signal),\r\n                            pause: createPause(internalTaskController.signal),\r\n                            extra: extra,\r\n                            signal: internalTaskController.signal,\r\n                            fork: createFork(internalTaskController.signal, autoJoinPromises),\r\n                            unsubscribe: entry.unsubscribe,\r\n                            subscribe: function () {\r\n                                listenerMap.set(entry.id, entry);\r\n                            },\r\n                            cancelActiveListeners: function () {\r\n                                entry.pending.forEach(function (controller, _, set) {\r\n                                    if (controller !== internalTaskController) {\r\n                                        abortControllerWithReason(controller, listenerCancelled);\r\n                                        set.delete(controller);\r\n                                    }\r\n                                });\r\n                            }\r\n                        })))];\r\n                case 2:\r\n                    _c.sent();\r\n                    return [3 /*break*/, 6];\r\n                case 3:\r\n                    listenerError_1 = _c.sent();\r\n                    if (!(listenerError_1 instanceof TaskAbortError)) {\r\n                        safelyNotifyError(onError, listenerError_1, {\r\n                            raisedBy: \"effect\"\r\n                        });\r\n                    }\r\n                    return [3 /*break*/, 6];\r\n                case 4: return [4 /*yield*/, Promise.allSettled(autoJoinPromises)];\r\n                case 5:\r\n                    _c.sent();\r\n                    abortControllerWithReason(internalTaskController, listenerCompleted);\r\n                    entry.pending.delete(internalTaskController);\r\n                    return [7 /*endfinally*/];\r\n                case 6: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    var clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\r\n    var middleware = function (api) { return function (next) { return function (action) {\r\n        if (!isAction(action)) {\r\n            return next(action);\r\n        }\r\n        if (addListener.match(action)) {\r\n            return startListening(action.payload);\r\n        }\r\n        if (clearAllListeners.match(action)) {\r\n            clearListenerMiddleware();\r\n            return;\r\n        }\r\n        if (removeListener.match(action)) {\r\n            return stopListening(action.payload);\r\n        }\r\n        var originalState = api.getState();\r\n        var getOriginalState = function () {\r\n            if (originalState === INTERNAL_NIL_TOKEN) {\r\n                throw new Error(alm + \": getOriginalState can only be called synchronously\");\r\n            }\r\n            return originalState;\r\n        };\r\n        var result;\r\n        try {\r\n            result = next(action);\r\n            if (listenerMap.size > 0) {\r\n                var currentState = api.getState();\r\n                var listenerEntries = Array.from(listenerMap.values());\r\n                for (var _i = 0, listenerEntries_1 = listenerEntries; _i < listenerEntries_1.length; _i++) {\r\n                    var entry = listenerEntries_1[_i];\r\n                    var runListener = false;\r\n                    try {\r\n                        runListener = entry.predicate(action, currentState, originalState);\r\n                    }\r\n                    catch (predicateError) {\r\n                        runListener = false;\r\n                        safelyNotifyError(onError, predicateError, {\r\n                            raisedBy: \"predicate\"\r\n                        });\r\n                    }\r\n                    if (!runListener) {\r\n                        continue;\r\n                    }\r\n                    notifyListener(entry, action, api, getOriginalState);\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            originalState = INTERNAL_NIL_TOKEN;\r\n        }\r\n        return result;\r\n    }; }; };\r\n    return {\r\n        middleware: middleware,\r\n        startListening: startListening,\r\n        stopListening: stopListening,\r\n        clearListeners: clearListenerMiddleware\r\n    };\r\n}\r\n// src/autoBatchEnhancer.ts\r\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\r\nvar prepareAutoBatched = function () { return function (payload) {\r\n    var _c;\r\n    return ({\r\n        payload: payload,\r\n        meta: (_c = {}, _c[SHOULD_AUTOBATCH] = true, _c)\r\n    });\r\n}; };\r\nvar promise;\r\nvar queueMicrotaskShim = typeof queueMicrotask === \"function\" ? queueMicrotask.bind(typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : globalThis) : function (cb) { return (promise || (promise = Promise.resolve())).then(cb).catch(function (err) { return setTimeout(function () {\r\n    throw err;\r\n}, 0); }); };\r\nvar createQueueWithTimer = function (timeout) {\r\n    return function (notify) {\r\n        setTimeout(notify, timeout);\r\n    };\r\n};\r\nvar rAF = typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10);\r\nvar autoBatchEnhancer = function (options) {\r\n    if (options === void 0) { options = { type: \"raf\" }; }\r\n    return function (next) { return function () {\r\n        var args = [];\r\n        for (var _i = 0; _i < arguments.length; _i++) {\r\n            args[_i] = arguments[_i];\r\n        }\r\n        var store = next.apply(void 0, args);\r\n        var notifying = true;\r\n        var shouldNotifyAtEndOfTick = false;\r\n        var notificationQueued = false;\r\n        var listeners = new Set();\r\n        var queueCallback = options.type === \"tick\" ? queueMicrotaskShim : options.type === \"raf\" ? rAF : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\r\n        var notifyListeners = function () {\r\n            notificationQueued = false;\r\n            if (shouldNotifyAtEndOfTick) {\r\n                shouldNotifyAtEndOfTick = false;\r\n                listeners.forEach(function (l) { return l(); });\r\n            }\r\n        };\r\n        return Object.assign({}, store, {\r\n            subscribe: function (listener2) {\r\n                var wrappedListener = function () { return notifying && listener2(); };\r\n                var unsubscribe = store.subscribe(wrappedListener);\r\n                listeners.add(listener2);\r\n                return function () {\r\n                    unsubscribe();\r\n                    listeners.delete(listener2);\r\n                };\r\n            },\r\n            dispatch: function (action) {\r\n                var _a;\r\n                try {\r\n                    notifying = !((_a = action == null ? void 0 : action.meta) == null ? void 0 : _a[SHOULD_AUTOBATCH]);\r\n                    shouldNotifyAtEndOfTick = !notifying;\r\n                    if (shouldNotifyAtEndOfTick) {\r\n                        if (!notificationQueued) {\r\n                            notificationQueued = true;\r\n                            queueCallback(notifyListeners);\r\n                        }\r\n                    }\r\n                    return store.dispatch(action);\r\n                }\r\n                finally {\r\n                    notifying = true;\r\n                }\r\n            }\r\n        });\r\n    }; };\r\n};\r\n// src/index.ts\r\nenableES5();\r\nexport { EnhancerArray, MiddlewareArray, SHOULD_AUTOBATCH, TaskAbortError, addListener, autoBatchEnhancer, clearAllListeners, configureStore, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, default2 as createNextState, createReducer, createSelector2 as createSelector, createSerializableStateInvariantMiddleware, createSlice, current2 as current, findNonSerializableValue, freeze, getDefaultMiddleware, getType, isAction, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isDraft4 as isDraft, isFSA as isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isPlainObject, isRejected, isRejectedWithValue, miniSerializeError, nanoid, original, prepareAutoBatched, removeListener, unwrapResult };\r\n//# sourceMappingURL=redux-toolkit.esm.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,SAAA;AACA;AACA,SAAAC,OAAA,IAAAC,QAAA,EAAAC,OAAA,IAAAC,QAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,OAAA,IAAAC,QAAA;AAQA,SAAAC,cAAA,IAAAC,eAAA;;ACVA,SAAAP,OAAA,EAAAI,OAAA;AACA,SAAAE,cAAA;AASO,IAAME,uBAAA,GAAiD,SAAAA,CAAA;EAAA,IAAAC,IAAA;OAAA,IAAAC,EAAA,IACzD,EADyDA,EAAA,GAAAC,SAAA,CAAAC,MACzD,EADyDF,EAAA,EACzD;IADyDD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAG5D,IAAMG,QAAA,GAAYP,cAAA,CAAAQ,KAAA,SAA0BL,IAAA;EAC5C,IAAMM,eAAA,GAAkB,SAAAA,CAACC,KAAA;IAAA,IAAAC,IAAA;SAAA,IAAAP,EAAA,IAAmB,EAAnBA,EAAA,GAAAC,SAAA,CAAAC,MAAmB,EAAnBF,EAAA,EAAmB;MAAnBO,IAAA,CAAAP,EAAA,QAAAC,SAAA,CAAAD,EAAA;;IACvB,OAAAG,QAAA,CAAAC,KAAA,SAAAI,aAAA,EAASd,OAAA,CAAQY,KAAA,IAAShB,OAAA,CAAQgB,KAAA,IAASA,KAAA,GAAUC,IAAA;EAArD,CAAqD;EACvD,OAAOF,eAAA;AAAA;;ACJT,SAAAI,WAAA,EAAAC,OAAA,IAAAC,QAAA,EAAAC,eAAA,EAAAC,eAAA;;ACXA,SAAAH,OAAA;AAmOO,IAAMI,mBAAA,GACX,OAAOC,MAAA,KAAW,eACjBA,MAAA,CAAeC,oCAAA,GACXD,MAAA,CAAeC,oCAAA,GAChB;EACE,IAAIf,SAAA,CAAUC,MAAA,KAAW,GAAG,OAAO;EACnC,IAAI,OAAOD,SAAA,CAAU,OAAO,UAAU,OAAOS,OAAA;EAC7C,OAAOA,OAAA,CAAQN,KAAA,CAAM,MAAMH,SAAA;AAAA;AAM5B,IAAMgB,gBAAA,GAGX,OAAOF,MAAA,KAAW,eAAgBA,MAAA,CAAeG,4BAAA,GAC5CH,MAAA,CAAeG,4BAAA,GAChB;EACE,OAAO,UAAUC,KAAA;IACf,OAAOA,KAAA;EAAA;AAAA;;AC9OF,SAAAC,cAAuBd,KAAA;EACpC,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM,OAAO;EAExD,IAAIe,KAAA,GAAQC,MAAA,CAAOC,cAAA,CAAejB,KAAA;EAClC,IAAIe,KAAA,KAAU,MAAM,OAAO;EAE3B,IAAIG,SAAA,GAAYH,KAAA;EAChB,OAAOC,MAAA,CAAOC,cAAA,CAAeC,SAAA,MAAe,MAAM;IAChDA,SAAA,GAAYF,MAAA,CAAOC,cAAA,CAAeC,SAAA;;EAGpC,OAAOH,KAAA,KAAUG,SAAA;AAAA;;ACnBnB,OAAAC,eAAA;;AC2KO,IAAMC,gBAAA,GAAmB,SAAAA,CAC9BC,CAAA;EAEA,OAAOA,CAAA,IAAK,OAAQA,CAAA,CAA0BC,KAAA,KAAU;AAAA;;ACqFnD,SAAAC,aAAsBC,IAAA,EAAcC,aAAA;EACzC,SAAAC,cAAA;IAAA,IAAAjC,IAAA;SAAA,IAAAC,EAAA,IAA0B,EAA1BA,EAAA,GAAAC,SAAA,CAAAC,MAA0B,EAA1BF,EAAA,EAA0B;MAA1BD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACE,IAAI+B,aAAA,EAAe;MACjB,IAAIE,QAAA,GAAWF,aAAA,CAAA3B,KAAA,SAAiBL,IAAA;MAChC,IAAI,CAACkC,QAAA,EAAU;QACb,MAAM,IAAIC,KAAA,CAAM;;MAGlB,OAAOC,cAAA,CAAAA,cAAA;QACLL,IAAA,EAAAA,IAAA;QACAM,OAAA,EAASH,QAAA,CAASG;OAAA,EACd,UAAUH,QAAA,IAAY;QAAEI,IAAA,EAAMJ,QAAA,CAASI;MAAA,IACvC,WAAWJ,QAAA,IAAY;QAAEK,KAAA,EAAOL,QAAA,CAASK;MAAA;;IAGjD,OAAO;MAAER,IAAA,EAAAA,IAAA;MAAMM,OAAA,EAASrC,IAAA,CAAK;IAAA;EAAA;EAG/BiC,aAAA,CAAcO,QAAA,GAAW;IAAM,YAAGT,IAAA;EAAH,CAAG;EAElCE,aAAA,CAAcF,IAAA,GAAOA,IAAA;EAErBE,aAAA,CAAcJ,KAAA,GAAQ,UAACY,MAAA;IACrB,OAAAA,MAAA,CAAOV,IAAA,KAASA,IAAA;EAAhB,CAAgB;EAElB,OAAOE,aAAA;AAAA;AAMF,SAAAS,SAAkBD,MAAA;EACvB,OAAOpB,aAAA,CAAcoB,MAAA,KAAW,UAAUA,MAAA;AAAA;AAMrC,SAAAE,gBACLF,MAAA;EAEA,OACE,OAAOA,MAAA,KAAW,cAClB,UAAUA,MAAA,IAEVd,gBAAA,CAAiBc,MAAA;AAAA;AAOd,SAAAG,MAAeH,MAAA;EAMpB,OACEC,QAAA,CAASD,MAAA,KACT,OAAOA,MAAA,CAAOV,IAAA,KAAS,YACvBR,MAAA,CAAOsB,IAAA,CAAKJ,MAAA,EAAQK,KAAA,CAAMC,UAAA;AAAA;AAI9B,SAAAA,WAAoBC,GAAA;EAClB,OAAO,CAAC,QAAQ,WAAW,SAAS,QAAQC,OAAA,CAAQD,GAAA,IAAO;AAAA;AAatD,SAAAE,QACLjB,aAAA;EAEA,OAAO,KAAGA,aAAA;AAAA;;AC5UL,SAAAkB,WAAoBpB,IAAA;EACzB,IAAMqB,SAAA,GAAYrB,IAAA,GAAO,MAAGA,IAAA,EAAOsB,KAAA,CAAM,OAAO;EAChD,IAAMC,UAAA,GAAaF,SAAA,CAAUA,SAAA,CAAUjD,MAAA,GAAS,MAAM;EACtD,OAAO,6CACL4B,IAAA,IAAQ,wHAEsEuB,UAAA,kCAAyCA,UAAA;AAAA;AAGpH,SAAAC,uCACLC,OAAmD;EAAnD,IAAAA,OAAA;IAAAA,OAAA,KAAmD;EAAA;EAEnD,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO;MAAM,iBAACC,IAAA;QAAS,iBAACnB,MAAA;UAAW,OAAAmB,IAAA,CAAKnB,MAAA;QAAL,CAAK;MAAjB,CAAiB;IAA3B,CAA2B;;EAElC,IAAAoB,EAAA,GAAkCL,OAAA,CAAAb,eAAhB;IAAlBmB,gBAAA,GAAAD,EAAA,cAAkBlB,eAAA,GAAAkB,EAAA;EAC1B,OAAO;IAAM,iBAACD,IAAA;MAAS,iBAACnB,MAAA;QACtB,IAAIqB,gBAAA,CAAgBrB,MAAA,GAAS;UAC3BsB,OAAA,CAAQC,IAAA,CAAKb,UAAA,CAAWV,MAAA,CAAOV,IAAA;;QAEjC,OAAO6B,IAAA,CAAKnB,MAAA;MAAA;IAJS,CAIT;EAJD,CAIC;AAAA;;AC/BhB,OAAAwB,eAAA,IAAAC,WAAA;AAGO,SAAAC,oBAA6BC,QAAA,EAAkBC,MAAA;EACpD,IAAIC,OAAA,GAAU;EACd,OAAO;IACLC,WAAA,WAAAA,CAAeC,EAAA;MACb,IAAMC,OAAA,GAAUC,IAAA,CAAKC,GAAA;MACrB,IAAI;QACF,OAAOH,EAAA;OAAA,SACP;QACA,IAAMI,QAAA,GAAWF,IAAA,CAAKC,GAAA;QACtBL,OAAA,IAAWM,QAAA,GAAWH,OAAA;;IAAA;IAG1BI,cAAA,WAAAA,CAAA;MACE,IAAIP,OAAA,GAAUF,QAAA,EAAU;QACtBL,OAAA,CAAQC,IAAA,CAAQK,MAAA,cAAeC,OAAA,wDAA0DF,QAAA;;IAAA;GAAA;AAAA;AAe1F,IAAAU,eAAA,0BAAAC,MAAA;EAEGC,SAAA,CAAAF,eAAA,EAAAC,MAAA;EAER,SAAAD,gBAAA;IAAA,IAAA9E,IAAA;SAAA,IAAAC,EAAA,IAAe,EAAfA,EAAA,GAAAC,SAAA,CAAAC,MAAe,EAAfF,EAAA,EAAe;MAAfD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IAAA,IAAAgF,KAAA,GAAAF,MAAA,CAAA1E,KAAA,OACWL,IAAA;IACTuB,MAAA,CAAO2D,cAAA,CAAeD,KAAA,EAAMH,eAAA,CAAgBK,SAAA;;EAAA;EAAA5D,MAAA,CAAA6D,cAAA,CAAAN,eAAA,EAGlCO,MAAA,CAAOC,OAAA;SAH2B,SAAAC,CAAA;MAI5C,OAAOT,eAAA;IAAA;;;;EAUTA,eAAA,CAAAK,SAAA,CAAAK,MAAA;IAAA,IAAAC,GAAA;SAAA,IAAAxF,EAAA,IAAU,EAAVA,EAAA,GAAAC,SAAA,CAAAC,MAAU,EAAVF,EAAA,EAAU;MAAVwF,GAAA,CAAAxF,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACE,OAAO8E,MAAA,CAAAI,SAAA,CAAMK,MAAA,CAAOnF,KAAA,CAAM,MAAMoF,GAAA;EAAA;EAWlCX,eAAA,CAAAK,SAAA,CAAAO,OAAA;IAAA,IAAAD,GAAA;SAAA,IAAAxF,EAAA,IAAW,EAAXA,EAAA,GAAAC,SAAA,CAAAC,MAAW,EAAXF,EAAA,EAAW;MAAXwF,GAAA,CAAAxF,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACE,IAAIwF,GAAA,CAAItF,MAAA,KAAW,KAAKwF,KAAA,CAAMC,OAAA,CAAQH,GAAA,CAAI,KAAK;MAC7C,YAAWX,eAAA,CAAAe,IAAA,CAAAxF,KAAA,CAAAyE,eAAA,EAAArE,aAAA,WAAmBgF,GAAA,CAAI,GAAGD,MAAA,CAAO;;IAE9C,YAAWV,eAAA,CAAAe,IAAA,CAAAxF,KAAA,CAAAyE,eAAA,EAAArE,aAAA,WAAmBgF,GAAA,CAAID,MAAA,CAAO;EAAA;EAAA,OAAAV,eAAA;AAAA,CApCtC,CAEGa,KAAA,CAkCmC;AAOtC,IAAAG,aAAA,0BAAAf,MAAA;EAEGC,SAAA,CAAAc,aAAA,EAAAf,MAAA;EAER,SAAAe,cAAA;IAAA,IAAA9F,IAAA;SAAA,IAAAC,EAAA,IAAe,EAAfA,EAAA,GAAAC,SAAA,CAAAC,MAAe,EAAfF,EAAA,EAAe;MAAfD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IAAA,IAAAgF,KAAA,GAAAF,MAAA,CAAA1E,KAAA,OACWL,IAAA;IACTuB,MAAA,CAAO2D,cAAA,CAAeD,KAAA,EAAMa,aAAA,CAAcX,SAAA;;EAAA;EAAA5D,MAAA,CAAA6D,cAAA,CAAAU,aAAA,EAGhCT,MAAA,CAAOC,OAAA;SAHyB,SAAAC,CAAA;MAI1C,OAAOO,aAAA;IAAA;;;;EAUTA,aAAA,CAAAX,SAAA,CAAAK,MAAA;IAAA,IAAAC,GAAA;SAAA,IAAAxF,EAAA,IAAU,EAAVA,EAAA,GAAAC,SAAA,CAAAC,MAAU,EAAVF,EAAA,EAAU;MAAVwF,GAAA,CAAAxF,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACE,OAAO8E,MAAA,CAAAI,SAAA,CAAMK,MAAA,CAAOnF,KAAA,CAAM,MAAMoF,GAAA;EAAA;EAWlCK,aAAA,CAAAX,SAAA,CAAAO,OAAA;IAAA,IAAAD,GAAA;SAAA,IAAAxF,EAAA,IAAW,EAAXA,EAAA,GAAAC,SAAA,CAAAC,MAAW,EAAXF,EAAA,EAAW;MAAXwF,GAAA,CAAAxF,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACE,IAAIwF,GAAA,CAAItF,MAAA,KAAW,KAAKwF,KAAA,CAAMC,OAAA,CAAQH,GAAA,CAAI,KAAK;MAC7C,YAAWK,aAAA,CAAAD,IAAA,CAAAxF,KAAA,CAAAyF,aAAA,EAAArF,aAAA,WAAiBgF,GAAA,CAAI,GAAGD,MAAA,CAAO;;IAE5C,YAAWM,aAAA,CAAAD,IAAA,CAAAxF,KAAA,CAAAyF,aAAA,EAAArF,aAAA,WAAiBgF,GAAA,CAAID,MAAA,CAAO;EAAA;EAAA,OAAAM,aAAA;AAAA,CApCpC,CAEGH,KAAA,CAkCiC;AAIpC,SAAAI,gBAA4BC,GAAA;EACjC,OAAO9B,WAAA,CAAY8B,GAAA,IAAO/B,eAAA,CAAgB+B,GAAA,EAAK,aAAM,KAAMA,GAAA;AAAA;;AC/G7D,IAAMC,YAAA,GAAwBxC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;AACvD,IAAMuC,MAAA,GAAiB;AAKvB,SAAAC,UAAmBC,SAAA,EAAgBC,OAAA;EACjC,IAAID,SAAA,EAAW;IACb;;EAKF,IAAIH,YAAA,EAAc;IAChB,MAAM,IAAI9D,KAAA,CAAM+D,MAAA;;EAKlB,MAAM,IAAI/D,KAAA,CAAS+D,MAAA,WAAWG,OAAA,IAAW;AAAA;AAG3C,SAAAC,UACEC,GAAA,EACAC,UAAA,EACAC,MAAA,EACAC,QAAA;EAEA,OAAOC,IAAA,CAAKL,SAAA,CAAUC,GAAA,EAAKK,YAAA,CAAaJ,UAAA,EAAYE,QAAA,GAAWD,MAAA;AAAA;AAGjE,SAAAG,aACEJ,UAAA,EACAE,QAAA;EAEA,IAAIG,KAAA,GAAe;IACjBhE,IAAA,GAAc;EAEhB,IAAI,CAAC6D,QAAA,EACHA,QAAA,GAAW,SAAAA,CAAUI,CAAA,EAAWvG,KAAA;IAC9B,IAAIsG,KAAA,CAAM,OAAOtG,KAAA,EAAO,OAAO;IAC/B,OACE,iBAAiBsC,IAAA,CAAKkE,KAAA,CAAM,GAAGF,KAAA,CAAM5D,OAAA,CAAQ1C,KAAA,GAAQyG,IAAA,CAAK,OAAO;EAAA;EAIvE,OAAO,UAAqBhE,GAAA,EAAazC,KAAA;IACvC,IAAIsG,KAAA,CAAM1G,MAAA,GAAS,GAAG;MACpB,IAAI8G,OAAA,GAAUJ,KAAA,CAAM5D,OAAA,CAAQ;MAC5B,CAACgE,OAAA,GAAUJ,KAAA,CAAMK,MAAA,CAAOD,OAAA,GAAU,KAAKJ,KAAA,CAAMM,IAAA,CAAK;MAClD,CAACF,OAAA,GAAUpE,IAAA,CAAKqE,MAAA,CAAOD,OAAA,EAASG,QAAA,EAAUpE,GAAA,IAAOH,IAAA,CAAKsE,IAAA,CAAKnE,GAAA;MAC3D,IAAI,CAAC6D,KAAA,CAAM5D,OAAA,CAAQ1C,KAAA,GAAQA,KAAA,GAAQmG,QAAA,CAAUW,IAAA,CAAK,MAAMrE,GAAA,EAAKzC,KAAA;KAAA,MACxDsG,KAAA,CAAMM,IAAA,CAAK5G,KAAA;IAElB,OAAOiG,UAAA,IAAc,OAAOjG,KAAA,GAAQiG,UAAA,CAAWa,IAAA,CAAK,MAAMrE,GAAA,EAAKzC,KAAA;EAAA;AAAA;AAS5D,SAAA+G,mBAA4B/G,KAAA;EACjC,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,IAAS,QAAQgB,MAAA,CAAOgG,QAAA,CAAShH,KAAA;AAAA;AAGhE,SAAAiH,kBACLC,WAAA,EACAC,WAAA,EACAnB,GAAA;EAEA,IAAMoB,iBAAA,GAAoBC,eAAA,CAAgBH,WAAA,EAAaC,WAAA,EAAanB,GAAA;EACpE,OAAO;IACLsB,eAAA,WAAAA,CAAA;MACE,OAAOA,eAAA,CAAgBJ,WAAA,EAAaC,WAAA,EAAaC,iBAAA,EAAmBpB,GAAA;IAAA;GAAA;AAAA;AAU1E,SAAAqB,gBACEH,WAAA,EACAC,WAA2B,EAC3BnB,GAAA,EACAuB,IAAe,EACfC,cAA+C;EAH/C,IAAAL,WAAA;IAAAA,WAAA,KAA2B;EAAA;EAE3B,IAAAI,IAAA;IAAAA,IAAA,KAAe;EAAA;EACf,IAAAC,cAAA;IAAAA,cAAA,OAA+CC,GAAA;EAAA;EAE/C,IAAMC,OAAA,GAAoC;IAAE1H,KAAA,EAAOgG;EAAA;EAEnD,IAAI,CAACkB,WAAA,CAAYlB,GAAA,KAAQ,CAACwB,cAAA,CAAeG,GAAA,CAAI3B,GAAA,GAAM;IACjDwB,cAAA,CAAeI,GAAA,CAAI5B,GAAA;IACnB0B,OAAA,CAAQG,QAAA,GAAW;IAEnB,SAAWpF,GAAA,IAAOuD,GAAA,EAAK;MACrB,IAAM8B,SAAA,GAAYP,IAAA,GAAOA,IAAA,GAAO,MAAM9E,GAAA,GAAMA,GAAA;MAC5C,IAAI0E,WAAA,CAAYvH,MAAA,IAAUuH,WAAA,CAAYzE,OAAA,CAAQoF,SAAA,MAAe,IAAI;QAC/D;;MAGFJ,OAAA,CAAQG,QAAA,CAASpF,GAAA,IAAO4E,eAAA,CACtBH,WAAA,EACAC,WAAA,EACAnB,GAAA,CAAIvD,GAAA,GACJqF,SAAA;;;EAIN,OAAOJ,OAAA;AAAA;AAKT,SAAAJ,gBACEJ,WAAA,EACAa,YAA4B,EAC5BC,eAAA,EACAhC,GAAA,EACAiC,aAAyB,EACzBV,IAAe;EAJf,IAAAQ,YAAA;IAAAA,YAAA,KAA4B;EAAA;EAG5B,IAAAE,aAAA;IAAAA,aAAA,QAAyB;EAAA;EACzB,IAAAV,IAAA;IAAAA,IAAA,KAAe;EAAA;EAEf,IAAMW,OAAA,GAAUF,eAAA,GAAkBA,eAAA,CAAgBhI,KAAA,GAAQ;EAE1D,IAAMmI,OAAA,GAAUD,OAAA,KAAYlC,GAAA;EAE5B,IAAIiC,aAAA,IAAiB,CAACE,OAAA,IAAW,CAACC,MAAA,CAAOC,KAAA,CAAMrC,GAAA,GAAM;IACnD,OAAO;MAAEsC,UAAA,EAAY;MAAMf,IAAA,EAAAA;IAAA;;EAG7B,IAAIL,WAAA,CAAYgB,OAAA,KAAYhB,WAAA,CAAYlB,GAAA,GAAM;IAC5C,OAAO;MAAEsC,UAAA,EAAY;IAAA;;EAIvB,IAAMC,YAAA,GAAwC;EAC9C,SAAS9F,GAAA,IAAOuF,eAAA,CAAgBH,QAAA,EAAU;IACxCU,YAAA,CAAa9F,GAAA,IAAO;;EAEtB,SAASA,GAAA,IAAOuD,GAAA,EAAK;IACnBuC,YAAA,CAAa9F,GAAA,IAAO;;EAGtB,IAAM+F,eAAA,GAAkBT,YAAA,CAAanI,MAAA,GAAS;0BAErC6C,GAAA;IACP,IAAMgG,UAAA,GAAalB,IAAA,GAAOA,IAAA,GAAO,MAAM9E,GAAA,GAAMA,GAAA;IAE7C,IAAI+F,eAAA,EAAiB;MACnB,IAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAK,UAACC,OAAA;QACpC,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAA;;QAEtB,OAAOA,UAAA,KAAeG,OAAA;MAAA;MAExB,IAAIF,UAAA,EAAY;;;;IAKlB,IAAMK,MAAA,GAASzB,eAAA,CACbJ,WAAA,EACAa,YAAA,EACAC,eAAA,CAAgBH,QAAA,CAASpF,GAAA,GACzBuD,GAAA,CAAIvD,GAAA,GACJ0F,OAAA,EACAM,UAAA;IAGF,IAAIM,MAAA,CAAOT,UAAA,EAAY;;eACdS;MAAA;;;EAzBX,SAAStG,GAAA,IAAO8F,YAAA;0BAAP9F,GAAA;;;EA4BT,OAAO;IAAE6F,UAAA,EAAY;EAAA;AAAA;AAuChB,SAAAU,wCACL/F,OAAoD;EAApD,IAAAA,OAAA;IAAAA,OAAA,KAAoD;EAAA;EAEpD,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO;MAAM,iBAACC,IAAA;QAAS,iBAACnB,MAAA;UAAW,OAAAmB,IAAA,CAAKnB,MAAA;QAAL,CAAK;MAAjB,CAAiB;IAA3B,CAA2B;;EAIxC,IAAAoB,EAAA,GAIEL,OAAA,CAAAiE,WAJY;IAAdA,WAAA,GAAA5D,EAAA,cAAcyD,kBAAA,GAAAzD,EAAA;IACdyE,YAAA,GAGE9E,OAAA,CAAA8E,YAHF;IACAkB,EAAA,GAEEhG,OAAA,CAAAiG,SAFU;IAAZA,SAAA,GAAAD,EAAA,cAAY,KAAAA,EAAA;IACZE,MAAA,GACElG,OAAA,CAAAkG,MADF;EAIFpB,YAAA,GAAeA,YAAA,IAAgBoB,MAAA;EAE/B,IAAMC,KAAA,GAAQnC,iBAAA,CAAkB3B,IAAA,CAAK,MAAM4B,WAAA,EAAaa,YAAA;EAExD,OAAO,UAACzE,EAAE;QAAA+F,QAAA,GAAA/F,EAAA,CAAA+F,QAAA;IACR,IAAIC,KAAA,GAAQD,QAAA;IACZ,IAAIE,OAAA,GAAUH,KAAA,CAAME,KAAA;IAEpB,IAAIP,MAAA;IACJ,OAAO,UAAC1F,IAAA;MAAS,iBAACnB,MAAA;QAChB,IAAMsH,YAAA,GAAe5F,mBAAA,CACnBsF,SAAA,EACA;QAGFM,YAAA,CAAaxF,WAAA,CAAY;UACvBsF,KAAA,GAAQD,QAAA;UAERN,MAAA,GAASQ,OAAA,CAAQjC,eAAA;UAEjBiC,OAAA,GAAUH,KAAA,CAAME,KAAA;UAEhB1D,SAAA,CACE,CAACmD,MAAA,CAAOT,UAAA,EACR,qEACES,MAAA,CAAOxB,IAAA,IAAQ;QAAA;QAKrB,IAAMkC,gBAAA,GAAmBpG,IAAA,CAAKnB,MAAA;QAE9BsH,YAAA,CAAaxF,WAAA,CAAY;UACvBsF,KAAA,GAAQD,QAAA;UAERN,MAAA,GAASQ,OAAA,CAAQjC,eAAA;UAEjBiC,OAAA,GAAUH,KAAA,CAAME,KAAA;UAEhBP,MAAA,CAAOT,UAAA,IACL1C,SAAA,CACE,CAACmD,MAAA,CAAOT,UAAA,EACR,oEACES,MAAA,CAAOxB,IAAA,IAAQ,+DACsCxB,SAAA,CACrD7D,MAAA;QAAA;QAKRsH,YAAA,CAAalF,cAAA;QAEb,OAAOmF,gBAAA;MAAA;IA3CQ,CA2CR;EAAA;AAAA;;AClRN,SAAAC,QAAiBjE,GAAA;EACtB,IAAMjE,IAAA,GAAO,OAAOiE,GAAA;EACpB,OACEA,GAAA,IAAO,QACPjE,IAAA,KAAS,YACTA,IAAA,KAAS,aACTA,IAAA,KAAS,YACT4D,KAAA,CAAMC,OAAA,CAAQI,GAAA,KACd3E,aAAA,CAAc2E,GAAA;AAAA;AAcX,SAAAkE,yBACL3J,KAAA,EACAuH,IAAe,EACfqC,cAA8C,EAC9CC,UAAA,EACA9B,YAA4B,EAC5B+B,KAAA;EAJA,IAAAvC,IAAA;IAAAA,IAAA,KAAe;EAAA;EACf,IAAAqC,cAAA;IAAAA,cAAA,GAAAF,OAA8C;EAAA;EAE9C,IAAA3B,YAAA;IAAAA,YAAA,KAA4B;EAAA;EAG5B,IAAIgC,uBAAA;EAEJ,IAAI,CAACH,cAAA,CAAe5J,KAAA,GAAQ;IAC1B,OAAO;MACLgK,OAAA,EAASzC,IAAA,IAAQ;MACjBvH,KAAA,EAAAA;KAAA;;EAIJ,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,OAAO;;EAGT,IAAI8J,KAAA,oBAAAA,KAAA,CAAOnC,GAAA,CAAI3H,KAAA,GAAQ,OAAO;EAE9B,IAAMiK,OAAA,GAAUJ,UAAA,IAAc,OAAOA,UAAA,CAAW7J,KAAA,IAASgB,MAAA,CAAOiJ,OAAA,CAAQjK,KAAA;EAExE,IAAMwI,eAAA,GAAkBT,YAAA,CAAanI,MAAA,GAAS;0BAElC6C,GAAA,EAAKyH,WAAA;IACf,IAAMzB,UAAA,GAAalB,IAAA,GAAOA,IAAA,GAAO,MAAM9E,GAAA,GAAMA,GAAA;IAE7C,IAAI+F,eAAA,EAAiB;MACnB,IAAME,UAAA,GAAaX,YAAA,CAAaY,IAAA,CAAK,UAACC,OAAA;QACpC,IAAIA,OAAA,YAAmBC,MAAA,EAAQ;UAC7B,OAAOD,OAAA,CAAQE,IAAA,CAAKL,UAAA;;QAEtB,OAAOA,UAAA,KAAeG,OAAA;MAAA;MAExB,IAAIF,UAAA,EAAY;;;;IAKlB,IAAI,CAACkB,cAAA,CAAeM,WAAA,GAAc;;eACzB;UACLF,OAAA,EAASvB,UAAA;UACTzI,KAAA,EAAOkK;;MAAA;;IAIX,IAAI,OAAOA,WAAA,KAAgB,UAAU;MACnCH,uBAAA,GAA0BJ,wBAAA,CACxBO,WAAA,EACAzB,UAAA,EACAmB,cAAA,EACAC,UAAA,EACA9B,YAAA,EACA+B,KAAA;MAGF,IAAIC,uBAAA,EAAyB;;iBACpBA;QAAA;;;;EAjCb,KAAiC,IAAArK,EAAA,MAAAyK,SAAA,GAAAF,OAAA,EAAAvK,EAAA,GAAAyK,SAAA,CAAAvK,MAAA,EAAAF,EAAA;IAAtB,IAAA4D,EAAA,GAAA6G,SAAA,CAAAzK,EAAA,CAAM;MAAL+C,GAAA,GAAAa,EAAA;MAAK4G,WAAA,GAAA5G,EAAA;0BAALb,GAAA,EAAKyH,WAAA;;;EAsCjB,IAAIJ,KAAA,IAASM,cAAA,CAAepK,KAAA,GAAQ8J,KAAA,CAAMlC,GAAA,CAAI5H,KAAA;EAE9C,OAAO;AAAA;AAGF,SAAAoK,eAAwBpK,KAAA;EAC7B,IAAI,CAACgB,MAAA,CAAOgG,QAAA,CAAShH,KAAA,GAAQ,OAAO;EAEpC,KAA0B,IAAAN,EAAA,IAAc,EAAd4D,EAAA,GAAAtC,MAAA,CAAOqJ,MAAA,CAAOrK,KAAA,GAAdN,EAAA,GAAA4D,EAAA,CAAA1D,MAAc,EAAdF,EAAA,EAAc,EAAQ;IAAhD,IAAWwK,WAAA,GAAA5G,EAAA,CAAA5D,EAAA;IACT,IAAI,OAAOwK,WAAA,KAAgB,YAAYA,WAAA,KAAgB,MAAM;IAE7D,IAAI,CAACE,cAAA,CAAeF,WAAA,GAAc,OAAO;;EAG3C,OAAO;AAAA;AAyEF,SAAAI,2CACLrH,OAAuD;EAAvD,IAAAA,OAAA;IAAAA,OAAA,KAAuD;EAAA;EAEvD,IAAIC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,OAAO;MAAM,iBAACC,IAAA;QAAS,iBAACnB,MAAA;UAAW,OAAAmB,IAAA,CAAKnB,MAAA;QAAL,CAAK;MAAjB,CAAiB;IAA3B,CAA2B;;EAGxC,IAAAoB,EAAA,GASEL,OAAA,CAAA2G,cATe;IAAjBA,cAAA,GAAAtG,EAAA,cAAiBoG,OAAA,GAAApG,EAAA;IACjBuG,UAAA,GAQE5G,OAAA,CAAA4G,UARF;IACAZ,EAAA,GAOEhG,OAAA,CAAAsH,cAPe;IAAjBA,cAAA,GAAAtB,EAAA,cAAiB,KAAAA,EAAA;IACjBuB,EAAA,GAMEvH,OAAA,CAAAwH,kBANgC;IAAlCA,kBAAA,GAAAD,EAAA,cAAqB,CAAC,YAAY,wBAAAA,EAAA;IAClCE,EAAA,GAKEzH,OAAA,CAAA8E,YALa;IAAfA,YAAA,GAAA2C,EAAA,cAAe,KAAAA,EAAA;IACfC,EAAA,GAIE1H,OAAA,CAAAiG,SAJU;IAAZA,SAAA,GAAAyB,EAAA,cAAY,KAAAA,EAAA;IACZC,EAAA,GAGE3H,OAAA,CAAA4H,WAHY;IAAdA,WAAA,GAAAD,EAAA,cAAc,QAAAA,EAAA;IACdE,EAAA,GAEE7H,OAAA,CAAA8H,aAFc;IAAhBA,aAAA,GAAAD,EAAA,cAAgB,QAAAA,EAAA;IAChBE,EAAA,GACE/H,OAAA,CAAAgI,YADa;IAAfA,YAAA,GAAAD,EAAA,cAAe,QAAAA,EAAA;EAGjB,IAAMlB,KAAA,GACJ,CAACmB,YAAA,IAAgBC,OAAA,GAAU,IAAIA,OAAA,KAAY;EAE7C,OAAO,UAACC,QAAA;IAAa,iBAAC9H,IAAA;MAAS,iBAACnB,MAAA;QAC9B,IAAM6G,MAAA,GAAS1F,IAAA,CAAKnB,MAAA;QAEpB,IAAMsH,YAAA,GAAe5F,mBAAA,CACnBsF,SAAA,EACA;QAGF,IACE,CAAC6B,aAAA,IACD,EAAER,cAAA,CAAe3K,MAAA,IAAU2K,cAAA,CAAe7H,OAAA,CAAQR,MAAA,CAAOV,IAAA,MAAU,KACnE;UACAgI,YAAA,CAAaxF,WAAA,CAAY;YACvB,IAAMoH,+BAAA,GAAkCzB,wBAAA,CACtCzH,MAAA,EACA,IACA0H,cAAA,EACAC,UAAA,EACAY,kBAAA,EACAX,KAAA;YAGF,IAAIsB,+BAAA,EAAiC;cAC3B,IAAApB,OAAA,GAAmBoB,+BAAA,CAAApB,OAAnB;gBAAShK,KAAA,GAAUoL,+BAAA,CAAApL,KAAV;cAEjBwD,OAAA,CAAQxB,KAAA,CACN,uEAAsEgI,OAAA,gBACtEhK,KAAA,EACA,4DACAkC,MAAA,EACA,yIACA;;UAAA;;QAMR,IAAI,CAAC2I,WAAA,EAAa;UAChBrB,YAAA,CAAaxF,WAAA,CAAY;YACvB,IAAMsF,KAAA,GAAQ6B,QAAA,CAAS9B,QAAA;YAEvB,IAAMgC,8BAAA,GAAiC1B,wBAAA,CACrCL,KAAA,EACA,IACAM,cAAA,EACAC,UAAA,EACA9B,YAAA,EACA+B,KAAA;YAGF,IAAIuB,8BAAA,EAAgC;cAC1B,IAAArB,OAAA,GAAmBqB,8BAAA,CAAArB,OAAnB;gBAAShK,KAAA,GAAUqL,8BAAA,CAAArL,KAAV;cAEjBwD,OAAA,CAAQxB,KAAA,CACN,uEAAsEgI,OAAA,gBACtEhK,KAAA,EACA,gEAC+CkC,MAAA,CAAOV,IAAA;;UAAA;UAM5DgI,YAAA,CAAalF,cAAA;;QAGf,OAAOyE,MAAA;MAAA;IAlEsB,CAkEtB;EAlEY,CAkEZ;AAAA;;ANnQX,SAAAuC,UAAmBC,CAAA;EACjB,OAAO,OAAOA,CAAA,KAAM;AAAA;AAoCf,SAAAC,0BAAA;EAGL,OAAO,SAAAC,4BAAqCxI,OAAA;IAC1C,OAAOyI,oBAAA,CAAqBzI,OAAA;EAAA;AAAA;AAgBzB,SAAAyI,qBASLzI,OAAa;EAAb,IAAAA,OAAA;IAAAA,OAAA,KAAa;EAAA;EAGX,IAAAK,EAAA,GAIEL,OAAA,CAAA0I,KAJM;IAARA,KAAA,GAAArI,EAAA,cAAQ,OAAAA,EAAA;IACR2F,EAAA,GAGEhG,OAAA,CAAA2I,cAHe;IAAjBA,cAAA,GAAA3C,EAAA,cAAiB,OAAAA,EAAA;IACjBuB,EAAA,GAEEvH,OAAA,CAAA4I,iBAFkB;IAApBA,iBAAA,GAAArB,EAAA,cAAoB,OAAAA,EAAA;IACpBE,EAAA,GACEzH,OAAA,CAAA6I,kBADmB;IAArBA,kBAAA,GAAApB,EAAA,cAAqB,OAAAA,EAAA;EAGvB,IAAIqB,eAAA,GAAkB,IAAIxH,eAAA;EAE1B,IAAIoH,KAAA,EAAO;IACT,IAAIL,SAAA,CAAUK,KAAA,GAAQ;MACpBI,eAAA,CAAgBnF,IAAA,CAAKzF,eAAA;KAAA,MAChB;MACL4K,eAAA,CAAgBnF,IAAA,CACdzF,eAAA,CAAgB6K,iBAAA,CAAkBL,KAAA,CAAMM,aAAA;;;EAK9C,IAAI/I,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAIwI,cAAA,EAAgB;MAElB,IAAIM,gBAAA,GAA6D;MAEjE,IAAI,CAACZ,SAAA,CAAUM,cAAA,GAAiB;QAC9BM,gBAAA,GAAmBN,cAAA;;MAGrBG,eAAA,CAAgBI,OAAA,CACdnD,uCAAA,CAAwCkD,gBAAA;;IAK5C,IAAIL,iBAAA,EAAmB;MACrB,IAAIO,mBAAA,GAAmE;MAEvE,IAAI,CAACd,SAAA,CAAUO,iBAAA,GAAoB;QACjCO,mBAAA,GAAsBP,iBAAA;;MAGxBE,eAAA,CAAgBnF,IAAA,CACd0D,0CAAA,CAA2C8B,mBAAA;;IAG/C,IAAIN,kBAAA,EAAoB;MACtB,IAAIO,oBAAA,GAAgE;MAEpE,IAAI,CAACf,SAAA,CAAUQ,kBAAA,GAAqB;QAClCO,oBAAA,GAAuBP,kBAAA;;MAGzBC,eAAA,CAAgBI,OAAA,CACdnJ,sCAAA,CAAuCqJ,oBAAA;;;EAK7C,OAAON,eAAA;AAAA;;AH/GT,IAAMO,aAAA,GAAgBpJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;AAiHxC,SAAAmJ,eAKLtJ,OAAA;EACA,IAAMwI,2BAAA,GAA8BD,yBAAA;EAE9B,IAAAlI,EAAA,GAMFL,OAAA,IAAW;IALbgG,EAAA,GAAA3F,EAAA,CAAAkJ,OAAU;IAAVA,OAAA,GAAAvD,EAAA,cAAU,SAAAA,EAAA;IACVuB,EAAA,GAAAlH,EAAA,CAAAmJ,UAAa;IAAbA,UAAA,GAAAjC,EAAA,cAAaiB,2BAAA,KAAAjB,EAAA;IACbE,EAAA,GAAApH,EAAA,CAAAoJ,QAAW;IAAXA,QAAA,GAAAhC,EAAA,cAAW,OAAAA,EAAA;IACXC,EAAA,GAAArH,EAAA,CAAAqJ,cAAiB;IAAjBA,cAAA,GAAAhC,EAAA,cAAiB,SAAAA,EAAA;IACjBC,EAAA,GAAAtH,EAAA,CAAAsJ,SAAY;IAAZA,SAAA,GAAAhC,EAAA,cAAY,SAAAA,EACC;EAEf,IAAIiC,WAAA;EAEJ,IAAI,OAAOL,OAAA,KAAY,YAAY;IACjCK,WAAA,GAAcL,OAAA;GAAA,UACL1L,aAAA,CAAc0L,OAAA,GAAU;IACjCK,WAAA,GAActM,eAAA,CAAgBiM,OAAA;GAAA,MACzB;IACL,MAAM,IAAI5K,KAAA,CACR;;EAIJ,IAAIkL,eAAA,GAAkBL,UAAA;EACtB,IAAI,OAAOK,eAAA,KAAoB,YAAY;IACzCA,eAAA,GAAkBA,eAAA,CAAgBrB,2BAAA;IAElC,IAAI,CAACa,aAAA,IAAiB,CAAClH,KAAA,CAAMC,OAAA,CAAQyH,eAAA,GAAkB;MACrD,MAAM,IAAIlL,KAAA,CACR;;;EAIN,IACE,CAAC0K,aAAA,IACDQ,eAAA,CAAgBnE,IAAA,CAAK,UAACoE,IAAA;IAAc,cAAOA,IAAA,KAAS;EAAhB,CAAgB,GACpD;IACA,MAAM,IAAInL,KAAA,CACR;;EAIJ,IAAMoL,kBAAA,GAAoC1M,eAAA,CAAAR,KAAA,SAAmBgN,eAAA;EAE7D,IAAIG,YAAA,GAAe5M,QAAA;EAEnB,IAAIqM,QAAA,EAAU;IACZO,YAAA,GAAezM,mBAAA,CAAoBqB,cAAA;MAEjCqL,KAAA,EAAO,CAACZ;KAAA,EACJ,OAAOI,QAAA,KAAa,YAAYA,QAAA;;EAIxC,IAAMS,gBAAA,GAAmB,IAAI5H,aAAA,CAAcyH,kBAAA;EAC3C,IAAII,cAAA,GAA4BD,gBAAA;EAEhC,IAAI/H,KAAA,CAAMC,OAAA,CAAQuH,SAAA,GAAY;IAC5BQ,cAAA,GAAAlN,aAAA,EAAkB8M,kBAAA,GAAuBJ,SAAA;GAAA,UAChC,OAAOA,SAAA,KAAc,YAAY;IAC1CQ,cAAA,GAAiBR,SAAA,CAAUO,gBAAA;;EAG7B,IAAME,gBAAA,GAAmBJ,YAAA,CAAAnN,KAAA,SAAgBsN,cAAA;EAEzC,OAAOjN,WAAA,CAAY0M,WAAA,EAAaF,cAAA,EAAgBU,gBAAA;AAAA;;AUpNlD,OAAAC,gBAAA,IAAAlO,OAAA,IAAAmO,QAAA,EAAA5J,WAAA,IAAA6J,YAAA;;AC4HO,SAAAC,8BACLC,eAAA;EAMA,IAAMC,UAAA,GAAmC;EACzC,IAAMC,cAAA,GAAwD;EAC9D,IAAIC,kBAAA;EACJ,IAAMC,OAAA,GAAU;IACdC,OAAA,WAAAA,CACEC,mBAAA,EACAxB,OAAA;MAEA,IAAItJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QAMzC,IAAIwK,cAAA,CAAehO,MAAA,GAAS,GAAG;UAC7B,MAAM,IAAIgC,KAAA,CACR;;QAGJ,IAAIiM,kBAAA,EAAoB;UACtB,MAAM,IAAIjM,KAAA,CACR;;;MAIN,IAAMJ,IAAA,GACJ,OAAOwM,mBAAA,KAAwB,WAC3BA,mBAAA,GACAA,mBAAA,CAAoBxM,IAAA;MAC1B,IAAI,CAACA,IAAA,EAAM;QACT,MAAM,IAAII,KAAA,CACR;;MAGJ,IAAIJ,IAAA,IAAQmM,UAAA,EAAY;QACtB,MAAM,IAAI/L,KAAA,CACR;;MAGJ+L,UAAA,CAAWnM,IAAA,IAAQgL,OAAA;MACnB,OAAOsB,OAAA;IAAA;IAETG,UAAA,WAAAA,CACEC,OAAA,EACA1B,OAAA;MAEA,IAAItJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAIyK,kBAAA,EAAoB;UACtB,MAAM,IAAIjM,KAAA,CACR;;;MAINgM,cAAA,CAAehH,IAAA,CAAK;QAAEsH,OAAA,EAAAA,OAAA;QAAS1B,OAAA,EAAAA;MAAA;MAC/B,OAAOsB,OAAA;IAAA;IAETK,cAAA,WAAAA,CAAe3B,OAAA;MACb,IAAItJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzC,IAAIyK,kBAAA,EAAoB;UACtB,MAAM,IAAIjM,KAAA,CAAM;;;MAGpBiM,kBAAA,GAAqBrB,OAAA;MACrB,OAAOsB,OAAA;IAAA;GAAA;EAGXJ,eAAA,CAAgBI,OAAA;EAChB,OAAO,CAACH,UAAA,EAAYC,cAAA,EAAgBC,kBAAA;AAAA;;AD7HtC,SAAAO,gBAA4B7C,CAAA;EAC1B,OAAO,OAAOA,CAAA,KAAM;AAAA;AAOtB,IAAI8C,4BAAA,GAA+B;AAqI5B,SAAAC,cACLC,YAAA,EACAC,oBAAA,EAGAZ,cAAgE,EAChEC,kBAAA;EADA,IAAAD,cAAA;IAAAA,cAAA,KAAgE;EAAA;EAGhE,IAAI1K,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;IACzC,IAAI,OAAOoL,oBAAA,KAAyB,UAAU;MAC5C,IAAI,CAACH,4BAAA,EAA8B;QACjCA,4BAAA,GAA+B;QAC/B7K,OAAA,CAAQC,IAAA,CACN;;;;EAMJ,IAAAH,EAAA,GACF,OAAOkL,oBAAA,KAAyB,aAC5Bf,6BAAA,CAA8Be,oBAAA,IAC9B,CAACA,oBAAA,EAAsBZ,cAAA,EAAgBC,kBAAA;IAHxCF,UAAA,GAAArK,EAAA;IAAYmL,mBAAA,GAAAnL,EAAA;IAAqBoL,uBAAA,GAAApL,EAAA,GAGO;EAG7C,IAAIqL,eAAA;EACJ,IAAIP,eAAA,CAAgBG,YAAA,GAAe;IACjCI,eAAA,GAAkB,SAAAA,CAAA;MAAM,OAAAnJ,eAAA,CAAgB+I,YAAA;IAAhB,CAAgB;GAAA,MACnC;IACL,IAAMK,oBAAA,GAAqBpJ,eAAA,CAAgB+I,YAAA;IAC3CI,eAAA,GAAkB,SAAAA,CAAA;MAAM,OAAAC,oBAAA;IAAA;;EAG1B,SAAApC,QAAiBlD,KAAQ,EAAmBpH,MAAA;IAA3B,IAAAoH,KAAA;MAAAA,KAAA,GAAQqF,eAAA;IAAA;IACvB,IAAIE,YAAA,GAAA3O,aAAA,EACFyN,UAAA,CAAWzL,MAAA,CAAOV,IAAA,E,EACfiN,mBAAA,CACAK,MAAA,CAAO,UAACxL,EAAE;UAAA4K,OAAA,GAAA5K,EAAA,CAAA4K,OAAA;MAAc,OAAAA,OAAA,CAAQhM,MAAA;IAAR,CAAQ,EAChC6M,GAAA,CAAI,UAACzL,EAAE;UAAA0L,QAAA,GAAA1L,EAAA,CAAAkJ,OAAA;MAAc,OAAAwC,QAAA;IAAA;IAE1B,IAAIH,YAAA,CAAaC,MAAA,CAAO,UAACG,EAAA;MAAO,QAAC,CAACA,EAAA;IAAF,CAAE,EAAIrP,MAAA,KAAW,GAAG;MAClDiP,YAAA,GAAe,CAACH,uBAAA;;IAGlB,OAAOG,YAAA,CAAaK,MAAA,CAAO,UAACC,aAAA,EAAeC,WAAA;MACzC,IAAIA,WAAA,EAAa;QACf,IAAI7B,QAAA,CAAQ4B,aAAA,GAAgB;UAI1B,IAAME,KAAA,GAAQF,aAAA;UACd,IAAMpG,MAAA,GAASqG,WAAA,CAAYC,KAAA,EAAOnN,MAAA;UAElC,IAAI6G,MAAA,KAAW,QAAW;YACxB,OAAOoG,aAAA;;UAGT,OAAOpG,MAAA;SAAA,UACE,CAACyE,YAAA,CAAY2B,aAAA,GAAgB;UAGtC,IAAMpG,MAAA,GAASqG,WAAA,CAAYD,aAAA,EAAsBjN,MAAA;UAEjD,IAAI6G,MAAA,KAAW,QAAW;YACxB,IAAIoG,aAAA,KAAkB,MAAM;cAC1B,OAAOA,aAAA;;YAET,MAAMvN,KAAA,CACJ;;UAIJ,OAAOmH,MAAA;SAAA,MACF;UAIL,OAAOuE,gBAAA,CAAgB6B,aAAA,EAAe,UAACE,KAAA;YACrC,OAAOD,WAAA,CAAYC,KAAA,EAAOnN,MAAA;UAAA;;;MAKhC,OAAOiN,aAAA;IAAA,GACN7F,KAAA;EAAA;EAGLkD,OAAA,CAAQmC,eAAA,GAAkBA,eAAA;EAE1B,OAAOnC,OAAA;AAAA;;AE3RT,IAAI8C,6BAAA,GAA+B;AA6OnC,SAAAC,SAAiB/I,KAAA,EAAegJ,SAAA;EAC9B,OAAUhJ,KAAA,SAASgJ,SAAA;AAAA;AAad,SAAAC,YAKLxM,OAAA;EAEQ,IAAAyM,IAAA,GAASzM,OAAA,CAAAyM,IAAT;EACR,IAAI,CAACA,IAAA,EAAM;IACT,MAAM,IAAI9N,KAAA,CAAM;;EAGlB,IACE,OAAOsB,OAAA,KAAY,eACnBA,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,eACzB;IACA,IAAIH,OAAA,CAAQsL,YAAA,KAAiB,QAAW;MACtC/K,OAAA,CAAQxB,KAAA,CACN;;;EAKN,IAAMuM,YAAA,GACJ,OAAOtL,OAAA,CAAQsL,YAAA,IAAgB,aAC3BtL,OAAA,CAAQsL,YAAA,GACR/I,eAAA,CAAgBvC,OAAA,CAAQsL,YAAA;EAE9B,IAAMoB,QAAA,GAAW1M,OAAA,CAAQ0M,QAAA,IAAY;EAErC,IAAMC,YAAA,GAAe5O,MAAA,CAAOsB,IAAA,CAAKqN,QAAA;EAEjC,IAAME,uBAAA,GAAuD;EAC7D,IAAMC,uBAAA,GAAuD;EAC7D,IAAMC,cAAA,GAA2C;EAEjDH,YAAA,CAAaI,OAAA,CAAQ,UAACC,WAAA;IACpB,IAAMC,uBAAA,GAA0BP,QAAA,CAASM,WAAA;IACzC,IAAMzO,IAAA,GAAO+N,QAAA,CAAQG,IAAA,EAAMO,WAAA;IAE3B,IAAIb,WAAA;IACJ,IAAIe,eAAA;IAEJ,IAAI,aAAaD,uBAAA,EAAyB;MACxCd,WAAA,GAAcc,uBAAA,CAAwB1D,OAAA;MACtC2D,eAAA,GAAkBD,uBAAA,CAAwBE,OAAA;KAAA,MACrC;MACLhB,WAAA,GAAcc,uBAAA;;IAGhBL,uBAAA,CAAwBI,WAAA,IAAeb,WAAA;IACvCU,uBAAA,CAAwBtO,IAAA,IAAQ4N,WAAA;IAChCW,cAAA,CAAeE,WAAA,IAAeE,eAAA,GAC1B5O,YAAA,CAAaC,IAAA,EAAM2O,eAAA,IACnB5O,YAAA,CAAaC,IAAA;EAAA;EAGnB,SAAA6O,aAAA;IACE,IAAInN,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;MACzC,IAAI,OAAOH,OAAA,CAAQqN,aAAA,KAAkB,UAAU;QAC7C,IAAI,CAAChB,6BAAA,EAA8B;UACjCA,6BAAA,GAA+B;UAC/B9L,OAAA,CAAQC,IAAA,CACN;;;;IAKF,IAAAH,EAAA,GAKJ,OAAOL,OAAA,CAAQqN,aAAA,KAAkB,aAC7B7C,6BAAA,CAA8BxK,OAAA,CAAQqN,aAAA,IACtC,CAACrN,OAAA,CAAQqN,aAAA;MANbrH,EAAA,GAAA3F,EAAA,GAAgB;MAAhBgN,aAAA,GAAArH,EAAA,cAAgB,KAAAA,EAAA;MAChBuB,EAAA,GAAAlH,EAAA,GAAiB;MAAjBsK,cAAA,GAAApD,EAAA,cAAiB,KAAAA,EAAA;MACjBE,EAAA,GAAApH,EAAA,GAAqB;MAArBuK,kBAAA,GAAAnD,EAAA,cAAqB,SAAAA,EAIR;IAEf,IAAM6F,iBAAA,GAAoB1O,cAAA,CAAAA,cAAA,KAAKyO,aAAA,GAAkBR,uBAAA;IAEjD,OAAOxB,aAAA,CAAcC,YAAA,EAAc,UAACT,OAAA;MAClC,SAASrL,GAAA,IAAO8N,iBAAA,EAAmB;QACjCzC,OAAA,CAAQC,OAAA,CAAQtL,GAAA,EAAK8N,iBAAA,CAAkB9N,GAAA;;MAEzC,KAAc,IAAA/C,EAAA,MAAA8Q,gBAAA,GAAA5C,cAAA,EAAAlO,EAAA,GAAA8Q,gBAAA,CAAA5Q,MAAA,EAAAF,EAAA,IAAgB;QAA9B,IAAS+Q,CAAA,GAAAD,gBAAA,CAAA9Q,EAAA;QACPoO,OAAA,CAAQG,UAAA,CAAWwC,CAAA,CAAEvC,OAAA,EAASuC,CAAA,CAAEjE,OAAA;;MAElC,IAAIqB,kBAAA,EAAoB;QACtBC,OAAA,CAAQK,cAAA,CAAeN,kBAAA;;IAAA;EAAA;EAK7B,IAAI6C,QAAA;EAEJ,OAAO;IACLhB,IAAA,EAAAA,IAAA;IACAlD,OAAA,WAAAA,CAAQlD,KAAA,EAAOpH,MAAA;MACb,IAAI,CAACwO,QAAA,EAAUA,QAAA,GAAWL,YAAA;MAE1B,OAAOK,QAAA,CAASpH,KAAA,EAAOpH,MAAA;IAAA;IAEzByO,OAAA,EAASZ,cAAA;IACTlB,YAAA,EAAcgB,uBAAA;IACdlB,eAAA,WAAAA,CAAA;MACE,IAAI,CAAC+B,QAAA,EAAUA,QAAA,GAAWL,YAAA;MAE1B,OAAOK,QAAA,CAAS/B,eAAA;IAAA;GAAA;AAAA;;ACxXf,SAAAiC,sBAAA;EACL,OAAO;IACLC,GAAA,EAAK;IACLC,QAAA,EAAU;GAAA;AAAA;AAIP,SAAAC,0BAAA;EAKL,SAAApC,gBAAyBqC,eAAuB;IAAvB,IAAAA,eAAA;MAAAA,eAAA,KAAuB;IAAA;IAC9C,OAAOhQ,MAAA,CAAOiQ,MAAA,CAAOL,qBAAA,IAAyBI,eAAA;EAAA;EAGhD,OAAO;IAAErC,eAAA,EAAAA;EAAA;AAAA;;ACTJ,SAAAuC,uBAAA;EAKL,SAAAC,aACEC,WAAA;IAEA,IAAMC,SAAA,GAAY,SAAAA,CAAC/H,KAAA;MAA0B,OAAAA,KAAA,CAAMuH,GAAA;IAAN,CAAM;IAEnD,IAAMS,cAAA,GAAiB,SAAAA,CAAChI,KAAA;MAA0B,OAAAA,KAAA,CAAMwH,QAAA;IAAN,CAAM;IAExD,IAAMS,SAAA,GAAY/R,uBAAA,CAChB6R,SAAA,EACAC,cAAA,EACA,UAACT,GAAA,EAAKC,QAAA;MAAkB,OAAAD,GAAA,CAAI9B,GAAA,CAAI,UAACyC,EAAA;QAAO,OAAAV,QAAA,CAASU,EAAA;MAAT,CAAS;IAAzB,CAAyB;IAGnD,IAAMC,QAAA,GAAW,SAAAA,CAAClL,CAAA,EAAYiL,EAAA;MAAiB,OAAAA,EAAA;IAAA;IAE/C,IAAME,UAAA,GAAa,SAAAA,CAACZ,QAAA,EAAyBU,EAAA;MAAiB,OAAAV,QAAA,CAASU,EAAA;IAAT,CAAS;IAEvE,IAAMG,WAAA,GAAcnS,uBAAA,CAAwB6R,SAAA,EAAW,UAACR,GAAA;MAAQ,OAAAA,GAAA,CAAIjR,MAAA;IAAJ,CAAI;IAEpE,IAAI,CAACwR,WAAA,EAAa;MAChB,OAAO;QACLC,SAAA,EAAAA,SAAA;QACAC,cAAA,EAAAA,cAAA;QACAC,SAAA,EAAAA,SAAA;QACAI,WAAA,EAAAA,WAAA;QACAD,UAAA,EAAYlS,uBAAA,CACV8R,cAAA,EACAG,QAAA,EACAC,UAAA;OAAA;;IAKN,IAAME,wBAAA,GAA2BpS,uBAAA,CAC/B4R,WAAA,EACAE,cAAA;IAGF,OAAO;MACLD,SAAA,EAAW7R,uBAAA,CAAwB4R,WAAA,EAAaC,SAAA;MAChDC,cAAA,EAAgBM,wBAAA;MAChBL,SAAA,EAAW/R,uBAAA,CAAwB4R,WAAA,EAAaG,SAAA;MAChDI,WAAA,EAAanS,uBAAA,CAAwB4R,WAAA,EAAaO,WAAA;MAClDD,UAAA,EAAYlS,uBAAA,CACVoS,wBAAA,EACAH,QAAA,EACAC,UAAA;KAAA;EAAA;EAKN,OAAO;IAAEP,YAAA,EAAAA;EAAA;AAAA;;ACjEX,OAAAU,gBAAA,IAAAzS,OAAA,IAAA0S,QAAA;AAMO,SAAAC,kCACLC,OAAA;EAEA,IAAMC,QAAA,GAAWC,mBAAA,CAAoB,UAAC3L,CAAA,EAAc+C,KAAA;IAClD,OAAA0I,OAAA,CAAQ1I,KAAA;EAAR,CAAQ;EAGV,OAAO,SAAA6I,UACL7I,KAAA;IAEA,OAAO2I,QAAA,CAAS3I,KAAA,EAAY;EAAA;AAAA;AAIzB,SAAA4I,oBACLF,OAAA;EAEA,OAAO,SAAAG,UACL7I,KAAA,EACA8I,GAAA;IAEA,SAAAC,wBACEC,IAAA;MAEA,OAAOjQ,KAAA,CAAMiQ,IAAA;IAAA;IAGf,IAAMC,UAAA,GAAa,SAAAA,CAAClD,KAAA;MAClB,IAAIgD,uBAAA,CAAwBD,GAAA,GAAM;QAChCJ,OAAA,CAAQI,GAAA,CAAItQ,OAAA,EAASuN,KAAA;OAAA,MAChB;QACL2C,OAAA,CAAQI,GAAA,EAAK/C,KAAA;;IAAA;IAIjB,IAAIyC,QAAA,CAAQxI,KAAA,GAAQ;MAIlBiJ,UAAA,CAAWjJ,KAAA;MAGX,OAAOA,KAAA;KAAA,MACF;MAIL,OAAOuI,gBAAA,CAAgBvI,KAAA,EAAOiJ,UAAA;;EAAA;AAAA;;ACnD7B,SAAAC,cAA0BC,MAAA,EAAWhB,QAAA;EAC1C,IAAMhP,GAAA,GAAMgP,QAAA,CAASgB,MAAA;EAErB,IAAIvP,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgBX,GAAA,KAAQ,QAAW;IAC9De,OAAA,CAAQC,IAAA,CACN,0EACA,mEACA,+BACAgP,MAAA,EACA,kCACAhB,QAAA,CAASxP,QAAA;;EAIb,OAAOQ,GAAA;AAAA;AAGF,SAAAiQ,oBACL5B,QAAA;EAEA,IAAI,CAAC1L,KAAA,CAAMC,OAAA,CAAQyL,QAAA,GAAW;IAC5BA,QAAA,GAAW9P,MAAA,CAAOqJ,MAAA,CAAOyG,QAAA;;EAG3B,OAAOA,QAAA;AAAA;AAGF,SAAA6B,0BACLC,WAAA,EACAnB,QAAA,EACAnI,KAAA;EAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;EAElC,IAAMC,KAAA,GAAa;EACnB,IAAMC,OAAA,GAAuB;EAE7B,KAAqB,IAAApT,EAAA,MAAAqT,aAAA,GAAAH,WAAA,EAAAlT,EAAA,GAAAqT,aAAA,CAAAnT,MAAA,EAAAF,EAAA,IAAa;IAAlC,IAAW+S,MAAA,GAAAM,aAAA,CAAArT,EAAA;IACT,IAAM8R,EAAA,GAAKgB,aAAA,CAAcC,MAAA,EAAQhB,QAAA;IACjC,IAAID,EAAA,IAAMlI,KAAA,CAAMwH,QAAA,EAAU;MACxBgC,OAAA,CAAQlM,IAAA,CAAK;QAAE4K,EAAA,EAAAA,EAAA;QAAIwB,OAAA,EAASP;MAAA;KAAA,MACvB;MACLI,KAAA,CAAMjM,IAAA,CAAK6L,MAAA;;;EAGf,OAAO,CAACI,KAAA,EAAOC,OAAA;AAAA;;AC9BV,SAAAG,2BACLxB,QAAA;EAIA,SAAAyB,cAAuBT,MAAA,EAAWnJ,KAAA;IAChC,IAAM7G,GAAA,GAAM+P,aAAA,CAAcC,MAAA,EAAQhB,QAAA;IAElC,IAAIhP,GAAA,IAAO6G,KAAA,CAAMwH,QAAA,EAAU;MACzB;;IAGFxH,KAAA,CAAMuH,GAAA,CAAIjK,IAAA,CAAKnE,GAAA;IACf6G,KAAA,CAAMwH,QAAA,CAASrO,GAAA,IAAOgQ,MAAA;EAAA;EAGxB,SAAAU,eACEP,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAElC,KAAqB,IAAAlT,EAAA,MAAA0T,aAAA,GAAAR,WAAA,EAAAlT,EAAA,GAAA0T,aAAA,CAAAxT,MAAA,EAAAF,EAAA,IAAa;MAAlC,IAAW+S,MAAA,GAAAW,aAAA,CAAA1T,EAAA;MACTwT,aAAA,CAAcT,MAAA,EAAQnJ,KAAA;;EAAA;EAI1B,SAAA+J,cAAuBZ,MAAA,EAAWnJ,KAAA;IAChC,IAAM7G,GAAA,GAAM+P,aAAA,CAAcC,MAAA,EAAQhB,QAAA;IAClC,IAAI,EAAEhP,GAAA,IAAO6G,KAAA,CAAMwH,QAAA,GAAW;MAC5BxH,KAAA,CAAMuH,GAAA,CAAIjK,IAAA,CAAKnE,GAAA;;IAEjB6G,KAAA,CAAMwH,QAAA,CAASrO,GAAA,IAAOgQ,MAAA;EAAA;EAGxB,SAAAa,eACEV,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAClC,KAAqB,IAAAlT,EAAA,MAAA6T,aAAA,GAAAX,WAAA,EAAAlT,EAAA,GAAA6T,aAAA,CAAA3T,MAAA,EAAAF,EAAA,IAAa;MAAlC,IAAW+S,MAAA,GAAAc,aAAA,CAAA7T,EAAA;MACT2T,aAAA,CAAcZ,MAAA,EAAQnJ,KAAA;;EAAA;EAI1B,SAAAkK,cACEZ,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAElCtJ,KAAA,CAAMuH,GAAA,GAAM;IACZvH,KAAA,CAAMwH,QAAA,GAAW;IAEjBqC,cAAA,CAAeP,WAAA,EAAatJ,KAAA;EAAA;EAG9B,SAAAmK,iBAA0BhR,GAAA,EAAe6G,KAAA;IACvC,OAAOoK,iBAAA,CAAkB,CAACjR,GAAA,GAAM6G,KAAA;EAAA;EAGlC,SAAAoK,kBAA2BpR,IAAA,EAA2BgH,KAAA;IACpD,IAAIqK,SAAA,GAAY;IAEhBrR,IAAA,CAAK0N,OAAA,CAAQ,UAACvN,GAAA;MACZ,IAAIA,GAAA,IAAO6G,KAAA,CAAMwH,QAAA,EAAU;QACzB,OAAOxH,KAAA,CAAMwH,QAAA,CAASrO,GAAA;QACtBkR,SAAA,GAAY;;IAAA;IAIhB,IAAIA,SAAA,EAAW;MACbrK,KAAA,CAAMuH,GAAA,GAAMvH,KAAA,CAAMuH,GAAA,CAAI/B,MAAA,CAAO,UAAC0C,EAAA;QAAO,OAAAA,EAAA,IAAMlI,KAAA,CAAMwH,QAAA;MAAZ,CAAY;;EAAA;EAIrD,SAAA8C,iBAA0BtK,KAAA;IACxBtI,MAAA,CAAOiQ,MAAA,CAAO3H,KAAA,EAAO;MACnBuH,GAAA,EAAK;MACLC,QAAA,EAAU;KAAA;EAAA;EAId,SAAA+C,WACEvR,IAAA,EACAwR,MAAA,EACAxK,KAAA;IAEA,IAAMyK,SAAA,GAAWzK,KAAA,CAAMwH,QAAA,CAASgD,MAAA,CAAOtC,EAAA;IACvC,IAAMsB,OAAA,GAAa9R,MAAA,CAAOiQ,MAAA,CAAO,IAAI8C,SAAA,EAAUD,MAAA,CAAOd,OAAA;IACtD,IAAMgB,MAAA,GAASxB,aAAA,CAAcM,OAAA,EAASrB,QAAA;IACtC,IAAMwC,SAAA,GAAYD,MAAA,KAAWF,MAAA,CAAOtC,EAAA;IAEpC,IAAIyC,SAAA,EAAW;MACb3R,IAAA,CAAKwR,MAAA,CAAOtC,EAAA,IAAMwC,MAAA;MAClB,OAAO1K,KAAA,CAAMwH,QAAA,CAASgD,MAAA,CAAOtC,EAAA;;IAG/BlI,KAAA,CAAMwH,QAAA,CAASkD,MAAA,IAAUlB,OAAA;IAEzB,OAAOmB,SAAA;EAAA;EAGT,SAAAC,iBAA0BJ,MAAA,EAAmBxK,KAAA;IAC3C,OAAO6K,iBAAA,CAAkB,CAACL,MAAA,GAASxK,KAAA;EAAA;EAGrC,SAAA6K,kBACEC,OAAA,EACA9K,KAAA;IAEA,IAAM+K,OAAA,GAAsC;IAE5C,IAAMC,gBAAA,GAAgD;IAEtDF,OAAA,CAAQpE,OAAA,CAAQ,UAAC8D,MAAA;MAEf,IAAIA,MAAA,CAAOtC,EAAA,IAAMlI,KAAA,CAAMwH,QAAA,EAAU;QAE/BwD,gBAAA,CAAiBR,MAAA,CAAOtC,EAAA,IAAM;UAC5BA,EAAA,EAAIsC,MAAA,CAAOtC,EAAA;UAGXwB,OAAA,EAASnR,cAAA,CAAAA,cAAA,KACHyS,gBAAA,CAAiBR,MAAA,CAAOtC,EAAA,IACxB8C,gBAAA,CAAiBR,MAAA,CAAOtC,EAAA,EAAIwB,OAAA,GAC5B,OACDc,MAAA,CAAOd,OAAA;SAAA;;IAAA;IAMlBoB,OAAA,GAAUpT,MAAA,CAAOqJ,MAAA,CAAOiK,gBAAA;IAExB,IAAMC,iBAAA,GAAoBH,OAAA,CAAQxU,MAAA,GAAS;IAE3C,IAAI2U,iBAAA,EAAmB;MACrB,IAAMC,YAAA,GACJJ,OAAA,CAAQtF,MAAA,CAAO,UAACgF,MAAA;QAAW,OAAAD,UAAA,CAAWQ,OAAA,EAASP,MAAA,EAAQxK,KAAA;MAA5B,CAA4B,EAAQ1J,MAAA,GAC/D;MAEF,IAAI4U,YAAA,EAAc;QAChBlL,KAAA,CAAMuH,GAAA,GAAM7P,MAAA,CAAOsB,IAAA,CAAKgH,KAAA,CAAMwH,QAAA;;;EAAA;EAKpC,SAAA2D,iBAA0BhC,MAAA,EAAWnJ,KAAA;IACnC,OAAOoL,iBAAA,CAAkB,CAACjC,MAAA,GAASnJ,KAAA;EAAA;EAGrC,SAAAoL,kBACE9B,WAAA,EACAtJ,KAAA;IAEM,IAAAhG,EAAA,GAAmBqP,yBAAA,CACvBC,WAAA,EACAnB,QAAA,EACAnI,KAAA;MAHKuJ,KAAA,GAAAvP,EAAA;MAAOwP,OAAA,GAAAxP,EAAA,GAGZ;IAGF6Q,iBAAA,CAAkBrB,OAAA,EAASxJ,KAAA;IAC3B6J,cAAA,CAAeN,KAAA,EAAOvJ,KAAA;EAAA;EAGxB,OAAO;IACLqL,SAAA,EAAW5C,iCAAA,CAAkC6B,gBAAA;IAC7CgB,MAAA,EAAQ1C,mBAAA,CAAoBgB,aAAA;IAC5B2B,OAAA,EAAS3C,mBAAA,CAAoBiB,cAAA;IAC7B2B,MAAA,EAAQ5C,mBAAA,CAAoBmB,aAAA;IAC5B0B,OAAA,EAAS7C,mBAAA,CAAoBoB,cAAA;IAC7B0B,MAAA,EAAQ9C,mBAAA,CAAoBsB,aAAA;IAC5ByB,SAAA,EAAW/C,mBAAA,CAAoBgC,gBAAA;IAC/BgB,UAAA,EAAYhD,mBAAA,CAAoBiC,iBAAA;IAChCgB,SAAA,EAAWjD,mBAAA,CAAoBuC,gBAAA;IAC/BW,UAAA,EAAYlD,mBAAA,CAAoBwC,iBAAA;IAChCW,SAAA,EAAWnD,mBAAA,CAAoBuB,gBAAA;IAC/B6B,UAAA,EAAYpD,mBAAA,CAAoBwB,iBAAA;GAAA;AAAA;;ACnL7B,SAAA6B,yBACL9D,QAAA,EACA+D,IAAA;EAIM,IAAAlS,EAAA,GACJ2P,0BAAA,CAA2BxB,QAAA;IADrB4D,SAAA,GAAA/R,EAAA,CAAA+R,SAAA;IAAWC,UAAA,GAAAhS,EAAA,CAAAgS,UAAA;IAAYX,SAAA,GAAArR,EAAA,CAAAqR,SACF;EAE7B,SAAAzB,cAAuBT,MAAA,EAAWnJ,KAAA;IAChC,OAAO6J,cAAA,CAAe,CAACV,MAAA,GAASnJ,KAAA;EAAA;EAGlC,SAAA6J,eACEP,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAElC,IAAM6C,MAAA,GAAS7C,WAAA,CAAY9D,MAAA,CACzB,UAAC4G,KAAA;MAAU,SAAElD,aAAA,CAAckD,KAAA,EAAOjE,QAAA,KAAanI,KAAA,CAAMwH,QAAA;IAA1C,CAA0C;IAGvD,IAAI2E,MAAA,CAAO7V,MAAA,KAAW,GAAG;MACvB+V,KAAA,CAAMF,MAAA,EAAQnM,KAAA;;EAAA;EAIlB,SAAA+J,cAAuBZ,MAAA,EAAWnJ,KAAA;IAChC,OAAOgK,cAAA,CAAe,CAACb,MAAA,GAASnJ,KAAA;EAAA;EAGlC,SAAAgK,eACEV,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAClC,IAAIA,WAAA,CAAYhT,MAAA,KAAW,GAAG;MAC5B+V,KAAA,CAAM/C,WAAA,EAAatJ,KAAA;;EAAA;EAIvB,SAAAkK,cACEZ,WAAA,EACAtJ,KAAA;IAEAsJ,WAAA,GAAcF,mBAAA,CAAoBE,WAAA;IAClCtJ,KAAA,CAAMwH,QAAA,GAAW;IACjBxH,KAAA,CAAMuH,GAAA,GAAM;IAEZsC,cAAA,CAAeP,WAAA,EAAatJ,KAAA;EAAA;EAG9B,SAAA4K,iBAA0BJ,MAAA,EAAmBxK,KAAA;IAC3C,OAAO6K,iBAAA,CAAkB,CAACL,MAAA,GAASxK,KAAA;EAAA;EAGrC,SAAA6K,kBACEC,OAAA,EACA9K,KAAA;IAEA,IAAIsM,cAAA,GAAiB;IAErB,KAAmB,IAAAlW,EAAA,MAAAmW,SAAA,GAAAzB,OAAA,EAAA1U,EAAA,GAAAmW,SAAA,CAAAjW,MAAA,EAAAF,EAAA,IAAS;MAA5B,IAASoU,MAAA,GAAA+B,SAAA,CAAAnW,EAAA;MACP,IAAM+S,MAAA,GAASnJ,KAAA,CAAMwH,QAAA,CAASgD,MAAA,CAAOtC,EAAA;MACrC,IAAI,CAACiB,MAAA,EAAQ;QACX;;MAGFmD,cAAA,GAAiB;MAEjB5U,MAAA,CAAOiQ,MAAA,CAAOwB,MAAA,EAAQqB,MAAA,CAAOd,OAAA;MAC7B,IAAM8C,KAAA,GAAQrE,QAAA,CAASgB,MAAA;MACvB,IAAIqB,MAAA,CAAOtC,EAAA,KAAOsE,KAAA,EAAO;QACvB,OAAOxM,KAAA,CAAMwH,QAAA,CAASgD,MAAA,CAAOtC,EAAA;QAC7BlI,KAAA,CAAMwH,QAAA,CAASgF,KAAA,IAASrD,MAAA;;;IAI5B,IAAImD,cAAA,EAAgB;MAClBG,cAAA,CAAezM,KAAA;;EAAA;EAInB,SAAAmL,iBAA0BhC,MAAA,EAAWnJ,KAAA;IACnC,OAAOoL,iBAAA,CAAkB,CAACjC,MAAA,GAASnJ,KAAA;EAAA;EAGrC,SAAAoL,kBACE9B,WAAA,EACAtJ,KAAA;IAEM,IAAAhG,EAAA,GAAmBqP,yBAAA,CACvBC,WAAA,EACAnB,QAAA,EACAnI,KAAA;MAHKuJ,KAAA,GAAAvP,EAAA;MAAOwP,OAAA,GAAAxP,EAAA,GAGZ;IAGF6Q,iBAAA,CAAkBrB,OAAA,EAASxJ,KAAA;IAC3B6J,cAAA,CAAeN,KAAA,EAAOvJ,KAAA;EAAA;EAGxB,SAAA0M,eAAwBC,CAAA,EAAuBC,CAAA;IAC7C,IAAID,CAAA,CAAErW,MAAA,KAAWsW,CAAA,CAAEtW,MAAA,EAAQ;MACzB,OAAO;;IAGT,SAASuW,CAAA,GAAI,GAAGA,CAAA,GAAIF,CAAA,CAAErW,MAAA,IAAUuW,CAAA,GAAID,CAAA,CAAEtW,MAAA,EAAQuW,CAAA,IAAK;MACjD,IAAIF,CAAA,CAAEE,CAAA,MAAOD,CAAA,CAAEC,CAAA,GAAI;QACjB;;MAEF,OAAO;;IAET,OAAO;EAAA;EAGT,SAAAR,MAAeF,MAAA,EAAsBnM,KAAA;IAEnCmM,MAAA,CAAOzF,OAAA,CAAQ,UAAC0F,KAAA;MACdpM,KAAA,CAAMwH,QAAA,CAASW,QAAA,CAASiE,KAAA,KAAUA,KAAA;IAAA;IAGpCK,cAAA,CAAezM,KAAA;EAAA;EAGjB,SAAAyM,eAAwBzM,KAAA;IACtB,IAAM8M,WAAA,GAAcpV,MAAA,CAAOqJ,MAAA,CAAOf,KAAA,CAAMwH,QAAA;IACxCsF,WAAA,CAAYZ,IAAA,CAAKA,IAAA;IAEjB,IAAMa,YAAA,GAAeD,WAAA,CAAYrH,GAAA,CAAI0C,QAAA;IAC7B,IAAAZ,GAAA,GAAQvH,KAAA,CAAAuH,GAAR;IAER,IAAI,CAACmF,cAAA,CAAenF,GAAA,EAAKwF,YAAA,GAAe;MACtC/M,KAAA,CAAMuH,GAAA,GAAMwF,YAAA;;EAAA;EAIhB,OAAO;IACLhB,SAAA,EAAAA,SAAA;IACAC,UAAA,EAAAA,UAAA;IACAX,SAAA,EAAAA,SAAA;IACAC,MAAA,EAAQ1C,mBAAA,CAAoBgB,aAAA;IAC5B+B,SAAA,EAAW/C,mBAAA,CAAoBgC,gBAAA;IAC/BiB,SAAA,EAAWjD,mBAAA,CAAoBuC,gBAAA;IAC/BK,MAAA,EAAQ5C,mBAAA,CAAoBmB,aAAA;IAC5B0B,OAAA,EAAS7C,mBAAA,CAAoBoB,cAAA;IAC7B0B,MAAA,EAAQ9C,mBAAA,CAAoBsB,aAAA;IAC5BqB,OAAA,EAAS3C,mBAAA,CAAoBiB,cAAA;IAC7B+B,UAAA,EAAYhD,mBAAA,CAAoBiC,iBAAA;IAChCiB,UAAA,EAAYlD,mBAAA,CAAoBwC,iBAAA;GAAA;AAAA;;ACpJ7B,SAAA4B,oBACLrT,OAGI;EAHJ,IAAAA,OAAA;IAAAA,OAAA,KAGI;EAAA;EAEE,IAAAK,EAAA,GAAkDzB,cAAA;MACtD0U,YAAA,EAAc;MACd9E,QAAA,EAAU,SAAAA,CAAC+E,QAAA;QAAkB,OAAAA,QAAA,CAAShF,EAAA;MAAT;KAAS,EACnCvO,OAAA;IAHGwO,QAAA,GAAAnO,EAAA,CAAAmO,QAAA;IAAU8E,YAAA,GAAAjT,EAAA,CAAAiT,YAGb;EAGL,IAAME,YAAA,GAAe1F,yBAAA;EACrB,IAAM2F,gBAAA,GAAmBxF,sBAAA;EACzB,IAAMyF,YAAA,GAAeJ,YAAA,GACjBhB,wBAAA,CAAyB9D,QAAA,EAAU8E,YAAA,IACnCtD,0BAAA,CAA2BxB,QAAA;EAE/B,OAAO5P,cAAA,CAAAA,cAAA,CAAAA,cAAA;IACL4P,QAAA,EAAAA,QAAA;IACA8E,YAAA,EAAAA;GAAA,EACGE,YAAA,GACAC,gBAAA,GACAC,YAAA;AAAA;;ACrCP,IAAIC,WAAA,GACF;AAMK,IAAIC,MAAA,GAAS,SAAAA,CAACC,IAAO;EAAP,IAAAA,IAAA;IAAAA,IAAA,KAAO;EAAA;EAC1B,IAAItF,EAAA,GAAK;EAET,IAAI2E,CAAA,GAAIW,IAAA;EACR,OAAOX,CAAA,IAAK;IAEV3E,EAAA,IAAMoF,WAAA,CAAaG,IAAA,CAAKC,MAAA,KAAW,KAAM;;EAE3C,OAAOxF,EAAA;AAAA;;ACqCT,IAAMyF,gBAAA,GAAiD,CACrD,QACA,WACA,SACA;AAGF,IAAAC,eAAA;EAME,SAAAA,gBACkBpV,OAAA,EACAC,IAAA;IADA,KAAAD,OAAA,GAAAA,OAAA;IACA,KAAAC,IAAA,GAAAA,IAAA;EAAA;EAAA,OAAAmV,eAAA;AAAA,CARpB,CAQoB;AAIpB,IAAAC,eAAA;EAME,SAAAA,gBACkBrV,OAAA,EACAC,IAAA;IADA,KAAAD,OAAA,GAAAA,OAAA;IACA,KAAAC,IAAA,GAAAA,IAAA;EAAA;EAAA,OAAAoV,eAAA;AAAA,CARpB,CAQoB;AAUb,IAAMC,kBAAA,GAAqB,SAAAA,CAACpX,KAAA;EACjC,IAAI,OAAOA,KAAA,KAAU,YAAYA,KAAA,KAAU,MAAM;IAC/C,IAAMqX,WAAA,GAA+B;IACrC,KAAuB,IAAA3X,EAAA,MAAA4X,kBAAA,GAAAL,gBAAA,EAAAvX,EAAA,GAAA4X,kBAAA,CAAA1X,MAAA,EAAAF,EAAA,IAAkB;MAAzC,IAAW6X,QAAA,GAAAD,kBAAA,CAAA5X,EAAA;MACT,IAAI,OAAOM,KAAA,CAAMuX,QAAA,MAAc,UAAU;QACvCF,WAAA,CAAYE,QAAA,IAAYvX,KAAA,CAAMuX,QAAA;;;IAIlC,OAAOF,WAAA;;EAGT,OAAO;IAAEvR,OAAA,EAAS0R,MAAA,CAAOxX,KAAA;EAAA;AAAA;AA8WpB,IAAMyX,gBAAA,GAAoB;EAC/B,SAAAC,kBAKEC,UAAA,EACAC,cAAA,EAKA3U,OAAA;IAOA,IAAM4U,SAAA,GAIFtW,YAAA,CACFoW,UAAA,GAAa,cACb,UACE7V,OAAA,EACAgW,SAAA,EACA1F,GAAA,EACArQ,IAAA;MACI;QACJD,OAAA,EAAAA,OAAA;QACAC,IAAA,EAAMgW,aAAA,CAAAlW,cAAA,KACCE,IAAA,IAAgB,KADjB;UAEJqQ,GAAA,EAAAA,GAAA;UACA0F,SAAA,EAAAA,SAAA;UACAE,aAAA,EAAe;SAAA;OAAA;IANb,CAMa;IAKrB,IAAMC,OAAA,GACJ1W,YAAA,CACEoW,UAAA,GAAa,YACb,UAACG,SAAA,EAAmB1F,GAAA,EAAerQ,IAAA;MAAwB;QACzDD,OAAA,EAAS;QACTC,IAAA,EAAMgW,aAAA,CAAAlW,cAAA,KACCE,IAAA,IAAgB,KADjB;UAEJqQ,GAAA,EAAAA,GAAA;UACA0F,SAAA,EAAAA,SAAA;UACAE,aAAA,EAAe;SAAA;OAAA;IANwC,CAMxC;IAKvB,IAAME,QAAA,GACJ3W,YAAA,CACEoW,UAAA,GAAa,aACb,UACE3V,KAAA,EACA8V,SAAA,EACA1F,GAAA,EACAtQ,OAAA,EACAC,IAAA;MACI;QACJD,OAAA,EAAAA,OAAA;QACAE,KAAA,EAAS,CAAAiB,OAAA,IAAWA,OAAA,CAAQkV,cAAA,IAAmBf,kBAAA,EAC7CpV,KAAA,IAAS;QAEXD,IAAA,EAAMgW,aAAA,CAAAlW,cAAA,KACCE,IAAA,IAAgB,KADjB;UAEJqQ,GAAA,EAAAA,GAAA;UACA0F,SAAA,EAAAA,SAAA;UACAM,iBAAA,EAAmB,CAAC,CAACtW,OAAA;UACrBkW,aAAA,EAAe;UACfK,OAAA,EAAS,CAAArW,KAAA,oBAAAA,KAAA,CAAO0N,IAAA,MAAS;UACzB7J,SAAA,EAAW,CAAA7D,KAAA,oBAAAA,KAAA,CAAO0N,IAAA,MAAS;SAAA;OAAA;IAZzB,CAYyB;IAKnC,IAAI4I,gBAAA,GAAmB;IAEvB,IAAMC,EAAA,GACJ,OAAOC,eAAA,KAAoB,cACvBA,eAAA;MACA,SAAAC,QAAA;QACE,KAAAC,MAAA,GAAS;UACPL,OAAA,EAAS;UACTM,gBAAA,WAAAA,CAAA,GAAmB;UACnBC,aAAA,WAAAA,CAAA;YACE,OAAO;UAAA;UAETC,OAAA,WAAAA,CAAA,GAAU;UACVC,mBAAA,WAAAA,CAAA,GAAsB;UACtBC,MAAA,EAAQ;UACRC,cAAA,WAAAA,CAAA,GAAiB;SAAA;MAAA;MAEnBP,OAAA,CAAA7T,SAAA,CAAAqU,KAAA;QACE,IAAI/V,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;UACzC,IAAI,CAACkV,gBAAA,EAAkB;YACrBA,gBAAA,GAAmB;YACnB9U,OAAA,CAAQ0V,IAAA,CACN;;;MAAA;MAAA,OAAAT,OAAA;IAAA,CAjBV,CAiBU;IAQhB,SAAA/W,cACE0Q,GAAA;MAEA,OAAO,UAAC+G,QAAA,EAAU9P,QAAA,EAAU+P,KAAA;QAC1B,IAAMtB,SAAA,GAAY,CAAA7U,OAAA,oBAAAA,OAAA,CAASoW,WAAA,IACvBpW,OAAA,CAAQoW,WAAA,CAAYjH,GAAA,IACpByE,MAAA;QAEJ,IAAMyC,eAAA,GAAkB,IAAIf,EAAA;QAC5B,IAAIgB,WAAA;QAEJ,IAAIrV,OAAA,GAAU;QACd,SAAA+U,MAAeF,MAAA;UACbQ,WAAA,GAAcR,MAAA;UACdO,eAAA,CAAgBL,KAAA;QAAA;QAGlB,IAAMO,QAAA,GAAW;UAAkB,OAAAC,OAAA;;;;;;kBAG3BC,eAAA,GAAkB,CAAAC,EAAA,GAAA1W,OAAA,oBAAAA,OAAA,CAAS4C,SAAA,KAAT,gBAAA8T,EAAA,CAAA7S,IAAA,CAAA7D,OAAA,EAAqBmP,GAAA,EAAK;oBAAE/I,QAAA,EAAAA,QAAA;oBAAU+P,KAAA,EAAAA;kBAAA;uBACxDQ,UAAA,CAAWF,eAAA,GAAX;kBACgB,qBAAMA,eAAA;;kBAAxBA,eAAA,GAAkBpW,EAAA,CAAAuW,IAAA,EAAM;;;kBAG1B,IAAIH,eAAA,KAAoB,SAASJ,eAAA,CAAgBZ,MAAA,CAAOL,OAAA,EAAS;oBAE/D,MAAM;sBACJ3I,IAAA,EAAM;sBACN5J,OAAA,EAAS;qBAAA;;kBAGb5B,OAAA,GAAU;kBAEJ4V,cAAA,GAAiB,IAAIC,OAAA,CAAe,UAACxT,CAAA,EAAGyT,MAAA;oBAC5C,OAAAV,eAAA,CAAgBZ,MAAA,CAAOC,gBAAA,CAAiB,SAAS;sBAC/C,OAAAqB,MAAA,CAAO;wBACLtK,IAAA,EAAM;wBACN5J,OAAA,EAASyT,WAAA,IAAe;uBAAA;oBAF1B,CAE0B;kBAH5B,CAG4B;kBAI9BJ,QAAA,CACElB,OAAA,CACEH,SAAA,EACA1F,GAAA,EACA,CAAA6H,EAAA,GAAAhX,OAAA,oBAAAA,OAAA,CAASiX,cAAA,KAAT,gBAAAD,EAAA,CAAAnT,IAAA,CAAA7D,OAAA,EACE;oBAAE6U,SAAA,EAAAA,SAAA;oBAAW1F,GAAA,EAAAA;kBAAA,GACb;oBAAE/I,QAAA,EAAAA,QAAA;oBAAU+P,KAAA,EAAAA;kBAAA;kBAIJ,qBAAMW,OAAA,CAAQI,IAAA,CAAK,CAC/BL,cAAA,EACAC,OAAA,CAAQK,OAAA,CACNxC,cAAA,CAAexF,GAAA,EAAK;oBAClB+G,QAAA,EAAAA,QAAA;oBACA9P,QAAA,EAAAA,QAAA;oBACA+P,KAAA,EAAAA,KAAA;oBACAtB,SAAA,EAAAA,SAAA;oBACAY,MAAA,EAAQY,eAAA,CAAgBZ,MAAA;oBACxBO,KAAA,EAAAA,KAAA;oBACAoB,eAAA,EAAkB,SAAAA,CAChBra,KAAA,EACA+B,IAAA;sBAEA,OAAO,IAAImV,eAAA,CAAgBlX,KAAA,EAAO+B,IAAA;oBAAA;oBAEpCuY,gBAAA,EAAmB,SAAAA,CAACta,KAAA,EAAgB+B,IAAA;sBAClC,OAAO,IAAIoV,eAAA,CAAgBnX,KAAA,EAAO+B,IAAA;oBAAA;mBAAA,GAGtCwY,IAAA,CAAK,UAACxR,MAAA;oBACN,IAAIA,MAAA,YAAkBmO,eAAA,EAAiB;sBACrC,MAAMnO,MAAA;;oBAER,IAAIA,MAAA,YAAkBoO,eAAA,EAAiB;sBACrC,OAAOU,SAAA,CAAU9O,MAAA,CAAOjH,OAAA,EAASgW,SAAA,EAAW1F,GAAA,EAAKrJ,MAAA,CAAOhH,IAAA;;oBAE1D,OAAO8V,SAAA,CAAU9O,MAAA,EAAe+O,SAAA,EAAW1F,GAAA;kBAAA;;kBA3B/CoI,WAAA,GAAclX,EAAA,CAAAuW,IAAA,EA2BiC;;;;kBAI/CW,WAAA,GACEC,KAAA,YAAevD,eAAA,GACXgB,QAAA,CAAS,MAAMJ,SAAA,EAAW1F,GAAA,EAAKqI,KAAA,CAAI3Y,OAAA,EAAS2Y,KAAA,CAAI1Y,IAAA,IAChDmW,QAAA,CAASuC,KAAA,EAAY3C,SAAA,EAAW1F,GAAA;;;kBAOlCsI,YAAA,GACJzX,OAAA,IACA,CAACA,OAAA,CAAQ0X,0BAAA,IACTzC,QAAA,CAAS5W,KAAA,CAAMkZ,WAAA,KACdA,WAAA,CAAoBzY,IAAA,CAAK8D,SAAA;kBAE5B,IAAI,CAAC6U,YAAA,EAAc;oBACjBvB,QAAA,CAASqB,WAAA;;kBAEX,sBAAOA,WAAA;;;WAAA;QAAA;QAET,OAAOxZ,MAAA,CAAOiQ,MAAA,CAAOuI,QAAA,EAAyB;UAC5CP,KAAA,EAAAA,KAAA;UACAnB,SAAA,EAAAA,SAAA;UACA1F,GAAA,EAAAA,GAAA;UACAwI,MAAA,WAAAA,CAAA;YACE,OAAOpB,QAAA,CAAQe,IAAA,CAAUM,YAAA;UAAA;SAAA;MAAA;IAAA;IAMjC,OAAO7Z,MAAA,CAAOiQ,MAAA,CACZvP,aAAA,EAKA;MACEuW,OAAA,EAAAA,OAAA;MACAC,QAAA,EAAAA,QAAA;MACAL,SAAA,EAAAA,SAAA;MACAF,UAAA,EAAAA;KAAA;EAAA;EAIND,iBAAA,CAAiBoD,SAAA,GAAY;IAAM,OAAApD,iBAAA;EAAA;EAEnC,OAAOA,iBAAA;AAAA;AAiBF,SAAAmD,aACL3Y,MAAA;EAEA,IAAIA,MAAA,CAAOH,IAAA,IAAQG,MAAA,CAAOH,IAAA,CAAKqW,iBAAA,EAAmB;IAChD,MAAMlW,MAAA,CAAOJ,OAAA;;EAEf,IAAII,MAAA,CAAOF,KAAA,EAAO;IAChB,MAAME,MAAA,CAAOF,KAAA;;EAEf,OAAOE,MAAA,CAAOJ,OAAA;AAAA;AAOhB,SAAA8X,WAAoB5Z,KAAA;EAClB,OACEA,KAAA,KAAU,QACV,OAAOA,KAAA,KAAU,YACjB,OAAOA,KAAA,CAAMua,IAAA,KAAS;AAAA;;ACxtB1B,IAAMQ,OAAA,GAAU,SAAAA,CAAC7M,OAAA,EAAuBhM,MAAA;EACtC,IAAId,gBAAA,CAAiB8M,OAAA,GAAU;IAC7B,OAAOA,OAAA,CAAQ5M,KAAA,CAAMY,MAAA;GAAA,MAChB;IACL,OAAOgM,OAAA,CAAQhM,MAAA;;AAAA;AAaZ,SAAA8Y,QAAA;EAAA,IAAAC,QAAA;OAAA,IAAAvb,EAAA,IACF,EADEA,EAAA,GAAAC,SAAA,CAAAC,MACF,EADEF,EAAA,EACF;IADEub,QAAA,CAAAvb,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,OAAO,UAACwC,MAAA;IACN,OAAO+Y,QAAA,CAAStS,IAAA,CAAK,UAACuF,OAAA;MAAY,OAAA6M,OAAA,CAAQ7M,OAAA,EAAShM,MAAA;IAAjB,CAAiB;EAAA;AAAA;AAahD,SAAAgZ,QAAA;EAAA,IAAAD,QAAA;OAAA,IAAAvb,EAAA,IACF,EADEA,EAAA,GAAAC,SAAA,CAAAC,MACF,EADEF,EAAA,EACF;IADEub,QAAA,CAAAvb,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,OAAO,UAACwC,MAAA;IACN,OAAO+Y,QAAA,CAAS1Y,KAAA,CAAM,UAAC2L,OAAA;MAAY,OAAA6M,OAAA,CAAQ7M,OAAA,EAAShM,MAAA;IAAjB,CAAiB;EAAA;AAAA;AAUjD,SAAAiZ,2BACLjZ,MAAA,EACAkZ,WAAA;EAEA,IAAI,CAAClZ,MAAA,IAAU,CAACA,MAAA,CAAOH,IAAA,EAAM,OAAO;EAEpC,IAAMsZ,iBAAA,GAAoB,OAAOnZ,MAAA,CAAOH,IAAA,CAAK+V,SAAA,KAAc;EAC3D,IAAMwD,qBAAA,GACJF,WAAA,CAAY1Y,OAAA,CAAQR,MAAA,CAAOH,IAAA,CAAKiW,aAAA,IAAiB;EAEnD,OAAOqD,iBAAA,IAAqBC,qBAAA;AAAA;AAG9B,SAAAC,kBAA2BtF,CAAA;EACzB,OACE,OAAOA,CAAA,CAAE,OAAO,cAChB,aAAaA,CAAA,CAAE,MACf,eAAeA,CAAA,CAAE,MACjB,cAAcA,CAAA,CAAE;AAAA;AAwCb,SAAAuF,UAAA;EAAA,IAAAC,WAAA;OAAA,IAAA/b,EAAA,IAEF,EAFEA,EAAA,GAAAC,SAAA,CAAAC,MAEF,EAFEF,EAAA,EAEF;IAFE+b,WAAA,CAAA/b,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,IAAI+b,WAAA,CAAY7b,MAAA,KAAW,GAAG;IAC5B,OAAO,UAACsC,MAAA;MAAgB,OAAAiZ,0BAAA,CAA2BjZ,MAAA,EAAQ,CAAC;IAApC,CAAoC;;EAG9D,IAAI,CAACqZ,iBAAA,CAAkBE,WAAA,GAAc;IACnC,OAAOD,SAAA,GAAYC,WAAA,CAAY;;EAGjC,OAAO,UACLvZ,MAAA;IAGA,IAAM+Y,QAAA,GAA8CQ,WAAA,CAAY1M,GAAA,CAC9D,UAAC2M,UAAA;MAAe,OAAAA,UAAA,CAAWzD,OAAA;IAAX,CAAW;IAG7B,IAAM0D,eAAA,GAAkBX,OAAA,CAAAlb,KAAA,SAAWmb,QAAA;IAEnC,OAAOU,eAAA,CAAgBzZ,MAAA;EAAA;AAAA;AA0CpB,SAAA0Z,WAAA;EAAA,IAAAH,WAAA;OAAA,IAAA/b,EAAA,IAEF,EAFEA,EAAA,GAAAC,SAAA,CAAAC,MAEF,EAFEF,EAAA,EAEF;IAFE+b,WAAA,CAAA/b,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,IAAI+b,WAAA,CAAY7b,MAAA,KAAW,GAAG;IAC5B,OAAO,UAACsC,MAAA;MAAgB,OAAAiZ,0BAAA,CAA2BjZ,MAAA,EAAQ,CAAC;IAApC,CAAoC;;EAG9D,IAAI,CAACqZ,iBAAA,CAAkBE,WAAA,GAAc;IACnC,OAAOG,UAAA,GAAaH,WAAA,CAAY;;EAGlC,OAAO,UACLvZ,MAAA;IAGA,IAAM+Y,QAAA,GAA8CQ,WAAA,CAAY1M,GAAA,CAC9D,UAAC2M,UAAA;MAAe,OAAAA,UAAA,CAAWxD,QAAA;IAAX,CAAW;IAG7B,IAAMyD,eAAA,GAAkBX,OAAA,CAAAlb,KAAA,SAAWmb,QAAA;IAEnC,OAAOU,eAAA,CAAgBzZ,MAAA;EAAA;AAAA;AA+CpB,SAAA2Z,oBAAA;EAAA,IAAAJ,WAAA;OAAA,IAAA/b,EAAA,IAEF,EAFEA,EAAA,GAAAC,SAAA,CAAAC,MAEF,EAFEF,EAAA,EAEF;IAFE+b,WAAA,CAAA/b,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,IAAMoc,OAAA,GAAU,SAAAA,CAAC5Z,MAAA;IACf,OAAOA,MAAA,IAAUA,MAAA,CAAOH,IAAA,IAAQG,MAAA,CAAOH,IAAA,CAAKqW,iBAAA;EAAA;EAG9C,IAAIqD,WAAA,CAAY7b,MAAA,KAAW,GAAG;IAC5B,OAAO,UAACsC,MAAA;MACN,IAAMyZ,eAAA,GAAkBT,OAAA,CAAQU,UAAA,CAAA9b,KAAA,SAAc2b,WAAA,GAAcK,OAAA;MAE5D,OAAOH,eAAA,CAAgBzZ,MAAA;IAAA;;EAI3B,IAAI,CAACqZ,iBAAA,CAAkBE,WAAA,GAAc;IACnC,OAAOI,mBAAA,GAAsBJ,WAAA,CAAY;;EAG3C,OAAO,UACLvZ,MAAA;IAEA,IAAMyZ,eAAA,GAAkBT,OAAA,CAAQU,UAAA,CAAA9b,KAAA,SAAc2b,WAAA,GAAcK,OAAA;IAE5D,OAAOH,eAAA,CAAgBzZ,MAAA;EAAA;AAAA;AA0CpB,SAAA6Z,YAAA;EAAA,IAAAN,WAAA;OAAA,IAAA/b,EAAA,IAEF,EAFEA,EAAA,GAAAC,SAAA,CAAAC,MAEF,EAFEF,EAAA,EAEF;IAFE+b,WAAA,CAAA/b,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,IAAI+b,WAAA,CAAY7b,MAAA,KAAW,GAAG;IAC5B,OAAO,UAACsC,MAAA;MAAgB,OAAAiZ,0BAAA,CAA2BjZ,MAAA,EAAQ,CAAC;IAApC,CAAoC;;EAG9D,IAAI,CAACqZ,iBAAA,CAAkBE,WAAA,GAAc;IACnC,OAAOM,WAAA,GAAcN,WAAA,CAAY;;EAGnC,OAAO,UACLvZ,MAAA;IAGA,IAAM+Y,QAAA,GAA8CQ,WAAA,CAAY1M,GAAA,CAC9D,UAAC2M,UAAA;MAAe,OAAAA,UAAA,CAAW7D,SAAA;IAAX,CAAW;IAG7B,IAAM8D,eAAA,GAAkBX,OAAA,CAAAlb,KAAA,SAAWmb,QAAA;IAEnC,OAAOU,eAAA,CAAgBzZ,MAAA;EAAA;AAAA;AAiDpB,SAAA8Z,mBAAA;EAAA,IAAAP,WAAA;OAAA,IAAA/b,EAAA,IAEF,EAFEA,EAAA,GAAAC,SAAA,CAAAC,MAEF,EAFEF,EAAA,EAEF;IAFE+b,WAAA,CAAA/b,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAGL,IAAI+b,WAAA,CAAY7b,MAAA,KAAW,GAAG;IAC5B,OAAO,UAACsC,MAAA;MACN,OAAAiZ,0BAAA,CAA2BjZ,MAAA,EAAQ,CAAC,WAAW,aAAa;IAA5D,CAA4D;;EAGhE,IAAI,CAACqZ,iBAAA,CAAkBE,WAAA,GAAc;IACnC,OAAOO,kBAAA,GAAqBP,WAAA,CAAY;;EAG1C,OAAO,UACLvZ,MAAA;IAGA,IAAM+Y,QAAA,GAA8C;IAEpD,KAAyB,IAAAvb,EAAA,MAAAuc,aAAA,GAAAR,WAAA,EAAA/b,EAAA,GAAAuc,aAAA,CAAArc,MAAA,EAAAF,EAAA,IAAa;MAAtC,IAAWgc,UAAA,GAAAO,aAAA,CAAAvc,EAAA;MACTub,QAAA,CAASrU,IAAA,CACP8U,UAAA,CAAWzD,OAAA,EACXyD,UAAA,CAAWxD,QAAA,EACXwD,UAAA,CAAW7D,SAAA;;IAIf,IAAM8D,eAAA,GAAkBX,OAAA,CAAAlb,KAAA,SAAWmb,QAAA;IAEnC,OAAOU,eAAA,CAAgBzZ,MAAA;EAAA;AAAA;;ACpapB,IAAMga,cAAA,GAG0C,SAAAA,CACrDC,IAAA,EACAC,QAAA;EAEA,IAAI,OAAOD,IAAA,KAAS,YAAY;IAC9B,MAAM,IAAIE,SAAA,CAAaD,QAAA;;AAAA;AAIpB,IAAME,IAAA,GAAO,SAAAA,CAAA,GAAM;AAEnB,IAAMC,cAAA,GAAiB,SAAAA,CAC5B/C,QAAA,EACAgD,OAAU;EAAV,IAAAA,OAAA;IAAAA,OAAA,GAAAF,IAAU;EAAA;EAEV9C,QAAA,CAAQiD,KAAA,CAAMD,OAAA;EAEd,OAAOhD,QAAA;AAAA;AAGF,IAAMkD,sBAAA,GAAyB,SAAAA,CACpCC,WAAA,EACAC,QAAA;EAEAD,WAAA,CAAYhE,gBAAA,CAAiB,SAASiE,QAAA,EAAU;IAAEC,IAAA,EAAM;EAAA;EACxD,OAAO;IAAM,OAAAF,WAAA,CAAY7D,mBAAA,CAAoB,SAAS8D,QAAA;EAAzC,CAAyC;AAAA;AAajD,IAAME,yBAAA,GAA4B,SAAAA,CACvCxD,eAAA,EACAP,MAAA;EAIA,IAAML,MAAA,GAASY,eAAA,CAAgBZ,MAAA;EAE/B,IAAIA,MAAA,CAAOL,OAAA,EAAS;IAClB;;EAOF,IAAI,EAAE,YAAYK,MAAA,GAAS;IACzB1X,MAAA,CAAO6D,cAAA,CAAe6T,MAAA,EAAQ,UAAU;MACtCqE,UAAA,EAAY;MACZ/c,KAAA,EAAO+Y,MAAA;MACPiE,YAAA,EAAc;MACdC,QAAA,EAAU;KAAA;;EAId;EAAE3D,eAAA,CAAgBL,KAAA,CAAkCF,MAAA;AAAA;;AClEtD,IAAMmE,IAAA,GAAO;AACb,IAAMC,QAAA,GAAW;AACjB,IAAMC,SAAA,GAAY;AAClB,IAAMC,SAAA,GAAY;AAGX,IAAMC,aAAA,GAAgB,UAAQD,SAAA;AAC9B,IAAME,aAAA,GAAgB,UAAQH,SAAA;AAC9B,IAAMI,iBAAA,GAAuBL,QAAA,SAAYE,SAAA;AACzC,IAAMI,iBAAA,GAAuBN,QAAA,SAAYC,SAAA;AAEzC,IAAAM,cAAA;EAGL,SAAAA,eAAmBC,IAAA;IAAA,KAAAA,IAAA,GAAAA,IAAA;IAFnB,KAAAjO,IAAA,GAAO;IAGL,KAAK5J,OAAA,GAAaoX,IAAA,SAAQG,SAAA,kBAAsBM,IAAA;EAAA;EAAA,OAAAD,cAAA;AAAA,CAJ7C,CAI6C;;ACP7C,IAAME,cAAA,GAAiB,SAAAA,CAAClF,MAAA;EAC7B,IAAIA,MAAA,CAAOL,OAAA,EAAS;IAClB,MAAM,IAAIqF,cAAA,CAAgBhF,MAAA,CAAyCK,MAAA;;AAAA;AAShE,SAAA8E,eACLnF,MAAA,EACAc,QAAA;EAEA,IAAIsE,OAAA,GAAUxB,IAAA;EACd,OAAO,IAAIvC,OAAA,CAAW,UAACK,OAAA,EAASJ,MAAA;IAC9B,IAAM+D,eAAA,GAAkB,SAAAA,CAAA;MAAM,OAAA/D,MAAA,CAAO,IAAI0D,cAAA,CAAehF,MAAA,CAAOK,MAAA;IAAjC,CAAiC;IAE/D,IAAIL,MAAA,CAAOL,OAAA,EAAS;MAClB0F,eAAA;MACA;;IAGFD,OAAA,GAAUpB,sBAAA,CAAuBhE,MAAA,EAAQqF,eAAA;IACzCvE,QAAA,CAAQwE,OAAA,CAAQ;MAAM,OAAAF,OAAA;IAAA,GAAWvD,IAAA,CAAKH,OAAA,EAASJ,MAAA;EAAA,GAC9CgE,OAAA,CAAQ;IAETF,OAAA,GAAUxB,IAAA;EAAA;AAAA;AAWP,IAAM2B,OAAA,GAAU,SAAAA,CACrBC,KAAA,EACAC,OAAA;EAC2B,OAAA1E,OAAA;;;;;;UAEzB,qBAAMM,OAAA,CAAQK,OAAA;;UAAd9W,EAAA,CAAAuW,IAAA,EAAc;UACA,qBAAMqE,KAAA;;UAAdle,KAAA,GAAQsD,EAAA,CAAAuW,IAAA,EAAM;UACpB,sBAAO;YACLuE,MAAA,EAAQ;YACRpe,KAAA,EAAAA;WAAA;;;UAGF,sBAAO;YACLoe,MAAA,EAAQC,OAAA,YAAiBX,cAAA,GAAiB,cAAc;YACxD1b,KAAA,EAAAqc;WAAA;;UAGFF,OAAA,oBAAAA,OAAA;;;;;;GAAA;AAdyB,CAczB;AAWG,IAAMG,WAAA,GAAc,SAAAA,CAAI5F,MAAA;EAC7B,OAAO,UAACc,QAAA;IACN,OAAO+C,cAAA,CACLsB,cAAA,CAAenF,MAAA,EAAQc,QAAA,EAASe,IAAA,CAAK,UAACgE,MAAA;MACpCX,cAAA,CAAelF,MAAA;MACf,OAAO6F,MAAA;IAAA;EAAA;AAAA;AAYR,IAAMC,WAAA,GAAc,SAAAA,CAAC9F,MAAA;EAC1B,IAAM+F,KAAA,GAAQH,WAAA,CAAkB5F,MAAA;EAChC,OAAO,UAACgG,SAAA;IACN,OAAOD,KAAA,CAAM,IAAI1E,OAAA,CAAc,UAACK,OAAA;MAAY,OAAAuE,UAAA,CAAWvE,OAAA,EAASsE,SAAA;IAApB,CAAoB;EAAA;AAAA;;ACxB5D,IAAAzN,MAAA,GAAWjQ,MAAA,CAAAiQ,MAAX;AAIR,IAAM2N,kBAAA,GAAqB;AAE3B,IAAMC,GAAA,GAAM;AAEZ,IAAMC,UAAA,GAAa,SAAAA,CACjBC,iBAAA,EACAC,sBAAA;EAEA,IAAMC,eAAA,GAAkB,SAAAA,CAACC,UAAA;IACvB,OAAAxC,sBAAA,CAAuBqC,iBAAA,EAAmB;MACxC,OAAAjC,yBAAA,CAA0BoC,UAAA,EAAYH,iBAAA,CAAkBhG,MAAA;IAAxD,CAAwD;EAD1D,CAC0D;EAG5D,OAAO,UACLoG,YAAA,EACAC,IAAA;IAEAlD,cAAA,CAAeiD,YAAA,EAAc;IAC7B,IAAME,oBAAA,GAAuB,IAAI7G,eAAA;IAEjCyG,eAAA,CAAgBI,oBAAA;IAEhB,IAAMtW,MAAA,GAASkV,OAAA,CACb;MAAwB,OAAAxE,OAAA;;;;;cACtBmE,cAAA,CAAemB,iBAAA;cACfnB,cAAA,CAAeyB,oBAAA,CAAqB3G,MAAA;cACpB,qBAAMyG,YAAA,CAAa;gBACjCV,KAAA,EAAOH,WAAA,CAAYe,oBAAA,CAAqB3G,MAAA;gBACxC4G,KAAA,EAAOd,WAAA,CAAYa,oBAAA,CAAqB3G,MAAA;gBACxCA,MAAA,EAAQ2G,oBAAA,CAAqB3G;eAAA;;cAHzB6G,OAAA,GAAUjc,EAAA,CAAAuW,IAAA,EAGe;cAE/B+D,cAAA,CAAeyB,oBAAA,CAAqB3G,MAAA;cACpC,sBAAO6G,OAAA;;;OAAA;IATe,CASf,EAET;MAAM,OAAAzC,yBAAA,CAA0BuC,oBAAA,EAAsB9B,aAAA;IAAhD,CAAgD;IAGxD,IAAI6B,IAAA,oBAAAA,IAAA,CAAMI,QAAA,EAAU;MAClBR,sBAAA,CAAuBpY,IAAA,CAAKmC,MAAA;;IAG9B,OAAO;MACLA,MAAA,EAAQuV,WAAA,CAA2BS,iBAAA,EAAmBhW,MAAA;MACtD0W,MAAA,WAAAA,CAAA;QACE3C,yBAAA,CAA0BuC,oBAAA,EAAsB/B,aAAA;MAAA;KAAA;EAAA;AAAA;AAMxD,IAAMoC,iBAAA,GAAoB,SAAAA,CACxBC,cAAA,EAKAjH,MAAA;EASA,IAAMkH,IAAA,GAAO,SAAAA,CACXC,SAAA,EACAC,OAAA;IACG,OAAArG,OAAA;;;;;YACHmE,cAAA,CAAelF,MAAA;YAGXqH,WAAA,GAAmC,SAAAA,CAAA,GAAM;YAEvCC,YAAA,GAAe,IAAIjG,OAAA,CAA2B,UAACK,OAAA,EAASJ,MAAA;cAE5D,IAAIiG,aAAA,GAAgBN,cAAA,CAAe;gBACjCE,SAAA,EAAAA,SAAA;gBACAK,MAAA,EAAQ,SAAAA,CAAChe,MAAA,EAAQie,WAAA;kBAEfA,WAAA,CAAYJ,WAAA;kBAEZ3F,OAAA,CAAQ,CACNlY,MAAA,EACAie,WAAA,CAAY9W,QAAA,IACZ8W,WAAA,CAAYC,gBAAA;gBAAA;eAAA;cAIlBL,WAAA,GAAc,SAAAA,CAAA;gBACZE,aAAA;gBACAjG,MAAA;cAAA;YAAA;YAIEqG,QAAA,GAA2D,CAC/DL,YAAA;YAGF,IAAIF,OAAA,IAAW,MAAM;cACnBO,QAAA,CAASzZ,IAAA,CACP,IAAImT,OAAA,CAAc,UAACK,OAAA;gBAAY,OAAAuE,UAAA,CAAWvE,OAAA,EAAS0F,OAAA,EAAS;cAA7B,CAA6B;;;;;YAK/C,qBAAMjC,cAAA,CAAenF,MAAA,EAAQqB,OAAA,CAAQI,IAAA,CAAKkG,QAAA;;YAAnD9B,MAAA,GAASjb,EAAA,CAAAuW,IAAA,EAA0C;YAEzD+D,cAAA,CAAelF,MAAA;YACf,sBAAO6F,MAAA;;YAGPwB,WAAA;;;;;;KAAA;EA5CC,CA4CD;EAIJ,OAAQ,UAACF,SAAA,EAAoCC,OAAA;IAC3C,OAAAvD,cAAA,CAAeqD,IAAA,CAAKC,SAAA,EAAWC,OAAA;EAA/B,CAA+B;AAAA;AAGnC,IAAMQ,yBAAA,GAA4B,SAAAA,CAACrd,OAAA;EAC3B,IAAAzB,IAAA,GAAoDyB,OAAA,CAAAzB,IAApD;IAAME,aAAA,GAA8CuB,OAAA,CAAAvB,aAA9C;IAAewM,OAAA,GAA+BjL,OAAA,CAAAiL,OAA/B;IAAS2R,SAAA,GAAsB5c,OAAA,CAAA4c,SAAtB;IAAWK,MAAA,GAAWjd,OAAA,CAAAid,MAAX;EAE/C,IAAI1e,IAAA,EAAM;IACRqe,SAAA,GAAYte,YAAA,CAAaC,IAAA,EAAMF,KAAA;GAAA,UACtBI,aAAA,EAAe;IACxBF,IAAA,GAAOE,aAAA,CAAeF,IAAA;IACtBqe,SAAA,GAAYne,aAAA,CAAcJ,KAAA;GAAA,UACjB4M,OAAA,EAAS;IAClB2R,SAAA,GAAY3R,OAAA;GAAA,UACH2R,SAAA,EAAW,C,CAAA,MAEf;IACL,MAAM,IAAIje,KAAA,CACR;;EAIJsa,cAAA,CAAegE,MAAA,EAAQ;EAEvB,OAAO;IAAEL,SAAA,EAAAA,SAAA;IAAWre,IAAA,EAAAA,IAAA;IAAM0e,MAAA,EAAAA;EAAA;AAAA;AAIrB,IAAMK,mBAAA,GAAyD,SAAAA,CACpEtd,OAAA;EAEM,IAAAK,EAAA,GAA8Bgd,yBAAA,CAA0Brd,OAAA;IAAtDzB,IAAA,GAAA8B,EAAA,CAAA9B,IAAA;IAAMqe,SAAA,GAAAvc,EAAA,CAAAuc,SAAA;IAAWK,MAAA,GAAA5c,EAAA,CAAA4c,MAAqC;EAE9D,IAAM1O,EAAA,GAAKqF,MAAA;EACX,IAAM2J,KAAA,GAAgC;IACpChP,EAAA,EAAAA,EAAA;IACA0O,MAAA,EAAAA,MAAA;IACA1e,IAAA,EAAAA,IAAA;IACAqe,SAAA,EAAAA,SAAA;IACA5H,OAAA,EAAS,IAAIxQ,GAAA;IACbsY,WAAA,EAAa,SAAAA,CAAA;MACX,MAAM,IAAIne,KAAA,CAAM;IAAA;GAAA;EAIpB,OAAO4e,KAAA;AAAA;AAGT,IAAMC,qBAAA,GAAwB,SAAAA,CAC5BD,KAAA;EAEAA,KAAA,CAAMvI,OAAA,CAAQjI,OAAA,CAAQ,UAACkP,UAAA;IACrBpC,yBAAA,CAA0BoC,UAAA,EAAY1B,iBAAA;EAAA;AAAA;AAI1C,IAAMkD,6BAAA,GAAgC,SAAAA,CACpCC,WAAA;EAEA,OAAO;IACLA,WAAA,CAAY3Q,OAAA,CAAQyQ,qBAAA;IAEpBE,WAAA,CAAYC,KAAA;EAAA;AAAA;AAWhB,IAAMC,iBAAA,GAAoB,SAAAA,CACxBC,YAAA,EACAC,aAAA,EACAC,SAAA;EAEA,IAAI;IACFF,YAAA,CAAaC,aAAA,EAAeC,SAAA;GAAA,QACrBC,iBAAA,EAAP;IAGAtC,UAAA,CAAW;MACT,MAAMsC,iBAAA;IAAA,GACL;;AAAA;AAOA,IAAMC,WAAA,GAAc3f,YAAA,CACtBsd,GAAA;AAME,IAAMsC,iBAAA,GAAoB5f,YAAA,CAAgBsd,GAAA;AAK1C,IAAMuC,cAAA,GAAiB7f,YAAA,CACzBsd,GAAA;AAGL,IAAMwC,mBAAA,GAA4C,SAAAA,CAAA;EAAA,IAAA5hB,IAAA;OAAA,IAAAC,EAAA,IAAI,EAAJA,EAAA,GAAAC,SAAA,CAAAC,MAAI,EAAJF,EAAA,EAAI;IAAJD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAChD8D,OAAA,CAAQxB,KAAA,CAAAlC,KAAA,CAAR0D,OAAA,EAAAtD,aAAA,EAAiB2e,GAAA,cAAgBpf,IAAA;AAAA;AAM5B,SAAA6hB,yBAILC,iBAAoE;EAJ/D,IAAA7c,KAAA;EAIL,IAAA6c,iBAAA;IAAAA,iBAAA,KAAoE;EAAA;EACpE,IAAMZ,WAAA,GAAc,IAAIa,GAAA;EAChB,IAAApI,KAAA,GAAyCmI,iBAAA,CAAAnI,KAAzC;IAAO9V,EAAA,GAAkCie,iBAAA,CAAA/E,OAAxB;IAAVA,OAAA,GAAAlZ,EAAA,cAAU+d,mBAAA,GAAA/d,EAAA;EAEzB4Y,cAAA,CAAeM,OAAA,EAAS;EAExB,IAAMiF,WAAA,GAAc,SAAAA,CAACjB,KAAA;IACnBA,KAAA,CAAMT,WAAA,GAAc;MAAM,OAAAY,WAAA,CAAYe,MAAA,CAAOlB,KAAA,CAAOhP,EAAA;IAA1B,CAA0B;IAEpDmP,WAAA,CAAYgB,GAAA,CAAInB,KAAA,CAAMhP,EAAA,EAAIgP,KAAA;IAC1B,OAAO,UAACoB,aAAA;MACNpB,KAAA,CAAMT,WAAA;MACN,IAAI6B,aAAA,oBAAAA,aAAA,CAAeC,YAAA,EAAc;QAC/BpB,qBAAA,CAAsBD,KAAA;;IAAA;EAAA;EAK5B,IAAMsB,iBAAA,GAAoB,SAAAA,CACxBC,UAAA;IAEA,KAAoB,IAAAriB,EAAA,IAAuB,EAAvB4D,EAAA,GAAA8B,KAAA,CAAM4c,IAAA,CAAKrB,WAAA,CAAYtW,MAAA,KAAvB3K,EAAA,GAAA4D,EAAA,CAAA1D,MAAuB,EAAvBF,EAAA,EAAuB,EAAW;MAAtD,IAAW8gB,KAAA,GAAAld,EAAA,CAAA5D,EAAA;MACT,IAAIqiB,UAAA,CAAWvB,KAAA,GAAQ;QACrB,OAAOA,KAAA;;;IAIX,OAAO;EAAA;EAGT,IAAMb,cAAA,GAAiB,SAAAA,CAAC1c,OAAA;IACtB,IAAIud,KAAA,GAAQsB,iBAAA,CACV,UAACG,aAAA;MAAkB,OAAAA,aAAA,CAAc/B,MAAA,KAAWjd,OAAA,CAAQid,MAAA;IAAjC,CAAiC;IAGtD,IAAI,CAACM,KAAA,EAAO;MACVA,KAAA,GAAQD,mBAAA,CAAoBtd,OAAA;;IAG9B,OAAOwe,WAAA,CAAYjB,KAAA;EAAA;EAGrB,IAAMP,aAAA,GAAgB,SAAAA,CACpBhd,OAAA;IAEM,IAAAK,EAAA,GAA8Bgd,yBAAA,CAA0Brd,OAAA;MAAtDzB,IAAA,GAAA8B,EAAA,CAAA9B,IAAA;MAAM0e,MAAA,GAAA5c,EAAA,CAAA4c,MAAA;MAAQL,SAAA,GAAAvc,EAAA,CAAAuc,SAAwC;IAE9D,IAAMW,KAAA,GAAQsB,iBAAA,CAAkB,UAACI,MAAA;MAC/B,IAAMC,oBAAA,GACJ,OAAO3gB,IAAA,KAAS,WACZ0gB,MAAA,CAAM1gB,IAAA,KAASA,IAAA,GACf0gB,MAAA,CAAMrC,SAAA,KAAcA,SAAA;MAE1B,OAAOsC,oBAAA,IAAwBD,MAAA,CAAMhC,MAAA,KAAWA,MAAA;IAAA;IAGlD,IAAIM,KAAA,EAAO;MACTA,KAAA,CAAMT,WAAA;MACN,IAAI9c,OAAA,CAAQ4e,YAAA,EAAc;QACxBpB,qBAAA,CAAsBD,KAAA;;;IAI1B,OAAO,CAAC,CAACA,KAAA;EAAA;EAGX,IAAM4B,cAAA,GAAiB,SAAAA,CACrB5B,KAAA,EACAte,MAAA,EACAmgB,GAAA,EACAjC,gBAAA;IACG,OAAA3G,OAAA,CAAA/U,KAAA;;;;;YACG4d,sBAAA,GAAyB,IAAI9J,eAAA;YAC7BoH,IAAA,GAAOF,iBAAA,CACXC,cAAA,EACA2C,sBAAA,CAAuB5J,MAAA;YAEnB6J,gBAAA,GAAmC;;;;YAGvC/B,KAAA,CAAMvI,OAAA,CAAQrQ,GAAA,CAAI0a,sBAAA;YAClB,qBAAMvI,OAAA,CAAQK,OAAA,CACZoG,KAAA,CAAMN,MAAA,CACJhe,MAAA,EAEA+O,MAAA,CAAO,IAAIoR,GAAA,EAAK;cACdjC,gBAAA,EAAAA,gBAAA;cACAva,SAAA,EAAW,SAAAA,CACTga,SAAA,EACAC,OAAA;gBACG,OAAAF,IAAA,CAAKC,SAAA,EAAWC,OAAA,EAASvF,IAAA,CAAKiI,OAAA;cAA9B,CAA8B;cACnC5C,IAAA,EAAAA,IAAA;cACAN,KAAA,EAAOd,WAAA,CAAY8D,sBAAA,CAAuB5J,MAAA;cAC1C+F,KAAA,EAAOH,WAAA,CAAiBgE,sBAAA,CAAuB5J,MAAA;cAC/CU,KAAA,EAAAA,KAAA;cACAV,MAAA,EAAQ4J,sBAAA,CAAuB5J,MAAA;cAC/B+J,IAAA,EAAM3D,UAAA,CAAWwD,sBAAA,CAAuB5J,MAAA,EAAQ6J,gBAAA;cAChDxC,WAAA,EAAaS,KAAA,CAAMT,WAAA;cACnB2C,SAAA,EAAW,SAAAA,CAAA;gBACT/B,WAAA,CAAYgB,GAAA,CAAInB,KAAA,CAAMhP,EAAA,EAAIgP,KAAA;cAAA;cAE5BC,qBAAA,EAAuB,SAAAA,CAAA;gBACrBD,KAAA,CAAMvI,OAAA,CAAQjI,OAAA,CAAQ,UAACkP,UAAA,EAAY3Y,CAAA,EAAGob,GAAA;kBACpC,IAAIzC,UAAA,KAAeoD,sBAAA,EAAwB;oBACzCxF,yBAAA,CAA0BoC,UAAA,EAAY1B,iBAAA;oBACtCmE,GAAA,CAAID,MAAA,CAAOxC,UAAA;;gBAAA;cAAA;aAAA;;YAxBvB5b,EAAA,CAAAuW,IAAA,EAwBuB;;;;YAQvB,IAAI,EAAE8I,eAAA,YAAyBjF,cAAA,GAAiB;cAC9CmD,iBAAA,CAAkBrE,OAAA,EAASmG,eAAA,EAAe;gBACxCC,QAAA,EAAU;eAAA;;;;YAId,qBAAM7I,OAAA,CAAQ8I,UAAA,CAAWN,gBAAA;;YAAzBjf,EAAA,CAAAuW,IAAA,EAAyB;YAEzBiD,yBAAA,CAA0BwF,sBAAA,EAAwB7E,iBAAA;YAClD+C,KAAA,CAAMvI,OAAA,CAAQyJ,MAAA,CAAOY,sBAAA;;;;;;KAAA;EAnDpB,CAmDoB;EAIzB,IAAMQ,uBAAA,GAA0BpC,6BAAA,CAA8BC,WAAA;EAE9D,IAAMlU,UAAA,GACJ,SAAAA,CAAC4V,GAAA;IAAQ,iBAAChf,IAAA;MAAS,iBAACnB,MAAA;QAClB,IAAI,CAACC,QAAA,CAASD,MAAA,GAAS;UAErB,OAAOmB,IAAA,CAAKnB,MAAA;;QAGd,IAAIgf,WAAA,CAAY5f,KAAA,CAAMY,MAAA,GAAS;UAC7B,OAAOyd,cAAA,CAAezd,MAAA,CAAOJ,OAAA;;QAG/B,IAAIqf,iBAAA,CAAkB7f,KAAA,CAAMY,MAAA,GAAS;UACnC4gB,uBAAA;UACA;;QAGF,IAAI1B,cAAA,CAAe9f,KAAA,CAAMY,MAAA,GAAS;UAChC,OAAO+d,aAAA,CAAc/d,MAAA,CAAOJ,OAAA;;QAI9B,IAAIihB,aAAA,GAA+CV,GAAA,CAAIhZ,QAAA;QAIvD,IAAM+W,gBAAA,GAAmB,SAAAA,CAAA;UACvB,IAAI2C,aAAA,KAAkBnE,kBAAA,EAAoB;YACxC,MAAM,IAAIhd,KAAA,CACLid,GAAA;;UAIP,OAAOkE,aAAA;QAAA;QAGT,IAAIha,MAAA;QAEJ,IAAI;UAEFA,MAAA,GAAS1F,IAAA,CAAKnB,MAAA;UAEd,IAAIye,WAAA,CAAY7J,IAAA,GAAO,GAAG;YACxB,IAAIkM,YAAA,GAAeX,GAAA,CAAIhZ,QAAA;YAEvB,IAAM4Z,eAAA,GAAkB7d,KAAA,CAAM4c,IAAA,CAAKrB,WAAA,CAAYtW,MAAA;YAC/C,KAAkB,IAAA3K,EAAA,MAAAwjB,iBAAA,GAAAD,eAAA,EAAAvjB,EAAA,GAAAwjB,iBAAA,CAAAtjB,MAAA,EAAAF,EAAA,IAAiB;cAAnC,IAAS8gB,KAAA,GAAA0C,iBAAA,CAAAxjB,EAAA;cACP,IAAIyjB,WAAA,GAAc;cAElB,IAAI;gBACFA,WAAA,GAAc3C,KAAA,CAAMX,SAAA,CAAU3d,MAAA,EAAQ8gB,YAAA,EAAcD,aAAA;eAAA,QAC7CK,cAAA,EAAP;gBACAD,WAAA,GAAc;gBAEdtC,iBAAA,CAAkBrE,OAAA,EAAS4G,cAAA,EAAgB;kBACzCR,QAAA,EAAU;iBAAA;;cAId,IAAI,CAACO,WAAA,EAAa;gBAChB;;cAGFf,cAAA,CAAe5B,KAAA,EAAOte,MAAA,EAAQmgB,GAAA,EAAKjC,gBAAA;;;SAAA,SAGvC;UAEA2C,aAAA,GAAgBnE,kBAAA;;QAGlB,OAAO7V,MAAA;MAAA;IArEU,CAqEV;EArEA,CAqEA;EAGX,OAAO;IACL0D,UAAA,EAAAA,UAAA;IACAkT,cAAA,EAAAA,cAAA;IACAM,aAAA,EAAAA,aAAA;IACAoD,cAAA,EAAgBP;GAAA;AAAA;;ACngBb,IAAMQ,gBAAA,GAAmB;AAEzB,IAAMC,kBAAA,GACX,SAAAA,CAAA;EACA,iBAACzhB,OAAA;;IAA+C;MAC9CA,OAAA,EAAAA,OAAA;MACAC,IAAA,GAAAuB,EAAA,OAAMA,EAAA,CAAGggB,gBAAA,IAAmB,MAAAhgB,EAAA;KAAA;EAFkB,CAElB;AAF9B,CAE8B;AAKhC,IAAIkgB,OAAA;AACJ,IAAMC,kBAAA,GACJ,OAAOC,cAAA,KAAmB,aACtBA,cAAA,CAAepe,IAAA,CACb,OAAO7E,MAAA,KAAW,cACdA,MAAA,GACA,OAAOkjB,MAAA,KAAW,cAClBA,MAAA,GACAC,UAAA,IAGN,UAACC,EAAA;EACE,QAAAL,OAAA,KAAYA,OAAA,GAAUzJ,OAAA,CAAQK,OAAA,KAAYG,IAAA,CAAKsJ,EAAA,EAAIpH,KAAA,CAAM,UAACqH,GAAA;IACzD,OAAAnF,UAAA,CAAW;MACT,MAAMmF,GAAA;IAAA,GACL;EAFH,CAEG;AAHJ,CAGI;AAGb,IAAMC,oBAAA,GAAuB,SAAAA,CAACjE,OAAA;EAC5B,OAAO,UAACkE,MAAA;IACNrF,UAAA,CAAWqF,MAAA,EAAQlE,OAAA;EAAA;AAAA;AAMvB,IAAMmE,GAAA,GACJ,OAAOxjB,MAAA,KAAW,eAAeA,MAAA,CAAOyjB,qBAAA,GACpCzjB,MAAA,CAAOyjB,qBAAA,GACPH,oBAAA,CAAqB;AA8BpB,IAAMI,iBAAA,GACX,SAAAA,CAAClhB,OAAoC;EAApC,IAAAA,OAAA;IAAAA,OAAA;MAA8BzB,IAAA,EAAM;IAAA;EAAA;EACrC,iBAAC6B,IAAA;IACD;MAAA,IAAA5D,IAAA;WAAA,IAAAC,EAAA,IAAI,EAAJA,EAAA,GAAAC,SAAA,CAAAC,MAAI,EAAJF,EAAA,EAAI;QAAJD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;MACE,IAAM0kB,KAAA,GAAQ/gB,IAAA,CAAAvD,KAAA,SAAQL,IAAA;MAEtB,IAAI4kB,SAAA,GAAY;MAChB,IAAIC,uBAAA,GAA0B;MAC9B,IAAIC,kBAAA,GAAqB;MAEzB,IAAMC,SAAA,GAAY,IAAI/c,GAAA;MAEtB,IAAMgd,aAAA,GACJxhB,OAAA,CAAQzB,IAAA,KAAS,SACbiiB,kBAAA,GACAxgB,OAAA,CAAQzB,IAAA,KAAS,QACjByiB,GAAA,GACAhhB,OAAA,CAAQzB,IAAA,KAAS,aACjByB,OAAA,CAAQyhB,iBAAA,GACRX,oBAAA,CAAqB9gB,OAAA,CAAQ6c,OAAA;MAEnC,IAAM6E,eAAA,GAAkB,SAAAA,CAAA;QAGtBJ,kBAAA,GAAqB;QACrB,IAAID,uBAAA,EAAyB;UAC3BA,uBAAA,GAA0B;UAC1BE,SAAA,CAAUxU,OAAA,CAAQ,UAAC4U,CAAA;YAAM,OAAAA,CAAA;UAAA;;MAAA;MAI7B,OAAO5jB,MAAA,CAAOiQ,MAAA,CAAO,IAAImT,KAAA,EAAO;QAG9B1B,SAAA,WAAAA,CAAUmC,SAAA;UAKR,IAAMC,eAAA,GAAmC,SAAAA,CAAA;YAAM,OAAAT,SAAA,IAAaQ,SAAA;UAAb,CAAa;UAC5D,IAAM9E,WAAA,GAAcqE,KAAA,CAAM1B,SAAA,CAAUoC,eAAA;UACpCN,SAAA,CAAU5c,GAAA,CAAIid,SAAA;UACd,OAAO;YACL9E,WAAA;YACAyE,SAAA,CAAU9C,MAAA,CAAOmD,SAAA;UAAA;QAAA;QAKrB1L,QAAA,WAAAA,CAASjX,MAAA;UAzHf,IAAAyX,EAAA;UA0HQ,IAAI;YAGF0K,SAAA,GAAY,EAAC,CAAA1K,EAAA,GAAAzX,MAAA,oBAAAA,MAAA,CAAQH,IAAA,KAAR,gBAAA4X,EAAA,CAAe2J,gBAAA;YAG5BgB,uBAAA,GAA0B,CAACD,SAAA;YAC3B,IAAIC,uBAAA,EAAyB;cAI3B,IAAI,CAACC,kBAAA,EAAoB;gBACvBA,kBAAA,GAAqB;gBACrBE,aAAA,CAAcE,eAAA;;;YASlB,OAAOP,KAAA,CAAMjL,QAAA,CAASjX,MAAA;WAAA,SACtB;YAEAmiB,SAAA,GAAY;;QAAA;OAAA;IAAA;EAxEpB,CAwEoB;AAzEpB,CAyEoB;;A7B3HtBxlB,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}