{"ast": null, "code": "'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n    var keys = getOwnPropertyNames(sourceComponent);\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n  return targetComponent;\n}\nmodule.exports = hoistNonReactStatics;", "map": {"version": 3, "names": ["reactIs", "require", "REACT_STATICS", "childContextTypes", "contextType", "contextTypes", "defaultProps", "displayName", "getDefaultProps", "getDerivedStateFromError", "getDerivedStateFromProps", "mixins", "propTypes", "type", "KNOWN_STATICS", "name", "length", "prototype", "caller", "callee", "arguments", "arity", "FORWARD_REF_STATICS", "render", "MEMO_STATICS", "compare", "TYPE_STATICS", "ForwardRef", "Memo", "getStatics", "component", "isMemo", "defineProperty", "Object", "getOwnPropertyNames", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "getPrototypeOf", "objectPrototype", "hoistNonReactStatics", "targetComponent", "sourceComponent", "blacklist", "inheritedComponent", "keys", "concat", "targetStatics", "sourceStatics", "i", "key", "descriptor", "e", "module", "exports"], "sources": ["/Users/<USER>/code_base/lms_project/frontend/node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"], "sourcesContent": ["'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAEjC;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG;EAClBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,wBAAwB,EAAE,IAAI;EAC9BC,wBAAwB,EAAE,IAAI;EAC9BC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,mBAAmB,GAAG;EACxB,UAAU,EAAE,IAAI;EAChBC,MAAM,EAAE,IAAI;EACZjB,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBK,SAAS,EAAE;AACb,CAAC;AACD,IAAIY,YAAY,GAAG;EACjB,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,IAAI;EACbnB,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBK,SAAS,EAAE,IAAI;EACfC,IAAI,EAAE;AACR,CAAC;AACD,IAAIa,YAAY,GAAG,CAAC,CAAC;AACrBA,YAAY,CAAC1B,OAAO,CAAC2B,UAAU,CAAC,GAAGL,mBAAmB;AACtDI,YAAY,CAAC1B,OAAO,CAAC4B,IAAI,CAAC,GAAGJ,YAAY;AAEzC,SAASK,UAAUA,CAACC,SAAS,EAAE;EAC7B;EACA,IAAI9B,OAAO,CAAC+B,MAAM,CAACD,SAAS,CAAC,EAAE;IAC7B,OAAON,YAAY;EACrB,CAAC,CAAC;;EAGF,OAAOE,YAAY,CAACI,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI5B,aAAa;AAC7D;AAEA,IAAI8B,cAAc,GAAGC,MAAM,CAACD,cAAc;AAC1C,IAAIE,mBAAmB,GAAGD,MAAM,CAACC,mBAAmB;AACpD,IAAIC,qBAAqB,GAAGF,MAAM,CAACE,qBAAqB;AACxD,IAAIC,wBAAwB,GAAGH,MAAM,CAACG,wBAAwB;AAC9D,IAAIC,cAAc,GAAGJ,MAAM,CAACI,cAAc;AAC1C,IAAIC,eAAe,GAAGL,MAAM,CAAChB,SAAS;AACtC,SAASsB,oBAAoBA,CAACC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAE;EACzE,IAAI,OAAOD,eAAe,KAAK,QAAQ,EAAE;IACvC;IACA,IAAIH,eAAe,EAAE;MACnB,IAAIK,kBAAkB,GAAGN,cAAc,CAACI,eAAe,CAAC;MAExD,IAAIE,kBAAkB,IAAIA,kBAAkB,KAAKL,eAAe,EAAE;QAChEC,oBAAoB,CAACC,eAAe,EAAEG,kBAAkB,EAAED,SAAS,CAAC;MACtE;IACF;IAEA,IAAIE,IAAI,GAAGV,mBAAmB,CAACO,eAAe,CAAC;IAE/C,IAAIN,qBAAqB,EAAE;MACzBS,IAAI,GAAGA,IAAI,CAACC,MAAM,CAACV,qBAAqB,CAACM,eAAe,CAAC,CAAC;IAC5D;IAEA,IAAIK,aAAa,GAAGjB,UAAU,CAACW,eAAe,CAAC;IAC/C,IAAIO,aAAa,GAAGlB,UAAU,CAACY,eAAe,CAAC;IAE/C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAAC5B,MAAM,EAAE,EAAEgC,CAAC,EAAE;MACpC,IAAIC,GAAG,GAAGL,IAAI,CAACI,CAAC,CAAC;MAEjB,IAAI,CAAClC,aAAa,CAACmC,GAAG,CAAC,IAAI,EAAEP,SAAS,IAAIA,SAAS,CAACO,GAAG,CAAC,CAAC,IAAI,EAAEF,aAAa,IAAIA,aAAa,CAACE,GAAG,CAAC,CAAC,IAAI,EAAEH,aAAa,IAAIA,aAAa,CAACG,GAAG,CAAC,CAAC,EAAE;QAC7I,IAAIC,UAAU,GAAGd,wBAAwB,CAACK,eAAe,EAAEQ,GAAG,CAAC;QAE/D,IAAI;UACF;UACAjB,cAAc,CAACQ,eAAe,EAAES,GAAG,EAAEC,UAAU,CAAC;QAClD,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;MACf;IACF;EACF;EAEA,OAAOX,eAAe;AACxB;AAEAY,MAAM,CAACC,OAAO,GAAGd,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}