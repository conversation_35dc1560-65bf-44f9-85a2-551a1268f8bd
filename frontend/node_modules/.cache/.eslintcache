[{"/Users/<USER>/code_base/lms_project/frontend/src/index.tsx": "1", "/Users/<USER>/code_base/lms_project/frontend/src/App.tsx": "2", "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx": "3", "/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx": "4", "/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx": "5", "/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx": "6", "/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx": "7", "/Users/<USER>/code_base/lms_project/frontend/src/store/index.ts": "8", "/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts": "9", "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx": "10", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/ScaleLetterTransition.tsx": "11", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx": "12", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx": "13"}, {"size": 274, "mtime": 1736643651000, "results": "14", "hashOfConfig": "15"}, {"size": 1324, "mtime": 1756762293588, "results": "16", "hashOfConfig": "15"}, {"size": 983, "mtime": 1748119898829, "results": "17", "hashOfConfig": "15"}, {"size": 5792, "mtime": 1756762293591, "results": "18", "hashOfConfig": "15"}, {"size": 16415, "mtime": 1750017415798, "results": "19", "hashOfConfig": "15"}, {"size": 15730, "mtime": 1756762293592, "results": "20", "hashOfConfig": "15"}, {"size": 213, "mtime": 1748119898827, "results": "21", "hashOfConfig": "15"}, {"size": 298, "mtime": 1742490296000, "results": "22", "hashOfConfig": "15"}, {"size": 1081, "mtime": 1742490296000, "results": "23", "hashOfConfig": "15"}, {"size": 4817, "mtime": 1756762293593, "results": "24", "hashOfConfig": "15"}, {"size": 1713, "mtime": 1756658191901, "results": "25", "hashOfConfig": "15"}, {"size": 17751, "mtime": 1756670325247, "results": "26", "hashOfConfig": "15"}, {"size": 6413, "mtime": 1756762293594, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "175ord9", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/code_base/lms_project/frontend/src/index.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/App.tsx", ["67"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx", ["68"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx", ["69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/store/index.ts", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/ScaleLetterTransition.tsx", ["80"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx", ["81"], [], {"ruleId": "82", "severity": 1, "message": "83", "line": 12, "column": 10, "nodeType": "84", "messageId": "85", "endLine": 12, "endColumn": 21}, {"ruleId": "82", "severity": 1, "message": "86", "line": 1, "column": 17, "nodeType": "84", "messageId": "85", "endLine": 1, "endColumn": 26}, {"ruleId": "82", "severity": 1, "message": "87", "line": 26, "column": 12, "nodeType": "84", "messageId": "85", "endLine": 26, "endColumn": 20}, {"ruleId": "82", "severity": 1, "message": "88", "line": 26, "column": 22, "nodeType": "84", "messageId": "85", "endLine": 26, "endColumn": 33}, {"ruleId": "82", "severity": 1, "message": "89", "line": 28, "column": 30, "nodeType": "84", "messageId": "85", "endLine": 28, "endColumn": 49}, {"ruleId": "82", "severity": 1, "message": "90", "line": 29, "column": 12, "nodeType": "84", "messageId": "85", "endLine": 29, "endColumn": 25}, {"ruleId": "82", "severity": 1, "message": "91", "line": 30, "column": 12, "nodeType": "84", "messageId": "85", "endLine": 30, "endColumn": 24}, {"ruleId": "82", "severity": 1, "message": "92", "line": 31, "column": 12, "nodeType": "84", "messageId": "85", "endLine": 31, "endColumn": 25}, {"ruleId": "82", "severity": 1, "message": "93", "line": 33, "column": 12, "nodeType": "84", "messageId": "85", "endLine": 33, "endColumn": 32}, {"ruleId": "82", "severity": 1, "message": "94", "line": 105, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 105, "endColumn": 31}, {"ruleId": "82", "severity": 1, "message": "95", "line": 138, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 138, "endColumn": 32}, {"ruleId": "82", "severity": 1, "message": "96", "line": 150, "column": 16, "nodeType": "84", "messageId": "85", "endLine": 150, "endColumn": 31}, {"ruleId": "82", "severity": 1, "message": "97", "line": 165, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 165, "endColumn": 33}, {"ruleId": "98", "severity": 1, "message": "99", "line": 21, "column": 6, "nodeType": "100", "endLine": 21, "endColumn": 16, "suggestions": "101"}, {"ruleId": "82", "severity": 1, "message": "102", "line": 5, "column": 11, "nodeType": "84", "messageId": "85", "endLine": 5, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'ProfilePage' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'keywords' is assigned a value but never used.", "'setKeywords' is assigned a value but never used.", "'setSelectedVideoIds' is assigned a value but never used.", "'isDownloading' is assigned a value but never used.", "'isProcessing' is assigned a value but never used.", "'statusMessage' is assigned a value but never used.", "'generatedDescription' is assigned a value but never used.", "'handleDownloadVideos' is assigned a value but never used.", "'handleProcessSelected' is assigned a value but never used.", "'videosToProcess' is assigned a value but never used.", "'handleCreateFinalVideo' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'startTransition'. Either include it or remove the dependency array.", "ArrayExpression", ["103"], "'RotationState' is defined but never used.", {"desc": "104", "fix": "105"}, "Update the dependencies array to be: [isActive, startTransition]", {"range": "106", "text": "107"}, [415, 425], "[isActive, startTransition]"]