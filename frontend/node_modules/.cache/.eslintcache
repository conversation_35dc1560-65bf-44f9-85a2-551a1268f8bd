[{"/Users/<USER>/code_base/lms_project/frontend/src/index.tsx": "1", "/Users/<USER>/code_base/lms_project/frontend/src/App.tsx": "2", "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx": "3", "/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx": "4", "/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx": "5", "/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx": "6", "/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx": "7", "/Users/<USER>/code_base/lms_project/frontend/src/store/index.ts": "8", "/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts": "9", "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx": "10", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/ScaleLetterTransition.tsx": "11", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx": "12", "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx": "13", "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/GyroscopePage.tsx": "14"}, {"size": 274, "mtime": 1736643651000, "results": "15", "hashOfConfig": "16"}, {"size": 1456, "mtime": 1756757366513, "results": "17", "hashOfConfig": "16"}, {"size": 983, "mtime": 1748119898829, "results": "18", "hashOfConfig": "16"}, {"size": 6335, "mtime": 1756661213290, "results": "19", "hashOfConfig": "16"}, {"size": 16415, "mtime": 1750017415798, "results": "20", "hashOfConfig": "16"}, {"size": 15590, "mtime": 1756670825898, "results": "21", "hashOfConfig": "16"}, {"size": 213, "mtime": 1748119898827, "results": "22", "hashOfConfig": "16"}, {"size": 298, "mtime": 1742490296000, "results": "23", "hashOfConfig": "16"}, {"size": 1081, "mtime": 1742490296000, "results": "24", "hashOfConfig": "16"}, {"size": 9899, "mtime": 1756749957085, "results": "25", "hashOfConfig": "16"}, {"size": 1713, "mtime": 1756658191901, "results": "26", "hashOfConfig": "16"}, {"size": 17751, "mtime": 1756670325247, "results": "27", "hashOfConfig": "16"}, {"size": 11842, "mtime": 1756750123536, "results": "28", "hashOfConfig": "16"}, {"size": 248, "mtime": 1756739467235, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "175ord9", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/code_base/lms_project/frontend/src/index.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/App.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoPreview/VideoPreview.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Home/HomePage.tsx", ["72"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/ManualVideoCreation/ManualVideoCreationPage.tsx", ["73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Samples/SamplesPage.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Checkout/CheckoutPage.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/store/index.ts", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/store/lessonSlice.ts", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/CreateVideoPage.tsx", ["84"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/ScaleLetterTransition.tsx", ["85"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/HyperOrangeTransition.tsx", [], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/Xtra/Gyroscope.tsx", ["86"], [], "/Users/<USER>/code_base/lms_project/frontend/src/components/VideoCreation/GyroscopePage.tsx", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 1, "column": 17, "nodeType": "89", "messageId": "90", "endLine": 1, "endColumn": 26}, {"ruleId": "87", "severity": 1, "message": "91", "line": 26, "column": 12, "nodeType": "89", "messageId": "90", "endLine": 26, "endColumn": 20}, {"ruleId": "87", "severity": 1, "message": "92", "line": 26, "column": 22, "nodeType": "89", "messageId": "90", "endLine": 26, "endColumn": 33}, {"ruleId": "87", "severity": 1, "message": "93", "line": 28, "column": 30, "nodeType": "89", "messageId": "90", "endLine": 28, "endColumn": 49}, {"ruleId": "87", "severity": 1, "message": "94", "line": 29, "column": 12, "nodeType": "89", "messageId": "90", "endLine": 29, "endColumn": 25}, {"ruleId": "87", "severity": 1, "message": "95", "line": 30, "column": 12, "nodeType": "89", "messageId": "90", "endLine": 30, "endColumn": 24}, {"ruleId": "87", "severity": 1, "message": "96", "line": 31, "column": 12, "nodeType": "89", "messageId": "90", "endLine": 31, "endColumn": 25}, {"ruleId": "87", "severity": 1, "message": "97", "line": 33, "column": 12, "nodeType": "89", "messageId": "90", "endLine": 33, "endColumn": 32}, {"ruleId": "87", "severity": 1, "message": "98", "line": 105, "column": 11, "nodeType": "89", "messageId": "90", "endLine": 105, "endColumn": 31}, {"ruleId": "87", "severity": 1, "message": "99", "line": 138, "column": 11, "nodeType": "89", "messageId": "90", "endLine": 138, "endColumn": 32}, {"ruleId": "87", "severity": 1, "message": "100", "line": 150, "column": 16, "nodeType": "89", "messageId": "90", "endLine": 150, "endColumn": 31}, {"ruleId": "87", "severity": 1, "message": "101", "line": 165, "column": 11, "nodeType": "89", "messageId": "90", "endLine": 165, "endColumn": 33}, {"ruleId": "102", "severity": 1, "message": "103", "line": 37, "column": 6, "nodeType": "104", "endLine": 37, "endColumn": 39, "suggestions": "105"}, {"ruleId": "102", "severity": 1, "message": "106", "line": 21, "column": 6, "nodeType": "104", "endLine": 21, "endColumn": 16, "suggestions": "107"}, {"ruleId": "87", "severity": 1, "message": "108", "line": 142, "column": 11, "nodeType": "89", "messageId": "90", "endLine": 142, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'keywords' is assigned a value but never used.", "'setKeywords' is assigned a value but never used.", "'setSelectedVideoIds' is assigned a value but never used.", "'isDownloading' is assigned a value but never used.", "'isProcessing' is assigned a value but never used.", "'statusMessage' is assigned a value but never used.", "'generatedDescription' is assigned a value but never used.", "'handleDownloadVideos' is assigned a value but never used.", "'handleProcessSelected' is assigned a value but never used.", "'videosToProcess' is assigned a value but never used.", "'handleCreateFinalVideo' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'formFields.length'. Either include it or remove the dependency array.", "ArrayExpression", ["109"], "React Hook useEffect has a missing dependency: 'startTransition'. Either include it or remove the dependency array.", ["110"], "'perspective' is assigned a value but never used.", {"desc": "111", "fix": "112"}, {"desc": "113", "fix": "114"}, "Update the dependencies array to be: [formVisible, childFieldsVisible, formFields.length]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [isActive, startTransition]", {"range": "117", "text": "118"}, [1090, 1123], "[formVisible, childFieldsVisible, formFields.length]", [415, 425], "[isActive, startTransition]"]