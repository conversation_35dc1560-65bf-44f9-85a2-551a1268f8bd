{"name": "@csstools/postcss-font-format-keywords", "description": "Use unquoted format on @font-face CSS definitions.", "version": "4.0.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/utilities": "^2.0.0", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-font-format-keywords#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-font-format-keywords"}, "keywords": ["css", "embedded-opentype", "font", "font-format-keywords", "format", "opentype", "postcss-plugin", "truetype", "woff", "woff2"]}