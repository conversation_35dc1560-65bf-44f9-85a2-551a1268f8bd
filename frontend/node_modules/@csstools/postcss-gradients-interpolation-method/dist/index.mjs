import o from"@csstools/postcss-progressive-custom-properties";import{hasFallback as e,hasSupportsAtRuleAncestor as t}from"@csstools/utilities";import{WhitespaceNode as i,TokenNode as r,FunctionNode as n,isCommentNode as s,isWhitespaceNode as l,isTokenNode as a,stringify as c,replaceComponentValues as u,parseCommaSeparatedListOfComponentValues as p,isFunctionNode as v}from"@csstools/css-parser-algorithms";import{TokenType as f,isTokenComma as h,isTokenIdent as m,tokenize as g}from"@csstools/css-tokenizer";import{serializeP3 as d,color as w,colorDataFitsRGB_Gamut as D,serializeRGB as C,SyntaxFlag as b}from"@csstools/css-color-parser";const x=/(?:repeating-)?(?:linear|radial|conic)-gradient\(/i,W=/\bin\b/i,P={test:o=>x.test(o)&&W.test(o)},A=/^(repeating-)?(linear|radial|conic)-gradient$/i;function interpolateColorsInColorStopsList(o,e,t,s=!1){const l=[],a=[];for(let s=0;s<o.length-1;s++){const l=o[s],c=o[s+1];if(a.push(l),t||d(l.colorData,!1).toString()!==d(c.colorData,!1).toString()&&l.position.toString()!==c.position.toString())for(let o=1;o<=9;o++){const s=10*o;let u=[];t&&(u=[new i([[f.Whitespace," ",-1,-1,void 0]]),t,new i([[f.Whitespace," ",-1,-1,void 0]]),new r([f.Ident,"hue",-1,-1,{value:"hue"}])]);const p=new n([f.Function,"color-mix(",-1,-1,{value:"color-mix"}],[f.CloseParen,")",-1,-1,void 0],[new r([f.Ident,"in",-1,-1,{value:"in"}]),new i([[f.Whitespace," ",-1,-1,void 0]]),e,...u,new r([f.Comma,",",-1,-1,void 0]),new i([[f.Whitespace," ",-1,-1,void 0]]),l.color,new i([[f.Whitespace," ",-1,-1,void 0]]),new r([f.Percentage,100-s+"%",-1,-1,{value:100-s}]),new r([f.Comma,",",-1,-1,void 0]),new i([[f.Whitespace," ",-1,-1,void 0]]),c.color,new i([[f.Whitespace," ",-1,-1,void 0]]),new r([f.Percentage,`${s}%`,-1,-1,{value:s}])]),v=w(p);if(!v)return!1;a.push({colorData:v})}s===o.length-2&&a.push(c)}for(let o=0;o<a.length;o++)s&&!D(a[o].colorData)?a[o].color=d(a[o].colorData,!1):a[o].color=C(a[o].colorData,!1);for(let o=0;o<a.length;o++){const e=a[o];e.position?l.push(e.color,new i([[f.Whitespace," ",-1,-1,void 0]]),e.position):l.push(e.color),o!==a.length-1&&l.push(new r([f.Comma,",",-1,-1,void 0]),new i([[f.Whitespace," ",-1,-1,void 0]]))}return l}function parseColorStops(o){const e=[];let t={};for(let i=0;i<o.length;i++){const r=o[i];if(s(r)||l(r))continue;if(a(r)&&h(r.value)){if(t.color&&t.colorData&&t.positionA){e.push({color:t.color,colorData:t.colorData,position:t.positionA}),t.positionB&&e.push({color:t.color,colorData:t.colorData,position:t.positionB}),t={};continue}return!1}const n=w(r);if(n){if(t.color)return!1;if(n.syntaxFlags.has(b.Experimental))return!1;t.color=r,t.colorData=n}else{if(!t.color)return!1;if(t.positionA){if(!t.positionA||t.positionB)return!1;t.positionB=r}else t.positionA=r}}return!(!t.color||!t.positionA)&&(t.color&&t.colorData&&t.positionA&&(e.push({color:t.color,colorData:t.colorData,position:t.positionA}),t.positionB&&e.push({color:t.color,colorData:t.colorData,position:t.positionB})),!(e.length<2)&&e)}const B=/^(?:srgb|srgb-linear|lab|oklab|xyz|xyz-d50|xyz-d65|hsl|hwb|lch|oklch)$/i,S=/^(?:hsl|hwb|lch|oklch)$/i,y=/^(?:shorter|longer|increasing|decreasing)$/i,$=/^in$/i,k=/^hue$/i;function modifyGradientFunctionComponentValues(o,e=!1){const t=o.getName();if(!A.test(t))return!1;let n="srgb",c=null,u=null,p=null,v=null,g=null,d=[];{let e=0,t=o.value[e];for(;t&&!(a(t)&&m(t.value)&&$.test(t.value[4].value));){if(a(t)&&h(t.value))return!1;e++,t=o.value[e]}for(c=t,e++,t=o.value[e];s(t)||l(t);)e++,t=o.value[e];if(a(t)&&m(t.value)&&B.test(t.value[4].value)){if(u)return!1;u=t,n=t.value[4].value,e++,t=o.value[e]}for(;s(t)||l(t);)e++,t=o.value[e];if(a(t)&&m(t.value)&&y.test(t.value[4].value)&&S.test(n)){if(p||!u)return!1;p=t,e++,t=o.value[e]}for(;s(t)||l(t);)e++,t=o.value[e];if(a(t)&&m(t.value)&&k.test(t.value[4].value)){if(v||!u||!p)return!1;v=t,e++,t=o.value[e]}for(;t&&(!a(t)||!h(t.value));)e++,t=o.value[e];if(g=t,!g)return!1;d=o.value.slice(e+1)}if(!u)return!1;if(p&&!v)return!1;if(v&&!p)return!1;const w=parseColorStops(d);if(!w)return!1;const D=interpolateColorsInColorStopsList(w,u,p,e);if(!D)return!1;const C=trim([...o.value.slice(0,o.value.indexOf(c)),...o.value.slice(o.value.indexOf(v||u)+1,o.value.indexOf(g))]);return C.length>0&&C.some(o=>!s(o))&&C.push(new r([f.Comma,",",-1,-1,void 0]),new i([[f.Whitespace," ",-1,-1,void 0]])),trim([...C,...trim(D)])}function trim(o){let e=0,t=o.length-1;for(let t=0;t<o.length;t++)if(!l(o[t])){e=t;break}for(let e=o.length-1;e>=0;e--)if(!l(o[e])){t=e;break}return o.slice(e,t+1)}const basePlugin=o=>({postcssPlugin:"postcss-gradients-interpolation-method",Declaration(i){if(!P.test(i.value))return;if(e(i))return;if(t(i,P))return;const r=g({css:i.value}),n=c(u(p(r),o=>{if(!v(o))return;const e=modifyGradientFunctionComponentValues(o);e&&(o.value=e)}));if(n===i.value)return;const s=c(u(p(r),o=>{if(!v(o))return;const e=modifyGradientFunctionComponentValues(o,!0);e&&(o.value=e)}));i.cloneBefore({value:n}),n!==s&&i.cloneBefore({value:s}),o?.preserve||i.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!0},e);return t.enableProgressiveCustomProperties?{postcssPlugin:"postcss-gradients-interpolation-method",plugins:[o(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
