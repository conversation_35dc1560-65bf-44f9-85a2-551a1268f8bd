import e from"@csstools/postcss-progressive-custom-properties";import{tokenize as o,isTokenIdent as r,TokenType as s,isTokenHash as t}from"@csstools/css-tokenizer";import{color as l,SyntaxFlag as a}from"@csstools/css-color-parser";import{hasFallback as n,hasSupportsAtRuleAncestor as i}from"@csstools/utilities";import{replaceComponentValues as c,parseCommaSeparatedListOfComponentValues as v,isFunctionNode as u,isWhiteSpaceOrCommentNode as p,isTokenNode as d,WhitespaceNode as h,TokenNode as f,stringify as g}from"@csstools/css-parser-algorithms";const b=/\balpha\(/i,w=/^alpha$/i,m=new Set(["srgb","srgb-linear","display-p3","display-p3-linear","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]),basePlugin=e=>({postcssPlugin:"postcss-color-function",Declaration(C){const y=C.value;if(!b.test(y))return;if(n(C))return;if(i(C,b))return;const x=o({css:y}),R=c(v(x),e=>{if(u(e)&&w.test(e.getName()))for(let o=0;o<e.value.length;o++){let n=e.value[o];for(;p(n);)o++,n=e.value[o];for(d(n)&&r(n.value)&&"from"===n.value[4].value.toLowerCase()&&(o++,n=e.value[o]);p(n);)o++,n=e.value[o];const i=n;if(u(i)){const t=i.getName().toLowerCase();if("var"===t)return;if("color"===t){let t="";for(let e=0;e<i.value.length;e++){const o=i.value[e];if(d(o)&&r(o.value)&&m.has(o.value[4].value.toLowerCase())){if(t)return;t=o.value[4].value}}if(!t)return;e.name[1]="color(",e.name[4].value="color";let l=["r","g","b"];return"xyz"!==t&&"xyz-d50"!==t&&"xyz-d65"!==t||(l=["x","y","z"]),void e.value.splice(o+1,0,new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,t,-1,-1,{value:t}]),new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,l[0],-1,-1,{value:l[0]}]),new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,l[1],-1,-1,{value:l[1]}]),new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,l[2],-1,-1,{value:l[2]}]))}switch(t){case"rgb":case"rgba":return void convertToRelativeColor(e,o+1,"rgb",["r","g","b"]);case"hsl":case"hsla":return void convertToRelativeColor(e,o+1,"hsl",["h","s","l"]);case"hwb":return void convertToRelativeColor(e,o+1,"hwb",["h","w","b"]);case"lab":return void convertToRelativeColor(e,o+1,"lab",["l","a","b"]);case"lch":return void convertToRelativeColor(e,o+1,"lch",["l","c","h"]);case"oklab":return void convertToRelativeColor(e,o+1,"oklab",["l","a","b"]);case"oklch":return void convertToRelativeColor(e,o+1,"oklch",["l","c","h"])}}if(d(i)&&(t(i.value)||r(i.value))){const r=l(i);if(!r)return;if(!r.syntaxFlags.has(a.Hex)&&!r.syntaxFlags.has(a.ColorKeyword))return;return void convertToRelativeColor(e,o+1,"rgb",["r","g","b"])}}}),T=g(R);T!==y&&(C.cloneBefore({value:T}),e?.preserve||C.remove())}});function convertToRelativeColor(e,o,r,t){e.name[1]=r+"(",e.name[4].value=r,e.value.splice(o,0,new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,t[0],-1,-1,{value:t[0]}]),new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,t[1],-1,-1,{value:t[1]}]),new h([[s.Whitespace," ",-1,-1,void 0]]),new f([s.Ident,t[2],-1,-1,{value:t[2]}]))}basePlugin.postcss=!0;const postcssPlugin=o=>{const r=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},o);return r.enableProgressiveCustomProperties&&r.preserve?{postcssPlugin:"postcss-color-function",plugins:[e(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
