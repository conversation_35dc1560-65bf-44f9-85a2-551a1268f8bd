"use strict";var e=require("@csstools/postcss-progressive-custom-properties"),o=require("@csstools/css-tokenizer"),s=require("@csstools/css-color-parser"),t=require("@csstools/utilities"),r=require("@csstools/css-parser-algorithms");const n=/\balpha\(/i,a=/^alpha$/i,l=new Set(["srgb","srgb-linear","display-p3","display-p3-linear","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]),basePlugin=e=>({postcssPlugin:"postcss-color-function",Declaration(i){const c=i.value;if(!n.test(c))return;if(t.hasFallback(i))return;if(t.hasSupportsAtRuleAncestor(i,n))return;const v=o.tokenize({css:c}),u=r.replaceComponentValues(r.parseCommaSeparatedListOfComponentValues(v),e=>{if(r.isFunctionNode(e)&&a.test(e.getName()))for(let t=0;t<e.value.length;t++){let n=e.value[t];for(;r.isWhiteSpaceOrCommentNode(n);)t++,n=e.value[t];for(r.isTokenNode(n)&&o.isTokenIdent(n.value)&&"from"===n.value[4].value.toLowerCase()&&(t++,n=e.value[t]);r.isWhiteSpaceOrCommentNode(n);)t++,n=e.value[t];const a=n;if(r.isFunctionNode(a)){const s=a.getName().toLowerCase();if("var"===s)return;if("color"===s){let s="";for(let e=0;e<a.value.length;e++){const t=a.value[e];if(r.isTokenNode(t)&&o.isTokenIdent(t.value)&&l.has(t.value[4].value.toLowerCase())){if(s)return;s=t.value[4].value}}if(!s)return;e.name[1]="color(",e.name[4].value="color";let n=["r","g","b"];return"xyz"!==s&&"xyz-d50"!==s&&"xyz-d65"!==s||(n=["x","y","z"]),void e.value.splice(t+1,0,new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,s,-1,-1,{value:s}]),new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[0],-1,-1,{value:n[0]}]),new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[1],-1,-1,{value:n[1]}]),new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[2],-1,-1,{value:n[2]}]))}switch(s){case"rgb":case"rgba":return void convertToRelativeColor(e,t+1,"rgb",["r","g","b"]);case"hsl":case"hsla":return void convertToRelativeColor(e,t+1,"hsl",["h","s","l"]);case"hwb":return void convertToRelativeColor(e,t+1,"hwb",["h","w","b"]);case"lab":return void convertToRelativeColor(e,t+1,"lab",["l","a","b"]);case"lch":return void convertToRelativeColor(e,t+1,"lch",["l","c","h"]);case"oklab":return void convertToRelativeColor(e,t+1,"oklab",["l","a","b"]);case"oklch":return void convertToRelativeColor(e,t+1,"oklch",["l","c","h"])}}if(r.isTokenNode(a)&&(o.isTokenHash(a.value)||o.isTokenIdent(a.value))){const o=s.color(a);if(!o)return;if(!o.syntaxFlags.has(s.SyntaxFlag.Hex)&&!o.syntaxFlags.has(s.SyntaxFlag.ColorKeyword))return;return void convertToRelativeColor(e,t+1,"rgb",["r","g","b"])}}}),p=r.stringify(u);p!==c&&(i.cloneBefore({value:p}),e?.preserve||i.remove())}});function convertToRelativeColor(e,s,t,n){e.name[1]=t+"(",e.name[4].value=t,e.value.splice(s,0,new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[0],-1,-1,{value:n[0]}]),new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[1],-1,-1,{value:n[1]}]),new r.WhitespaceNode([[o.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([o.TokenType.Ident,n[2],-1,-1,{value:n[2]}]))}basePlugin.postcss=!0;const postcssPlugin=o=>{const s=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},o);return s.enableProgressiveCustomProperties&&s.preserve?{postcssPlugin:"postcss-color-function",plugins:[e(),basePlugin(s)]}:basePlugin(s)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
