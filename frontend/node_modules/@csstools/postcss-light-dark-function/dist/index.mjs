import e from"@csstools/postcss-progressive-custom-properties";import{tokenize as r,isTokenIdent as t,isTokenComma as o,TokenType as s}from"@csstools/css-tokenizer";import{hasFallback as n,hasSupportsAtRuleAncestor as a}from"@csstools/utilities";import{isFunctionNode as i,isWhitespaceNode as c,isCommentNode as u,walk as l,isTokenNode as p,replaceComponentValues as f,parseCommaSeparatedListOfComponentValues as g,FunctionNode as v,TokenNode as m,WhitespaceNode as d,stringify as h}from"@csstools/css-parser-algorithms";const k="--csstools-color-scheme--light",D="initial";function toggleNameGenerator(e){return`--csstools-light-dark-toggle--${e}`}const L=/dark/i,w=/light/i;function colorSchemes(e){const o=r({css:e});let s=!1,n=!1;return o.forEach(e=>{t(e)&&(w.test(e[4].value)?s=!0:L.test(e[4].value)&&(n=!0))}),[s,n]}const C=/^light-dark$/i;function isComma(e){return p(e)&&o(e.value)}function parseLightDark(e){if(!i(e)||!C.test(e.getName()))return!1;const r=e.value.filter(e=>!c(e)&&!u(e));if(3!==r.length)return!1;let t=r[0];const o=r[1];let s=r[2];if(!t||!o||!s)return!1;if(!isComma(o))return!1;if(isComma(t)||isComma(s))return!1;if(i(t)){const e=[t];l(e,({node:e,parent:r},t)=>{recurseLightDark(e,r,t,!0)}),[t]=e}if(i(s)){const e=[s];l(e,({node:e,parent:r},t)=>{recurseLightDark(e,r,t,!1)}),[s]=e}return[t,s]}function recurseLightDark(e,r,t,o){if("number"!=typeof t)return;const s=parseLightDark(e);if(!s)return;let n=s[o?0:1];if(i(n)){const e=[n];l(e,({node:e,parent:r},t)=>{recurseLightDark(e,r,t,o)}),[n]=e}r.value[t]=n}function transformLightDark(e,t){const o=new Map,n=f(g(r({css:e})),e=>{const r=parseLightDark(e);if(!r)return;const[n,a]=r,i=t();return o.set(i,`var(${k}) ${a.toString()}`),new v([s.Function,"var(",-1,-1,{value:"var"}],[s.CloseParen,")",-1,-1,void 0],[new m([s.Ident,i,-1,-1,{value:i}]),new m([s.Comma,",",-1,-1,void 0]),new d([[s.Whitespace," ",-1,-1,void 0]]),n])});return{value:h(n),toggles:o}}function newNestedRuleWithSupportsNot(e,r,t,o){const s=r({selector:"& *",source:e.source});if(!o)return{inner:s,outer:s};const n=t({name:"supports",params:"not (color: light-dark(tan, tan))",source:e.source});return n.append(s),{inner:s,outer:n}}const P=/^color-scheme$/i,N=/\blight-dark\(/i,basePlugin=e=>({postcssPlugin:"postcss-light-dark-function",prepare(){let r=0;const currentToggleNameGenerator=()=>toggleNameGenerator(r++),t=new Map;return{postcssPlugin:"postcss-light-dark-function",Declaration(r,{atRule:o,rule:s}){const i=r.parent;if(i){if(P.test(r.prop)){if(i.some(e=>"decl"===e.type&&e.prop===k))return;const[e,t]=colorSchemes(r.value);if(e&&t){r.cloneBefore({prop:k,value:D});const e=i.clone();e.removeAll(),e.append(r.clone({prop:k,value:" "}));const t=o({name:"media",params:"(prefers-color-scheme: dark)",source:i.source});return t.append(e),void i.after(t)}return t?void r.cloneBefore({prop:k,value:" "}):e?void r.cloneBefore({prop:k,value:D}):void 0}if(N.test(r.value)){if(n(r))return;if(a(r,N))return;const i=transformLightDark(r.value,currentToggleNameGenerator);if(i.value===r.value)return;for(const[e,t]of i.toggles)r.cloneBefore({prop:e,value:t});if(r.cloneBefore({value:i.value}),r.variable&&r.parent){const n=t.get(r.parent)??newNestedRuleWithSupportsNot(r,s,o,e?.preserve);for(const[e,t]of i.toggles)n.inner.append(r.clone({prop:e,value:t}));n.inner.append(r.clone({value:i.value})),t.has(r.parent)||(r.parent.append(n.outer),t.set(r.parent,n))}e?.preserve||r.remove()}}}}}});basePlugin.postcss=!0;const postcssPlugin=r=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!0},r);return t.enableProgressiveCustomProperties&&t.preserve?{postcssPlugin:"postcss-light-dark-function",plugins:[e(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
