"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-color-parser"),a=require("@csstools/utilities"),r=require("@csstools/css-parser-algorithms"),t=require("@csstools/css-tokenizer");const o=/\b(?:rgb|rgba|hsl|hsla|hwb|lab|lch|oklch|oklab|color)\(/i,l=/\b(?:rgb|rgba|hsl|hsla|hwb|lab|lch|oklch|oklab|color)\(\s*from/i,i=/^(?:rgb|rgba|hsl|hsla|hwb|lab|lch|oklch|oklab|color)$/i,n=/from/i,basePlugin=s=>({postcssPlugin:"postcss-relative-color-syntax",Declaration(c){const u=c.value;if(!o.test(u)||!n.test(u))return;if(a.hasFallback(c))return;if(a.hasSupportsAtRuleAncestor(c,l))return;const p=t.tokenize({css:u}),g=r.replaceComponentValues(r.parseCommaSeparatedListOfComponentValues(p),s=>{if(!r.isFunctionNode(s)||!i.test(s.getName()))return;const a=e.color(s);return a&&!a.syntaxFlags.has(e.SyntaxFlag.Experimental)&&!a.syntaxFlags.has(e.SyntaxFlag.HasNoneKeywords)&&a.syntaxFlags.has(e.SyntaxFlag.RelativeColorSyntax)?e.serializeRGB(a):void 0}),b=r.stringify(g);if(b===u)return;let y=b;s?.subFeatures.displayP3&&(y=r.stringify(r.replaceComponentValues(r.parseCommaSeparatedListOfComponentValues(p),s=>{if(!r.isFunctionNode(s)||!i.test(s.getName()))return;const a=e.color(s);return a&&!a.syntaxFlags.has(e.SyntaxFlag.Experimental)&&!a.syntaxFlags.has(e.SyntaxFlag.HasNoneKeywords)&&a.syntaxFlags.has(e.SyntaxFlag.RelativeColorSyntax)?e.colorDataFitsRGB_Gamut(a)?e.serializeRGB(a):e.serializeP3(a):void 0}))),c.cloneBefore({value:b}),s?.subFeatures.displayP3&&y!==b&&c.cloneBefore({value:y}),s?.preserve||c.remove()}});basePlugin.postcss=!0;const postcssPlugin=e=>{const a=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},e);return a.subFeatures=Object.assign({displayP3:!0},a.subFeatures),a.enableProgressiveCustomProperties&&(a.preserve||a.subFeatures.displayP3)?{postcssPlugin:"postcss-relative-color-syntax",plugins:[s(),basePlugin(a)]}:basePlugin(a)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
