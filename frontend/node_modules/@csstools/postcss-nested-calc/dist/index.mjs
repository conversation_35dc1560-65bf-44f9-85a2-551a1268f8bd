import e from"postcss-value-parser";import{hasFallback as t}from"@csstools/utilities";const r=/calc\(/gi;const creator=s=>{const a=Object.assign({preserve:!0},s);return{postcssPlugin:"postcss-nested-calc",Declaration(s,{result:o}){if((s.value.match(r)||[]).length<2)return;if(s.variable)return;if(t(s))return;const n=s.value;let c;try{c=e(n)}catch{return void s.warn(o,`Failed to parse value '${n}'. Leaving the original value intact.`)}if(void 0===c)return;e.walk(c.nodes,(t=>{t.type&&"function"===t.type&&"calc"===t.value.toLowerCase()&&e.walk(t.nodes,(e=>{if(e.type&&"function"===e.type)return"calc"===e.value.toLowerCase()&&void(e.value="")}))}),!0);const l=String(c);l!==n&&(s.cloneBefore({value:l}),a.preserve||s.remove())}}};creator.postcss=!0;export{creator as default};
