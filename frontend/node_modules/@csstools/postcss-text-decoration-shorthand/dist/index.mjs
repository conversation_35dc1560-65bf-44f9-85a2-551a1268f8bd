import e from"postcss-value-parser";import{namedColors as t}from"@csstools/color-helpers";const o=/^text-decoration$/i,creator=t=>{const c=Object.assign({preserve:!0},t);return{postcssPlugin:"postcss-text-decoration-shorthand",prepare(){const t=new Map;return{postcssPlugin:"postcss-text-decoration-shorthand",OnceExit(){t.clear()},Declaration(i){if(!o.test(i.prop))return;const a=i.parent;if(!a)return;const u=a.index(i);if(a.nodes.some(e=>"decl"===e.type&&o.test(e.prop)&&t.get(i.value)===e.value&&a.index(e)!==u))return;const d=e(i.value),p=d.nodes.filter(e=>"space"!==e.type&&"comment"!==e.type);if(p.find(e=>"var"===e.value.toLowerCase()&&"function"===e.type))return;if(p.find(e=>"word"===e.type&&r.includes(e.value)))return;const f={line:[],style:null,color:null,thickness:null};for(let t=0;t<p.length;t++){const o=p[t];if(!f.line.length&&"word"===o.type&&n.includes(o.value.toLowerCase())){const e=o;let r=o;for(;;){const e=p[t+1];if(!e||"word"!==e.type||!n.includes(e.value.toLowerCase()))break;r=e,t++}f.line=d.nodes.slice(d.nodes.indexOf(e),d.nodes.indexOf(r)+1);continue}if(f.line.length||"word"!==o.type||"none"!==o.value.toLowerCase())if(f.style||"word"!==o.type||!s.includes(o.value.toLowerCase()))if(f.thickness||"word"!==o.type||!l.includes(o.value.toLowerCase()))if(f.thickness||"function"!==o.type||"calc"!==o.value.toLowerCase()){if(f.color||!nodeIsAColor(o)){if("word"===o.type){let t;try{t=e.unit(o.value)}catch{return}if(!t||!t.unit)return;f.thickness=o,"%"===t.unit&&(f.thickness={...genericNodeParts(),type:"function",value:"calc",nodes:[{...genericNodeParts(),type:"word",value:"0.01em"},{...genericNodeParts(),type:"space",value:" "},{...genericNodeParts(),type:"word",value:"*"},{...genericNodeParts(),type:"space",value:" "},{...genericNodeParts(),type:"word",value:t.number}]});continue}return}f.color=o}else f.thickness=o;else f.thickness=o;else f.style=o;else f.line.push(o)}f.line.length||f.line.push({before:"",after:"",sourceIndex:0,sourceEndIndex:0,type:"word",value:"none"}),f.style||(f.style={before:"",after:"",sourceIndex:0,sourceEndIndex:0,type:"word",value:"solid"}),f.color||(f.color={before:"",after:"",sourceIndex:0,sourceEndIndex:0,type:"word",value:"currentColor"});const v=e.stringify(f.line);if(i.value.toLowerCase()===v.toLowerCase()){const e=i.next();return void(e&&"decl"===e.type&&"text-decoration"===e.prop.toLowerCase()||i.cloneBefore({prop:"-webkit-text-decoration",value:v}))}i.cloneBefore({prop:"text-decoration",value:v});const y=e.stringify([...f.line,{before:"",after:"",sourceIndex:0,sourceEndIndex:0,type:"space",value:" "},f.style,{before:"",after:"",sourceIndex:0,sourceEndIndex:0,type:"space",value:" "},f.color]);f.thickness&&i.cloneBefore({prop:"text-decoration",value:y}),f.thickness&&i.cloneBefore({prop:"text-decoration-thickness",value:e.stringify([f.thickness])}),t.set(i.value,v),t.set(y,v),c.preserve||i.remove()}}}}};function nodeIsAColor(e){return!("word"!==e.type||!e.value.startsWith("#"))||(!("word"!==e.type||!i.includes(e.value.toLowerCase()))||!("function"!==e.type||!c.includes(e.value.toLowerCase())))}creator.postcss=!0;const r=["unset","inherit","initial","revert","revert-layer"],n=["underline","overline","line-through","blink","spelling-error","grammar-error"],s=["solid","double","dotted","dashed","wavy"],l=["auto","from-font"],c=["color","color-mix","hsl","hsla","hwb","lab","lch","oklab","oklch","rgb","rgba"],i=["currentcolor","transparent",...Object.keys(t)];function genericNodeParts(){return{before:"",after:"",sourceIndex:0,sourceEndIndex:0}}export{creator as default};
