"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-tokenizer"),o=require("@csstools/css-color-parser"),r=require("@csstools/utilities"),t=require("@csstools/css-parser-algorithms");const a=/\bcolor\(/i,n=/^color$/i,basePlugin=s=>({postcssPlugin:"postcss-color-function",Declaration(i){const c=i.value;if(!a.test(c))return;if(r.hasFallback(i))return;if(r.hasSupportsAtRuleAncestor(i,a))return;const l=e.tokenize({css:c}),u=t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(l),s=>{if(!t.isFunctionNode(s)||!n.test(s.getName()))return;const e=o.color(s);return e&&!(e.syntaxFlags.has(o.SyntaxFlag.Experimental)||e.syntaxFlags.has(o.SyntaxFlag.HasNoneKeywords)||e.syntaxFlags.has(o.SyntaxFlag.RelativeColorSyntax))?o.serializeRGB(e):void 0}),p=t.stringify(u);p!==c&&(i.cloneBefore({value:p}),s?.preserve||i.remove())}});basePlugin.postcss=!0;const postcssPlugin=e=>{const o=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return o.enableProgressiveCustomProperties&&o.preserve?{postcssPlugin:"postcss-color-function",plugins:[s(),basePlugin(o)]}:basePlugin(o)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
