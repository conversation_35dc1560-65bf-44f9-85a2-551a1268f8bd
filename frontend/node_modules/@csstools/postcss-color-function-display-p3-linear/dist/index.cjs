"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("@csstools/css-tokenizer"),o=require("@csstools/css-color-parser"),r=require("@csstools/utilities"),t=require("@csstools/css-parser-algorithms");const i=/\bdisplay-p3-linear\b/i,a=/^color$/i,basePlugin=s=>({postcssPlugin:"postcss-color-function-display-p3-linear",Declaration(n){const l=n.value;if(!i.test(l))return;if(r.hasFallback(n))return;if(r.hasSupportsAtRuleAncestor(n,i))return;const c=e.tokenize({css:l}),p=t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(c),s=>{if(!t.isFunctionNode(s)||!a.test(s.getName()))return;const e=o.color(s);return!e||e.colorNotation!==o.ColorNotation.Linear_Display_P3||e.syntaxFlags.has(o.SyntaxFlag.Experimental)||e.syntaxFlags.has(o.SyntaxFlag.HasNoneKeywords)?void 0:o.serializeP3(e)}),u=t.stringify(p);u!==l&&(n.cloneBefore({value:u}),s?.preserve||n.remove())}});basePlugin.postcss=!0;const postcssPlugin=e=>{const o=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return o.enableProgressiveCustomProperties&&o.preserve?{postcssPlugin:"postcss-color-function-display-p3-linear",plugins:[s(),basePlugin(o)]}:basePlugin(o)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
