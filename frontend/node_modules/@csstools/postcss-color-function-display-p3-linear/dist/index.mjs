import s from"@csstools/postcss-progressive-custom-properties";import{tokenize as o}from"@csstools/css-tokenizer";import{color as r,ColorNotation as e,SyntaxFlag as t,serializeP3 as i}from"@csstools/css-color-parser";import{hasFallback as n,hasSupportsAtRuleAncestor as c}from"@csstools/utilities";import{replaceComponentValues as l,parseCommaSeparatedListOfComponentValues as p,isFunctionNode as a,stringify as u}from"@csstools/css-parser-algorithms";const m=/\bdisplay-p3-linear\b/i,f=/^color$/i,basePlugin=s=>({postcssPlugin:"postcss-color-function-display-p3-linear",Declaration(g){const v=g.value;if(!m.test(v))return;if(n(g))return;if(c(g,m))return;const y=o({css:v}),P=l(p(y),s=>{if(!a(s)||!f.test(s.getName()))return;const o=r(s);return!o||o.colorNotation!==e.Linear_Display_P3||o.syntaxFlags.has(t.Experimental)||o.syntaxFlags.has(t.HasNoneKeywords)?void 0:i(o)}),d=u(P);d!==v&&(g.cloneBefore({value:d}),s?.preserve||g.remove())}});basePlugin.postcss=!0;const postcssPlugin=o=>{const r=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},o);return r.enableProgressiveCustomProperties&&r.preserve?{postcssPlugin:"postcss-color-function-display-p3-linear",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
