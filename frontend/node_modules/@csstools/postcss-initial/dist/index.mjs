const o=new Map([["animation","none 0s ease 0s 1 normal none running"],["animation-delay","0s"],["animation-direction","normal"],["animation-duration","0s"],["animation-fill-mode","none"],["animation-iteration-count","1"],["animation-name","none"],["animation-play-state","running"],["animation-timing-function","ease"],["backface-visibility","visible"],["background","transparent none repeat 0 0 / auto auto padding-box border-box scroll"],["background-attachment","scroll"],["background-clip","border-box"],["background-color","transparent"],["background-image","none"],["background-origin","padding-box"],["background-position","0 0"],["background-position-x","0"],["background-position-y","0"],["background-repeat","repeat"],["background-size","auto auto"],["border","medium none currentcolor"],["border-style","none"],["border-width","medium"],["border-color","currentcolor"],["border-bottom","0"],["border-bottom-color","currentcolor"],["border-bottom-left-radius","0"],["border-bottom-right-radius","0"],["border-bottom-style","none"],["border-bottom-width","medium"],["border-collapse","separate"],["border-image","none"],["border-left","0"],["border-left-color","currentcolor"],["border-left-style","none"],["border-left-width","medium"],["border-radius","0"],["border-right","0"],["border-right-color","currentcolor"],["border-right-style","none"],["border-right-width","medium"],["border-spacing","0"],["border-top","0"],["border-top-color","currentcolor"],["border-top-left-radius","0"],["border-top-right-radius","0"],["border-top-style","none"],["border-top-width","medium"],["bottom","auto"],["box-shadow","none"],["box-sizing","content-box"],["caption-side","top"],["clear","none"],["clip","auto"],["color","#000"],["columns","auto"],["column-count","auto"],["column-fill","balance"],["column-gap","normal"],["column-rule","medium none currentcolor"],["column-rule-color","currentcolor"],["column-rule-style","none"],["column-rule-width","medium"],["column-span","1"],["column-width","auto"],["content","normal"],["counter-increment","none"],["counter-reset","none"],["cursor","auto"],["direction","ltr"],["display","inline"],["empty-cells","show"],["float","none"],["font-family","serif"],["font-size","medium"],["font-style","normal"],["font-variant","normal"],["font-weight","normal"],["font-stretch","normal"],["line-height","normal"],["height","auto"],["hyphens","none"],["left","auto"],["letter-spacing","normal"],["list-style","disc outside none"],["list-style-image","none"],["list-style-position","outside"],["list-style-type","disc"],["margin","0"],["margin-bottom","0"],["margin-left","0"],["margin-right","0"],["margin-top","0"],["max-height","none"],["max-width","none"],["min-height","0"],["min-width","0"],["opacity","1"],["orphans","2"],["outline","medium none currentcolor"],["outline-color","currentcolor"],["outline-style","none"],["outline-width","medium"],["overflow","visible"],["overflow-x","visible"],["overflow-y","visible"],["padding","0"],["padding-bottom","0"],["padding-left","0"],["padding-right","0"],["padding-top","0"],["page-break-after","auto"],["page-break-before","auto"],["page-break-inside","auto"],["perspective","none"],["perspective-origin","50% 50%"],["position","static"],["quotes",'"“" "”" "‘" "’"'],["right","auto"],["tab-size","8"],["table-layout","auto"],["text-align","left"],["text-align-last","auto"],["text-decoration","none"],["text-decoration-color","currentcolor"],["text-decoration-line","none"],["text-decoration-style","solid"],["text-indent","0"],["text-shadow","none"],["text-transform","none"],["top","auto"],["transform","none"],["transform-origin","50% 50% 0"],["transform-style","flat"],["transition","none 0s ease 0s"],["transition-delay","0s"],["transition-duration","0s"],["transition-property","none"],["transition-timing-function","ease"],["unicode-bidi","normal"],["vertical-align","baseline"],["visibility","visible"],["white-space","normal"],["widows","2"],["width","auto"],["word-spacing","normal"],["z-index","auto"]]),t=["animation","backface-visibility","background","border","border-collapse","border-image","border-radius","border-spacing","bottom","box-shadow","box-sizing","caption-side","clear","clip","color","columns","column-count","column-fill","column-gap","column-rule","column-span","column-width","content","counter-increment","counter-reset","cursor","display","empty-cells","float","font-family","font-size","font-style","font-variant","font-weight","font-stretch","line-height","font","height","hyphens","left","letter-spacing","list-style","margin","max-height","max-width","min-height","min-width","opacity","orphans","outline","overflow","overflow-x","overflow-y","padding","page-break-after","page-break-before","page-break-inside","perspective","perspective-origin","position","right","tab-size","table-layout","text-align","text-align-last","text-decoration","text-indent","text-shadow","text-transform","top","transform","transform-origin","transform-style","transition","vertical-align","visibility","white-space","widows","width","word-spacing","z-index"];function hasExactFallback(o,t){const e=o.parent;if(!e)return!1;const n=o.prop.toLowerCase();for(let r=e.index(o)-1;r>=0;r--){const o=e.nodes[r];if("decl"===o.type&&o.prop.toLowerCase()===n)return o.value===t}return!1}const e=/^\s?initial\s?$/i,n=/^font$/i,r=/^all$/i,i=["font-family","font-size","font-style","font-variant","font-weight","font-stretch","line-height"],creator=a=>{const l=Object.assign({preserve:!0},a);return{postcssPlugin:"postcss-initial",Declaration(a){if(a.variable)return;if(!e.test(a.value))return;let s;s=n.test(a.prop)?i:r.test(a.prop)?t:[a.prop.toLowerCase()];let c=!1;s.forEach((t=>{const e=o.get(t);e&&(hasExactFallback(a,e)||(c=!0,a.cloneBefore({prop:t,value:e})))})),!l.preserve&&c&&a.remove()}}};creator.postcss=!0;export{creator as default};
