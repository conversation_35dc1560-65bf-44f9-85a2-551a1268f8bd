import{calcFromComponentValues as e}from"@csstools/css-calc";import{tokenize as s,TokenType as o,NumberType as n}from"@csstools/css-tokenizer";import{replaceComponentValues as t,parseCommaSeparatedListOfComponentValues as a,stringify as r,isFunctionNode as i,FunctionNode as c,SimpleBlockNode as l,TokenNode as p,WhitespaceNode as v,parseListOfComponentValues as u}from"@csstools/css-parser-algorithms";const m=/(?<![-\w])(?:sign|abs)\(/i,f=/(?<![-\w])(?:sign|abs)\(/i,creator=o=>{const n=Object.assign({preserve:!1},o);return{postcssPlugin:"postcss-sign-functions",Declaration(o){if(!m.test(o.value))return;let i;i=f.test(o.value)?t(a(s({css:o.value})),replacer):a(s({css:o.value}));const c=r(e(i,{precision:5,toCanonicalUnits:!0}));c!==o.value&&(o.cloneBefore({value:c}),n.preserve||o.remove())}}};function replacer(e){if(!i(e))return;if("abs"!==e.getName().toLowerCase())return;const[s]=t([e.value],replacer);return[new c([o.Function,"max(",-1,-1,{value:"max"}],[o.CloseParen,")",-1,-1,void 0],[new l([o.OpenParen,"(",-1,-1,void 0],[o.CloseParen,")",-1,-1,void 0],u(s.flatMap((e=>e.tokens())))),new p([o.Comma,",",-1,-1,void 0]),new v([[o.Whitespace," ",-1,-1,void 0]]),new p([o.Number,"-1",-1,-1,{value:-1,type:n.Integer,signCharacter:"-"}]),new v([[o.Whitespace," ",-1,-1,void 0]]),new p([o.Delim,"*",-1,-1,{value:"*"}]),new v([[o.Whitespace," ",-1,-1,void 0]]),new l([o.OpenParen,"(",-1,-1,void 0],[o.CloseParen,")",-1,-1,void 0],u(s.flatMap((e=>e.tokens()))))])]}creator.postcss=!0;export{creator as default};
