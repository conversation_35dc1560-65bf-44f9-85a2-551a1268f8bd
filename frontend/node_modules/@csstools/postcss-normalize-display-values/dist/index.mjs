import e from"postcss-value-parser";var l=new Map([["block,flex","flex"],["block,flow","block"],["block,flow-root","flow-root"],["block,grid","grid"],["block,table","table"],["inline,flex","inline-flex"],["inline,flow","inline"],["inline,flow,list-item","inline list-item"],["inline,flow-root","inline-block"],["inline,grid","inline-grid"],["inline,ruby","ruby"],["inline,table","inline-table"],["list-item,block,flow","list-item"],["ruby-base,flow","ruby-base"],["ruby-text,flow","ruby-text"],["run-in,flow","run-in"],["table-caption,flow","table-caption"],["table-cell,flow","table-cell"]]);function transform(n){if(!n.trim())return n;const{nodes:t}=e(n);if(t.length<=1)return n;const i=t.filter((e=>"word"===e.type)).map((e=>e.value.toLowerCase()));if(i.length<=1)return n;const o=l.get(i.join(","));return o||n}const n=/^display$/i,creator=e=>{const l=!("preserve"in Object(e))||Boolean(e?.preserve);return{postcssPlugin:"postcss-normalize-display-values",Declaration(e){if(!n.test(e.prop))return;const t=e.value;if(!t)return;const i=transform(t);e.value!==i&&(e.cloneBefore({value:i}),l||e.remove())}}};creator.postcss=!0;export{creator as default};
