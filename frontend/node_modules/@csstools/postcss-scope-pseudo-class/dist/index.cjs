"use strict";var e=require("postcss-selector-parser");const creator=s=>{const r=Object.assign({preserve:!1},s);return{postcssPlugin:"postcss-scope-pseudo-class",prepare(){const s=new WeakSet;return{postcssPlugin:"postcss-scope-pseudo-class",Rule(t,{result:o}){if(!t.selector.toLowerCase().includes(":scope"))return;if(s.has(t))return;{let e=t.parent;for(;e;){if("atrule"===e.type&&"scope"===e.name.toLowerCase())return;e=e.parent}}let c=t.selector;try{const s=e().astSync(c);if(!s)return;s.walkPseudos((e=>{if(":has"===e.value.toLowerCase())return!1;":scope"===e.value.toLowerCase()&&(e.value=":root")})),c=s.toString()}catch(e){t.warn(o,`Failed to parse selector : "${t.selector}" with message: "${e instanceof Error?e.message:e}"`)}c!==t.selector&&(s.add(t),t.cloneBefore({selector:c}),r.preserve||t.remove())}}}}};creator.postcss=!0,module.exports=creator;
