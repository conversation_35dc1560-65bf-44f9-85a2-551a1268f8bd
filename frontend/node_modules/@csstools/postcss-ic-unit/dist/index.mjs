import s from"@csstools/postcss-progressive-custom-properties";import e from"postcss-value-parser";import{hasFallback as t,hasSupportsAtRuleAncestor as r}from"@csstools/utilities";const o=/ic\b/i,i=/\(font-size: \d+ic\)/i,basePlugin=s=>({postcssPlugin:"postcss-ic-unit",Declaration(n){if(!o.test(n.value))return;if(t(n))return;if(r(n,i))return;const c=e(n.value);c.walk(s=>{if(!s.type||"word"!==s.type)return;const t=e.unit(s.value);t&&"ic"===t.unit.toLowerCase()&&(s.value=`${t.number}em`)});const u=String(c);u!==n.value&&(n.cloneBefore({value:u}),s?.preserve||n.remove())}});basePlugin.postcss=!0;const postcssPlugin=e=>{const t=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return t.enableProgressiveCustomProperties&&t.preserve?{postcssPlugin:"postcss-ic-unit",plugins:[s(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0;export{postcssPlugin as default};
