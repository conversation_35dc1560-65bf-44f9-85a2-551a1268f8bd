import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
// ... existing imports ...
import  HomePage  from './components/Home/HomePage';
import { VideoPreview } from './components/VideoPreview/VideoPreview';
import { CheckoutPage } from './components/Checkout/CheckoutPage';
import   VideoCreationPage   from './components/VideoCreation/CreateVideoPage';
import  ManualVideoCreationPage  from './components/ManualVideoCreation/ManualVideoCreationPage'; // Import the manual creation page component
import SamplesPage from './components/Samples/SamplesPage';
import GyroscopePage from './components/VideoCreation/GyroscopePage';


function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<GyroscopePage />} />
          <Route path="/home" element={<HomePage />} />
          <Route path="/gyroscope" element={<GyroscopePage />} />
          <Route path="/create-video" element={<VideoCreationPage />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          <Route path="/preview/:videoId" element={<VideoPreview />} />
          <Route path="/manual-create-video" element={<ManualVideoCreationPage />} />
          <Route path="/samples" element={<SamplesPage />} />
        </Routes>
      </BrowserRouter>
    </Provider>
  );
}

export default App; 