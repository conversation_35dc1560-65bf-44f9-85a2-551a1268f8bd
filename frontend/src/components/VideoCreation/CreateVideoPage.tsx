import React, { useState, useEffect } from 'react';
import './CreateVideoPage.css';
import GyroscopeButton from '../Xtra/GyroscopeButton';

const CreateVideoPage = () => {
  const [formVisible, setFormVisible] = useState(false);
  const [currentField, setCurrentField] = useState(0);
  const [lessonTitle, setLessonTitle] = useState('');
  const [childFieldsVisible, setChildFieldsVisible] = useState(false);
  const [formData, setFormData] = useState({
    lessonTitle: '',
    studentName: '',
    studentAge: '',
    teacherVoice: '',
    studentVoice: ''
  });

  useEffect(() => {
    // Delayed entrance for dramatic effect
    setTimeout(() => {
      setFormVisible(true);
      setCurrentField(1); // Show lesson title
    }, 800);
  }, []);

  useEffect(() => {
    if (lessonTitle.length >= 3) {
      setChildFieldsVisible(true);
    } else {
      setChildFieldsVisible(false);
      setCurrentField(1); // Reset to show only first
    }
  }, [lessonTitle]);

  useEffect(() => {
    if (formVisible && childFieldsVisible) {
      const timer = setInterval(() => {
        setCurrentField(prev => prev < formFields.length ? prev + 1 : prev);
      }, 600);

      return () => clearInterval(timer);
    }
  }, [formVisible, childFieldsVisible]);

  const formFields = [
    { label: "lesson title", type: "text", placeholder: "enter your video lesson title...", key: "lessonTitle" },
    { label: "student's first name", type: "text", placeholder: "All Of This Is Optional!!", key: "studentName" },
    { label: "student's age", type: "select", options: ["6", "7", "8", "9"], key: "studentAge" },
    { label: "teacher's voice", type: "select", options: ["male", "female", "clone your own"], key: "teacherVoice" },
    { label: "student's voice", type: "select", options: ["male", "female", "clone your own"], key: "studentVoice" }
  ];

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
    if (key === 'lessonTitle') {
      setLessonTitle(value);
    }
  };

  const handleGenerate = () => {
    console.log('Generating video with data:', formData);
    // Add your video generation logic here
    // You could navigate to a different page, show loading state, etc.
  };

  const isFormValid = () => {
    return formData.lessonTitle.trim().length >= 3;
  };

  return (
    <div className="westworld-container">
      {/* Floating particles for depth */}
      <div className="particles-container">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="floating-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Milk-like surface waves */}
      <div className="milk-waves">
        <svg width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none">
          <defs>
            <linearGradient id="milkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ffffff" stopOpacity="0.8" />
              <stop offset="50%" stopColor="#f8fafc" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#f1f5f9" stopOpacity="0.4" />
            </linearGradient>
          </defs>
          <path
            d="M0,500 Q250,300 500,500 T1000,400 L1000,1000 L0,1000 Z"
            fill="url(#milkGradient)"
            className="milk-surface"
          />
        </svg>
      </div>

      {/* Main content */}
      <div className="content-wrapper">
        <div className={`form-container ${formVisible ? 'visible' : 'hidden'}`}>
          {/* Title */}
          <div className="title-section">
            <h1 className="main-title">CREATE</h1>
            <div className="title-line"></div>
          </div>

          {/* Form */}
          <div className="form-fields">
            <>
              {formFields.map((field, index) => {
                // Special handling for student's first name and age on same line
                if (index === 1) {
                  return (
                    <div
                      key={`combined-${index}`}
                      className={`field-wrapper combined-fields ${
                        childFieldsVisible
                          ? currentField > index ? 'emerged' : 'emerging'
                          : 'hidden'
                      }`}
                      style={{ transitionDelay: `${index * 200}ms` }}
                    >
                      {/* First Name Field */}
                      <div className="inline-field">
                        <label className="field-label">{field.label}</label>
                        <input
                          type="text"
                          placeholder={field.placeholder}
                          className="field-input"
                          value={formData.studentName}
                          onChange={(e) => handleInputChange('studentName', e.target.value)}
                        />
                      </div>

                      {/* Age Field */}
                      <div className="inline-field">
                        <label className="field-label">{formFields[2].label}</label>
                        <select 
                          className="field-select"
                          value={formData.studentAge}
                          onChange={(e) => handleInputChange('studentAge', e.target.value)}
                        >
                          <option value="">Select {formFields[2].label.toLowerCase()}...</option>
                          {formFields[2].options?.map((option, optIndex) => (
                            <option key={optIndex} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  );
                }

                // Skip the age field since it's included in the combined container
                if (index === 2) return null;

                // Special handling for teacher's voice and student's voice on same line
                if (index === 3) {
                  return (
                    <div
                      key={`combined-voices-${index}`}
                      className={`field-wrapper combined-fields ${
                        childFieldsVisible
                          ? currentField > index ? 'emerged' : 'emerging'
                          : 'hidden'
                      }`}
                      style={{ transitionDelay: `${index * 200}ms` }}
                    >
                      {/* Teacher's Voice Field */}
                      <div className="inline-field">
                        <label className="field-label">{field.label}</label>
                        <select 
                          className="field-select"
                          value={formData.teacherVoice}
                          onChange={(e) => handleInputChange('teacherVoice', e.target.value)}
                        >
                          <option value="">Select {field.label.toLowerCase()}...</option>
                          {field.options?.map((option, optIndex) => (
                            <option key={optIndex} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>

                      {/* Student's Voice Field */}
                      <div className="inline-field">
                        <label className="field-label">{formFields[4].label}</label>
                        <select 
                          className="field-select"
                          value={formData.studentVoice}
                          onChange={(e) => handleInputChange('studentVoice', e.target.value)}
                        >
                          <option value="">Select {formFields[4].label.toLowerCase()}...</option>
                          {formFields[4].options?.map((option, optIndex) => (
                            <option key={optIndex} value={option}>{option}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  );
                }

                // Skip the student's voice field since it's included in the combined container
                if (index === 4) return null;

                // Regular field rendering
                return (
                  <div
                    key={index}
                    className={`field-wrapper ${
                      index === 0
                        ? 'emerged'
                        : childFieldsVisible
                        ? currentField > index ? 'emerged' : 'emerging'
                        : 'hidden'
                    }`}
                    style={{ transitionDelay: `${index * 200}ms` }}
                  >
                    <label className="field-label">{field.label}</label>
                    {field.type === 'text' && (
                      <input
                        type="text"
                        placeholder={field.placeholder}
                        className="field-input"
                        value={formData[field.key as keyof typeof formData]}
                        onChange={(e) => handleInputChange(field.key, e.target.value)}
                      />
                    )}
                    {field.type === 'select' && (
                      <select 
                        className="field-select"
                        value={formData[field.key as keyof typeof formData]}
                        onChange={(e) => handleInputChange(field.key, e.target.value)}
                      >
                        <option value="">Select {field.label.toLowerCase()}...</option>
                        {field.options?.map((option, optIndex) => (
                          <option key={optIndex} value={option}>{option}</option>
                        ))}
                      </select>
                    )}
                    {field.type === 'textarea' && (
                      <textarea
                        placeholder={field.placeholder}
                        rows={4}
                        className="field-textarea"
                        value={formData[field.key as keyof typeof formData]}
                        onChange={(e) => handleInputChange(field.key, e.target.value)}
                      />
                    )}
                  </div>
                );
              })}
            </>

            {/* Gyroscope Generate Button */}
            <div className={`gyroscope-wrapper ${
              childFieldsVisible && currentField > formFields.length - 1 ? 'emerged' : 'hidden'
            }`}>
              <GyroscopeButton
                onClick={handleGenerate}
                disabled={!isFormValid()}
                size={140}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Grid overlay and ambient light */}
      <div className="grid-overlay">
        <div className="grid-pattern">
          {[...Array(400)].map((_, i) => <div key={i} className="grid-cell"></div>)}
        </div>
      </div>
      <div className="ambient-light"></div>
    </div>
  );
};

export default CreateVideoPage;