/* Gyroscope.css - Complete styles for the gyroscope component */

/* Base container styles */
.gyroscope-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  font-family: system-ui, -apple-system, sans-serif;
}

/* Ambient background effects */
.ambient-effects {
  position: absolute;
  inset: 0;
  opacity: 0.2;
  pointer-events: none;
}

.ambient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(3rem);
  animation: ambient-pulse 4s ease-in-out infinite alternate;
}

.ambient-orb:nth-child(1) { background: radial-gradient(circle, #3b82f644, transparent); }
.ambient-orb:nth-child(2) { background: radial-gradient(circle, #8b5cf644, transparent); }
.ambient-orb:nth-child(3) { background: radial-gradient(circle, #ec489944, transparent); }
.ambient-orb:nth-child(4) { background: radial-gradient(circle, #f59e0b44, transparent); }
.ambient-orb:nth-child(5) { background: radial-gradient(circle, #10b98144, transparent); }
.ambient-orb:nth-child(6) { background: radial-gradient(circle, #ef444444, transparent); }

@keyframes ambient-pulse {
  0% { transform: scale(0.8) translate(10px, 10px); opacity: 0.1; }
  100% { transform: scale(1.2) translate(-10px, -10px); opacity: 0.3; }
}

/* Title styles */
.gyroscope-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 2rem;
  text-align: center;
  background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: title-pulse 2s ease-in-out infinite alternate;
  position: relative;
  z-index: 10;
}

@keyframes title-pulse {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* Main gyroscope container */
.gyroscope-main {
  position: relative;
  width: 24rem;
  height: 24rem;
  margin: 0 auto;
  cursor: grab;
  perspective: 1200px;
  transform-style: preserve-3d;
}

.gyroscope-main:active {
  cursor: grabbing;
}

/* Particle system */
.particles-container {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform-origin: center center;
  transform-style: preserve-3d;
}

.particle:nth-child(5n+1) { 
  background: radial-gradient(circle, #3b82f6, transparent);
  box-shadow: 0 0 10px #3b82f6;
}
.particle:nth-child(5n+2) { 
  background: radial-gradient(circle, #8b5cf6, transparent);
  box-shadow: 0 0 10px #8b5cf6;
}
.particle:nth-child(5n+3) { 
  background: radial-gradient(circle, #ec4899, transparent);
  box-shadow: 0 0 10px #ec4899;
}
.particle:nth-child(5n+4) { 
  background: radial-gradient(circle, #f59e0b, transparent);
  box-shadow: 0 0 10px #f59e0b;
}
.particle:nth-child(5n+5) { 
  background: radial-gradient(circle, #10b981, transparent);
  box-shadow: 0 0 10px #10b981;
}

/* Gyroscope rings */
.outer-ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 4px solid #3b82f6;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 30px #3b82f6,
    inset 0 0 30px #3b82f6aa,
    0 0 60px #3b82f6aa;
}

.middle-ring {
  position: absolute;
  inset: 2rem;
  border-radius: 50%;
  border: 4px solid #8b5cf6;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 40px #8b5cf6,
    inset 0 0 40px #8b5cf6aa;
}

.inner-ring {
  position: absolute;
  inset: 2rem;
  border-radius: 50%;
  border: 4px solid #ec4899;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 50px #ec4899,
    inset 0 0 50px #ec4899aa;
}

.gyroscope-core {
  position: absolute;
  inset: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff, #fef3c7, #fed7aa);
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 60px #ffffff,
    inset 0 0 20px #f59e0b88;
}

.core-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1.5rem;
  height: 1.5rem;
  margin-top: -0.75rem;
  margin-left: -0.75rem;
  border-radius: 50%;
  background: linear-gradient(45deg, #ef4444, #eab308);
}

/* Rotation axes */
.rotation-axes {
  position: absolute;
  inset: -4rem;
  pointer-events: none;
  opacity: 0.6;
}

.axis-horizontal {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ef4444, transparent);
  box-shadow: 0 0 10px #ef4444;
}

.axis-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background: linear-gradient(180deg, transparent, #22c55e, transparent);
  box-shadow: 0 0 10px #22c55e;
}

.axis-diagonal {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8rem;
  height: 2px;
  margin-left: -4rem;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  box-shadow: 0 0 10px #3b82f6;
}

/* Info text */
.gyroscope-info {
  margin-top: 1.5rem;
  text-align: center;
  position: relative;
  z-index: 10;
}

.info-line {
  font-size: 0.875rem;
  margin: 0.5rem 0;
  color: #d1d5db;
}

.info-primary { color: #60a5fa; }
.info-secondary { 
  color: #a78bfa; 
  font-size: 0.75rem;
}
.info-tertiary { 
  color: #f472b6; 
  font-size: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gyroscope-title {
    font-size: 2rem;
  }
  
  .gyroscope-main {
    width: 20rem;
    height: 20rem;
  }
  
  .rotation-axes {
    inset: -2rem;
  }
  
  .axis-diagonal {
    width: 6rem;
    margin-left: -3rem;
  }
}

/* Ensure proper 3D context */
.gyroscope-container * {
  box-sizing: border-box;
}

/* Performance optimizations */
.gyroscope-main,
.outer-ring,
.middle-ring,
.inner-ring,
.gyroscope-core,
.rotation-axes {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}