/* GyroscopeButton.css - Compact gyroscope for generate button */

.gyroscope-button-wrapper {
  position: relative;
  display: inline-block;
  margin: 2rem auto;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.gyroscope-button-wrapper:hover:not(.disabled) {
  transform: scale(1.05);
}

.gyroscope-button-wrapper.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Main button container */
.gyroscope-button {
  position: relative;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.gyroscope-button:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.gyroscope-button:active {
  transform: scale(0.98);
}

/* Particle system for button */
.button-particles-container {
  position: absolute;
  inset: 0;
  pointer-events: none;
  border-radius: 50%;
  overflow: hidden;
}

.button-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform-origin: center center;
  transform-style: preserve-3d;
}

.button-particle:nth-child(4n+1) { 
  background: radial-gradient(circle, #3b82f6, transparent);
  box-shadow: 0 0 4px #3b82f6;
}
.button-particle:nth-child(4n+2) { 
  background: radial-gradient(circle, #8b5cf6, transparent);
  box-shadow: 0 0 4px #8b5cf6;
}
.button-particle:nth-child(4n+3) { 
  background: radial-gradient(circle, #ec4899, transparent);
  box-shadow: 0 0 4px #ec4899;
}
.button-particle:nth-child(4n+4) { 
  background: radial-gradient(circle, #f59e0b, transparent);
  box-shadow: 0 0 4px #f59e0b;
}

/* Button gyroscope rings - scaled down */
.button-outer-ring {
  position: absolute;
  inset: 8px;
  border-radius: 50%;
  border: 2px solid #3b82f6;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 15px rgba(59, 130, 246, 0.6),
    inset 0 0 15px rgba(59, 130, 246, 0.3);
}

.button-middle-ring {
  position: absolute;
  inset: 8px;
  border-radius: 50%;
  border: 2px solid #8b5cf6;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 12px rgba(139, 92, 246, 0.6),
    inset 0 0 12px rgba(139, 92, 246, 0.3);
}

.button-inner-ring {
  position: absolute;
  inset: 8px;
  border-radius: 50%;
  border: 2px solid #ec4899;
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 10px rgba(236, 72, 153, 0.6),
    inset 0 0 10px rgba(236, 72, 153, 0.3);
}

.button-gyroscope-core {
  position: absolute;
  inset: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff, #fef3c7, #fed7aa);
  transform-style: preserve-3d;
  transform-origin: center center;
  box-shadow: 
    0 0 20px rgba(255, 255, 255, 0.8),
    inset 0 0 8px rgba(245, 158, 11, 0.4);
}

.button-core-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  margin-top: -3px;
  margin-left: -3px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ef4444, #eab308);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.8);
}

/* Generate label */
.generate-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 11px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.15em;
  text-transform: uppercase;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.gyroscope-button-wrapper:hover .generate-label {
  color: #1f2937;
}

.gyroscope-button-wrapper.disabled .generate-label {
  color: #9ca3af;
}

/* Performance optimizations */
.gyroscope-button,
.button-outer-ring,
.button-middle-ring,
.button-inner-ring,
.button-gyroscope-core {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Responsive sizing */
@media (max-width: 768px) {
  .gyroscope-button-wrapper {
    margin: 1.5rem auto;
  }
  
  .generate-label {
    font-size: 10px;
    bottom: -25px;
  }
}

@media (max-width: 480px) {
  .gyroscope-button-wrapper {
    margin: 1rem auto;
  }
  
  .generate-label {
    font-size: 9px;
    bottom: -22px;
  }
}