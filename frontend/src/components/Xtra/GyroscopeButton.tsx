import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import './GyroscopeButton.css';

interface GyroscopeButtonProps {
  onClick?: () => void;
  disabled?: boolean;
  size?: number;
}

const GyroscopeButton = ({ onClick, disabled = false, size = 120 }: GyroscopeButtonProps) => {
  const gyroscopeRef = useRef<HTMLDivElement>(null);
  const outerRingRef = useRef<HTMLDivElement>(null);
  const middleRingRef = useRef<HTMLDivElement>(null);
  const innerRingRef = useRef<HTMLDivElement>(null);
  const coreRef = useRef<HTMLDivElement>(null);
  const particleContainerRef = useRef<HTMLDivElement>(null);
  
  const [isHovered, setIsHovered] = useState(false);
  const animationsRef = useRef<gsap.core.Tween[]>([]);

  // Initialize GSAP animations
  useEffect(() => {
    // Clear any existing animations
    animationsRef.current.forEach(tween => tween.kill());
    animationsRef.current = [];

    // Initialize all elements with proper 3D setup
    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];
    
    gsap.set(elements, {
      transformOrigin: "center center",
      transformStyle: "preserve-3d",
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0
    });

    // Continuous base rotations - slower for button use
    const outerAnim = gsap.to(outerRingRef.current, {
      duration: 30,
      rotationY: 360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    const middleAnim = gsap.to(middleRingRef.current, {
      duration: 25,
      rotationX: -360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    const innerAnim = gsap.to(innerRingRef.current, {
      duration: 20,
      rotationZ: 360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    // Subtle core pulsing
    const coreAnim = gsap.to(coreRef.current, {
      duration: 4,
      scale: 1.1,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      transformOrigin: "center center"
    });

    // Store animation references
    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim];

    // Initialize particles with GSAP
    const particles = particleContainerRef.current?.children;
    if (particles) {
      Array.from(particles).forEach((particle, index) => {
        const element = particle as HTMLElement;
        
        // Set initial state - smaller orbits for button
        gsap.set(element, {
          x: (Math.random() - 0.5) * 60,
          y: (Math.random() - 0.5) * 60,
          scale: Math.random() * 0.3 + 0.4,
          opacity: Math.random() * 0.4 + 0.3
        });
        
        // Gentle floating animation
        const particleAnim = gsap.to(element, {
          duration: 3 + Math.random() * 4,
          x: (Math.random() - 0.5) * 80,
          y: (Math.random() - 0.5) * 80,
          rotation: Math.random() * 180,
          scale: Math.random() * 0.5 + 0.5,
          ease: "sine.inOut",
          repeat: -1,
          yoyo: true,
          delay: Math.random() * 2
        });
        
        animationsRef.current.push(particleAnim);
      });
    }

    // Cleanup function
    return () => {
      animationsRef.current.forEach(tween => tween.kill());
      animationsRef.current = [];
    };
  }, []);

  // Handle hover effects
  useEffect(() => {
    if (isHovered && !disabled) {
      // Speed up rotations on hover
      gsap.to(outerRingRef.current, { duration: 15, rotationY: "+=360", ease: "none" });
      gsap.to(middleRingRef.current, { duration: 12, rotationX: "-=360", ease: "none" });
      gsap.to(innerRingRef.current, { duration: 10, rotationZ: "+=360", ease: "none" });
      
      // Brighten particles
      const particles = particleContainerRef.current?.children;
      if (particles) {
        gsap.to(Array.from(particles), {
          opacity: 0.8,
          scale: "+=0.2",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    }
  }, [isHovered, disabled]);

  const handleClick = () => {
    if (disabled || !onClick) return;
    
    // Click animation
    gsap.to(gyroscopeRef.current, {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: "power2.inOut",
      onComplete: onClick
    });
  };

  return (
    <div 
      className={`gyroscope-button-wrapper ${disabled ? 'disabled' : ''}`}
      style={{ width: size, height: size }}
    >
      <div
        ref={gyroscopeRef}
        className="gyroscope-button"
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ 
          width: size,
          height: size,
          perspective: '400px'
        }}
      >
        {/* Particle System - fewer particles for button */}
        <div ref={particleContainerRef} className="button-particles-container">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="button-particle"
            />
          ))}
        </div>

        {/* Outer Ring */}
        <div ref={outerRingRef} className="button-outer-ring">
          {/* Middle Ring */}
          <div ref={middleRingRef} className="button-middle-ring">
            {/* Inner Ring */}
            <div ref={innerRingRef} className="button-inner-ring">
              {/* Core */}
              <div ref={coreRef} className="button-gyroscope-core">
                <div className="button-core-center" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Generate label */}
      <div className="generate-label">GENERATE</div>
    </div>
  );
};

export default GyroscopeButton;