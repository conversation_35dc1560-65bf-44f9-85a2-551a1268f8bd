import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import './Gyroscope.css';

interface RotationState {
  x: number;
  y: number;
  z: number;
}

const GSAPGyroscope = () => {
  const gyroscopeRef = useRef<HTMLDivElement>(null);
  const outerRingRef = useRef<HTMLDivElement>(null);
  const middleRingRef = useRef<HTMLDivElement>(null);
  const innerRingRef = useRef<HTMLDivElement>(null);
  const coreRef = useRef<HTMLDivElement>(null);
  const particleContainerRef = useRef<HTMLDivElement>(null);
  const axesRef = useRef<HTMLDivElement>(null);
  
  const [isDragging, setIsDragging] = useState(false);
  const animationsRef = useRef<gsap.core.Tween[]>([]);
  const lastMousePos = useRef({ x: 0, y: 0 });

  // Initialize GSAP animations
  useEffect(() => {
    // Clear any existing animations
    animationsRef.current.forEach(tween => tween.kill());
    animationsRef.current = [];

    // Initialize all elements with proper 3D setup
    const elements = [outerRingRef.current, middleRingRef.current, innerRingRef.current, coreRef.current];
    
    gsap.set(elements, {
      transformOrigin: "center center",
      transformStyle: "preserve-3d",
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0
    });

    // Continuous base rotations - store references for cleanup
    const outerAnim = gsap.to(outerRingRef.current, {
      duration: 20,
      rotationY: 360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    const middleAnim = gsap.to(middleRingRef.current, {
      duration: 15,
      rotationX: -360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    const innerAnim = gsap.to(innerRingRef.current, {
      duration: 10,
      rotationZ: 360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    // Core pulsing animation
    const coreAnim = gsap.to(coreRef.current, {
      duration: 3,
      scale: 1.15,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      transformOrigin: "center center"
    });

    // Axes rotation
    const axesAnim = gsap.to(axesRef.current, {
      duration: 30,
      rotationZ: 360,
      ease: "none",
      repeat: -1,
      transformOrigin: "center center"
    });

    // Store animation references
    animationsRef.current = [outerAnim, middleAnim, innerAnim, coreAnim, axesAnim];

    // Initialize particles with GSAP
    const particles = particleContainerRef.current?.children;
    if (particles) {
      Array.from(particles).forEach((particle, index) => {
        const element = particle as HTMLElement;
        
        // Set initial state
        gsap.set(element, {
          x: (Math.random() - 0.5) * 300,
          y: (Math.random() - 0.5) * 300,
          scale: Math.random() * 0.5 + 0.5,
          opacity: Math.random() * 0.6 + 0.4
        });
        
        // Floating animation
        const particleAnim = gsap.to(element, {
          duration: 4 + Math.random() * 6,
          x: (Math.random() - 0.5) * 400,
          y: (Math.random() - 0.5) * 400,
          rotation: Math.random() * 360,
          scale: Math.random() * 0.8 + 0.6,
          ease: "sine.inOut",
          repeat: -1,
          yoyo: true,
          delay: Math.random() * 3
        });
        
        animationsRef.current.push(particleAnim);
      });
    }

    // Cleanup function
    return () => {
      animationsRef.current.forEach(tween => tween.kill());
      animationsRef.current = [];
    };
  }, []);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    lastMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !gyroscopeRef.current) return;
    
    const rect = gyroscopeRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;

    // Calculate rotation based on mouse position
    const rotationX = (mouseY / rect.height) * 180;
    const rotationY = (mouseX / rect.width) * 180;
    const rotationZ = ((mouseX + mouseY) / (rect.width + rect.height)) * 90;

    // Apply interactive rotations with GSAP - these will temporarily override base animations
    gsap.to(outerRingRef.current, {
      duration: 0.3,
      rotationX: rotationX,
      rotationY: rotationY,
      rotationZ: rotationZ,
      ease: "power2.out",
      overwrite: "auto" // This allows temporary override of base animation
    });

    gsap.to(middleRingRef.current, {
      duration: 0.4,
      rotationX: rotationX * 1.5,
      rotationY: rotationY * 0.7,
      rotationZ: rotationZ * -1,
      ease: "power2.out",
      overwrite: "auto"
    });

    gsap.to(innerRingRef.current, {
      duration: 0.5,
      rotationX: rotationX * 0.5,
      rotationY: rotationY * 1.8,
      rotationZ: rotationZ * 1.5,
      ease: "power2.out",
      overwrite: "auto"
    });

    lastMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    
    // Spring back to base rotations with elastic ease
    gsap.to(outerRingRef.current, {
      duration: 1.5,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      ease: "elastic.out(1, 0.3)",
      onComplete: () => {
        // Restart base rotation
        const newAnim = gsap.to(outerRingRef.current, {
          duration: 20,
          rotationY: 360,
          ease: "none",
          repeat: -1,
          transformOrigin: "center center"
        });
        animationsRef.current[0] = newAnim;
      }
    });

    gsap.to(middleRingRef.current, {
      duration: 1.5,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      ease: "elastic.out(1, 0.3)",
      onComplete: () => {
        const newAnim = gsap.to(middleRingRef.current, {
          duration: 15,
          rotationX: -360,
          ease: "none",
          repeat: -1,
          transformOrigin: "center center"
        });
        animationsRef.current[1] = newAnim;
      }
    });

    gsap.to(innerRingRef.current, {
      duration: 1.5,
      rotationX: 0,
      rotationY: 0,
      rotationZ: 0,
      ease: "elastic.out(1, 0.3)",
      onComplete: () => {
        const newAnim = gsap.to(innerRingRef.current, {
          duration: 10,
          rotationZ: 360,
          ease: "none",
          repeat: -1,
          transformOrigin: "center center"
        });
        animationsRef.current[2] = newAnim;
      }
    });
  };

  return (
    <div className="gyroscope-container">
      {/* Ambient Effects */}
      <div className="ambient-effects">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="ambient-orb"
            style={{
              width: `${150 + i * 50}px`,
              height: `${150 + i * 50}px`,
              left: `${15 + i * 12}%`,
              top: `${10 + i * 15}%`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${4 + i * 0.5}s`
            }}
          />
        ))}
      </div>

      <div style={{ position: 'relative', zIndex: 10 }}>
        <h1 className="gyroscope-title">
          GSAP Quantum Gyroscope
        </h1>

        <div
          ref={gyroscopeRef}
          className="gyroscope-main"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Enhanced Particle System */}
          <div ref={particleContainerRef} className="particles-container">
            {[...Array(25)].map((_, i) => (
              <div
                key={i}
                className="particle"
              />
            ))}
          </div>

          {/* Outer Ring */}
          <div ref={outerRingRef} className="outer-ring">
            {/* Middle Ring */}
            <div ref={middleRingRef} className="middle-ring">
              {/* Inner Ring */}
              <div ref={innerRingRef} className="inner-ring">
                {/* Core */}
                <div ref={coreRef} className="gyroscope-core">
                  <div className="core-center" />
                </div>
              </div>
            </div>
          </div>

          {/* Rotation Axes */}
          <div ref={axesRef} className="rotation-axes">
            <div className="axis-horizontal" />
            <div className="axis-vertical" />
            <div className="axis-diagonal" />
          </div>
        </div>
        
        <div className="gyroscope-info">
          <p className="info-line info-primary">✨ Drag to manipulate quantum fields with GSAP precision</p>
          <p className="info-line info-secondary">Enhanced with GreenSock Animation Platform</p>
          <p className="info-line info-tertiary">Smooth 60fps 3D transforms</p>
        </div>
      </div>
    </div>
  );
};

export default GSAPGyroscope;