import { useState, useEffect, useRef } from 'react';

interface RotationState {
  x: number;
  y: number;
  z: number;
}

interface ParticleState {
  id: number;
  x: number;
  y: number;
  z: number;
  vx: number;
  vy: number;
  vz: number;
  life: number;
  maxLife: number;
}

const Gyroscope = () => {
  const [rotation, setRotation] = useState<RotationState>({ x: 0, y: 0, z: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [momentum, setMomentum] = useState<RotationState>({ x: 0, y: 0, z: 0 });
  const [particles, setParticles] = useState<ParticleState[]>([]);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const animationRef = useRef<number>();
  const particleIdRef = useRef(0);

  // Initialize particles
  useEffect(() => {
    const initParticles = () => {
      const newParticles: ParticleState[] = [];
      for (let i = 0; i < 20; i++) {
        newParticles.push({
          id: particleIdRef.current++,
          x: Math.random() * 400 - 200,
          y: Math.random() * 400 - 200,
          z: Math.random() * 200 - 100,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          vz: (Math.random() - 0.5) * 1,
          life: Math.random() * 100,
          maxLife: 100 + Math.random() * 50
        });
      }
      setParticles(newParticles);
    };
    initParticles();
  }, []);

  // Enhanced animation loop with particle physics
  useEffect(() => {
    const animate = () => {
      if (!isDragging) {
        setRotation(prev => ({
          x: (prev.x + momentum.x + 0.3) % 360,
          y: (prev.y + momentum.y + 0.5) % 360,
          z: (prev.z + momentum.z + 0.2) % 360
        }));
        setMomentum(prev => ({
          x: prev.x * 0.985,
          y: prev.y * 0.985,
          z: prev.z * 0.985
        }));
      }

      // Update particles
      setParticles(prev => {
        return prev.map(particle => {
          const newParticle = {
            ...particle,
            x: particle.x + particle.vx,
            y: particle.y + particle.vy,
            z: particle.z + particle.vz,
            life: particle.life + 1,
            vx: particle.vx * 0.99 + (Math.random() - 0.5) * 0.1,
            vy: particle.vy * 0.99 + (Math.random() - 0.5) * 0.1,
            vz: particle.vz * 0.99 + (Math.random() - 0.5) * 0.05
          };

          // Reset particle if it's too old or too far
          if (newParticle.life > newParticle.maxLife || 
              Math.abs(newParticle.x) > 300 || 
              Math.abs(newParticle.y) > 300) {
            return {
              id: particleIdRef.current++,
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              z: Math.random() * 50 - 25,
              vx: (Math.random() - 0.5) * 3,
              vy: (Math.random() - 0.5) * 3,
              vz: (Math.random() - 0.5) * 1.5,
              life: 0,
              maxLife: 100 + Math.random() * 100
            };
          }

          return newParticle;
        });
      });

      animationRef.current = requestAnimationFrame(animate);
    };
    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) cancelAnimationFrame(animationRef.current);
    };
  }, [isDragging, momentum]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    lastMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    const deltaX = e.clientX - lastMousePos.current.x;
    const deltaY = e.clientY - lastMousePos.current.y;
    const rotationSpeed = 0.8;

    setRotation(prev => ({
      x: (prev.x + deltaY * rotationSpeed) % 360,
      y: (prev.y + deltaX * rotationSpeed) % 360,
      z: (prev.z + (deltaX + deltaY) * 0.2) % 360
    }));

    setMomentum({
      x: deltaY * 0.15,
      y: deltaX * 0.15,
      z: (deltaX + deltaY) * 0.08
    });

    lastMousePos.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseUp = () => setIsDragging(false);

  const getParticleStyle = (particle: ParticleState): React.CSSProperties => {
    const opacity = Math.max(0, 1 - (particle.life / particle.maxLife));
    const size = Math.max(1, 4 - (particle.life / particle.maxLife) * 3);
    const perspective = 400;
    const scale = Math.max(0.1, 1 - Math.abs(particle.z) / 200);

    return {
      position: 'absolute' as const,
      left: '50%',
      top: '50%',
      width: `${size}px`,
      height: `${size}px`,
      transform: `
        translate(-50%, -50%)
        translate3d(${particle.x * scale}px, ${particle.y * scale}px, ${particle.z}px)
        scale(${scale})
      `,
      opacity: opacity,
      background: `radial-gradient(circle,
        ${particle.z > 0 ? '#ffd700' : '#ff6b6b'} 0%,
        ${particle.z > 0 ? '#ff8c00' : '#ff1493'} 100%
      )`,
      borderRadius: '50%',
      boxShadow: `0 0 ${size * 3}px ${particle.z > 0 ? '#ffd70088' : '#ff69b488'}`,
      pointerEvents: 'none' as const,
      zIndex: Math.round(particle.z + 100)
    };
  };

  return (
    <div className="w-full h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 overflow-hidden">
      {/* Ambient background effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500 rounded-full blur-3xl opacity-20 animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full blur-3xl opacity-20 animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-pink-500 rounded-full blur-3xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="text-center relative z-10">
        <h1 className="text-4xl font-bold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
          Quantum Gyroscope
        </h1>
        
        <div 
          className="relative w-96 h-96 mx-auto cursor-grab active:cursor-grabbing select-none"
          style={{ perspective: '1000px' }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Particle System */}
          {particles.map(particle => (
            <div
              key={particle.id}
              style={getParticleStyle(particle)}
            />
          ))}

          {/* Outer Ring - Holographic */}
          <div
            className="absolute inset-0 rounded-full border-4 bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"
            style={{
              transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,
              transition: isDragging ? 'none' : 'transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: `
                0 0 30px #3b82f6,
                inset 0 0 30px #3b82f6,
                0 0 60px #3b82f6
              `,
              border: '4px solid #3b82f6',
              borderRadius: '50%'
            }}
          >
            {/* Middle Ring - Energy Field */}
            <div
              className="absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-purple-500/40 to-transparent"
              style={{
                transform: `rotateX(${rotation.x * 1.5}deg) rotateY(${rotation.y * 0.7}deg) rotateZ(${rotation.z * -1}deg)`,
                boxShadow: `
                  0 0 40px #8b5cf6,
                  inset 0 0 40px #8b5cf6
                `,
                border: '4px solid #8b5cf6',
                borderRadius: '50%'
              }}
            >
              {/* Inner Ring - Core Energy */}
              <div
                className="absolute inset-8 rounded-full border-4 bg-gradient-to-r from-transparent via-pink-500/50 to-transparent"
                style={{
                  transform: `rotateX(${rotation.x * 0.5}deg) rotateY(${rotation.y * 1.8}deg) rotateZ(${rotation.z * 1.5}deg)`,
                  boxShadow: `
                    0 0 50px #ec4899,
                    inset 0 0 50px #ec4899
                  `,
                  border: '4px solid #ec4899',
                  borderRadius: '50%'
                }}
              >
                {/* Quantum Core */}
                <div 
                  className="absolute inset-8 rounded-full bg-gradient-to-br from-white via-blue-200 to-purple-300"
                  style={{
                    transform: `rotateX(${rotation.x * -0.3}deg) rotateY(${rotation.y * -0.5}deg) rotateZ(${rotation.z * 2}deg)`,
                    boxShadow: `
                      0 0 60px #ffffffaa,
                      inset 0 0 20px #3b82f688
                    `
                  }}
                >
                  {/* Plasma Center */}
                  <div 
                    className="absolute top-1/2 left-1/2 w-8 h-8 -mt-4 -ml-4 rounded-full bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse"
                    style={{
                      boxShadow: '0 0 30px #fbbf24aa, 0 0 60px #f97316aa'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Rotation Axes */}
          <div className="absolute -inset-16 pointer-events-none">
            {/* X-Axis */}
            <div 
              className="absolute top-1/2 left-0 right-0 h-0.5 opacity-60" 
              style={{ 
                transform: `rotateZ(${rotation.x}deg)`,
                background: 'linear-gradient(90deg, transparent, #ef4444, transparent)',
                boxShadow: '0 0 10px #ef4444'
              }} 
            />
            {/* Y-Axis */}
            <div 
              className="absolute top-0 bottom-0 left-1/2 w-0.5 opacity-60" 
              style={{ 
                transform: `rotateZ(${rotation.y}deg)`,
                background: 'linear-gradient(180deg, transparent, #22c55e, transparent)',
                boxShadow: '0 0 10px #22c55e'
              }} 
            />
            {/* Z-Axis */}
            <div 
              className="absolute top-1/2 left-1/2 w-32 h-0.5 -ml-16 opacity-60" 
              style={{ 
                transform: `rotateZ(${rotation.z}deg)`,
                background: 'linear-gradient(90deg, transparent, #3b82f6, transparent)',
                boxShadow: '0 0 10px #3b82f6'
              }} 
            />
          </div>

          {/* Orbit rings */}
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full border opacity-20 pointer-events-none"
              style={{
                inset: `${-20 - i * 30}px`,
                borderColor: ['#3b82f6', '#8b5cf6', '#ec4899'][i],
                transform: `rotateX(${rotation.x * (0.3 + i * 0.1)}deg) rotateY(${rotation.y * (0.2 + i * 0.1)}deg)`,
                animation: `spin ${10 + i * 5}s linear infinite`
              }}
            />
          ))}
        </div>
        
        <div className="mt-6 text-sm text-gray-300 space-y-1">
          <p className="text-blue-400">✨ Click and drag to manipulate quantum fields</p>
          <p className="font-mono text-xs">
            Rotation: X: {Math.round(rotation.x)}° Y: {Math.round(rotation.y)}° Z: {Math.round(rotation.z)}°
          </p>
          <p className="text-purple-400 text-xs">Active Particles: {particles.length}</p>
        </div>
      </div>

      <style>{`
        @keyframes spin {
          from { transform: rotateZ(0deg); }
          to { transform: rotateZ(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Gyroscope;